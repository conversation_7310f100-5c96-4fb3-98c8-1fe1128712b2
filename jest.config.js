module.exports = {
  // Basic configuration
  preset: 'ts-jest',
  testEnvironment: 'node',

  // File patterns
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: 'src',
  testRegex: '.*\\.spec\\.ts$',

  // Transform configuration
  transform: {
    '^.+\\.(t|j)s$': [
      'ts-jest',
      {
        tsconfig: 'tsconfig.json',
        isolatedModules: true,
      },
    ],
  },

  // Coverage configuration
  collectCoverageFrom: ['**/*.(t|j)s', '!**/*.spec.ts', '!**/*.e2e-spec.ts', '!**/node_modules/**', '!**/dist/**'],
  coverageDirectory: '../coverage',
  coverageReporters: ['text', 'lcov', 'html'],

  // Memory and performance optimizations
  maxWorkers: 4, // Single worker to prevent memory issues
  workerIdleMemoryLimit: '512MB', // Restart workers when they exceed this limit

  // Cleanup and isolation
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,

  // Timeouts and detection
  testTimeout: 30000, // 30 second timeout per test
  detectOpenHandles: true,
  forceExit: true,

  // Performance optimizations
  cache: false, // Disable caching to prevent memory accumulation
  watchman: false, // Disable watchman for CI/memory-constrained environments
  logHeapUsage: true, // Log memory usage

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/test-setup.ts'],

  // Module resolution
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/$1',
  },

  // Test execution
  bail: false, // Don't stop on first failure
  verbose: false, // Reduce verbose output to save memory
  silent: false,

  // Error handling
  errorOnDeprecated: true,

  // Test environment options
  testEnvironmentOptions: {
    // Node.js specific options
  },

  // Reporter configuration
  reporters: ['default'],

  // Ignore patterns
  testPathIgnorePatterns: ['/node_modules/', '/dist/', '/coverage/'],

  // Transform ignore patterns
  transformIgnorePatterns: ['/node_modules/(?!(.*\\.mjs$))'],
};

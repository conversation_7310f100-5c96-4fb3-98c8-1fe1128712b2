## Installation

```bash
$ npm install
```

## Running the app

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Test

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Database Migrations

Migrations are crucial for managing the database schema changes. They are applied automatically when starting the application, ensuring that your database schema is always aligned with the entity definitions in your code.

### Managing Migrations

Here are several options for managing migrations in this project:

#### 1. Generating Migrations

To generate a new migration file based on changes to your entities, use the following command. This script auto-generates SQL changes needed to synchronize the database schema with your entity models. It creates a migration file in the `./src/migrations` directory, which you can review and adjust if necessary.

```bash
npm run migration:generate
```

#### 2. Creating Empty Migration Files

If you need to write a migration manually (for example, to perform complex schema changes or data transformations), you can create an empty migration file using:

```bash
npm run migration:create
```

#### 3. Running Migrations

To apply all new migrations to your database, run the following command:

```bash
npm run migration:run
```

This command applies all pending migrations, updating your database schema to match the current state of your entities as defined in the project.

#### 4. Reverting Migrations

If you need to undo the last migration applied, you can use:

```bash
npm run migration:revert
```

### Tips for Migrations

- Always review migration files generated by the **migration:generate** command before applying them, especially when preparing to deploy to a production environment.
- Use the **migration:create** command to handle custom or complex database transformations that aren't covered by the auto-generated migrations.

# Base image
FROM node:20-alpine AS base

# Create app directory
WORKDIR /usr/src/app

# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package*.json ./

# Install app dependencies
RUN npm install

FROM base

ARG GIT_SHA
ARG DATE
ENV DD_VERSION="${GIT_SHA} ${DATE}"

# Bundle app source
COPY . .

# Creates a "dist" folder with the production build
RUN npm run build

# Start the server using the production build
CMD [ "node", "dist/main.js" ]

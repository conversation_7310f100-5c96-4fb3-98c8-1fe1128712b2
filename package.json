{"name": "tallo-be", "version": "0.8.5", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest build --webpack --webpackPath webpack-hmr.config.js --watch", "start:debug": "nest start --debug --watch", "start:prod": "node --trace-deprecation dist/main", "typeorm": "ts-node ./node_modules/typeorm/cli", "migration:run": "npm run build && npm run typeorm migration:run -- -d ./src/config/orm/typeorm.ts", "migration:generate": "FORCE_COMPILE=true ts-node ./src/scripts/generate-migration.ts", "migration:create": "ts-node ./src/scripts/create-migration.ts", "migration:revert": "npm run build && npm run typeorm -- -d ./src/config/orm/typeorm.ts migration:revert", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "node --max-old-space-size=4096 --expose-gc node_modules/jest/bin/jest.js", "test:watch": "node --max-old-space-size=2048 --expose-gc node_modules/jest/bin/jest.js --watch", "test:cov": "node --max-old-space-size=2048 --expose-gc node_modules/jest/bin/jest.js --coverage", "test:debug": "node --inspect-brk --max-old-space-size=2048 --expose-gc -r tsconfig-paths/register -r ts-node/register node_modules/jest/bin/jest.js --runInBand", "test:e2e": "node --max-old-space-size=2048 --expose-gc node_modules/jest/bin/jest.js --config ./test/jest-e2e.json", "prepare": "husky install"}, "dependencies": {"@aws-sdk/client-s3": "3.738.0", "@inquirer/prompts": "7.2.4", "@langchain/anthropic": "0.3.19", "@langchain/core": "0.3.49", "@langchain/deepseek": "0.0.1", "@langchain/openai": "0.5.7", "@nestjs/axios": "4.0.0", "@nestjs/common": "11.0.6", "@nestjs/config": "4.0.0", "@nestjs/core": "11.0.6", "@nestjs/jwt": "11.0.0", "@nestjs/passport": "11.0.5", "@nestjs/platform-express": "^11.0.6", "@nestjs/platform-socket.io": "11.0.6", "@nestjs/schedule": "5.0.1", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "11.0.3", "@nestjs/typeorm": "11.0.0", "@nestjs/websockets": "11.0.6", "@sendgrid/mail": "8.1.4", "@sentry/tracing": "7.120.3", "@types/heic-convert": "^2.1.0", "basic-auth": "2.0.1", "bcrypt": "5.1.1", "city-timezones": "1.3.0", "class-transformer": "0.5.1", "class-validator": "0.14.1", "csv-stringify": "6.6.0", "date-fns": "4.1.0", "date-fns-tz": "3.2.0", "dd-trace": "5.48.1", "exceljs": "^4.4.0", "file-type": "20.0.0", "form-data": "4.0.1", "google-auth-library": "^9.15.1", "googleapis": "144.0.0", "heic-convert": "2.1.0", "hot-shots": "10.2.1", "image-type": "5.2.0", "moment-timezone": "0.5.47", "multer": "1.4.5-lts.1", "openai": "4.81.0", "passport": "0.7.0", "passport-google-oauth2": "0.2.0", "passport-jwt": "4.0.1", "passport-local": "1.0.0", "pg": "8.13.1", "reflect-metadata": "0.2.2", "rxjs": "7.8.1", "sharp": "^0.33.5", "sib-api-v3-sdk": "8.5.0", "smartystreets-javascript-sdk": "6.3.0", "stripe": "17.4.0", "ts-retry": "5.0.1", "twilio": "5.4.3", "typeorm": "0.3.20", "uuid": "11.0.5", "winston": "3.17.0", "xmlbuilder2": "3.1.1", "zod": "3.24.3"}, "devDependencies": {"@commitlint/cli": "19.6.1", "@commitlint/config-conventional": "19.6.0", "@faker-js/faker": "9.4.0", "@nestjs/cli": "11.0.2", "@nestjs/schematics": "11.0.0", "@nestjs/testing": "11.0.6", "@types/bcrypt": "5.0.2", "@types/express": "5.0.0", "@types/inquirer": "9.0.7", "@types/jest": "29.5.14", "@types/multer": "1.4.12", "@types/node": "22.12.0", "@types/passport-google-oauth2": "0.1.10", "@types/passport-jwt": "4.0.1", "@types/passport-local": "1.0.38", "@types/smartystreets-javascript-sdk": "5.1.0", "@types/supertest": "6.0.2", "@typescript-eslint/eslint-plugin": "8.22.0", "@typescript-eslint/parser": "8.22.0", "eslint": "8.57.1", "eslint-config-prettier": "8.10.0", "eslint-plugin-prettier": "5.2.3", "husky": "8.0.3", "inquirer": "12.3.3", "jest": "29.7.0", "lint-staged": "15.4.3", "prettier": "3.4.2", "run-script-webpack-plugin": "0.2.0", "source-map-support": "0.5.21", "supertest": "6.3.3", "ts-jest": "29.2.5", "ts-loader": "9.5.2", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.7.3", "webpack": "5.97.1", "webpack-node-externals": "3.0.0"}, "lint-staged": {"src/**/*.+(js|json|ts)": ["eslint --fix"], "*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write --ignore-unknown"]}}
# Tallo Backend - AI Coding Agent Instructions

## Business Overview

Tallo is an **AI-powered real estate rental platform** that automates the entire leasing process between property owners (investors) and prospective renters. The platform uses advanced AI to handle communications, screening, and application management.

### Core Business Flow

1. **Property Syndication**: Property owners add their rental properties to the platform and syndicate them to listing channels (currently supports Zillow with auto-refresh functionality)
2. **AI-Powered Lead Management**: When renters inquire about properties, Tallo AI handles all communications - answering questions about properties, neighborhood details, rental terms, and managing conversation context
3. **Automated Showing Coordination**: The AI schedules property showings by matching renter preferences with owner availability slots, handles showing requests, confirmations, and reminders
4. **Intelligent Follow-ups**: Automated follow-up sequences for various scenarios - renter stopped responding, showing reminders, post-showing feedback, and investor notifications
5. **Smart Renter Screening**: AI screens prospective renters by collecting income, credit score, and move-in date information according to criteria set by property owners, with qualification scoring
6. **Application Management**: When renters are qualified, owners can send rental applications through the platform (lease signing happens externally)

The platform eliminates manual work for property owners while providing a seamless, responsive experience for renters through AI-driven automation.

## Architecture Overview

This is a **NestJS TypeScript backend** for the Tallo platform. The architecture follows domain-driven design with clear module boundaries.

### Core Domains

- **Investor Module**: Property owners managing listings, showings, applications
- **Renter Module**: Tenants browsing properties, requesting showings, submitting applications
- **Shared Module**: Cross-cutting concerns (auth, communication, files, applications)
- **AI Module**: LangChain-powered conversation analysis and property data extraction

### Key Business Entities

- **Property**: Core asset with nested details (specs, amenities, location, images)
- **Application/ApplicationBundle**: Rental applications with multiple applicants
- **Conversation/Messages**: AI-managed communication between parties
- **Showing/ShowingRequest**: Property viewing coordination

## Development Patterns

### 1. Module Structure

Every domain follows this pattern:

```
module/
  ├── entity.module.ts         # TypeORM imports, DI setup
  ├── entity.controller.ts     # API endpoints
  ├── entity.service.ts        # Business logic
  ├── entity.entity.ts         # TypeORM entity
  └── dto/                     # Data transfer objects
```

### 2. Service Layer Pattern

Services use **Repository Pattern** with TypeORM:

```typescript
@Injectable()
export class ExampleService {
  constructor(
    @InjectRepository(Entity)
    private readonly entityRepository: Repository<Entity>,
  ) {}
```

### 3. Authentication & Authorization

- **JWT + Basic Auth guards** applied globally in `app.module.ts`
- **Role-based access** via `@HasRoles(Role.INVESTOR)` decorator
- **Resource ownership guards** like `IsPropertyOwnerGuard` for fine-grained access
- **Basic auth disabled locally** via `IS_LOCALHOST` env check in `BasicAuthGuard`

### 4. AI Integration (LangChain)

Uses multiple LLM providers (OpenAI, Anthropic, DeepSeek) for:

- **Conversation analysis**: Determining next steps in renter communications
- **Property data extraction**: Parsing free-text property descriptions
- **Image analysis**: Computer vision for property photos

AI service pattern:

```typescript
async getResponse(
  inputVariables: InputVariables,
  template: string,
  memory: PostgresChatTypeMemory,
  model: LanguageModelsEnum = LanguageModelsEnum.GPT_4
): Promise<any>
```

## Development Workflows

### Commit Message Conventions

Follow **Conventional Commits** specification with Tallo-specific patterns:

**🚨 MANDATORY FOR ALL COMMITS: Every commit message MUST include `ENG-XXX` placeholder 🚨**

#### Format

```
<type>(<scope>): ENG-XXX - <description>

[optional body]

[optional footer(s)]
```

**CRITICAL**: Always add `ENG-XXX` as placeholder for ticket number, it will be populated manually by developer.

#### Required Elements

1. **Type**: Must be one of the allowed types from `.commitlintrc.json`:

   - `feat`: New features or enhancements
   - `fix`: Bug fixes and corrections
   - `refactor`: Code restructuring without functionality changes
   - `chore`: Maintenance tasks, dependency updates
   - `docs`: Documentation changes
   - `test`: Test additions or modifications
   - `ci`: CI/CD pipeline changes
   - `perf`: Performance improvements
   - `style`: Code formatting, linting fixes
   - `revert`: Reverting previous commits

2. **Ticket Number**: Always include the JIRA ticket using `ENG-XXX` format

   - **CRITICAL FOR GITHUB COPILOT**: Always generate commit messages with `ENG-XXX` placeholder, never omit it
   - **Example**: Generate `refactor: ENG-XXX - update method name from 'enhance' to 'enhanceImage'` NOT `refactor: update method name from 'enhance' to 'enhanceImage' for consistency across image enhancer services`
   - **NEVER generate commits without ENG-XXX**: Every commit must follow this pattern

3. **Description**: Clear, concise explanation of the change

#### Examples

```bash
# Feature with ticket number from branch
feat: ENG-XXX - add default copilot instruction generated by copilot suggestion

# Bug fix with scope
fix: ENG-XXX - fix issue when tallo selects single credit strategy instead of both credit and income

# Scoped fix for specific module
fix(post-showing-interview): ENG-XXX - fix post interview email error

# Feature with major functionality
feat: ENG-XXX - add renter qualifications feature

docs: ENG-XXX - update copilot instructions to enforce ENG-XXX placeholder for ticket numbers in commit messages
refactor: ENG-XXX - update method name from 'enhance' to 'enhance'
test: ENG-XXX - add unit tests for image enhancer service
```

#### Branch-Based Workflow

- **Use descriptive scope** when change affects specific module (e.g., `(auth)`, `(renter)`, `(investor)`)
- **Reference PR numbers** in merge commits when applicable

#### Best Practices

- **Start description with verb**: "add", "fix", "update", "remove"
- **Use lowercase**: All descriptions should be lowercase
- **Be specific**: Avoid vague terms like "update stuff" or "fix issues"
- **Reference related entities**: Mention specific modules, services, or features affected

#### Validation

Commits are validated by `@commitlint/config-conventional` with custom rules:

- No header length limit
- No subject case enforcement
- Strict type enumeration
- Required ticket number format

### Database Migrations

**CRITICAL**: Always generate migrations, never use `synchronize: true`

```bash
# Generate migration from entity changes
npm run migration:generate

# Create empty migration for custom SQL
npm run migration:create

# Apply migrations
npm run migration:run
```

### Testing

```bash
# Unit tests with memory optimization
npm run test

# Watch mode
npm run test:watch
```

### Local Development

```bash
# HMR-enabled development
npm run start:dev

# Environment-aware config in src/config/envs/.env
# IS_LOCALHOST=true disables BasicAuth
```

## Configuration Patterns

### Environment Configuration

- **Validated config** via `DotEnvConfig` class with class-validator
- **Type-safe access** via `ConfigService.get()`
- **Environment-specific behavior** using `IS_LOCALHOST`, `IS_STAGING`, `IS_PRODUCTION`

### TypeORM Configuration

- **Entity auto-discovery**: `dist/**/*.entity{.ts,.js}`
- **Migration-based schema management**: No synchronization in production
- **Custom logger**: `TypeormLogger` for query debugging

## Communication Architecture

### Messaging System

- **Conversation/Message entities** store all communications
- **AI-driven response generation** analyzes conversation context
- **Multi-channel delivery**: Email (SendGrid) + SMS (Twilio) via `OutboundCommunicationService`
- **Follow-up automation** for showing requests and applications

### WebSocket Integration

Real-time updates via `@nestjs/websockets` for live messaging.

## File & Image Handling

### File Management

- **S3 integration** for file storage via `@aws-sdk/client-s3`
- **Image processing** with Sharp for optimization
- **HEIC conversion** support for mobile uploads
- **AI image analysis** for property feature extraction

## Security & Monitoring

### Security

- **Helmet.js** for security headers
- **CORS enabled** for cross-origin requests
- **Input validation** via class-validator pipes
- **Global exception filters** for consistent error handling

### Monitoring

- **DataDog tracing** with `dd-trace`
- **StatsD metrics** via `hot-shots`
- **Winston logging** with custom log levels
- **Sentry integration** for error tracking

## Common Pitfalls

1. **Memory Issues**: Use single Jest worker (`maxWorkers: 4`) and memory limits
2. **Circular Dependencies**: Use `forwardRef()` when modules depend on each other
3. **Migration Order**: Always test migrations in staging before production
4. **AI Timeouts**: Set appropriate timeouts for LLM calls (15s default)
5. **File Uploads**: Validate file types and sizes in multer configuration

## Key Files to Reference

- `src/app.module.ts` - Global guards and module structure
- `src/main.ts` - App bootstrap with global pipes and filters
- `src/config/orm/typeorm.ts` - Database configuration
- `src/modules/ai/ai.service.ts` - LangChain integration patterns
- `src/modules/shared/auth/` - Authentication and authorization
- `src/modules/testing/data-gen.controller.ts` - Test data generation patterns

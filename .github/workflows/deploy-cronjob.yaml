name: Deployment - <PERSON>ronJobs

on:
  workflow_dispatch:
    inputs:
      stage:
        required: true
        default: "production"
      aws_region:
        required: true
        default: "us-east-1"
      tag:
        required: true
        default: "main"

concurrency:
  group: ${{ github.workflow }}-${{ github.event.inputs.stage }}

env:
  GA_SERVICE: cronjob
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: ${{ inputs.aws_region }}
  AWS_ACCOUNT_ID: ************
  STAGE: ${{ inputs.stage }}
  HELM_BRANCH: ${{ inputs.tag }}

jobs:
  deploy-eks:
    name: "Deploy to EKS"
    runs-on: ubuntu-latest

    steps:
    - name: Map AWS resources to stage ${{ env.STAGE }}
      uses: kanga333/variable-mapper@master
      with:
        key: "${{ env.STAGE }}"
        map: |
          {
            "production": {
              "namespace": "tallo-be-production",
              "cluster_name": "tallo-usea1-eks-production"
            },
            "staging": {
              "namespace": "tallo-be-staging",
              "cluster_name": "tallo-usea1-eks-production"
            }
          }
    
    - name: Fetch helm manifests
      uses: actions/checkout@master
      with:
        repository: TalloAI/tallo-helm
        ref: refs/heads/${{ env.HELM_BRANCH }}
        path: helm
        token: ${{ secrets.GH_TOKEN }}

    - name: Install awscli
      run: |
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o /tmp/awscliv2.zip
        unzip -q /tmp/awscliv2.zip -d /tmp
        rm /tmp/awscliv2.zip
        sudo /tmp/aws/install --update
        rm -rf /tmp/aws/

    - name: Install kubectl
      run: |
        set +x
        VERSION=$(curl --silent https://storage.googleapis.com/kubernetes-release/release/stable.txt)
        curl https://storage.googleapis.com/kubernetes-release/release/$VERSION/bin/linux/amd64/kubectl \
            --progress-bar \
            --location \
            --remote-name
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/

    - name: Configure kubectl
      run: |
        aws eks update-kubeconfig --name ${{ env.cluster_name }}

    - name: Install helm
      run: |
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

    #Deploy to appropirate namespace
    - name: Deploy to eks
      env:
        K8S_NAMESPACE: ${{ env.namespace }}
      run: |
        set -x
        helm -n ${{ env.K8S_NAMESPACE }} uninstall ${{ env.GA_SERVICE }} --ignore-not-found
        helm --namespace ${{ env.K8S_NAMESPACE }} \
          upgrade --install \
          ${{ env.GA_SERVICE }} \
          helm/tallo-be-cronjob \
          -f helm/tallo-be-cronjob/values/${{ env.STAGE }}/values.yaml \
          --set tempVersion=$(printf "%s:%s" $(date +%s) $(openssl rand -hex 32) | openssl dgst -sha256 | sed 's/^.* //')

name: Build tallo-be docker image

on:
  pull_request:
  push:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.ref_name }}
  cancel-in-progress: true

env:
  GA_SERVICE: tallo-be

jobs:
  build-docker-image:
    name: Build docker image
    runs-on: github-hosted-arm64

    steps:
      - name: Checkout
        uses: actions/checkout@master
        with:
          ref: ${{ github.head_ref || github.ref_name }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@master

      - name: Define variables
        id: envVars
        run: |
          export VALUE=$(sed 's#/#-#g' <<< ${{ github.head_ref || github.ref_name }})
          echo "tag=$(awk '{print tolower($0)}' <<< $VALUE)" >> $GITHUB_OUTPUT
          echo "sha=$(git log --format=format:%H HEAD -1)" >> $GITHUB_OUTPUT
          echo "date=$(date +'%d-%m-%Y %H:%M:%S')" >> $GITHUB_OUTPUT

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push
        uses: docker/build-push-action@master
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ steps.envVars.outputs.tag }}
        with:
          context: .
          platforms: linux/arm64
          push: true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BUILD_NUMBER=${{ github.run_number }}
            GIT_SHA=${{ steps.envVars.outputs.sha }}
            DATE=${{ steps.envVars.outputs.date }}
          labels: |
            git.sha="${{ steps.envVars.outputs.sha }}"
            org.opencontainers.image.revision="${{ steps.envVars.outputs.sha }}"
          tags: |
            ${{ env.ECR_REGISTRY }}/${{ env.GA_SERVICE }}:${{ env.IMAGE_TAG }}
            ${{ env.ECR_REGISTRY }}/${{ env.GA_SERVICE }}:${{ steps.envVars.outputs.sha }}

      - name: Send failure to slack
        if: ${{ failure() }}
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: "${{ vars.SLACK_CHANNEL }}"
          SLACK_MSG_AUTHOR: "tallo"
          SLACK_COLOR: "failure"
          SLACK_MESSAGE: "
            Branch: `${{ github.head_ref || github.ref_name }}`\n
            Docker Image Tags: \n
              - `${{ steps.envVars.outputs.tag }}`\n
              - `${{ steps.envVars.outputs.sha }}`\n
            "
          SLACK_TITLE: "Tallo-BE: Failed to build docker image"
          MSG_MINIMAL: "actions url"

  deploy-staging:
    name: Deploy to staging
    if: github.ref_name == 'main'
    needs:
      - build-docker-image
    uses: ./.github/workflows/deploy.yaml
    with:
      tag: ${{ github.sha }}
      stage: 'staging'
      aws_region: 'us-east-1'
    secrets: inherit

  e2e:
    name: E2E
    if: github.ref_name == 'main'
    needs: deploy-staging
    uses: TalloAI/tallo-fe/.github/workflows/e2e.yml@main
    secrets: inherit

  approve-production:
    name: Approve production deployment
    if: github.ref_name == 'main'
    needs:
      - e2e
      - deploy-staging
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - uses: trstringer/manual-approval@v1
        env:
          IMAGE_TAG: ${{ github.sha }}
        with:
          secret: ${{ secrets.GH_TOKEN }}
          approvers: devs
          minimum-approvals: 1
          issue-title: "Production: Deploying ${{ github.sha }}"
          issue-body: "Please approve or deny the deployment of ${{ github.sha }} image tag"
          exclude-workflow-initiator-as-approver: false
          additional-approved-words: 'zaebok'
          additional-denied-words: 'ebat ti lox'

  deploy-production:
    name: Deploy to production
    needs:
      - approve-production
    if: github.ref_name == 'main'
    uses: ./.github/workflows/deploy.yaml
    with:
      tag: ${{ github.sha }}
      stage: 'production'
      aws_region: 'us-east-1'
    secrets: inherit

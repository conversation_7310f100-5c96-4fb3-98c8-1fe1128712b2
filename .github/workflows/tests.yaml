# This is a basic workflow to help you get started with Actions
name: Run tests

# Controls when the workflow will run
on:
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}
  cancel-in-progress: true

env:
  GA_SERVICE: tallo-be

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  build-docker-image:
    name: Run tests
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@master
    
    - name: Setup node
      uses: actions/setup-node@v4
      with:
        node-version: 20
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Cache node modules
      id: cache-nodemodules
      uses: actions/cache@v4
      env:
        cache-name: cache-node-modules
      with:
        # caching node_modules
        path: node_modules
        key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-build-${{ env.cache-name }}-
          ${{ runner.os }}-build-
          ${{ runner.os }}-

    - name: Install dependencies
      if: steps.cache-nodemodules.outputs.cache-hit != 'true'
      run: |
        echo "Installing dependencies"
        npm install

    - name: Run tests
      run: |
        echo "Running tests"
        npm run test
    
    - name: Send failure to slack
      if: ${{ failure() }}
      uses: rtCamp/action-slack-notify@v2
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        SLACK_CHANNEL: "${{ vars.SLACK_CHANNEL }}"
        SLACK_MSG_AUTHOR: "tallo"
        SLACK_COLOR: "failure"
        SLACK_MESSAGE: "
          Branch: `${{ github.ref_name }}`\n
          Commit: `${{ github.sha }}`\n
        "
        SLACK_TITLE: "Tallo-BE: Tests failed"
        MSG_MINIMAL: "actions url"

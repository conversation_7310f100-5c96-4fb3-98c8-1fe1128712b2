name: Deployment - tallo-be

on:
  workflow_call:
    inputs:
      tag:
        type: string
        description: 'Tag/Commit/Branch Name'
        required: true
      stage:
        type: string
        required: true
        default: "production"
      aws_region:
        type: string
        required: true
        default: "us-east-1"
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag/Commit/Branch Name'
        required: true
      stage:
        type: choice
        required: true
        options:
          - production
          - staging
      aws_region:
        required: true
        default: "us-east-1"

concurrency:
  group: ${{ github.workflow }}-${{ github.event.inputs.tag }}

env:
  GA_SERVICE: tallo-be
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: ${{ inputs.aws_region }}
  AWS_ACCOUNT_ID: ************
  STAGE: ${{ inputs.stage }}
  TAG: ${{ inputs.tag }}
  HELM_BRANCH: main
  CONFIGS_DEFAULT_BRANCH: main

jobs:
  deploy-eks:
    name: "${{ inputs.tag }} ${{ inputs.stage }}: Deploy to EKS"
    runs-on: ubuntu-latest
    outputs:
      tag: ${{ steps.envVars.outputs.tag }}
      stage: ${{ env.STAGE }}
      trigger_production: ${{ inputs.trigger_production }}

    steps:
    - name: Map AWS resources to stage ${{ env.STAGE }}
      uses: kanga333/variable-mapper@master
      with:
        key: "${{ env.STAGE }}"
        map: |
          {
            "production": {
              "namespace": "tallo-be-production",
              "cluster_name": "tallo-usea1-eks-production"
            },
            "staging": {
              "namespace": "tallo-be-staging",
              "cluster_name": "tallo-usea1-eks-production"
            }
          }

    - name: Prepare environment variables
      id: envVars
      run: |
        echo "tag=$(echo "${{ inputs.tag }}" | sed 's#/#-#g' | awk '{print tolower($0)}' )" >> $GITHUB_OUTPUT

    - name: Checkout pipeline code
      uses: actions/checkout@v3
      with:
        path: cicd
    
    - name: Fetch configs
      uses: actions/checkout@master
      with:
        repository: TalloAI/tallo-configs
        fetch-depth: 0
        path: configs
        token: ${{ secrets.GH_TOKEN }}
    
    - name: Fetch helm manifests
      uses: actions/checkout@master
      with:
        repository: TalloAI/tallo-helm
        ref: refs/heads/${{ env.HELM_BRANCH }}
        path: helm
        token: ${{ secrets.GH_TOKEN }}

    - name: Install awscli
      run: |
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o /tmp/awscliv2.zip
        unzip -q /tmp/awscliv2.zip -d /tmp
        rm /tmp/awscliv2.zip
        sudo /tmp/aws/install --update
        rm -rf /tmp/aws/

    - name: Install kubectl
      run: |
        set +x
        VERSION=$(curl --silent https://storage.googleapis.com/kubernetes-release/release/stable.txt)
        curl https://storage.googleapis.com/kubernetes-release/release/$VERSION/bin/linux/amd64/kubectl \
            --progress-bar \
            --location \
            --remote-name
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/

    - name: Configure kubectl
      run: |
        aws eks update-kubeconfig --name ${{ env.cluster_name }}

    - name: Install helm
      run: |
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
    
    - name: Create secrets
      env:
        K8S_NAMESPACE: ${{ env.namespace }}
      run: |
        kubectl -n ${{ env.K8S_NAMESPACE }} create secret generic tallo-be-secret-configs \
          --from-file=configs/tallo-be/${{ env.STAGE }}/.env \
          --save-config=true \
          --output=yaml \
          --dry-run=client \
          | kubectl apply -f -

    #Deploy to appropirate namespace
    - name: Deploy to eks
      env:
        K8S_NAMESPACE: ${{ env.namespace }}
      run: |
        # Install a new release
        helm --namespace ${{ env.K8S_NAMESPACE }} \
          upgrade --install \
          --wait --timeout 10m0s \
          ${{ env.GA_SERVICE }} \
          helm/tallo-be-argo-rollouts \
          -f helm/tallo-be-argo-rollouts/values/${{ env.STAGE }}/values.yaml \
          --set image.tag=${{ steps.envVars.outputs.tag }} \
          --set tempVersion=$(printf "%s:%s" $(date +%s) $(openssl rand -hex 32) | openssl dgst -sha256 | sed 's/^.* //')

    - name: Verify deployment
      timeout-minutes: 10
      env:
        K8S_NAMESPACE: ${{ env.namespace }}
      run: |
        sleep 10
        PREVIEW_HASH=$(kubectl -n ${{ env.K8S_NAMESPACE }} get rollout ${{ env.GA_SERVICE }} -o json | jq -r '.status.blueGreen.previewSelector')
        while true; do
          AVAILABLE_REPLICAS=$(kubectl -n ${{ env.K8S_NAMESPACE }} get rs -l rollouts-pod-template-hash=${PREVIEW_HASH} -o json | jq -r '.items[].status | select(.readyReplicas>=1) | .readyReplicas')
          if [ ! -z $AVAILABLE_REPLICAS ]; then
            break
          fi
          sleep 1
        done
    
    - name: Send failure to slack
      if: ${{ failure() }}
      uses: rtCamp/action-slack-notify@v2
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        SLACK_CHANNEL: "${{ vars.SLACK_CHANNEL }}"
        SLACK_MSG_AUTHOR: "tallo"
        SLACK_COLOR: "failure"
        SLACK_MESSAGE: "
            Stage: ${{ env.STAGE }}\n
            Docker Image Tags/Branches: \n
            - `${{ steps.envVars.outputs.tag }}`\n
            "
        SLACK_TITLE: "Tallo-BE: Failed to Deploy to ${{ env.STAGE }}"
        MSG_MINIMAL: "actions url"
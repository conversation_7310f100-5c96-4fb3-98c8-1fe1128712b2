{"eslint.enable": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "prettier.enable": true, "importSorter.generalConfiguration.sortOnBeforeSave": true, "importSorter.importStringConfiguration.maximumNumberOfImportExpressionsPerLine.type": "newLineEachExpressionAfterCountLimitExceptIfOnlyOne", "importSorter.importStringConfiguration.maximumNumberOfImportExpressionsPerLine.count": 120, "importSorter.importStringConfiguration.tabSize": 2, "importSorter.importStringConfiguration.quoteMark": "single", "tslint.autoFixOnSave": false, "tslint.enable": false, "editor.rulers": [120]}
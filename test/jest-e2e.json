{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "maxWorkers": 1, "workerIdleMemoryLimit": "512MB", "detectOpenHandles": true, "forceExit": true, "clearMocks": true, "resetMocks": true, "restoreMocks": true, "testTimeout": 60000, "logHeapUsage": true, "cache": false, "watchman": false, "setupFilesAfterEnv": ["<rootDir>/../src/test-setup.ts"]}
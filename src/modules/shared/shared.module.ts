import { Modu<PERSON> } from '@nestjs/common';
import { AuthModule } from './auth/auth.module';
import { CalendarModule } from './calendar/calendar.module';
import { CommunicationModule } from './communication/communication.module';
import { FileUploaderModule } from './file/file-uploader.module';
import { UserModule } from './user/user.module';
import { CompanyModule } from './company/company.module';
import { HealthcheckModule } from './healthcheck/healthcheck.module';
import { StripeModule } from './checkout/stripe/stripe.module';
import { ImageEnhancerModule } from './file/image-enhancer/image-enhancer.module';
import { SettingsModule } from './settings/settings.module';

@Module({
  imports: [
    AuthModule,
    UserModule,
    CompanyModule,
    CommunicationModule,
    FileUploaderModule,
    ImageEnhancerModule,
    HealthcheckModule,
    StripeModule,
    CalendarModule,
    SettingsModule,
  ],
})
export class SharedModule {}

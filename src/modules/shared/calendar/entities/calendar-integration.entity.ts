import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { User } from '../../user/entities/user.entity';
import { CalendarProvider } from '../models/calendar-provider.enum';

@Entity()
export class CalendarIntegration {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => User, (user) => user.calendars)
  @JoinColumn()
  user: User;

  @Column()
  userId: string;

  @Index()
  @Expose()
  @Column({ type: 'enum', enum: CalendarProvider })
  provider: CalendarProvider;

  @Column({ type: 'text' })
  accessToken: string;

  @Column({ type: 'text' })
  refreshToken: string;

  @Column({ type: 'timestamp', nullable: true })
  tokenExpiry: Date;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: true })
  isPrimary: boolean;

  @Column({ type: 'text', nullable: true })
  talloCalendarId: string;

  @Expose()
  @Column({ type: 'varchar', length: 255, nullable: true })
  userEmail: string;

  @DeleteDateColumn()
  deletedAt?: Date;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

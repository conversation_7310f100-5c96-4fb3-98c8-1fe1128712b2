import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsOptional, IsString, IsEnum } from 'class-validator';

export enum CalendarEventStatus {
  CONFIRMED = 'confirmed',
}

export class CalendarEventDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  startTime: string;

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  endTime: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  location?: string;

  @ApiProperty({ required: false, enum: CalendarEventStatus })
  @IsEnum(CalendarEventStatus)
  @IsOptional()
  status?: CalendarEventStatus;
}

export class CalendarEventResponseDto extends CalendarEventDto {
  @ApiProperty()
  id: string;
}

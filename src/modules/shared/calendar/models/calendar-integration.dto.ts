import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { CalendarProvider } from './calendar-provider.enum';

export class CalendarIntegrationDto {
  @ApiProperty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsEnum(CalendarProvider)
  provider: CalendarProvider;

  @ApiProperty()
  @IsBoolean()
  isActive: boolean;

  @ApiProperty()
  @IsBoolean()
  isPrimary: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  talloCalendarId?: string;

  @ApiProperty({ required: false, description: 'The email address of the connected calendar account' })
  @IsString()
  @IsOptional()
  userEmail?: string;
}

export class CreateCalendarIntegrationDto {
  @ApiProperty({
    description: 'The authorization code received from the calendar provider after the user grants permissions',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    enum: CalendarProvider,
    description: 'The calendar provider',
  })
  @IsEnum(CalendarProvider)
  provider: CalendarProvider;

  @ApiProperty({
    description: 'Whether this should be the primary calendar integration',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  isPrimary?: boolean;
}

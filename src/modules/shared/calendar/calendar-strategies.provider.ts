import { Provider } from '@nestjs/common';
import { CalendarProvider } from './models/calendar-provider.enum';
import { CalendarStrategy } from './strategies/calendar-strategy.interface';
import { GoogleCalendarStrategy } from './strategies/google-calendar.strategy';

export const CALENDAR_STRATEGIES = 'CALENDAR_STRATEGIES';

export const calendarStrategiesProvider: Provider = {
  provide: CALENDAR_STRATEGIES,
  useFactory: (googleCalendarStrategy: GoogleCalendarStrategy) => {
    const strategies = new Map<CalendarProvider, CalendarStrategy>();

    strategies.set(CalendarProvider.Google, googleCalendarStrategy);

    return strategies;
  },
  inject: [GoogleCalendarStrategy],
};

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../user/entities/user.entity';
import { UserModule } from '../user/user.module';
import { CalendarController } from './calendar.controller';
import { CalendarService } from './calendar.service';
import { CalendarIntegration } from './entities/calendar-integration.entity';
import { GoogleCalendarStrategy } from './strategies/google-calendar.strategy';
import { calendarStrategiesProvider } from './calendar-strategies.provider';

@Module({
  imports: [TypeOrmModule.forFeature([User, CalendarIntegration]), UserModule],
  controllers: [CalendarController],
  providers: [CalendarService, GoogleCalendarStrategy, calendarStrategiesProvider],
  exports: [CalendarService],
})
export class CalendarModule {}

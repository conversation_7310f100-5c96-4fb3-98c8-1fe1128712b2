import { Controller, Get, Post, Body, Delete, UseGuards, Param, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CalendarService } from './calendar.service';
import { Request } from 'express';
import { CalendarIntegrationDto, CreateCalendarIntegrationDto } from './models/calendar-integration.dto';

@ApiTags('calendar')
@Controller('calendar')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CalendarController {
  constructor(private readonly calendarService: CalendarService) {}

  @Get('auth-url')
  @ApiOperation({ summary: 'Get Google Calendar auth URL' })
  @ApiResponse({ status: 200, description: 'Returns the Google Calendar auth URL' })
  getGoogleAuthUrl(): { url: string; message: string } {
    return {
      url: this.calendarService.getGoogleAuthUrl(),
      message: 'Please visit this URL to connect your Google Calendar',
    };
  }

  @Post('link')
  @ApiOperation({ summary: 'Link user account with a calendar provider' })
  @ApiResponse({
    status: 201,
    description: 'Calendar linked successfully',
    type: CalendarIntegrationDto,
  })
  async linkCalendar(
    @Req() req: Request,
    @Body() createDto: CreateCalendarIntegrationDto,
  ): Promise<CalendarIntegrationDto> {
    return this.calendarService.linkCalendar(req.user.id, createDto);
  }

  @Delete('unlink/:provider')
  @ApiOperation({ summary: 'Unlink a calendar integration by provider' })
  @ApiParam({ name: 'provider', description: 'Calendar provider (e.g., "google")', enum: ['google'] })
  @ApiResponse({ status: 200, description: 'Calendar integration unlinked successfully' })
  @ApiResponse({ status: 404, description: 'Calendar integration not found' })
  async unlinkCalendarByProvider(
    @Req() req: Request,
    @Param('provider') provider: string,
  ): Promise<{ success: boolean; message: string }> {
    await this.calendarService.unlinkCalendarByProvider(req.user.id, provider);
    return { success: true, message: 'Calendar disconnected successfully' };
  }
}

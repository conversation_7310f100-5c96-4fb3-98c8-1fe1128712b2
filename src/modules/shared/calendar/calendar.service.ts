import { Injectable, BadRequestException, NotFoundException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { UserService } from '../user/user.service';
import { CalendarProvider } from './models/calendar-provider.enum';
import { CalendarEventDto, CalendarEventResponseDto } from './models/calendar-event.dto';
import { CalendarIntegration } from './entities/calendar-integration.entity';
import { CalendarIntegrationDto, CreateCalendarIntegrationDto } from './models/calendar-integration.dto';
import { CALENDAR_STRATEGIES } from './calendar-strategies.provider';
import { CalendarStrategy } from './strategies/calendar-strategy.interface';

@Injectable()
export class CalendarService {
  constructor(
    private readonly userService: UserService,
    @InjectRepository(CalendarIntegration)
    private readonly calendarIntegrationRepository: Repository<CalendarIntegration>,
    @Inject(CALENDAR_STRATEGIES)
    private readonly calendarStrategies: Map<CalendarProvider, CalendarStrategy>,
  ) {}

  getGoogleAuthUrl(): string {
    const googleStrategy = this.calendarStrategies.get(CalendarProvider.Google);
    if (!googleStrategy) {
      throw new BadRequestException('Google Calendar strategy not found');
    }
    return googleStrategy.getAuthUrl();
  }

  async linkCalendar(userId: string, createDto: CreateCalendarIntegrationDto): Promise<CalendarIntegrationDto> {
    try {
      const user = await this.userService.findById(userId);
      await this.validateNoExistingIntegration(userId, createDto.provider);
      const strategy = this.getCalendarStrategy(createDto.provider);

      if (createDto.isPrimary) {
        await this.calendarIntegrationRepository.update({ userId: user.id, isPrimary: true }, { isPrimary: false });
      }

      const integration = await strategy.linkCalendar(user, createDto);
      return this.mapToCalendarIntegrationDto(integration);
    } catch (error) {
      this.handleCalendarError(error, 'link calendar');
    }
  }

  /**
   * Validates that no existing integration exists for the given provider
   */
  private async validateNoExistingIntegration(userId: string, provider: CalendarProvider): Promise<void> {
    const existingIntegration = await this.calendarIntegrationRepository.findOne({
      where: {
        userId,
        provider,
      },
    });

    if (existingIntegration) {
      throw new BadRequestException({
        message: `A calendar integration for ${provider} already exists. Please unlink it first before adding a new one.`,
        code: 'INTEGRATION_ALREADY_EXISTS',
        integrationId: existingIntegration.id,
      });
    }
  }

  async unlinkCalendarByProvider(userId: string, providerStr: string): Promise<void> {
    try {
      const provider = this.validateCalendarProvider(providerStr);
      const integration = await this.findCalendarIntegrationByProvider(userId, provider);

      // Handle primary calendar reassignment
      if (integration.isPrimary) {
        await this.reassignPrimaryCalendar(userId, provider);
      }

      // Delete the Tallo calendar if it exists
      await this.deleteCalendarSafely(integration);

      // Remove the integration
      await this.calendarIntegrationRepository.delete(integration.id);
    } catch (error) {
      this.handleCalendarError(error, 'unlink calendar');
      throw error; // This line won't be reached due to handleCalendarError throwing, but TypeScript needs it
    }
  }

  /**
   * Validates and converts string to CalendarProvider enum
   */
  private validateCalendarProvider(providerStr: string): CalendarProvider {
    const provider = providerStr as CalendarProvider;
    if (!Object.values(CalendarProvider).includes(provider)) {
      throw new BadRequestException({
        message: `Invalid calendar provider: ${providerStr}`,
        code: 'INVALID_PROVIDER',
      });
    }
    return provider;
  }

  /**
   * Reassigns primary calendar to another active integration
   */
  private async reassignPrimaryCalendar(userId: string, currentProvider: CalendarProvider): Promise<void> {
    const otherIntegrations = await this.calendarIntegrationRepository
      .createQueryBuilder('integration')
      .where('integration.userId = :userId', { userId })
      .andWhere('integration.isActive = :isActive', { isActive: true })
      .andWhere('integration.provider != :provider', { provider: currentProvider })
      .orderBy('integration.createdAt', 'DESC')
      .take(1)
      .getMany();

    if (otherIntegrations.length > 0) {
      otherIntegrations[0].isPrimary = true;
      await this.calendarIntegrationRepository.save(otherIntegrations[0]);
    }
  }

  /**
   * Safely deletes the Tallo calendar, handling errors gracefully
   */
  private async deleteCalendarSafely(integration: CalendarIntegration): Promise<void> {
    if (!integration.talloCalendarId) {
      return;
    }

    try {
      const strategy = this.calendarStrategies.get(integration.provider);
      if (strategy) {
        await strategy.refreshTokenIfNeeded(integration);
        await strategy.deleteCalendar(integration);
      }
    } catch (error) {
      console.error(`Failed to delete Tallo calendar ${integration.talloCalendarId}: ${error.message}`);
    }
  }

  async getCalendarEvents(
    userId: string,
    startDate: Date,
    endDate: Date,
    provider?: CalendarProvider,
  ): Promise<CalendarEventResponseDto[]> {
    return this.executeWithIntegration(userId, provider, (strategy, integration) =>
      strategy.getCalendarEvents(integration, startDate, endDate),
    );
  }

  async createCalendarEvent(
    userId: string,
    eventDto: CalendarEventDto,
    provider?: CalendarProvider,
  ): Promise<CalendarEventResponseDto> {
    return this.executeWithIntegration(userId, provider, (strategy, integration) =>
      strategy.createCalendarEvent(integration, eventDto),
    );
  }

  async updateCalendarEvent(
    userId: string,
    eventId: string,
    eventDto: CalendarEventDto,
    provider?: CalendarProvider,
  ): Promise<CalendarEventResponseDto> {
    return this.executeWithIntegration(userId, provider, (strategy, integration) =>
      strategy.updateCalendarEvent(integration, eventId, eventDto),
    );
  }

  async deleteCalendarEvent(userId: string, eventId: string, provider?: CalendarProvider): Promise<void> {
    return this.executeWithIntegration(userId, provider, (strategy, integration) =>
      strategy.deleteCalendarEvent(integration, eventId),
    );
  }

  /**
   * Get all calendar integrations for a user
   */
  async getCalendarsByUserId(userId: string): Promise<CalendarIntegration[]> {
    return this.calendarIntegrationRepository.find({
      where: { user: { id: userId } },
    });
  }

  /**
   * Generic method to execute calendar operations with proper integration resolution and error handling
   */
  private async executeWithIntegration<T>(
    userId: string,
    provider: CalendarProvider | undefined,
    operation: (strategy: CalendarStrategy, integration: CalendarIntegration) => Promise<T>,
  ): Promise<T> {
    try {
      const integration = await this.resolveCalendarIntegration(userId, provider);
      const strategy = this.getCalendarStrategy(integration.provider);

      await strategy.refreshTokenIfNeeded(integration);
      return await operation(strategy, integration);
    } catch (error) {
      this.handleCalendarError(error, 'calendar operation');
    }
  }

  /**
   * Resolves calendar integration based on provider or gets primary integration
   */
  private async resolveCalendarIntegration(userId: string, provider?: CalendarProvider): Promise<CalendarIntegration> {
    if (provider) {
      return this.findCalendarIntegrationByProvider(userId, provider);
    }

    const integration = await this.calendarIntegrationRepository.findOne({
      where: { userId, isPrimary: true, isActive: true },
      relations: ['user'],
    });

    if (!integration) {
      throw new BadRequestException({
        message: 'No primary calendar connected. Please connect a calendar first.',
        code: 'CALENDAR_NOT_CONNECTED',
        authUrl: this.getGoogleAuthUrl(),
      });
    }

    return integration;
  }

  /**
   * Gets calendar strategy and validates it exists
   */
  private getCalendarStrategy(provider: CalendarProvider): CalendarStrategy {
    const strategy = this.calendarStrategies.get(provider);
    if (!strategy) {
      throw new BadRequestException({
        message: `Unsupported calendar provider: ${provider}`,
        code: 'UNSUPPORTED_PROVIDER',
      });
    }
    return strategy;
  }

  /**
   * Centralized error handling for calendar operations
   */
  private handleCalendarError(error: any, operation: string): never {
    if (error instanceof BadRequestException || error instanceof NotFoundException) {
      throw error;
    }

    // Log unexpected errors for debugging
    console.error(`Calendar service error during ${operation}:`, error);

    throw new BadRequestException({
      message: `Failed to perform ${operation}. Please try again or contact support if the issue persists.`,
      code: 'CALENDAR_OPERATION_FAILED',
      originalError: error.message,
    });
  }

  private async findCalendarIntegrationByProvider(
    userId: string,
    provider: CalendarProvider,
  ): Promise<CalendarIntegration> {
    const integration = await this.calendarIntegrationRepository.findOne({
      where: { provider, userId, isActive: true },
      relations: ['user'],
    });

    if (!integration) {
      throw new NotFoundException(`No calendar integration found for provider: ${provider}`);
    }

    return integration;
  }

  private mapToCalendarIntegrationDto(integration: CalendarIntegration): CalendarIntegrationDto {
    return {
      id: integration.id,
      provider: integration.provider,
      isActive: integration.isActive,
      isPrimary: integration.isPrimary,
      createdAt: integration.createdAt,
      talloCalendarId: integration.talloCalendarId,
      userEmail: integration.userEmail,
    };
  }
}

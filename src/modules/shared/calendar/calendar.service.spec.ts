import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BadRequestException, NotFoundException } from '@nestjs/common';

import { CalendarService } from './calendar.service';
import { UserService } from '../user/user.service';
import { CalendarIntegration } from './entities/calendar-integration.entity';
import { CalendarProvider } from './models/calendar-provider.enum';
import { CalendarStrategy } from './strategies/calendar-strategy.interface';
import { CALENDAR_STRATEGIES } from './calendar-strategies.provider';
import { CalendarEventDto, CalendarEventStatus } from './models/calendar-event.dto';
import { CreateCalendarIntegrationDto } from './models/calendar-integration.dto';

describe('CalendarService', () => {
  let service: CalendarService;
  let userService: jest.Mocked<UserService>;
  let calendarIntegrationRepository: jest.Mocked<Repository<CalendarIntegration>>;
  let mockGoogleStrategy: jest.Mocked<CalendarStrategy>;
  let calendarStrategies: Map<CalendarProvider, CalendarStrategy>;

  const mockUser = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
  };

  const mockIntegration = {
    id: 'integration-1',
    userId: 'user-1',
    provider: CalendarProvider.Google,
    accessToken: 'access-token',
    refreshToken: 'refresh-token',
    tokenExpiry: new Date(Date.now() + 3600000),
    isActive: true,
    isPrimary: true,
    talloCalendarId: 'tallo-calendar-id',
    userEmail: '<EMAIL>',
    user: mockUser,
    createdAt: new Date(),
  } as CalendarIntegration;

  beforeEach(async () => {
    mockGoogleStrategy = {
      getAuthUrl: jest.fn(),
      linkCalendar: jest.fn(),
      createCalendar: jest.fn(),
      deleteCalendar: jest.fn(),
      getCalendarEvents: jest.fn(),
      createCalendarEvent: jest.fn(),
      updateCalendarEvent: jest.fn(),
      deleteCalendarEvent: jest.fn(),
      refreshTokenIfNeeded: jest.fn(),
    };

    calendarStrategies = new Map();
    calendarStrategies.set(CalendarProvider.Google, mockGoogleStrategy);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CalendarService,
        {
          provide: UserService,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(CalendarIntegration),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: CALENDAR_STRATEGIES,
          useValue: calendarStrategies,
        },
      ],
    }).compile();

    service = module.get<CalendarService>(CalendarService);
    userService = module.get(UserService);
    calendarIntegrationRepository = module.get(getRepositoryToken(CalendarIntegration));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getGoogleAuthUrl', () => {
    it('should return Google auth URL', () => {
      const mockAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockGoogleStrategy.getAuthUrl.mockReturnValue(mockAuthUrl);

      const result = service.getGoogleAuthUrl();

      expect(result).toBe(mockAuthUrl);
      expect(mockGoogleStrategy.getAuthUrl).toHaveBeenCalled();
    });

    it('should throw error if Google strategy not found', () => {
      calendarStrategies.clear();

      expect(() => service.getGoogleAuthUrl()).toThrow(BadRequestException);
    });
  });

  describe('linkCalendar', () => {
    const createDto: CreateCalendarIntegrationDto = {
      provider: CalendarProvider.Google,
      code: 'auth-code',
      isPrimary: true,
    };

    it('should successfully link calendar', async () => {
      userService.findById.mockResolvedValue(mockUser as any);
      calendarIntegrationRepository.findOne.mockResolvedValue(null);
      mockGoogleStrategy.linkCalendar.mockResolvedValue(mockIntegration);

      const result = await service.linkCalendar('user-1', createDto);

      expect(result).toEqual({
        id: mockIntegration.id,
        provider: mockIntegration.provider,
        isActive: mockIntegration.isActive,
        isPrimary: mockIntegration.isPrimary,
        createdAt: mockIntegration.createdAt,
        talloCalendarId: mockIntegration.talloCalendarId,
        userEmail: mockIntegration.userEmail,
      });
      expect(userService.findById).toHaveBeenCalledWith('user-1');
      expect(mockGoogleStrategy.linkCalendar).toHaveBeenCalledWith(mockUser, createDto);
    });

    it('should throw error if integration already exists', async () => {
      userService.findById.mockResolvedValue(mockUser as any);
      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);

      await expect(service.linkCalendar('user-1', createDto)).rejects.toThrow(BadRequestException);
      expect(mockGoogleStrategy.linkCalendar).not.toHaveBeenCalled();
    });

    it('should update other integrations to not be primary when linking as primary', async () => {
      userService.findById.mockResolvedValue(mockUser as any);
      calendarIntegrationRepository.findOne.mockResolvedValue(null);
      mockGoogleStrategy.linkCalendar.mockResolvedValue(mockIntegration);

      await service.linkCalendar('user-1', createDto);

      expect(calendarIntegrationRepository.update).toHaveBeenCalledWith(
        { userId: 'user-1', isPrimary: true },
        { isPrimary: false },
      );
    });

    it('should throw error for unsupported provider', async () => {
      userService.findById.mockResolvedValue(mockUser as any);
      calendarIntegrationRepository.findOne.mockResolvedValue(null);
      calendarStrategies.clear();

      await expect(service.linkCalendar('user-1', createDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('unlinkCalendarByProvider', () => {
    it('should successfully unlink calendar', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      };

      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);
      calendarIntegrationRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      await service.unlinkCalendarByProvider('user-1', 'google');

      expect(calendarIntegrationRepository.findOne).toHaveBeenCalledWith({
        where: { provider: CalendarProvider.Google, userId: 'user-1', isActive: true },
        relations: ['user'],
      });
      expect(mockGoogleStrategy.refreshTokenIfNeeded).toHaveBeenCalledWith(mockIntegration);
      expect(mockGoogleStrategy.deleteCalendar).toHaveBeenCalledWith(mockIntegration);
      expect(calendarIntegrationRepository.delete).toHaveBeenCalledWith(mockIntegration.id);
    });

    it('should throw error for invalid provider', async () => {
      await expect(service.unlinkCalendarByProvider('user-1', 'InvalidProvider')).rejects.toThrow(BadRequestException);
    });

    it('should throw error if integration not found', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(null);

      await expect(service.unlinkCalendarByProvider('user-1', 'google')).rejects.toThrow(NotFoundException);
    });

    it('should reassign primary calendar when unlinking primary', async () => {
      const otherIntegration = { ...mockIntegration, id: 'integration-2', isPrimary: false };
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([otherIntegration]),
      };

      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);
      calendarIntegrationRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      await service.unlinkCalendarByProvider('user-1', 'google');

      expect(calendarIntegrationRepository.save).toHaveBeenCalledWith({
        ...otherIntegration,
        isPrimary: true,
      });
    });
  });

  describe('getCalendarEvents', () => {
    const startDate = new Date('2024-01-01');
    const endDate = new Date('2024-01-31');
    const mockEvents = [
      {
        id: 'event-1',
        title: 'Test Event',
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T11:00:00Z',
      },
    ];

    it('should get events from primary calendar', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);
      mockGoogleStrategy.getCalendarEvents.mockResolvedValue(mockEvents as any);

      const result = await service.getCalendarEvents('user-1', startDate, endDate);

      expect(result).toEqual(mockEvents);
      expect(mockGoogleStrategy.refreshTokenIfNeeded).toHaveBeenCalledWith(mockIntegration);
      expect(mockGoogleStrategy.getCalendarEvents).toHaveBeenCalledWith(mockIntegration, startDate, endDate);
    });

    it('should get events from specific provider', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);
      mockGoogleStrategy.getCalendarEvents.mockResolvedValue(mockEvents as any);

      const result = await service.getCalendarEvents('user-1', startDate, endDate, CalendarProvider.Google);

      expect(result).toEqual(mockEvents);
    });

    it('should throw error if no primary calendar connected', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(null);

      await expect(service.getCalendarEvents('user-1', startDate, endDate)).rejects.toThrow(BadRequestException);
    });
  });

  describe('createCalendarEvent', () => {
    const eventDto: CalendarEventDto = {
      title: 'Test Event',
      description: 'Test Description',
      location: 'Test Location',
      startTime: '2024-01-15T10:00:00Z',
      endTime: '2024-01-15T11:00:00Z',
      status: CalendarEventStatus.CONFIRMED,
    };

    const mockCreatedEvent = {
      id: 'event-1',
      ...eventDto,
    };

    it('should create calendar event', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);
      mockGoogleStrategy.createCalendarEvent.mockResolvedValue(mockCreatedEvent as any);

      const result = await service.createCalendarEvent('user-1', eventDto);

      expect(result).toEqual(mockCreatedEvent);
      expect(mockGoogleStrategy.refreshTokenIfNeeded).toHaveBeenCalledWith(mockIntegration);
      expect(mockGoogleStrategy.createCalendarEvent).toHaveBeenCalledWith(mockIntegration, eventDto);
    });

    it('should throw error if no primary calendar connected', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(null);

      await expect(service.createCalendarEvent('user-1', eventDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('updateCalendarEvent', () => {
    const eventDto: CalendarEventDto = {
      title: 'Updated Event',
      description: 'Updated Description',
      location: 'Updated Location',
      startTime: '2024-01-15T10:00:00Z',
      endTime: '2024-01-15T11:00:00Z',
      status: CalendarEventStatus.CONFIRMED,
    };

    const mockUpdatedEvent = {
      id: 'event-1',
      ...eventDto,
    };

    it('should update calendar event', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);
      mockGoogleStrategy.updateCalendarEvent.mockResolvedValue(mockUpdatedEvent as any);

      const result = await service.updateCalendarEvent('user-1', 'event-1', eventDto);

      expect(result).toEqual(mockUpdatedEvent);
      expect(mockGoogleStrategy.refreshTokenIfNeeded).toHaveBeenCalledWith(mockIntegration);
      expect(mockGoogleStrategy.updateCalendarEvent).toHaveBeenCalledWith(mockIntegration, 'event-1', eventDto);
    });
  });

  describe('deleteCalendarEvent', () => {
    it('should delete calendar event', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);

      await service.deleteCalendarEvent('user-1', 'event-1');

      expect(mockGoogleStrategy.refreshTokenIfNeeded).toHaveBeenCalledWith(mockIntegration);
      expect(mockGoogleStrategy.deleteCalendarEvent).toHaveBeenCalledWith(mockIntegration, 'event-1');
    });
  });

  describe('error handling', () => {
    it('should handle strategy operation failures gracefully', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);
      mockGoogleStrategy.getCalendarEvents.mockRejectedValue(new Error('API Error'));

      await expect(service.getCalendarEvents('user-1', new Date(), new Date())).rejects.toThrow(BadRequestException);
    });

    it('should preserve BadRequestException from strategies', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);
      const originalError = new BadRequestException('Original error');
      mockGoogleStrategy.getCalendarEvents.mockRejectedValue(originalError);

      await expect(service.getCalendarEvents('user-1', new Date(), new Date())).rejects.toThrow(originalError);
    });

    it('should handle calendar deletion errors gracefully during unlink', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      };

      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);
      calendarIntegrationRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
      mockGoogleStrategy.deleteCalendar.mockRejectedValue(new Error('Delete failed'));

      // Should not throw error, just log it
      await expect(service.unlinkCalendarByProvider('user-1', 'google')).resolves.not.toThrow();
      expect(calendarIntegrationRepository.delete).toHaveBeenCalledWith(mockIntegration.id);
    });
  });

  describe('private methods', () => {
    it('should find calendar integration by provider', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(mockIntegration);

      const result = await service['findCalendarIntegrationByProvider']('user-1', CalendarProvider.Google);

      expect(result).toEqual(mockIntegration);
      expect(calendarIntegrationRepository.findOne).toHaveBeenCalledWith({
        where: { provider: CalendarProvider.Google, userId: 'user-1', isActive: true },
        relations: ['user'],
      });
    });

    it('should throw NotFoundException when integration not found by provider', async () => {
      calendarIntegrationRepository.findOne.mockResolvedValue(null);

      await expect(service['findCalendarIntegrationByProvider']('user-1', CalendarProvider.Google)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should validate calendar provider correctly', () => {
      expect(service['validateCalendarProvider']('google')).toBe(CalendarProvider.Google);
      expect(() => service['validateCalendarProvider']('InvalidProvider')).toThrow(BadRequestException);
    });

    it('should get calendar strategy correctly', () => {
      expect(service['getCalendarStrategy'](CalendarProvider.Google)).toBe(mockGoogleStrategy);
      expect(() => service['getCalendarStrategy']('InvalidProvider' as any)).toThrow(BadRequestException);
    });
  });
});

import { Injectable, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { google, calendar_v3 } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';

import { User } from '../../user/entities/user.entity';
import { CalendarStrategy } from './calendar-strategy.interface';
import { CalendarProvider } from '../models/calendar-provider.enum';
import { CalendarEventDto, CalendarEventResponseDto } from '../models/calendar-event.dto';
import { CalendarIntegration } from '../entities/calendar-integration.entity';
import { CreateCalendarIntegrationDto } from '../models/calendar-integration.dto';

@Injectable()
export class GoogleCalendarStrategy implements CalendarStrategy {
  private readonly googleOAuth2Client: OAuth2Client;

  constructor(
    private readonly configService: ConfigService,
    @InjectRepository(CalendarIntegration)
    private readonly calendarIntegrationRepository: Repository<CalendarIntegration>,
  ) {
    const backEndUrl = this.configService.get('BACK_END_URL');
    const redirectPath = this.configService.get('AUTH_GOOGLE_REDIRECT_URI');
    const redirectUri = `${backEndUrl}/${redirectPath}`;

    this.googleOAuth2Client = new OAuth2Client(
      this.configService.get('AUTH_GOOGLE_CLIENT_ID'),
      this.configService.get('AUTH_GOOGLE_CLIENT_SECRET'),
      redirectUri,
    );
  }

  getAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/calendar.readonly',
      'https://www.googleapis.com/auth/calendar.events',
      'https://www.googleapis.com/auth/calendar.calendarlist',
      'https://www.googleapis.com/auth/calendar.calendars',
    ];

    return this.googleOAuth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent',
    });
  }

  async linkCalendar(user: User, createDto: CreateCalendarIntegrationDto): Promise<CalendarIntegration> {
    try {
      const { tokens } = await this.googleOAuth2Client.getToken(createDto.code);

      if (!tokens.refresh_token) {
        throw new BadRequestException({
          message: 'No refresh token received from Google. Please try again and make sure to approve all permissions.',
          code: 'NO_REFRESH_TOKEN',
          authUrl: this.getAuthUrl(),
        });
      }

      // Set up OAuth client with the new tokens
      this.googleOAuth2Client.setCredentials({
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
      });

      const calendar = google.calendar({ version: 'v3', auth: this.googleOAuth2Client });
      const calendarList = await calendar.calendarList.list();
      const primaryCalendar = calendarList.data.items?.find((cal) => cal.primary);
      const userEmail = primaryCalendar?.id || null;

      // Create a new Tallo calendar for the user
      const talloCalendarId = await this.createCalendar(null, user.name);

      // Create new calendar integration
      const calendarIntegration = this.calendarIntegrationRepository.create({
        user,
        userId: user.id,
        provider: CalendarProvider.Google,
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        tokenExpiry: tokens.expiry_date ? new Date(tokens.expiry_date) : null,
        isActive: true,
        isPrimary: createDto.isPrimary ?? true,
        talloCalendarId,
        userEmail,
      });

      return await this.calendarIntegrationRepository.save(calendarIntegration);
    } catch (error) {
      throw new BadRequestException(`Failed to link Google Calendar: ${error.message}`);
    }
  }

  async createCalendar(integration: CalendarIntegration | null, userName: string): Promise<string> {
    try {
      const calendar = google.calendar({ version: 'v3', auth: this.googleOAuth2Client });

      const response = await calendar.calendars.insert({
        requestBody: {
          summary: `Tallo - ${userName}`,
          timeZone: 'UTC',
        },
      });

      return response.data.id;
    } catch (error) {
      throw new BadRequestException(`Failed to create Tallo calendar: ${error.message}`);
    }
  }

  async deleteCalendar(integration: CalendarIntegration): Promise<void> {
    if (!integration.talloCalendarId) {
      return;
    }

    try {
      this.googleOAuth2Client.setCredentials({
        access_token: integration.accessToken,
        refresh_token: integration.refreshToken,
      });

      const calendar = google.calendar({ version: 'v3', auth: this.googleOAuth2Client });

      await calendar.calendars.delete({
        calendarId: integration.talloCalendarId,
      });
    } catch (error) {
      if (error.message.includes('notFound')) {
        console.log(`Tallo calendar not found: ${integration.talloCalendarId}`);
        return;
      }

      console.error(`Failed to delete Tallo calendar: ${error.message}`);
    }
  }

  async getCalendarEvents(
    integration: CalendarIntegration,
    startDate: Date,
    endDate: Date,
  ): Promise<CalendarEventResponseDto[]> {
    return this.executeCalendarOperation(
      integration,
      async (calendar) => {
        const events = [];

        // Get events from primary calendar
        const primaryResponse = await calendar.events.list({
          calendarId: 'primary',
          timeMin: startDate.toISOString(),
          timeMax: endDate.toISOString(),
          singleEvents: true,
          orderBy: 'startTime',
        });

        events.push(...(primaryResponse.data.items || []));

        // Get events from Tallo calendar if it exists
        if (integration.talloCalendarId) {
          try {
            const talloResponse = await calendar.events.list({
              calendarId: integration.talloCalendarId,
              timeMin: startDate.toISOString(),
              timeMax: endDate.toISOString(),
              singleEvents: true,
              orderBy: 'startTime',
            });

            events.push(...(talloResponse.data.items || []));
          } catch (talloError) {
            // If there's an error with the Tallo calendar, log it but continue with primary calendar events
            console.error(`Error fetching Tallo calendar events: ${talloError.message}`);

            // If the calendar doesn't exist, clear the ID so we don't try again
            if (this.isNotFoundError(talloError)) {
              integration.talloCalendarId = null;
              await this.calendarIntegrationRepository.save(integration);
            }
          }
        }

        return events.map((event) => this.mapGoogleEventToDto(event));
      },
      'get calendar events',
    );
  }

  async createCalendarEvent(
    integration: CalendarIntegration,
    eventDto: CalendarEventDto,
  ): Promise<CalendarEventResponseDto> {
    return this.executeCalendarOperation(
      integration,
      async (calendar) => {
        const event = this.buildCalendarEvent(eventDto);
        const calendarId = integration.talloCalendarId || 'primary';

        try {
          const response = await calendar.events.insert({
            calendarId,
            requestBody: event as calendar_v3.Schema$Event,
          });

          return this.mapGoogleEventToDto(response.data);
        } catch (error) {
          // Handle Tallo calendar not found error
          if (integration.talloCalendarId && this.isNotFoundError(error)) {
            return this.handleTalloCalendarNotFound(integration, eventDto, calendar);
          }
          throw error;
        }
      },
      'create calendar event',
    );
  }

  async updateCalendarEvent(
    integration: CalendarIntegration,
    eventId: string,
    eventDto: CalendarEventDto,
  ): Promise<CalendarEventResponseDto> {
    return this.executeCalendarOperation(
      integration,
      async (calendar) => {
        const event = this.buildCalendarEvent(eventDto);
        const calendarId = integration.talloCalendarId || 'primary';

        const response = await calendar.events.update({
          calendarId,
          eventId,
          requestBody: event as calendar_v3.Schema$Event,
        });

        return this.mapGoogleEventToDto(response.data);
      },
      'update calendar event',
    );
  }

  async deleteCalendarEvent(integration: CalendarIntegration, eventId: string): Promise<void> {
    return this.executeCalendarOperation(
      integration,
      async (calendar) => {
        const calendarId = integration.talloCalendarId || 'primary';

        try {
          await calendar.events.delete({
            calendarId,
            eventId,
          });
        } catch (error) {
          if (this.isNotFoundError(error)) {
            // Event doesn't exist, which is fine for deletion
            return;
          }
          throw error;
        }
      },
      'delete calendar event',
    );
  }

  /**
   * Generic method to execute calendar operations with proper authentication and error handling
   */
  private async executeCalendarOperation<T>(
    integration: CalendarIntegration,
    operation: (calendar: calendar_v3.Calendar) => Promise<T>,
    operationName: string,
  ): Promise<T> {
    try {
      this.setCredentials(integration);
      const calendar = google.calendar({ version: 'v3', auth: this.googleOAuth2Client });
      return await operation(calendar);
    } catch (error) {
      throw new BadRequestException(`Failed to ${operationName}: ${error.message}`);
    }
  }

  /**
   * Sets OAuth2 credentials for the integration
   */
  private setCredentials(integration: CalendarIntegration): void {
    this.googleOAuth2Client.setCredentials({
      access_token: integration.accessToken,
      refresh_token: integration.refreshToken,
    });
  }

  /**
   * Builds a Google Calendar event object from the DTO
   */
  private buildCalendarEvent(eventDto: CalendarEventDto): any {
    const event: any = {
      summary: eventDto.title,
      description: eventDto.description || '',
      location: eventDto.location || '',
      start: {
        dateTime: new Date(eventDto.startTime).toISOString(),
        timeZone: 'UTC',
      },
      end: {
        dateTime: new Date(eventDto.endTime).toISOString(),
        timeZone: 'UTC',
      },
    };

    // Only set status if explicitly provided
    if (eventDto.status) {
      event.status = eventDto.status;
    }

    return event;
  }

  /**
   * Handles the case when Tallo calendar is not found during event creation
   */
  private async handleTalloCalendarNotFound(
    integration: CalendarIntegration,
    eventDto: CalendarEventDto,
    calendar: calendar_v3.Calendar,
  ): Promise<CalendarEventResponseDto> {
    try {
      // Create a new Tallo calendar and update the integration
      const newTalloCalendarId = await this.createCalendar(integration, integration.user?.name || 'Tallo User');
      integration.talloCalendarId = newTalloCalendarId;
      await this.calendarIntegrationRepository.save(integration);

      // Try again with the new calendar
      return this.createCalendarEvent(integration, eventDto);
    } catch {
      // If creating a new calendar fails, fall back to primary calendar
      const event = this.buildCalendarEvent(eventDto);
      const response = await calendar.events.insert({
        calendarId: 'primary',
        requestBody: event as calendar_v3.Schema$Event,
      });

      return this.mapGoogleEventToDto(response.data);
    }
  }

  /**
   * Checks if an error is a "not found" error
   */
  private isNotFoundError(error: any): boolean {
    return error.message && error.message.includes('notFound');
  }

  async refreshTokenIfNeeded(integration: CalendarIntegration): Promise<void> {
    if (!integration.refreshToken) {
      throw new BadRequestException('Calendar not properly connected. Please reconnect your calendar.');
    }

    const tokenExpiry = integration.tokenExpiry ? new Date(integration.tokenExpiry).getTime() : 0;
    const now = Date.now();

    if (tokenExpiry <= now) {
      try {
        this.googleOAuth2Client.setCredentials({
          refresh_token: integration.refreshToken,
        });

        const { credentials } = await this.googleOAuth2Client.refreshAccessToken();

        integration.accessToken = credentials.access_token;
        integration.tokenExpiry = credentials.expiry_date ? new Date(credentials.expiry_date) : null;

        await this.calendarIntegrationRepository.save(integration);
      } catch (error) {
        // If refresh token is invalid, mark calendar as inactive
        if (error.message.includes('invalid_grant')) {
          integration.isActive = false;
          await this.calendarIntegrationRepository.save(integration);
          throw new UnauthorizedException('Calendar authorization expired. Please reconnect your calendar.');
        }
        throw new UnauthorizedException(`Failed to refresh token: ${error.message}`);
      }
    }
  }

  private mapGoogleEventToDto(event: calendar_v3.Schema$Event): CalendarEventResponseDto {
    return {
      id: event.id,
      title: event.summary,
      description: event.description,
      location: event.location,
      startTime: event.start.dateTime || event.start.date,
      endTime: event.end.dateTime || event.end.date,
      status: event.status as any,
    };
  }
}

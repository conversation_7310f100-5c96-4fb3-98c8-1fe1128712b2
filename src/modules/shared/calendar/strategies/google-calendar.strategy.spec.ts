import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';

import { GoogleCalendarStrategy } from './google-calendar.strategy';
import { CalendarIntegration } from '../entities/calendar-integration.entity';
import { CalendarProvider } from '../models/calendar-provider.enum';
import { CalendarEventDto, CalendarEventStatus } from '../models/calendar-event.dto';
import { CreateCalendarIntegrationDto } from '../models/calendar-integration.dto';

// Mock googleapis
jest.mock('googleapis');
jest.mock('google-auth-library');

describe('GoogleCalendarStrategy', () => {
  let strategy: GoogleCalendarStrategy;
  let calendarIntegrationRepository: jest.Mocked<Repository<CalendarIntegration>>;
  let mockOAuth2Client: any;
  let mockCalendar: any;

  const mockUser = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
  };

  const mockIntegration = {
    id: 'integration-1',
    userId: 'user-1',
    provider: CalendarProvider.Google,
    accessToken: 'access-token',
    refreshToken: 'refresh-token',
    tokenExpiry: new Date(Date.now() + 3600000),
    isActive: true,
    isPrimary: true,
    talloCalendarId: 'tallo-calendar-id',
    userEmail: '<EMAIL>',
    user: mockUser,
  } as CalendarIntegration;

  beforeEach(async () => {
    mockOAuth2Client = {
      generateAuthUrl: jest.fn(),
      getToken: jest.fn(),
      setCredentials: jest.fn(),
      refreshAccessToken: jest.fn(),
    };

    mockCalendar = {
      calendars: {
        insert: jest.fn(),
        delete: jest.fn(),
      },
      calendarList: {
        list: jest.fn(),
      },
      events: {
        list: jest.fn(),
        insert: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
    };

    // Mock the OAuth2Client constructor
    (OAuth2Client as unknown as jest.Mock).mockImplementation(() => mockOAuth2Client);
    (google.auth.OAuth2 as unknown as jest.Mock).mockImplementation(() => mockOAuth2Client);
    (google.calendar as unknown as jest.Mock).mockReturnValue(mockCalendar);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GoogleCalendarStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              switch (key) {
                case 'BACK_END_URL':
                  return 'http://localhost:3000';
                case 'AUTH_GOOGLE_REDIRECT_URI':
                  return 'auth/google/callback';
                case 'AUTH_GOOGLE_CLIENT_ID':
                  return 'client-id';
                case 'AUTH_GOOGLE_CLIENT_SECRET':
                  return 'client-secret';
                default:
                  return null;
              }
            }),
          },
        },
        {
          provide: getRepositoryToken(CalendarIntegration),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
      ],
    }).compile();

    strategy = module.get<GoogleCalendarStrategy>(GoogleCalendarStrategy);
    calendarIntegrationRepository = module.get(getRepositoryToken(CalendarIntegration));

    // Clear mocks and set up default behavior for each test
    jest.clearAllMocks();

    // Restore Google API mocks after clearing
    (OAuth2Client as unknown as jest.Mock).mockImplementation(() => mockOAuth2Client);
    (google.auth.OAuth2 as unknown as jest.Mock).mockImplementation(() => mockOAuth2Client);
    (google.calendar as unknown as jest.Mock).mockReturnValue(mockCalendar);

    // Set up default mock behaviors that tests can rely on
    mockOAuth2Client.generateAuthUrl.mockReturnValue(
      'https://accounts.google.com/o/oauth2/v2/auth?access_type=offline&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.readonly%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.events%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.calendarlist%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.calendars&prompt=consent&response_type=code&client_id=client-id&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fauth%2Fgoogle%2Fcallback',
    );
    mockOAuth2Client.getToken.mockResolvedValue({
      tokens: {
        access_token: 'access-token',
        refresh_token: 'refresh-token',
        expiry_date: Date.now() + 3600000,
      },
    });
    mockOAuth2Client.refreshAccessToken.mockResolvedValue({
      credentials: {
        access_token: 'new-access-token',
        expiry_date: Date.now() + 3600000,
      },
    });

    mockCalendar.calendars.insert.mockResolvedValue({
      data: { id: 'tallo-calendar-id' },
    });
    mockCalendar.calendarList.list.mockResolvedValue({
      data: {
        items: [
          { id: '<EMAIL>', primary: true },
          { id: '<EMAIL>', primary: false },
        ],
      },
    });
    mockCalendar.events.insert.mockResolvedValue({
      data: {
        id: 'event-1',
        summary: 'Test Event',
        description: 'Test Description',
        location: 'Test Location',
        start: { dateTime: '2024-01-15T10:00:00Z' },
        end: { dateTime: '2024-01-15T11:00:00Z' },
        status: 'confirmed',
      },
    });
    mockCalendar.events.list.mockResolvedValue({
      data: { items: [] },
    });

    calendarIntegrationRepository.create.mockReturnValue(mockIntegration);
    calendarIntegrationRepository.save.mockResolvedValue(mockIntegration);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('getAuthUrl', () => {
    it('should return Google auth URL', () => {
      const result = strategy.getAuthUrl();

      expect(result).toBe(
        'https://accounts.google.com/o/oauth2/v2/auth?access_type=offline&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.readonly%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.events%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.calendarlist%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.calendars&prompt=consent&response_type=code&client_id=client-id&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fauth%2Fgoogle%2Fcallback',
      );
      expect(mockOAuth2Client.generateAuthUrl).toHaveBeenCalledWith({
        access_type: 'offline',
        scope: [
          'https://www.googleapis.com/auth/calendar.readonly',
          'https://www.googleapis.com/auth/calendar.events',
          'https://www.googleapis.com/auth/calendar.calendarlist',
          'https://www.googleapis.com/auth/calendar.calendars',
        ],
        prompt: 'consent',
      });
    });
  });

  describe('linkCalendar', () => {
    const createDto: CreateCalendarIntegrationDto = {
      provider: CalendarProvider.Google,
      code: 'auth-code',
      isPrimary: true,
    };

    it('should successfully link calendar', async () => {
      const result = await strategy.linkCalendar(mockUser as any, createDto);

      expect(result).toEqual(mockIntegration);
      expect(mockOAuth2Client.getToken).toHaveBeenCalledWith('auth-code');
      expect(mockOAuth2Client.setCredentials).toHaveBeenCalledWith({
        access_token: 'access-token',
        refresh_token: 'refresh-token',
      });
      expect(mockCalendar.calendarList.list).toHaveBeenCalled();
      expect(mockCalendar.calendars.insert).toHaveBeenCalled();
      expect(calendarIntegrationRepository.save).toHaveBeenCalled();
    });

    it('should throw error if no refresh token received', async () => {
      // Override default behavior for this specific test
      mockOAuth2Client.getToken.mockResolvedValueOnce({
        tokens: {
          access_token: 'access-token',
          // No refresh_token
        },
      });

      await expect(strategy.linkCalendar(mockUser as any, createDto)).rejects.toThrow(BadRequestException);
    });

    it('should handle token exchange errors', async () => {
      // Override default behavior for this specific test
      mockOAuth2Client.getToken.mockRejectedValueOnce(new Error('Invalid code'));

      await expect(strategy.linkCalendar(mockUser as any, createDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('createCalendar', () => {
    it('should create Tallo calendar', async () => {
      // Override default behavior for this specific test
      mockCalendar.calendars.insert.mockResolvedValueOnce({
        data: { id: 'new-calendar-id' },
      });

      const result = await strategy.createCalendar(null, 'Test User');

      expect(result).toBe('new-calendar-id');
      expect(mockCalendar.calendars.insert).toHaveBeenCalledWith({
        requestBody: {
          summary: 'Tallo - Test User',
          timeZone: 'UTC',
        },
      });
    });

    it('should handle calendar creation errors', async () => {
      // Override default behavior for this specific test
      mockCalendar.calendars.insert.mockRejectedValueOnce(new Error('API Error'));

      await expect(strategy.createCalendar(null, 'Test User')).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteCalendar', () => {
    it('should delete Tallo calendar', async () => {
      // Ensure the integration has a Tallo calendar ID
      const integrationWithCalendar = {
        ...mockIntegration,
        talloCalendarId: 'tallo-calendar-id',
      };

      await strategy.deleteCalendar(integrationWithCalendar);

      expect(mockOAuth2Client.setCredentials).toHaveBeenCalledWith({
        access_token: integrationWithCalendar.accessToken,
        refresh_token: integrationWithCalendar.refreshToken,
      });
      expect(mockCalendar.calendars.delete).toHaveBeenCalledWith({
        calendarId: integrationWithCalendar.talloCalendarId,
      });
    });

    it('should handle calendar not found gracefully', async () => {
      const integrationWithCalendar = {
        ...mockIntegration,
        talloCalendarId: 'tallo-calendar-id',
      };

      const notFoundError = new Error('notFound');
      mockCalendar.calendars.delete.mockRejectedValueOnce(notFoundError);

      // Should not throw error
      await expect(strategy.deleteCalendar(integrationWithCalendar)).resolves.not.toThrow();
    });

    it('should return early if no talloCalendarId', async () => {
      const integrationWithoutCalendar = { ...mockIntegration, talloCalendarId: null };

      await strategy.deleteCalendar(integrationWithoutCalendar);

      expect(mockCalendar.calendars.delete).not.toHaveBeenCalled();
    });
  });

  describe('getCalendarEvents', () => {
    const startDate = new Date('2024-01-01');
    const endDate = new Date('2024-01-31');

    const mockGoogleEvent = {
      id: 'event-1',
      summary: 'Test Event',
      description: 'Test Description',
      location: 'Test Location',
      start: { dateTime: '2024-01-15T10:00:00Z' },
      end: { dateTime: '2024-01-15T11:00:00Z' },
      status: 'confirmed',
    };

    it('should get events from primary and Tallo calendars', async () => {
      // Override default behavior for this specific test
      mockCalendar.events.list
        .mockResolvedValueOnce({ data: { items: [mockGoogleEvent] } }) // Primary calendar
        .mockResolvedValueOnce({ data: { items: [mockGoogleEvent] } }); // Tallo calendar

      const result = await strategy.getCalendarEvents(mockIntegration, startDate, endDate);

      expect(result).toHaveLength(2);
      expect(mockCalendar.events.list).toHaveBeenCalledTimes(2);
      expect(mockCalendar.events.list).toHaveBeenCalledWith({
        calendarId: 'primary',
        timeMin: startDate.toISOString(),
        timeMax: endDate.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
      });
      expect(mockCalendar.events.list).toHaveBeenCalledWith({
        calendarId: mockIntegration.talloCalendarId,
        timeMin: startDate.toISOString(),
        timeMax: endDate.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
      });
    });

    it('should handle Tallo calendar not found and clear ID', async () => {
      // Override default behavior for this specific test
      mockCalendar.events.list
        .mockResolvedValueOnce({ data: { items: [mockGoogleEvent] } }) // Primary calendar
        .mockRejectedValueOnce(new Error('notFound')); // Tallo calendar

      const result = await strategy.getCalendarEvents(mockIntegration, startDate, endDate);

      expect(result).toHaveLength(1);
      expect(calendarIntegrationRepository.save).toHaveBeenCalledWith({
        ...mockIntegration,
        talloCalendarId: null,
      });
    });
  });

  describe('createCalendarEvent', () => {
    const eventDto: CalendarEventDto = {
      title: 'Test Event',
      description: 'Test Description',
      location: 'Test Location',
      startTime: '2024-01-15T10:00:00Z',
      endTime: '2024-01-15T11:00:00Z',
      status: CalendarEventStatus.CONFIRMED,
    };

    const mockCreatedEvent = {
      data: {
        id: 'event-1',
        summary: eventDto.title,
        description: eventDto.description,
        location: eventDto.location,
        start: { dateTime: eventDto.startTime },
        end: { dateTime: eventDto.endTime },
        status: eventDto.status,
      },
    };

    it('should create calendar event in Tallo calendar', async () => {
      // Override default behavior to ensure Tallo calendar is used
      mockCalendar.events.insert.mockResolvedValueOnce(mockCreatedEvent);

      const result = await strategy.createCalendarEvent(mockIntegration, eventDto);

      expect(result.id).toBe('event-1');
      expect(result.title).toBe(eventDto.title);
      // Since mockIntegration.talloCalendarId is null, it should fall back to 'primary'
      expect(mockCalendar.events.insert).toHaveBeenCalledWith({
        calendarId: 'primary',
        requestBody: expect.objectContaining({
          summary: eventDto.title,
          description: eventDto.description,
          location: eventDto.location,
          status: eventDto.status,
          start: expect.any(Object),
          end: expect.any(Object),
        }),
      });
    });

    it('should fall back to primary calendar if Tallo calendar not found', async () => {
      // Create an integration with a Tallo calendar ID that will fail
      const integrationWithTalloCalendar = {
        ...mockIntegration,
        talloCalendarId: 'non-existent-calendar-id',
      };

      const notFoundError = new Error('notFound');
      mockCalendar.events.insert
        .mockRejectedValueOnce(notFoundError) // Tallo calendar fails
        .mockResolvedValueOnce(mockCreatedEvent); // Primary calendar succeeds

      // Mock calendar creation failure to trigger fallback to primary
      mockCalendar.calendars.insert.mockRejectedValueOnce(new Error('Calendar creation failed'));

      const result = await strategy.createCalendarEvent(integrationWithTalloCalendar, eventDto);

      expect(result.id).toBe('event-1');
      expect(mockCalendar.events.insert).toHaveBeenCalledTimes(2);
      expect(mockCalendar.events.insert).toHaveBeenLastCalledWith({
        calendarId: 'primary',
        requestBody: expect.any(Object),
      });
    });
  });

  describe('refreshTokenIfNeeded', () => {
    it('should refresh token when expired', async () => {
      const expiredIntegration = {
        ...mockIntegration,
        tokenExpiry: new Date(Date.now() - 1000), // Expired
      };

      await strategy.refreshTokenIfNeeded(expiredIntegration);

      expect(mockOAuth2Client.setCredentials).toHaveBeenCalledWith({
        refresh_token: expiredIntegration.refreshToken,
      });
      expect(mockOAuth2Client.refreshAccessToken).toHaveBeenCalled();
      expect(calendarIntegrationRepository.save).toHaveBeenCalledWith({
        ...expiredIntegration,
        accessToken: 'new-access-token',
        tokenExpiry: expect.any(Date),
      });
    });

    it('should not refresh token when not expired', async () => {
      await strategy.refreshTokenIfNeeded(mockIntegration);

      expect(mockOAuth2Client.refreshAccessToken).not.toHaveBeenCalled();
    });

    it('should handle invalid refresh token', async () => {
      const expiredIntegration = {
        ...mockIntegration,
        tokenExpiry: new Date(Date.now() - 1000),
      };

      // Override default behavior for this specific test
      const invalidGrantError = new Error('invalid_grant');
      mockOAuth2Client.refreshAccessToken.mockRejectedValueOnce(invalidGrantError);

      await expect(strategy.refreshTokenIfNeeded(expiredIntegration)).rejects.toThrow(UnauthorizedException);
      expect(calendarIntegrationRepository.save).toHaveBeenCalledWith({
        ...expiredIntegration,
        isActive: false,
      });
    });

    it('should throw error if no refresh token', async () => {
      const integrationWithoutRefreshToken = {
        ...mockIntegration,
        refreshToken: null,
      };

      await expect(strategy.refreshTokenIfNeeded(integrationWithoutRefreshToken)).rejects.toThrow(BadRequestException);
    });
  });
});

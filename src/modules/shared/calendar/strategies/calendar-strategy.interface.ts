import { User } from '../../user/entities/user.entity';
import { CalendarEventDto, CalendarEventResponseDto } from '../models/calendar-event.dto';
import { CalendarIntegration } from '../entities/calendar-integration.entity';
import { CreateCalendarIntegrationDto } from '../models/calendar-integration.dto';

export interface CalendarStrategy {
  /**
   * Get the authorization URL for the calendar provider
   */
  getAuthUrl(): string;

  /**
   * Link a user's account with the calendar provider
   */
  linkCalendar(user: User, createDto: CreateCalendarIntegrationDto): Promise<CalendarIntegration>;

  /**
   * Create a calendar for the user in the provider's system
   */
  createCalendar(integration: CalendarIntegration, calendarName: string): Promise<string>;

  /**
   * Delete a calendar from the provider's system
   */
  deleteCalendar(integration: CalendarIntegration): Promise<void>;

  /**
   * Get events from the calendar provider
   */
  getCalendarEvents(
    integration: CalendarIntegration,
    startDate: Date,
    endDate: Date,
  ): Promise<CalendarEventResponseDto[]>;

  /**
   * Create an event in the calendar provider
   */
  createCalendarEvent(integration: CalendarIntegration, eventDto: CalendarEventDto): Promise<CalendarEventResponseDto>;

  /**
   * Update an existing event in the calendar provider
   */
  updateCalendarEvent(
    integration: CalendarIntegration,
    eventId: string,
    eventDto: CalendarEventDto,
  ): Promise<CalendarEventResponseDto>;

  /**
   * Delete an event from the calendar provider
   */
  deleteCalendarEvent(integration: CalendarIntegration, eventId: string): Promise<void>;

  /**
   * Refresh the access token if needed
   */
  refreshTokenIfNeeded(integration: CalendarIntegration): Promise<void>;
}

import { Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Conversation } from './conversation.entity';
import { Expose } from 'class-transformer';

@Entity()
export class EmailMetadata {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToOne(() => Conversation, (conversation) => conversation.emailMetadata, {
    lazy: true,
  })
  conversation: Promise<Conversation> | Conversation;

  @Column({ type: 'varchar' })
  subject: string;

  @Column({ type: 'text', nullable: true })
  previousText?: string;

  @Column({ type: 'varchar', nullable: true })
  lastMessageId?: string;

  @Column('text', { array: true, nullable: true })
  references?: string[];

  @Column({ type: 'boolean', default: false })
  public isDeleted: boolean;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;
}

import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Property } from '../../../../investor/property/property/entities/property.entity';
import { User } from '../../../user/entities/user.entity';
import { Message } from '../message/message.entity';
import { EmailMetadata } from './email.metadata.entity';
import { ConversationStatus } from '../enums/conversation-status.enum';

@Entity('conversation')
export class Conversation {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Property, (property) => property.conversations, {
    nullable: true,
    lazy: true,
  })
  property?: Promise<Property> | Property;

  @Expose()
  @ManyToMany(() => Message, (message) => message.conversations, {
    nullable: true,
    lazy: true,
    cascade: true,
  })
  @JoinTable()
  messages: Promise<Message[]> | Message[];

  @Index()
  @Column({ type: 'boolean', default: true })
  isAnswered: boolean;

  @Expose()
  @Column({
    type: 'enum',
    enum: ConversationStatus,
    default: ConversationStatus.PENDING,
  })
  status: ConversationStatus;

  @Expose()
  @ManyToMany(() => User, { nullable: true, lazy: true })
  @JoinTable()
  users: Promise<User[]> | User[];

  @OneToOne(() => EmailMetadata, (emailMetadata) => emailMetadata.conversation, {
    nullable: true,
    lazy: true,
  })
  @JoinColumn()
  emailMetadata: Promise<EmailMetadata> | EmailMetadata;

  @Expose()
  @Column({ type: 'boolean', default: false })
  isEmergencyStopped: boolean;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  slackLoggingThreadId: string | null;

  @DeleteDateColumn()
  deletedAt?: Date;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;
}

import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  ManyToMany,
  DeleteDateColumn,
} from 'typeorm';
import { User } from '../../../user/entities/user.entity';
import { Conversation } from '../entities/conversation.entity';
import { MessageType } from './message-type.enum';
import { CommunicationChannel } from '../enums/preferred-communication-channel.enum';

@Entity()
export class Message {
  @Expose()
  @PrimaryGeneratedColumn()
  public id!: number;

  @ManyToMany(() => Conversation, (conversation) => conversation.messages, {
    lazy: true,
    eager: false,
    nullable: false,
  })
  public conversations: Promise<Conversation[]> | Conversation[];

  @Expose()
  @Column({ type: 'text' })
  public content: string;

  @ManyToOne(() => User, {
    eager: true,
    nullable: true,
  })
  @Expose()
  @JoinColumn()
  user: User;

  @Expose()
  @Column({ type: 'varchar', length: 60 })
  type: MessageType;

  @Expose()
  @Column({ type: 'boolean', default: false })
  isSystem: boolean;

  @Expose()
  @Column({ type: 'boolean', default: false })
  isSeen: boolean;

  @Expose()
  @Column({ type: 'boolean', default: false })
  isAnswered: boolean;

  @Expose()
  @Column({
    type: 'enum',
    enum: CommunicationChannel,
  })
  communicationChannel: CommunicationChannel;

  @DeleteDateColumn()
  deletedAt?: Date;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;
}

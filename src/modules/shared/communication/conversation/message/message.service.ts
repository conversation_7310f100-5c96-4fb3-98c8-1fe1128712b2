import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { instanceTo<PERSON>lain } from 'class-transformer';
import { Repository } from 'typeorm';
import { MessageDto } from './message.dto';
import { Message } from './message.entity';

@Injectable()
export class MessageService {
  constructor(
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
  ) {}

  async getLatestMessageFromTallo(conversationId: string): Promise<Message | undefined> {
    return this.messageRepository
      .createQueryBuilder('message')
      .innerJoin('message.conversations', 'conversation')
      .where('conversation.id = :conversationId', { conversationId })
      .andWhere('message.isSystem = :isSystem', {
        isSystem: true,
      })
      .orderBy('message.createdAt', 'DESC')
      .getOne();
  }

  async create(messageData: Partial<Message> = {}): Promise<Message> {
    const message: Message = this.messageRepository.create();
    Object.assign(message, messageData);

    return this.messageRepository.save(message);
  }

  async createMultiple(messageData: Partial<Message>[]): Promise<Message[]> {
    const messages = messageData.map((data) => {
      const message: Message = this.messageRepository.create();
      Object.assign(message, data);
      return message;
    });

    return this.messageRepository.save(messages);
  }

  async countTimeSinceLastAiMessage(conversationId: string): Promise<number | undefined> {
    const lastAiMessage = await this.getLatestMessageFromTallo(conversationId);

    if (!lastAiMessage) {
      return undefined;
    }

    const lastAiMessageDate = lastAiMessage.createdAt;

    // Assuming lastAiMessageDate is a Date object
    if (!(lastAiMessageDate instanceof Date)) {
      return undefined;
    }

    const result = await this.messageRepository.query('SELECT NOW() as current_time;');
    if (!result || !result[0] || !result[0].current_time) {
      console.error('Failed to fetch current time from database');
      return undefined;
    }

    // Ensure we parse the database time correctly
    const dateNow = new Date(result[0].current_time);

    return dateNow.getTime() - lastAiMessageDate.getTime();
  }

  async save(message: Message): Promise<Message> {
    return await this.messageRepository.save(message);
  }

  async update(id: number, content: string): Promise<void> {
    await this.messageRepository.update(id, { content });
  }

  async updateMultiple(messages: Message[]): Promise<void> {
    await this.messageRepository.save(messages);
  }

  async delete(id: number): Promise<void> {
    await this.messageRepository.update(id, { deletedAt: new Date() });
  }

  async deleteMultiple(ids: number[]): Promise<void> {
    await this.messageRepository.softDelete(ids);
  }

  convertToDto(message: Message): MessageDto {
    return <MessageDto>instanceToPlain(message, { excludeExtraneousValues: true });
  }
}

export enum RentStageEnum {
  INITIAL_CONTACT = 'Initial Contact',
  REQUIREMENTS_NOT_MET = 'Requirements Not Met',
  SHOWING_REQUESTED = 'Showing Requested',
  SHOWING_DECLINED = 'Showing Declined',
  SHOWING_RESCHEDULE_REQUESTED_BY_RENTER = 'Showing Reschedule Requested by <PERSON><PERSON>',
  SHOWING_RESCHEDULE_REQUESTED_BY_OWNER = 'Showing Reschedule Requested by Owner',
  SHOWING_CONFIRMED = 'Showing Confirmed',
  SHOWING_COMPLETED = 'Showing Completed',
  SHOWING_CANCELLED_BY_RENTER = 'Showing Cancelled by Ren<PERSON>',
  SHOWING_CANCELLED_BY_OWNER = 'Showing Cancelled by Owner',
  SHOWING_REQUEST_IGNORED_BY_OWNER = 'Showing Request Ignored by Owner',
  STOPPED_PROPERTY_RENTED_OUT = 'Stopped Property Rented Out',
  APPLICATION_INVITE_SENT = 'Application Invite Sent',
  APPLICATION_IN_PROGRESS = 'Application In Progress',
  APPLICATION_COMPLETED = 'Application Completed',
  APPLICATION_ACCEPTED = 'Application Accepted',
  APPLICATION_REJECTED = 'Application Rejected',
  NO_LONGER_INTERESTED = 'No Longer Interested',
}

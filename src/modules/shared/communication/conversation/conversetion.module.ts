import { forwardRef, Module } from '@nestjs/common';
import { ConversationService } from './conversation.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Conversation } from './entities/conversation.entity';
import { MessageService } from './message/message.service';
import { Message } from './message/message.entity';
import { WebsocketService } from '../../../../websocket.service';
import { ConversationController } from './conversation.controller';
import { EmailMetadata } from './entities/email.metadata.entity';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { FollowUpModule } from '../follow-up/follow-up.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Conversation, Message, EmailMetadata, Property]),
    forwardRef(() => FollowUpModule),
  ],
  controllers: [ConversationController],
  providers: [ConversationService, MessageService, WebsocketService],
  exports: [ConversationService, MessageService],
})
export class ConversationModule {}

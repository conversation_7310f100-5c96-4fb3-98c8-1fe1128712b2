import { Controller, Get, Inject, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { HasRoles } from '../../auth/decorators/role-access.decorator';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Role } from '../../auth/models/roles-enum';
import { ConversationService } from './conversation.service';
import { ConversationDto } from './conversation.dto';
import { IsPropertyOwnerGuard } from '../../../../guards/investor/is-property-owner-guard';

@ApiTags('conversation')
@Controller('conversation')
@UseGuards(RolesGuard)
@HasRoles(Role.INVESTOR)
@ApiBearerAuth()
export class ConversationController {
  @Inject(ConversationService)
  private readonly service: ConversationService;

  @Get('renter-chat-transcript/:propertyId/:userId')
  @ApiOkResponse({ description: 'Returns conversation of Tallo and renter' })
  @UseGuards(IsPropertyOwnerGuard)
  async getRenterChatTranscriptByPropertyAndUser(
    @Param('userId') userId: string,
    @Param('propertyId') propertyId: string,
  ): Promise<ConversationDto[]> {
    return this.service.findAllByUserAndPropertyIds(propertyId, userId);
  }

  @Get('transcript/:propertyId/:renterEmail')
  @ApiOkResponse({ description: "Returns renter's conversation with Tallo" })
  @UseGuards(IsPropertyOwnerGuard)
  async getRenterChatTranscriptByPropertyAndEmail(
    @Param('renterEmail') renterEmail: string,
    @Param('propertyId') propertyId: string,
  ): Promise<ConversationDto> {
    return this.service.findConversationByPropertyAndRenterEmail(propertyId, renterEmail);
  }

  @Post('emergency-stop/:propertyId/:conversationId')
  @ApiOkResponse({ description: 'Returns void if conversation was stopped' })
  @ApiParam({ name: 'propertyId', required: true, description: 'ID of the property' })
  @ApiParam({ name: 'conversationId', required: true, description: 'ID of the conversation' })
  @UseGuards(IsPropertyOwnerGuard)
  async stopConversation(@Param('conversationId') conversationId: string): Promise<void> {
    await this.service.stopConversation(conversationId);
  }

  @Post('emergency-stop/resume/:propertyId/:conversationId')
  @ApiOkResponse({ description: 'Returns void if conversation was stopped' })
  @ApiParam({ name: 'propertyId', required: true, description: 'ID of the property' })
  @ApiParam({ name: 'conversationId', required: true, description: 'ID of the conversation' })
  @UseGuards(IsPropertyOwnerGuard)
  async resumeConversation(@Param('conversationId') conversationId: string): Promise<void> {
    await this.service.resumeConversation(conversationId);
  }
}

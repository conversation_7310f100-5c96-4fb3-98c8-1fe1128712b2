import { instance<PERSON><PERSON><PERSON><PERSON> } from 'class-transformer';
import { Repository } from 'typeorm';
import { UpdateResult } from 'typeorm/query-builder/result/UpdateResult';

import { forwardRef, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Property } from '../../../investor/property/property/entities/property.entity';
import { Renter } from '../../../renter/renter/renter.entity';
import { User } from '../../user/entities/user.entity';
import { FollowUpService } from '../follow-up/follow-up.service';
import { ConversationDto } from './conversation.dto';
import { Conversation } from './entities/conversation.entity';
import { EmailMetadata } from './entities/email.metadata.entity';
import { MessageType } from './message/message-type.enum';
import { Message } from './message/message.entity';
import { MessageService } from './message/message.service';
import { CommunicationChannel } from './enums/preferred-communication-channel.enum';
import { ConversationStatus } from './enums/conversation-status.enum';

@Injectable()
export class ConversationService {
  constructor(
    @InjectRepository(Conversation)
    private readonly repository: Repository<Conversation>,
    @InjectRepository(EmailMetadata)
    private readonly emailMetadataRepository: Repository<EmailMetadata>,
    private readonly messageService: MessageService,
    @Inject(forwardRef(() => FollowUpService))
    private readonly followUsService: FollowUpService,
  ) {}

  save(conversation: Conversation): Promise<Conversation> {
    return this.repository.save(conversation);
  }

  async create(users: User[], property: Property, emailMetadata?: EmailMetadata): Promise<Conversation> {
    const conversation = new Conversation();
    conversation.users = users;
    conversation.property = property;
    conversation.emailMetadata = emailMetadata;

    return this.repository.save(conversation);
  }

  async updateEmailMetadata(
    conversation: Conversation,
    newMetadata: Partial<EmailMetadata> = {},
  ): Promise<EmailMetadata> {
    let updatedMetadata: EmailMetadata;

    if (!(await conversation.emailMetadata)) {
      const savedMetadata = await this.emailMetadataRepository.save(
        Object.assign(this.emailMetadataRepository.create(), newMetadata),
      );

      await this.repository
        .createQueryBuilder()
        .update(Conversation)
        .set({ emailMetadata: { id: savedMetadata.id } })
        .where('id = :id', { id: conversation.id })
        .execute();

      updatedMetadata = savedMetadata;
    } else {
      await this.emailMetadataRepository.update((await conversation.emailMetadata).id, newMetadata);
      updatedMetadata = await conversation.emailMetadata;
    }

    return updatedMetadata;
  }

  update(conversationId: string, updateData: Partial<Conversation>): Promise<UpdateResult> {
    return this.repository.update(conversationId, updateData);
  }

  findById(id: string): Promise<Conversation> {
    return this.repository.findOneBy({ id });
  }

  findByPropertyUser(propertyId: string, userId: string): Promise<Conversation> {
    return this.repository.findOneBy({
      property: { id: propertyId },
      users: { id: userId },
    });
  }

  async findAllUnansweredConversations(): Promise<Conversation[]> {
    return this.repository
      .createQueryBuilder('conversation')
      .where('conversation.status = :status', { status: ConversationStatus.PENDING })
      .leftJoinAndSelect('conversation.messages', 'messages', 'messages.isAnswered = :isAnswered', {
        isAnswered: false,
      })
      .leftJoin('conversation.users', 'users')
      .leftJoin('conversation.property', 'property')
      .orderBy('messages.createdAt', 'ASC')
      .getMany();
  }

  async findLatestByUserId(userId: string): Promise<Conversation> {
    return await this.repository
      .createQueryBuilder('conversation')
      .leftJoinAndSelect('conversation.users', 'users')
      .andWhere('users.id = :userId', { userId })
      .orderBy('conversation.createdAt', 'DESC')
      .getOne();
  }

  async findAllByUserAndPropertyIds(propertyId: string, userId: string): Promise<ConversationDto[]> {
    const conversations = await this.repository.find({
      where: {
        users: { id: userId },
        property: { id: propertyId },
      },
      relations: {
        messages: true,
      },
    });
    return Promise.all(conversations.map(async (convo) => await this.convertToDto(convo, true)));
  }

  async findConversationByPropertyAndRenterEmail(propertyId: string, renterEmail: string): Promise<ConversationDto> {
    const conversation = await this.repository.findOne({
      where: {
        property: { id: propertyId },
        users: { email: renterEmail },
      },
      relations: {
        messages: true,
      },
    });

    if (!conversation) {
      throw new NotFoundException('Conversation not found');
    }

    return this.convertToDto(conversation, true);
  }

  async stopConversation(conversationId: string): Promise<void> {
    await this.update(conversationId, { isEmergencyStopped: true });
    await this.followUsService.deleteRenterFollowUpsByStatus(conversationId);
  }

  async resumeConversation(conversationId: string): Promise<void> {
    await this.update(conversationId, { isEmergencyStopped: false });
  }

  async convertToDto(conversation: Conversation, withMessages: boolean): Promise<ConversationDto> {
    const conversationDto = <ConversationDto>instanceToPlain(conversation, { strategy: 'excludeAll' });

    if (withMessages) {
      const messages = await conversation.messages;
      conversationDto.messages = [];
      messages?.forEach((message) => {
        conversationDto.messages.push(this.messageService.convertToDto(message));
      });
    }
    return conversationDto;
  }

  async saveRenterMessages(
    conversation: Conversation,
    renter: Renter,
    messages: string[],
    isAnswered = true,
    communicationChannel?: CommunicationChannel,
  ): Promise<void> {
    const messagesToSave: Partial<Message>[] = messages.map((text) => ({
      conversations: [conversation],
      content: text,
      user: renter.user,
      type: MessageType.TEXT,
      isSystem: false,
      isSeen: false,
      isAnswered,
      communicationChannel,
    }));

    await this.messageService.createMultiple(messagesToSave);

    // Update both the old isAnswered field and new status field
    const status = isAnswered ? ConversationStatus.ANSWERED : ConversationStatus.PENDING;
    await this.update(conversation.id, { isAnswered, status });
  }

  async saveTalloMessage(
    conversation: Conversation,
    aiResponse: string,
    messageType: MessageType = MessageType.TEXT,
    communicationChannel: CommunicationChannel,
  ): Promise<Message> {
    return this.messageService.create({
      conversations: [conversation],
      content: aiResponse,
      type: messageType,
      isSystem: true,
      isSeen: true,
      isAnswered: true,
      communicationChannel: communicationChannel,
    });
  }

  async saveSlackLoggingThreadId(conversation: Conversation, slackLoggingThreadId: string): Promise<void> {
    await this.repository.update(conversation.id, { slackLoggingThreadId });
  }

  async markConversationAsInProgress(conversationId: string): Promise<void> {
    await this.repository.update(conversationId, {
      status: ConversationStatus.IN_PROGRESS,
    });
  }

  async markConversationAsAnswered(conversationId: string): Promise<void> {
    await this.repository.update(conversationId, {
      status: ConversationStatus.ANSWERED,
      isAnswered: true, // Keep the old field in sync for backward compatibility
    });
  }

  async markConversationAsPending(conversationId: string): Promise<void> {
    await this.repository.update(conversationId, {
      status: ConversationStatus.PENDING,
      isAnswered: false, // Keep the old field in sync for backward compatibility
    });
  }

  async markConversationAsError(conversationId: string): Promise<void> {
    await this.repository.update(conversationId, {
      status: ConversationStatus.ERROR,
      isAnswered: false, // Keep the old field in sync for backward compatibility
    });
  }
}

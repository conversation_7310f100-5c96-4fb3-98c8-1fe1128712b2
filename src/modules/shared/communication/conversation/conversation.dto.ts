import { Type } from 'class-transformer';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { UserDto } from '../../user/model/user.dto';
import { MessageDto } from './message/message.dto';

export class ConversationDto {
  id: number;

  @Type(() => Property)
  property?: Property;

  @Type(() => MessageDto)
  messages?: MessageDto[];

  @Type(() => UserDto)
  users: UserDto[];

  createdAt!: Date;
  updatedAt!: Date;
}

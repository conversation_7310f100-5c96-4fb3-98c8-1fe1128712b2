import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FollowUp } from './follow-up.entity';
import { FollowUpController } from './follow-up.controller';
import { FollowUpService } from './follow-up.service';
import { AiModule } from '../../../ai/ai.module';
import { ConversationModule } from '../conversation/conversetion.module';
import { OutboundCommunicationModule } from '../outbound-communication/outbound-communication.module';
import { Renter } from '../../../renter/renter/renter.entity';
import { UserModule } from '../../user/user.module';
import { followUpStrategiesProvider } from './follow-up-strategies.provider';
import { RenterStoppedRespondingStrategy } from './strategies/renter-stopped-responding/renter-stopped-responding.strategy';
import { PostShowingRenterStrategy } from './strategies/post-showing-renter/post-showing-renter.strategy';
import { PostShowingInvestorStrategy } from './strategies/post-showing-investor/post-showing-investor.strategy';
import { InvestorShowingRequestStrategy } from './strategies/investor-showing-request/investor-showing-request.strategy';
import { ShowingReminderStrategy } from './strategies/showing-reminder/showing-reminder.strategy';
import { ShowingCancellationWarningStrategy } from './strategies/showing-cancellation-warning/showing-cancellation-warning.strategy';
import { InvestorShowingReminderStrategy } from './strategies/investor-showing-reminder/investor-showing-reminder.strategy';
import { followUpCreationStrategiesProvider } from './creation/follow-up-creation-strategies.provider';
import { RenterFollowUpCreationStrategy } from './creation/renter-follow-up-creation.strategy';
import { FirstRenterFollowUpCreationStrategy } from './creation/first-renter-follow-up-creation.strategy';
import { PostShowingFollowUpCreationStrategy } from './creation/post-showing-follow-up-creation.strategy';
import { InvestorFollowUpCreationStrategy } from './creation/investor-follow-up-creation.strategy';
import { ShowingReminderFollowUpCreationStrategy } from './creation/showing-reminder-follow-up-creation.strategy';

// TODO remove Renter after convos channel is no longer needed and we don't send slack notifications
@Module({
  imports: [
    TypeOrmModule.forFeature([FollowUp, Renter]),
    AiModule,
    UserModule,
    forwardRef(() => ConversationModule),
    OutboundCommunicationModule,
  ],
  controllers: [FollowUpController],
  providers: [
    FollowUpService,
    // Follow-up execution strategies
    RenterStoppedRespondingStrategy,
    PostShowingRenterStrategy,
    PostShowingInvestorStrategy,
    InvestorShowingRequestStrategy,
    ShowingReminderStrategy,
    ShowingCancellationWarningStrategy,
    InvestorShowingReminderStrategy,
    followUpStrategiesProvider,
    // Follow-up creation strategies
    RenterFollowUpCreationStrategy,
    FirstRenterFollowUpCreationStrategy,
    PostShowingFollowUpCreationStrategy,
    InvestorFollowUpCreationStrategy,
    ShowingReminderFollowUpCreationStrategy,
    followUpCreationStrategiesProvider,
  ],
  exports: [FollowUpService],
})
export class FollowUpModule {}

import { Expose } from 'class-transformer';
import { Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { PropertyInquiry } from '../../../investor/property-inquiry/property-inquiry.entity';
import { User } from '../../user/entities/user.entity';
import { Conversation } from '../conversation/entities/conversation.entity';
import { FollowUpStatus } from './enums/follow-up-status.enum';
import { FollowUpTypeEnum } from './enums/follow-up-type.enum';

@Entity('follow_up')
export class FollowUp {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Conversation, {
    nullable: true,
    lazy: true,
  })
  conversation: Promise<Conversation> | Conversation;

  @ManyToOne(() => PropertyInquiry, {
    nullable: true,
    lazy: true,
  })
  propertyInquiry: Promise<PropertyInquiry> | PropertyInquiry;

  @ManyToOne(() => User, {
    nullable: false,
    lazy: false,
  })
  recipient: User;

  @Expose()
  @Column({ type: 'timestamp' })
  scheduledTime: Date;

  @Column({
    type: 'enum',
    enum: FollowUpStatus,
    default: FollowUpStatus.PENDING,
  })
  status: FollowUpStatus;

  @Expose()
  @Column({
    type: 'enum',
    enum: FollowUpTypeEnum,
    default: FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
  })
  type: FollowUpTypeEnum;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;
}

import { Injectable } from '@nestjs/common';
import { OutboundCommunicationService } from '../../../outbound-communication/outbound-communication.service';
import { FollowUp } from '../../follow-up.entity';
import { FollowUpStrategy } from '../follow-up-strategy.interface';

@Injectable()
export class PostShowingInvestorStrategy implements FollowUpStrategy {
  constructor(private readonly outboundCommunicationService: OutboundCommunicationService) {}

  async execute(followUp: FollowUp): Promise<void> {
    const inquiry = await followUp.propertyInquiry;
    const property = await inquiry.property;
    const location = await property.location;
    const renter = await inquiry.renter;

    await this.outboundCommunicationService.sendAfterShowingInvestorFollowUp(
      property,
      location,
      followUp.recipient,
      renter,
    );
  }
}

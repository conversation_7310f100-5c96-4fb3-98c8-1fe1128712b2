import { Test, TestingModule } from '@nestjs/testing';
import { PostShowingInvestorStrategy } from './post-showing-investor.strategy';
import { OutboundCommunicationService } from '../../../outbound-communication/outbound-communication.service';
import { FollowUp } from '../../follow-up.entity';

describe('PostShowingInvestorStrategy', () => {
  let strategy: PostShowingInvestorStrategy;
  let outboundCommunicationService: OutboundCommunicationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostShowingInvestorStrategy,
        {
          provide: OutboundCommunicationService,
          useValue: {
            sendAfterShowingInvestorFollowUp: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    strategy = module.get<PostShowingInvestorStrategy>(PostShowingInvestorStrategy);
    outboundCommunicationService = module.get<OutboundCommunicationService>(OutboundCommunicationService);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('execute', () => {
    it('should send a post-showing follow-up to the investor', async () => {
      // Mock data
      const mockUser = {
        id: 'investor-id',
        name: 'Investor User',
        email: '<EMAIL>',
      };

      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
      };

      const mockRenter = {
        id: 'renter-id',
        user: {
          name: 'Renter User',
        },
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
      };

      const mockInquiry = {
        id: 'inquiry-id',
        property: Promise.resolve(mockProperty),
        renter: mockRenter,
      };

      const mockFollowUp = {
        id: 'follow-up-id',
        recipient: mockUser,
        propertyInquiry: Promise.resolve(mockInquiry),
      } as unknown as FollowUp;

      // Execute the strategy
      await strategy.execute(mockFollowUp);

      // Verify outbound communication service was called with correct parameters
      expect(outboundCommunicationService.sendAfterShowingInvestorFollowUp).toHaveBeenCalledWith(
        mockProperty,
        mockLocation,
        mockUser,
        mockRenter,
      );
    });
  });
});

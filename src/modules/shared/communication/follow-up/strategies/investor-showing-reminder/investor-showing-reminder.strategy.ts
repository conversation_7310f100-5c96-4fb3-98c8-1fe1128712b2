import { Injectable } from '@nestjs/common';
import { OutboundCommunicationService } from '../../../outbound-communication/outbound-communication.service';
import { FollowUp } from '../../follow-up.entity';
import { FollowUpStrategy } from '../follow-up-strategy.interface';
import { TimezoneUtils } from '../../../../../../utils/timezone.utils';
import { SlackConvoMessageBuilder } from '../../../outbound-communication/slack/slack-convo-message-builder.class';
import { SlackCommunicationService } from '../../../outbound-communication/slack/slack-communication.service';

@Injectable()
export class InvestorShowingReminderStrategy implements FollowUpStrategy {
  constructor(
    private readonly outboundCommunicationService: OutboundCommunicationService,
    private readonly slackCommunicationService: SlackCommunicationService,
  ) {}

  async execute(followUp: FollowUp): Promise<void> {
    const user = followUp.recipient;
    const inquiry = await followUp.propertyInquiry;
    const property = await inquiry.property;
    const location = await property.location;
    const showingRequest = await inquiry.showingRequest;
    const showing = await showingRequest.showing;
    const conversation = await inquiry.conversation;

    // Use the existing notification service to send the reminder
    await this.outboundCommunicationService.sendUpcomingShowingReminderToInvestor(
      showing.id,
      location,
      user,
      TimezoneUtils.convertDateToStringInCityTz(showing.startTime, location.city, location.timeZone),
      showing.tourType,
    );

    // Send a Slack notification
    const slackMessage = new SlackConvoMessageBuilder().appendTextLine(
      `🔔 _\`Tallo sent showing reminder to the property owner ${user.name}.\`_`,
    );
    await this.slackCommunicationService.sendMessageToConvosChannel(slackMessage.build(), conversation);
  }
}

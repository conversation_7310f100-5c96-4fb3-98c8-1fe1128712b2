import { Injectable } from '@nestjs/common';
import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { UserService } from '../../../../user/user.service';
import { CommunicationChannel } from '../../../conversation/enums/preferred-communication-channel.enum';
import { MessageType } from '../../../conversation/message/message-type.enum';
import { ConversationService } from '../../../conversation/conversation.service';
import { OutboundCommunicationService } from '../../../outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../../outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../../outbound-communication/slack/slack-convo-message-builder.class';
import { followUpTemplateByTypeMap } from '../../config/follow-up-template-by-type.map';
import { FollowUp } from '../../follow-up.entity';
import { FollowUpTypeEnum } from '../../enums/follow-up-type.enum';
import { FollowUpStrategy } from '../follow-up-strategy.interface';
import { TimezoneUtils } from '../../../../../../utils/timezone.utils';

@Injectable()
export class RenterStoppedRespondingStrategy implements FollowUpStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly conversationService: ConversationService,
    private readonly outboundCommunicationService: OutboundCommunicationService,
    private readonly slackCommunicationService: SlackCommunicationService,
    private readonly userService: UserService,
  ) {}

  async execute(followUp: FollowUp): Promise<void> {
    const conversation = await followUp.conversation;
    // find conversation renter
    const user = followUp.recipient;
    const property = await conversation.property;
    const { city, timeZone, address } = await property.location;
    let switchedToSms = false;

    if (!user.email) {
      throw new Error('User email is missing');
    }

    if (user.preferredCommunicationChannel === CommunicationChannel.EMAIL && user.phoneNumber) {
      user.preferredCommunicationChannel = CommunicationChannel.SMS;
      await this.userService.updateUser(user.id, {
        preferredCommunicationChannel: CommunicationChannel.SMS,
      });
      switchedToSms = true;
    }

    // send follow up to renter
    const followUpMessage = await this.aiService.getResponseWithChatMemory(
      {
        renterName: user.name,
        currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone),
        switchToSms: switchedToSms,
        propertyAddress: address,
      },
      conversation.id,
      followUpTemplateByTypeMap.get(FollowUpTypeEnum.RENTER_STOPPED_RESPONDING),
      5,
      LanguageModelsEnum.GPT_4,
    );

    await this.sendFollowUp(followUpMessage, conversation, user);
    await this.sendSlackNotification(followUpMessage, conversation, switchedToSms);
  }

  private async sendFollowUp(message: string, conversation: any, recipient: any): Promise<void> {
    await this.conversationService.saveTalloMessage(
      conversation,
      message,
      MessageType.FOLLOW_UP,
      recipient.preferredCommunicationChannel,
    );

    this.outboundCommunicationService
      .sendMessage(recipient, message, await conversation.emailMetadata, recipient.preferredCommunicationChannel)
      .then(() => console.log('Follow up sent'));
  }

  private async sendSlackNotification(message: string, conversation: any, switchedToSms = false): Promise<void> {
    const slackMessage = new SlackConvoMessageBuilder().appendTextLine(`💌 _\`Tallo followed up with:\`_ ${message}`);

    if (switchedToSms) {
      slackMessage.appendEmptyLine().appendTextLine(`📱 Switched to SMS`);
    }

    await this.slackCommunicationService.sendMessageToConvosChannel(slackMessage.build(), conversation);
  }
}

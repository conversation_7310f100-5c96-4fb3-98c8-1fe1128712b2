import { Test, TestingModule } from '@nestjs/testing';
import { RenterStoppedRespondingStrategy } from './renter-stopped-responding.strategy';
import { AiService } from '../../../../../ai/ai.service';
import { ConversationService } from '../../../conversation/conversation.service';
import { OutboundCommunicationService } from '../../../outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../../outbound-communication/slack/slack-communication.service';
import { UserService } from '../../../../user/user.service';
import { FollowUp } from '../../follow-up.entity';
import { CommunicationChannel } from '../../../conversation/enums/preferred-communication-channel.enum';
import { MessageType } from '../../../conversation/message/message-type.enum';
import { FollowUpTypeEnum } from '../../enums/follow-up-type.enum';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';

describe('RenterStoppedRespondingStrategy', () => {
  let strategy: RenterStoppedRespondingStrategy;
  let aiService: AiService;
  let conversationService: ConversationService;
  let outboundCommunicationService: OutboundCommunicationService;
  let slackCommunicationService: SlackCommunicationService;
  let userService: UserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RenterStoppedRespondingStrategy,
        {
          provide: AiService,
          useValue: {
            getResponseWithChatMemory: jest.fn().mockResolvedValue('AI generated follow-up message'),
          },
        },
        {
          provide: ConversationService,
          useValue: {
            saveTalloMessage: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: OutboundCommunicationService,
          useValue: {
            sendMessage: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: SlackCommunicationService,
          useValue: {
            sendMessageToConvosChannel: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: UserService,
          useValue: {
            updateUser: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    strategy = module.get<RenterStoppedRespondingStrategy>(RenterStoppedRespondingStrategy);
    aiService = module.get<AiService>(AiService);
    conversationService = module.get<ConversationService>(ConversationService);
    outboundCommunicationService = module.get<OutboundCommunicationService>(OutboundCommunicationService);
    slackCommunicationService = module.get<SlackCommunicationService>(SlackCommunicationService);
    userService = module.get<UserService>(UserService);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('execute', () => {
    it('should send a follow-up message to the renter', async () => {
      // Mock data
      const mockUser = {
        id: 'user-id',
        name: 'Test User',
        email: '<EMAIL>',
        preferredCommunicationChannel: CommunicationChannel.EMAIL,
        phoneNumber: null,
      };

      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
        address: '123 Test St',
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
      };

      const mockConversation = {
        id: 'conversation-id',
        property: Promise.resolve(mockProperty),
        emailMetadata: Promise.resolve({ subject: 'Test Subject' }),
      };

      const mockFollowUp = {
        id: 'follow-up-id',
        conversation: Promise.resolve(mockConversation),
        recipient: mockUser,
        type: FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      } as unknown as FollowUp;

      // Execute the strategy
      await strategy.execute(mockFollowUp);

      // Verify AI service was called with correct parameters
      expect(aiService.getResponseWithChatMemory).toHaveBeenCalledWith(
        expect.objectContaining({
          renterName: mockUser.name,
          switchToSms: false,
          propertyAddress: mockLocation.address,
        }),
        mockConversation.id,
        expect.any(String),
        5,
        LanguageModelsEnum.GPT_4,
      );

      // Verify conversation service was called to save the message
      expect(conversationService.saveTalloMessage).toHaveBeenCalledWith(
        mockConversation,
        'AI generated follow-up message',
        MessageType.FOLLOW_UP,
        mockUser.preferredCommunicationChannel,
      );

      // Verify outbound communication service was called to send the message
      expect(outboundCommunicationService.sendMessage).toHaveBeenCalledWith(
        mockUser,
        'AI generated follow-up message',
        expect.any(Object),
        mockUser.preferredCommunicationChannel,
      );

      // Verify slack notification was sent
      expect(slackCommunicationService.sendMessageToConvosChannel).toHaveBeenCalled();
    });

    it('should switch to SMS if user has phone number and preferred channel is email', async () => {
      // Mock data with phone number
      const mockUser = {
        id: 'user-id',
        name: 'Test User',
        email: '<EMAIL>',
        preferredCommunicationChannel: CommunicationChannel.EMAIL,
        phoneNumber: '+1234567890',
      };

      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
        address: '123 Test St',
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
      };

      const mockConversation = {
        id: 'conversation-id',
        property: Promise.resolve(mockProperty),
        emailMetadata: Promise.resolve({ subject: 'Test Subject' }),
      };

      const mockFollowUp = {
        id: 'follow-up-id',
        conversation: Promise.resolve(mockConversation),
        recipient: mockUser,
        type: FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      } as unknown as FollowUp;

      // Execute the strategy
      await strategy.execute(mockFollowUp);

      // Verify user service was called to update the preferred communication channel
      expect(userService.updateUser).toHaveBeenCalledWith(mockUser.id, {
        preferredCommunicationChannel: CommunicationChannel.SMS,
      });

      // Verify AI service was called with switchToSms = true
      expect(aiService.getResponseWithChatMemory).toHaveBeenCalledWith(
        expect.objectContaining({
          switchToSms: true,
        }),
        expect.any(String),
        expect.any(String),
        expect.any(Number),
        expect.any(String),
      );

      // Verify slack notification was sent with a message containing 'Switched to SMS'
      expect(slackCommunicationService.sendMessageToConvosChannel).toHaveBeenCalled();

      // Get the mock implementation to verify the arguments
      const mockSendMessage = slackCommunicationService.sendMessageToConvosChannel as jest.Mock;
      const firstCallFirstArg = mockSendMessage.mock.calls[0][0];
      expect(typeof firstCallFirstArg === 'string' ? firstCallFirstArg : firstCallFirstArg.text).toContain(
        'Switched to SMS',
      );
    });

    it('should throw an error if user email is missing', async () => {
      // Mock data with missing email
      const mockUser = {
        id: 'user-id',
        name: 'Test User',
        email: null,
        preferredCommunicationChannel: CommunicationChannel.EMAIL,
      };

      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
        address: '123 Test St',
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
      };

      const mockConversation = {
        id: 'conversation-id',
        property: Promise.resolve(mockProperty),
      };

      const mockFollowUp = {
        id: 'follow-up-id',
        conversation: Promise.resolve(mockConversation),
        recipient: mockUser,
        type: FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      } as unknown as FollowUp;

      // Execute the strategy and expect it to throw
      await expect(strategy.execute(mockFollowUp)).rejects.toThrow('User email is missing');
    });
  });
});

import { Test, TestingModule } from '@nestjs/testing';
import { PostShowingRenterStrategy } from './post-showing-renter.strategy';
import { AiService } from '../../../../../ai/ai.service';
import { ConversationService } from '../../../conversation/conversation.service';
import { OutboundCommunicationService } from '../../../outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../../outbound-communication/slack/slack-communication.service';
import { FollowUp } from '../../follow-up.entity';
import { CommunicationChannel } from '../../../conversation/enums/preferred-communication-channel.enum';
import { MessageType } from '../../../conversation/message/message-type.enum';
import { FollowUpTypeEnum } from '../../enums/follow-up-type.enum';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';

describe('PostShowingRenterStrategy', () => {
  let strategy: PostShowingRenterStrategy;
  let aiService: AiService;
  let conversationService: ConversationService;
  let outboundCommunicationService: OutboundCommunicationService;
  let slackCommunicationService: SlackCommunicationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostShowingRenterStrategy,
        {
          provide: AiService,
          useValue: {
            getResponseWithChatMemory: jest.fn().mockResolvedValue('AI generated post-showing follow-up message'),
          },
        },
        {
          provide: ConversationService,
          useValue: {
            saveTalloMessage: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: OutboundCommunicationService,
          useValue: {
            sendMessage: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: SlackCommunicationService,
          useValue: {
            sendMessageToConvosChannel: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    strategy = module.get<PostShowingRenterStrategy>(PostShowingRenterStrategy);
    aiService = module.get<AiService>(AiService);
    conversationService = module.get<ConversationService>(ConversationService);
    outboundCommunicationService = module.get<OutboundCommunicationService>(OutboundCommunicationService);
    slackCommunicationService = module.get<SlackCommunicationService>(SlackCommunicationService);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('execute', () => {
    it('should send a post-showing follow-up message to the renter', async () => {
      // Mock data
      const mockUser = {
        id: 'user-id',
        name: 'Test User',
        email: '<EMAIL>',
        preferredCommunicationChannel: CommunicationChannel.EMAIL,
      };

      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
      };

      const mockShowingRequest = {
        id: 'showing-request-id',
        property: Promise.resolve(mockProperty),
      };

      const mockInquiry = {
        id: 'inquiry-id',
        showingRequest: Promise.resolve(mockShowingRequest),
      };

      const mockConversation = {
        id: 'conversation-id',
        emailMetadata: Promise.resolve({ subject: 'Test Subject' }),
      };

      const mockFollowUp = {
        id: 'follow-up-id',
        conversation: Promise.resolve(mockConversation),
        recipient: mockUser,
        propertyInquiry: Promise.resolve(mockInquiry),
        type: FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
      } as unknown as FollowUp;

      // Execute the strategy
      await strategy.execute(mockFollowUp);

      // Verify AI service was called with correct parameters
      expect(aiService.getResponseWithChatMemory).toHaveBeenCalledWith(
        expect.objectContaining({
          renterName: mockUser.name,
          currentDate: expect.any(String),
        }),
        mockConversation.id,
        expect.any(String),
        5,
        LanguageModelsEnum.GPT_4,
      );

      // Verify conversation service was called to save the message
      expect(conversationService.saveTalloMessage).toHaveBeenCalledWith(
        mockConversation,
        'AI generated post-showing follow-up message',
        MessageType.FOLLOW_UP,
        mockUser.preferredCommunicationChannel,
      );

      // Verify outbound communication service was called to send the message
      expect(outboundCommunicationService.sendMessage).toHaveBeenCalledWith(
        mockUser,
        'AI generated post-showing follow-up message',
        expect.any(Object),
        mockUser.preferredCommunicationChannel,
      );

      // Verify slack notification was sent
      expect(slackCommunicationService.sendMessageToConvosChannel).toHaveBeenCalled();
    });
  });
});

import { Injectable } from '@nestjs/common';
import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { MessageType } from '../../../conversation/message/message-type.enum';
import { ConversationService } from '../../../conversation/conversation.service';
import { OutboundCommunicationService } from '../../../outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../../outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../../outbound-communication/slack/slack-convo-message-builder.class';
import { followUpTemplateByTypeMap } from '../../config/follow-up-template-by-type.map';
import { FollowUp } from '../../follow-up.entity';
import { FollowUpTypeEnum } from '../../enums/follow-up-type.enum';
import { FollowUpStrategy } from '../follow-up-strategy.interface';
import { TimezoneUtils } from '../../../../../../utils/timezone.utils';

@Injectable()
export class PostShowingRenterStrategy implements FollowUpStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly conversationService: ConversationService,
    private readonly outboundCommunicationService: OutboundCommunicationService,
    private readonly slackCommunicationService: SlackCommunicationService,
  ) {}

  async execute(followUp: FollowUp): Promise<void> {
    const conversation = await followUp.conversation;
    const user = followUp.recipient;
    const inquiry = await followUp.propertyInquiry;
    const showingRequest = await inquiry.showingRequest;
    const property = await showingRequest.property;
    const { city, timeZone } = await property.location;

    const followUpMessage = await this.aiService.getResponseWithChatMemory(
      {
        renterName: user.name,
        currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone),
      },
      conversation.id,
      followUpTemplateByTypeMap.get(FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP),
      5,
      LanguageModelsEnum.GPT_4,
    );

    await this.sendFollowUp(followUpMessage, conversation, user);
    await this.sendSlackNotification(followUpMessage, conversation);
  }

  private async sendFollowUp(message: string, conversation: any, recipient: any): Promise<void> {
    await this.conversationService.saveTalloMessage(
      conversation,
      message,
      MessageType.FOLLOW_UP,
      recipient.preferredCommunicationChannel,
    );

    this.outboundCommunicationService
      .sendMessage(recipient, message, await conversation.emailMetadata, recipient.preferredCommunicationChannel)
      .then(() => console.log('Follow up sent'));
  }

  private async sendSlackNotification(message: string, conversation: any): Promise<void> {
    const slackMessage = new SlackConvoMessageBuilder().appendTextLine(`💌 _\`Tallo followed up with:\`_ ${message}`);
    await this.slackCommunicationService.sendMessageToConvosChannel(slackMessage.build(), conversation);
  }
}

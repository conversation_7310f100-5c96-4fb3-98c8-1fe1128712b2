import { Test, TestingModule } from '@nestjs/testing';
import { InvestorShowingRequestStrategy } from './investor-showing-request.strategy';
import { OutboundCommunicationService } from '../../../outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../../outbound-communication/slack/slack-communication.service';
import { FollowUp } from '../../follow-up.entity';

describe('InvestorShowingRequestStrategy', () => {
  let strategy: InvestorShowingRequestStrategy;
  let outboundCommunicationService: OutboundCommunicationService;
  let slackCommunicationService: SlackCommunicationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InvestorShowingRequestStrategy,
        {
          provide: OutboundCommunicationService,
          useValue: {
            sendPendingShowingRequestReminderNotification: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: SlackCommunicationService,
          useValue: {
            sendMessageToConvosChannel: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    strategy = module.get<InvestorShowingRequestStrategy>(InvestorShowingRequestStrategy);
    outboundCommunicationService = module.get<OutboundCommunicationService>(OutboundCommunicationService);
    slackCommunicationService = module.get<SlackCommunicationService>(SlackCommunicationService);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('execute', () => {
    it('should send a reminder notification to the investor about pending showing request', async () => {
      // Mock data
      const mockOwnerUser = {
        id: 'investor-id',
        name: 'Investor User',
        email: '<EMAIL>',
      };

      const mockRenter = {
        id: 'renter-id',
        user: {
          name: 'Renter User',
        },
      };

      const mockProperty = {
        id: 'property-id',
        address: '123 Test St',
      };

      const mockConversation = {
        id: 'conversation-id',
      };

      const mockInquiry = {
        id: 'inquiry-id',
        property: mockProperty,
        renter: mockRenter,
        conversation: Promise.resolve(mockConversation),
      };

      const mockFollowUp = {
        id: 'follow-up-id',
        recipient: mockOwnerUser,
        propertyInquiry: Promise.resolve(mockInquiry),
      } as unknown as FollowUp;

      // Execute the strategy
      await strategy.execute(mockFollowUp);

      // Verify outbound communication service was called with correct parameters
      expect(outboundCommunicationService.sendPendingShowingRequestReminderNotification).toHaveBeenCalledWith(
        mockOwnerUser,
        mockProperty,
        mockRenter,
      );

      // Verify slack notification was sent
      expect(slackCommunicationService.sendMessageToConvosChannel).toHaveBeenCalled();

      // Get the mock implementation to verify the arguments
      const mockSendMessage = slackCommunicationService.sendMessageToConvosChannel as jest.Mock;
      const firstCallFirstArg = mockSendMessage.mock.calls[0][0];
      expect(typeof firstCallFirstArg === 'string' ? firstCallFirstArg : firstCallFirstArg.text).toContain(
        `Tallo followed up with the ${mockOwnerUser.name}`,
      );
    });
  });
});

import { Injectable } from '@nestjs/common';
import { OutboundCommunicationService } from '../../../outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../../outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../../outbound-communication/slack/slack-convo-message-builder.class';
import { FollowUp } from '../../follow-up.entity';
import { FollowUpStrategy } from '../follow-up-strategy.interface';

@Injectable()
export class InvestorShowingRequestStrategy implements FollowUpStrategy {
  constructor(
    private readonly outboundCommunicationService: OutboundCommunicationService,
    private readonly slackCommunicationService: SlackCommunicationService,
  ) {}

  async execute(followUp: FollowUp): Promise<void> {
    const ownerUser = followUp.recipient;
    const inquiry = await followUp.propertyInquiry;
    const property = await inquiry.property;
    const renter = await inquiry.renter;

    await this.outboundCommunicationService.sendPendingShowingRequestReminderNotification(ownerUser, property, renter);

    const conversation = await inquiry.conversation;
    this.slackCommunicationService.sendMessageToConvosChannel(
      new SlackConvoMessageBuilder()
        .appendTextLine(
          // eslint-disable-next-line max-len
          `🔔 _\`Tallo followed up with the ${ownerUser.name} about the pending showing request.\`_ It will be canceled in 4 hours if not answered.`,
        )
        .build(),
      conversation,
    );
  }
}

import { LessThan, Repository } from 'typeorm';

import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { PropertyInquiry } from '../../../investor/property-inquiry/property-inquiry.entity';
import { User } from '../../user/entities/user.entity';
import { Conversation } from '../conversation/entities/conversation.entity';
import { RentStageEnum } from '../conversation/enums/rent-stage.enum';
import { FollowUpStatus } from './enums/follow-up-status.enum';
import { FollowUpTypeEnum } from './enums/follow-up-type.enum';
import { FollowUp } from './follow-up.entity';
import { FollowUpsSchedule } from './models/follow-ups-schedule';
import { FOLLOW_UP_STRATEGIES } from './follow-up-strategies.provider';
import { FollowUpStrategy } from './strategies/follow-up-strategy.interface';
import {
  FOLLOW_UP_CREATION_STRATEGIES,
  FollowUpCreationStrategyType,
} from './creation/follow-up-creation-strategies.provider';
import { FollowUpCreationStrategy } from './creation/follow-up-creation-strategy.interface';

@Injectable()
export class FollowUpService {
  constructor(
    @InjectRepository(FollowUp)
    private readonly followUpRepository: Repository<FollowUp>,
    @Inject(FOLLOW_UP_STRATEGIES)
    private readonly followUpStrategies: Map<FollowUpTypeEnum, FollowUpStrategy>,
    @Inject(FOLLOW_UP_CREATION_STRATEGIES)
    private readonly followUpCreationStrategies: Map<FollowUpCreationStrategyType, FollowUpCreationStrategy>,
  ) {}

  async followUpConversations(): Promise<void> {
    // find all the followups where the scheduled time is less than now and the follow-up is not sent
    const followUps = await this.followUpRepository.find({
      where: {
        scheduledTime: LessThan(new Date()),
        status: FollowUpStatus.PENDING,
      },
      relations: ['conversation', 'recipient'],
    });

    await this.followUpRepository.update(
      {
        scheduledTime: LessThan(new Date()),
        status: FollowUpStatus.PENDING,
      },
      { status: FollowUpStatus.SENDING },
    );

    // for each follow up, send the follow-up and mark it as sent
    for (const followUp of followUps) {
      await this.markFollowUpAsSent(followUp);
      try {
        await this.triggerFollowUp(followUp);
      } catch (e) {
        console.error('Error sending follow-up', e);
        await this.markFollowUpAsPending(followUp);
      }
    }
  }

  async triggerFollowUp(followUp: FollowUp): Promise<void> {
    const strategy = this.followUpStrategies.get(followUp.type);

    if (strategy) {
      await strategy.execute(followUp);
    } else {
      console.error(`Unknown follow up type: ${followUp.type}`);
    }
  }

  async markFollowUpAsSent(followUp: FollowUp): Promise<void> {
    // mark the follow-up as sent
    await this.followUpRepository.update(followUp.id, {
      status: FollowUpStatus.SENT,
    });
  }

  async markFollowUpAsPending(followUp: FollowUp): Promise<void> {
    // mark the follow-up as sent
    await this.followUpRepository.update(followUp.id, {
      status: FollowUpStatus.PENDING,
    });
  }

  async createRenterFollowUps(
    conversation: Conversation,
    inquiry: PropertyInquiry,
    numberOfFollowups = 3,
    type = FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
  ): Promise<void> {
    const strategy = this.followUpCreationStrategies.get(FollowUpCreationStrategyType.RENTER);
    await strategy.createFollowUps({
      conversation,
      inquiry,
      numberOfFollowups,
      followUpType: type,
    });
    console.log('[Renter Follow ups] Created');
  }

  async createInvestorFollowUps(
    recipient: User,
    type: FollowUpTypeEnum,
    schedule: FollowUpsSchedule,
    inquiry?: PropertyInquiry,
  ): Promise<void> {
    const strategy = this.followUpCreationStrategies.get(FollowUpCreationStrategyType.INVESTOR);
    await strategy.createFollowUps({
      recipient,
      schedule,
      inquiry,
    });
  }

  async createFirstFollowUpsForRenter(
    conversation: Conversation,
    inquiry: PropertyInquiry,
    type = FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
  ): Promise<void> {
    const strategy = this.followUpCreationStrategies.get(FollowUpCreationStrategyType.FIRST_RENTER);
    await strategy.createFollowUps({
      conversation,
      inquiry,
      followUpType: type,
    });
  }

  async deletePostShowingFollowUps(inquiry: PropertyInquiry): Promise<void> {
    try {
      await this.followUpRepository.delete({
        propertyInquiry: { id: inquiry.id },
        status: FollowUpStatus.PENDING,
        type: FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
      });

      await this.followUpRepository.delete({
        propertyInquiry: { id: inquiry.id },
        status: FollowUpStatus.PENDING,
        type: FollowUpTypeEnum.POST_SHOWING_INVESTOR_FOLLOW_UP,
      });

      console.log('[Post Showing Follow ups] Deleted existing follow-ups');
    } catch (error) {
      console.log('[Post Showing Follow ups] Error deleting follow ups', error);
    }
  }

  async createPostShowingFollowUps(conversation: Conversation, inquiry: PropertyInquiry): Promise<void> {
    await this.deletePostShowingFollowUps(inquiry);

    const strategy = this.followUpCreationStrategies.get(FollowUpCreationStrategyType.POST_SHOWING);
    await strategy.createFollowUps({
      conversation,
      inquiry,
    });

    console.log('[Post Showing Follow ups] Created');
  }

  async deleteShowingReminderFollowUps(inquiry: PropertyInquiry): Promise<void> {
    try {
      await this.followUpRepository.delete({
        propertyInquiry: { id: inquiry.id },
        status: FollowUpStatus.PENDING,
        type: FollowUpTypeEnum.SHOWING_REMINDER_FOLLOW_UP,
      });

      await this.followUpRepository.delete({
        propertyInquiry: { id: inquiry.id },
        status: FollowUpStatus.PENDING,
        type: FollowUpTypeEnum.SHOWING_CANCELLATION_WARNING_FOLLOW_UP,
      });

      await this.followUpRepository.delete({
        propertyInquiry: { id: inquiry.id },
        status: FollowUpStatus.PENDING,
        type: FollowUpTypeEnum.INVESTOR_SHOWING_REMINDER_FOLLOW_UP,
      });

      console.log('[Showing Reminder Follow ups] Deleted existing follow-ups');
    } catch (error) {
      console.log('[Showing Reminder Follow ups] Error deleting follow ups', error);
    }
  }

  async createShowingReminderFollowUps(
    conversation: Conversation,
    inquiry: PropertyInquiry,
    showingTime: Date,
  ): Promise<void> {
    await this.deleteShowingReminderFollowUps(inquiry);

    const strategy = this.followUpCreationStrategies.get(FollowUpCreationStrategyType.SHOWING_REMINDER);
    await strategy.createFollowUps({
      conversation,
      inquiry,
      showingTime,
    });
    console.log('[Showing Reminder Follow ups] Created');
  }

  async refreshFollowUpsScheduledTime(conversation: Conversation, inquiry: PropertyInquiry): Promise<void> {
    if (
      inquiry.stage === RentStageEnum.INITIAL_CONTACT ||
      inquiry.stage === RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER
    ) {
      // delete all the pending follow-ups associated with the conversation and create new ones
      await this.deleteRenterFollowUpsByStatusAndType(conversation.id, FollowUpTypeEnum.RENTER_STOPPED_RESPONDING);
      await this.createRenterFollowUps(conversation, inquiry);
    }
  }

  async deleteRenterFollowUpsByStatus(conversationId: string, status = FollowUpStatus.PENDING): Promise<void> {
    try {
      await this.followUpRepository.delete({
        conversation: { id: conversationId },
        status,
      });

      console.log('[Follow ups] Deleted');
    } catch (error) {
      console.log('[Follow ups] Error deleting follow ups', error);
    }
  }

  async deleteRenterFollowUpsByStatusAndType(
    conversationId: string,
    type: FollowUpTypeEnum,
    status = FollowUpStatus.PENDING,
  ): Promise<void> {
    try {
      await this.followUpRepository.delete({
        conversation: { id: conversationId },
        status,
        type,
      });

      console.log('[Follow ups] Deleted by type');
    } catch (error) {
      console.log('[Follow ups] Error deleting follow ups', error);
    }
  }

  async findFollowUpByUserAndType(userId: string, type: FollowUpTypeEnum): Promise<FollowUp> {
    return this.followUpRepository.findOne({
      where: {
        recipient: { id: userId },
        type,
      },
    });
  }

  async deleteRenterFollowUpsByType(
    conversationId: string,
    type: FollowUpTypeEnum,
    status = FollowUpStatus.PENDING,
  ): Promise<void> {
    try {
      await this.followUpRepository.delete({
        conversation: { id: conversationId },
        status,
        type,
      });

      console.log('[Follow ups] Deleted by type');
    } catch (error) {
      console.log('[Follow ups] Error deleting follow ups', error);
    }
  }

  async deleteInvestorFollowUpsByInquiry(
    inquiry: PropertyInquiry,
    ownerId: string,
    type: FollowUpTypeEnum,
  ): Promise<void> {
    await this.followUpRepository.delete({
      propertyInquiry: { id: inquiry.id },
      recipient: { id: ownerId },
      type,
    });
  }

  async deleteFollowUpsByProperty(propertyId: string, keepInvestorFollowups = false): Promise<void> {
    const followupTypesToDelete = [FollowUpTypeEnum.RENTER_STOPPED_RESPONDING];

    if (!keepInvestorFollowups) {
      followupTypesToDelete.push(FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP);
    }

    const subQuery = this.followUpRepository
      .createQueryBuilder('followUp')
      .select('followUp.id')
      .leftJoin('followUp.propertyInquiry', 'propertyInquiry')
      .where('propertyInquiry.propertyId = :propertyId')
      .andWhere('followUp.status = :status')
      .andWhere('followUp.type IN (:...types)')
      .getQuery();

    await this.followUpRepository
      .createQueryBuilder()
      .delete()
      .from('follow_up')
      .where(`id IN (${subQuery})`)
      .setParameters({ propertyId, status: FollowUpStatus.PENDING, types: followupTypesToDelete })
      .execute();
  }

  async create(followUpData: Partial<FollowUp> = {}): Promise<FollowUp> {
    const followUp: FollowUp = this.followUpRepository.create({
      ...followUpData,
    });

    return await this.followUpRepository.save(followUp);
  }
}

import { Provider } from '@nestjs/common';
import { FollowUpTypeEnum } from './enums/follow-up-type.enum';
import { FollowUpStrategy } from './strategies/follow-up-strategy.interface';
import { RenterStoppedRespondingStrategy } from './strategies/renter-stopped-responding/renter-stopped-responding.strategy';
import { PostShowingRenterStrategy } from './strategies/post-showing-renter/post-showing-renter.strategy';
import { PostShowingInvestorStrategy } from './strategies/post-showing-investor/post-showing-investor.strategy';
import { InvestorShowingRequestStrategy } from './strategies/investor-showing-request/investor-showing-request.strategy';
import { ShowingReminderStrategy } from './strategies/showing-reminder/showing-reminder.strategy';
import { ShowingCancellationWarningStrategy } from './strategies/showing-cancellation-warning/showing-cancellation-warning.strategy';
import { InvestorShowingReminderStrategy } from './strategies/investor-showing-reminder/investor-showing-reminder.strategy';

export const FOLLOW_UP_STRATEGIES = 'FOLLOW_UP_STRATEGIES';

export const followUpStrategiesProvider: Provider = {
  provide: FOLLOW_UP_STRATEGIES,
  useFactory: (
    renterStoppedRespondingStrategy: RenterStoppedRespondingStrategy,
    postShowingRenterStrategy: PostShowingRenterStrategy,
    postShowingInvestorStrategy: PostShowingInvestorStrategy,
    investorShowingRequestStrategy: InvestorShowingRequestStrategy,
    showingReminderStrategy: ShowingReminderStrategy,
    showingCancellationWarningStrategy: ShowingCancellationWarningStrategy,
    investorShowingReminderStrategy: InvestorShowingReminderStrategy,
  ) => {
    const strategies = new Map<FollowUpTypeEnum, FollowUpStrategy>();

    strategies.set(FollowUpTypeEnum.RENTER_STOPPED_RESPONDING, renterStoppedRespondingStrategy);
    strategies.set(FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP, postShowingRenterStrategy);
    strategies.set(FollowUpTypeEnum.POST_SHOWING_INVESTOR_FOLLOW_UP, postShowingInvestorStrategy);
    strategies.set(FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP, investorShowingRequestStrategy);
    strategies.set(FollowUpTypeEnum.SHOWING_REMINDER_FOLLOW_UP, showingReminderStrategy);
    strategies.set(FollowUpTypeEnum.SHOWING_CANCELLATION_WARNING_FOLLOW_UP, showingCancellationWarningStrategy);
    strategies.set(FollowUpTypeEnum.INVESTOR_SHOWING_REMINDER_FOLLOW_UP, investorShowingReminderStrategy);

    return strategies;
  },
  inject: [
    RenterStoppedRespondingStrategy,
    PostShowingRenterStrategy,
    PostShowingInvestorStrategy,
    InvestorShowingRequestStrategy,
    ShowingReminderStrategy,
    ShowingCancellationWarningStrategy,
    InvestorShowingReminderStrategy,
  ],
};

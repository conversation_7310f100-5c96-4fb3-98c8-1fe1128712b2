import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { FollowUpService } from './follow-up.service';
import { FollowUp } from './follow-up.entity';
import { FollowUpStatus } from './enums/follow-up-status.enum';
import { FollowUpTypeEnum } from './enums/follow-up-type.enum';
import { FOLLOW_UP_STRATEGIES } from './follow-up-strategies.provider';
import { FollowUpStrategy } from './strategies/follow-up-strategy.interface';
import {
  FOLLOW_UP_CREATION_STRATEGIES,
  FollowUpCreationStrategyType,
} from './creation/follow-up-creation-strategies.provider';
import { FollowUpCreationStrategy } from './creation/follow-up-creation-strategy.interface';
import { Conversation } from '../conversation/entities/conversation.entity';
import { PropertyInquiry } from '../../../investor/property-inquiry/property-inquiry.entity';
import { User } from '../../user/entities/user.entity';
import { RentStageEnum } from '../conversation/enums/rent-stage.enum';
import { FollowUpsSchedule } from './models/follow-ups-schedule';

describe('FollowUpService', () => {
  let service: FollowUpService;
  let followUpRepository: Repository<FollowUp>;
  let followUpStrategies: Map<FollowUpTypeEnum, FollowUpStrategy>;
  let followUpCreationStrategies: Map<FollowUpCreationStrategyType, FollowUpCreationStrategy>;

  // Mock strategies
  const mockFollowUpStrategy: FollowUpStrategy = {
    execute: jest.fn().mockResolvedValue(undefined),
  };

  const mockFollowUpCreationStrategy: FollowUpCreationStrategy = {
    createFollowUps: jest.fn().mockResolvedValue([]),
  };

  // Mock data
  const mockFollowUp = {
    id: 'follow-up-id',
    status: FollowUpStatus.PENDING,
    type: FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
    conversation: Promise.resolve({
      id: 'conversation-id',
    }),
    recipient: {
      id: 'user-id',
      name: 'Test User',
    },
  } as unknown as FollowUp;

  const mockConversation = {
    id: 'conversation-id',
  } as Conversation;

  const mockInquiry = {
    id: 'inquiry-id',
    stage: RentStageEnum.INITIAL_CONTACT,
  } as PropertyInquiry;

  const mockUser = {
    id: 'user-id',
    name: 'Test User',
  } as User;

  const mockSchedule: FollowUpsSchedule = {
    followUpsSchedule: [new Date(), new Date(Date.now() + 86400000)], // Today and tomorrow
  };

  beforeEach(async () => {
    // Create mock strategies maps
    followUpStrategies = new Map<FollowUpTypeEnum, FollowUpStrategy>();
    followUpStrategies.set(FollowUpTypeEnum.RENTER_STOPPED_RESPONDING, mockFollowUpStrategy);
    followUpStrategies.set(FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP, mockFollowUpStrategy);
    followUpStrategies.set(FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP, mockFollowUpStrategy);
    followUpStrategies.set(FollowUpTypeEnum.POST_SHOWING_INVESTOR_FOLLOW_UP, mockFollowUpStrategy);
    followUpStrategies.set(FollowUpTypeEnum.SHOWING_REMINDER_FOLLOW_UP, mockFollowUpStrategy);
    followUpStrategies.set(FollowUpTypeEnum.SHOWING_CANCELLATION_WARNING_FOLLOW_UP, mockFollowUpStrategy);
    followUpStrategies.set(FollowUpTypeEnum.INVESTOR_SHOWING_REMINDER_FOLLOW_UP, mockFollowUpStrategy);

    followUpCreationStrategies = new Map<FollowUpCreationStrategyType, FollowUpCreationStrategy>();
    followUpCreationStrategies.set(FollowUpCreationStrategyType.RENTER, mockFollowUpCreationStrategy);
    followUpCreationStrategies.set(FollowUpCreationStrategyType.FIRST_RENTER, mockFollowUpCreationStrategy);
    followUpCreationStrategies.set(FollowUpCreationStrategyType.POST_SHOWING, mockFollowUpCreationStrategy);
    followUpCreationStrategies.set(FollowUpCreationStrategyType.INVESTOR, mockFollowUpCreationStrategy);
    followUpCreationStrategies.set(FollowUpCreationStrategyType.SHOWING_REMINDER, mockFollowUpCreationStrategy);

    // Mock repository
    const mockFollowUpRepository = {
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      createQueryBuilder: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getQuery: jest.fn().mockReturnValue('mock-query'),
        delete: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        setParameters: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ affected: 1 }),
      })),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FollowUpService,
        {
          provide: getRepositoryToken(FollowUp),
          useValue: mockFollowUpRepository,
        },
        {
          provide: FOLLOW_UP_STRATEGIES,
          useValue: followUpStrategies,
        },
        {
          provide: FOLLOW_UP_CREATION_STRATEGIES,
          useValue: followUpCreationStrategies,
        },
      ],
    }).compile();

    service = module.get<FollowUpService>(FollowUpService);
    followUpRepository = module.get<Repository<FollowUp>>(getRepositoryToken(FollowUp));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('followUpConversations', () => {
    it('should find pending follow-ups and process them', async () => {
      const mockFollowUps = [mockFollowUp];
      jest.spyOn(followUpRepository, 'find').mockResolvedValue(mockFollowUps);
      jest.spyOn(followUpRepository, 'update').mockResolvedValue(undefined);
      jest.spyOn(service, 'markFollowUpAsSent').mockResolvedValue(undefined);
      jest.spyOn(service, 'triggerFollowUp').mockResolvedValue(undefined);

      await service.followUpConversations();

      expect(followUpRepository.find).toHaveBeenCalledWith({
        where: {
          scheduledTime: LessThan(expect.any(Date)),
          status: FollowUpStatus.PENDING,
        },
        relations: ['conversation', 'recipient'],
      });
      expect(followUpRepository.update).toHaveBeenCalledWith(
        {
          scheduledTime: LessThan(expect.any(Date)),
          status: FollowUpStatus.PENDING,
        },
        { status: FollowUpStatus.SENDING },
      );
      expect(service.markFollowUpAsSent).toHaveBeenCalledWith(mockFollowUp);
      expect(service.triggerFollowUp).toHaveBeenCalledWith(mockFollowUp);
    });

    it('should handle errors during follow-up processing', async () => {
      const mockFollowUps = [mockFollowUp];
      jest.spyOn(followUpRepository, 'find').mockResolvedValue(mockFollowUps);
      jest.spyOn(service, 'markFollowUpAsSent').mockResolvedValue(undefined);
      jest.spyOn(service, 'triggerFollowUp').mockRejectedValue(new Error('Test error'));
      jest.spyOn(service, 'markFollowUpAsPending').mockResolvedValue(undefined);
      jest.spyOn(console, 'error').mockImplementation(() => {});

      await service.followUpConversations();

      expect(service.markFollowUpAsSent).toHaveBeenCalledWith(mockFollowUp);
      expect(service.triggerFollowUp).toHaveBeenCalledWith(mockFollowUp);
      expect(service.markFollowUpAsPending).toHaveBeenCalledWith(mockFollowUp);
      expect(console.error).toHaveBeenCalledWith('Error sending follow-up', expect.any(Error));
    });
  });

  describe('triggerFollowUp', () => {
    it('should execute the appropriate strategy for a follow-up', async () => {
      const mockStrategy = followUpStrategies.get(FollowUpTypeEnum.RENTER_STOPPED_RESPONDING);

      await service.triggerFollowUp(mockFollowUp);

      expect(mockStrategy.execute).toHaveBeenCalledWith(mockFollowUp);
    });

    it('should log an error for unknown follow-up types', async () => {
      const unknownTypeFollowUp = {
        ...mockFollowUp,
        type: 'Unknown Type' as FollowUpTypeEnum,
      };
      jest.spyOn(console, 'error').mockImplementation(() => {});

      await service.triggerFollowUp(unknownTypeFollowUp);

      expect(console.error).toHaveBeenCalledWith(`Unknown follow up type: ${unknownTypeFollowUp.type}`);
    });
  });

  describe('markFollowUpAsSent', () => {
    it('should update the follow-up status to SENT', async () => {
      await service.markFollowUpAsSent(mockFollowUp);

      expect(followUpRepository.update).toHaveBeenCalledWith(mockFollowUp.id, {
        status: FollowUpStatus.SENT,
      });
    });
  });

  describe('markFollowUpAsPending', () => {
    it('should update the follow-up status to PENDING', async () => {
      await service.markFollowUpAsPending(mockFollowUp);

      expect(followUpRepository.update).toHaveBeenCalledWith(mockFollowUp.id, {
        status: FollowUpStatus.PENDING,
      });
    });
  });

  describe('createRenterFollowUps', () => {
    it('should use the RENTER strategy to create follow-ups', async () => {
      const strategy = followUpCreationStrategies.get(FollowUpCreationStrategyType.RENTER);
      jest.spyOn(strategy, 'createFollowUps').mockResolvedValue([]);
      jest.spyOn(console, 'log').mockImplementation(() => {});

      await service.createRenterFollowUps(mockConversation, mockInquiry);

      expect(strategy.createFollowUps).toHaveBeenCalledWith({
        conversation: mockConversation,
        inquiry: mockInquiry,
        numberOfFollowups: 3,
        followUpType: FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      });
      expect(console.log).toHaveBeenCalledWith('[Renter Follow ups] Created');
    });

    it('should allow custom number of follow-ups and type', async () => {
      const strategy = followUpCreationStrategies.get(FollowUpCreationStrategyType.RENTER);
      jest.spyOn(strategy, 'createFollowUps').mockResolvedValue([]);

      await service.createRenterFollowUps(
        mockConversation,
        mockInquiry,
        5,
        FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
      );

      expect(strategy.createFollowUps).toHaveBeenCalledWith({
        conversation: mockConversation,
        inquiry: mockInquiry,
        numberOfFollowups: 5,
        followUpType: FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
      });
    });
  });

  describe('createInvestorFollowUps', () => {
    it('should use the INVESTOR strategy to create follow-ups', async () => {
      const strategy = followUpCreationStrategies.get(FollowUpCreationStrategyType.INVESTOR);
      jest.spyOn(strategy, 'createFollowUps').mockResolvedValue([]);

      await service.createInvestorFollowUps(
        mockUser,
        FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
        mockSchedule,
        mockInquiry,
      );

      expect(strategy.createFollowUps).toHaveBeenCalledWith({
        recipient: mockUser,
        schedule: mockSchedule,
        inquiry: mockInquiry,
      });
    });

    it('should work without an inquiry', async () => {
      const strategy = followUpCreationStrategies.get(FollowUpCreationStrategyType.INVESTOR);
      jest.spyOn(strategy, 'createFollowUps').mockResolvedValue([]);

      await service.createInvestorFollowUps(
        mockUser,
        FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
        mockSchedule,
      );

      expect(strategy.createFollowUps).toHaveBeenCalledWith({
        recipient: mockUser,
        schedule: mockSchedule,
        inquiry: undefined,
      });
    });
  });

  describe('createFirstFollowUpsForRenter', () => {
    it('should use the FIRST_RENTER strategy to create follow-ups', async () => {
      const strategy = followUpCreationStrategies.get(FollowUpCreationStrategyType.FIRST_RENTER);
      jest.spyOn(strategy, 'createFollowUps').mockResolvedValue([]);

      await service.createFirstFollowUpsForRenter(mockConversation, mockInquiry);

      expect(strategy.createFollowUps).toHaveBeenCalledWith({
        conversation: mockConversation,
        inquiry: mockInquiry,
        followUpType: FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      });
    });

    it('should allow custom follow-up type', async () => {
      const strategy = followUpCreationStrategies.get(FollowUpCreationStrategyType.FIRST_RENTER);
      jest.spyOn(strategy, 'createFollowUps').mockResolvedValue([]);

      await service.createFirstFollowUpsForRenter(
        mockConversation,
        mockInquiry,
        FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
      );

      expect(strategy.createFollowUps).toHaveBeenCalledWith({
        conversation: mockConversation,
        inquiry: mockInquiry,
        followUpType: FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
      });
    });
  });

  describe('deletePostShowingFollowUps', () => {
    it('should delete post-showing follow-ups for both renter and investor', async () => {
      jest.spyOn(followUpRepository, 'delete').mockResolvedValue(undefined);
      jest.spyOn(console, 'log').mockImplementation(() => {});

      await service.deletePostShowingFollowUps(mockInquiry);

      // Should delete renter follow-ups
      expect(followUpRepository.delete).toHaveBeenCalledWith({
        propertyInquiry: { id: mockInquiry.id },
        status: FollowUpStatus.PENDING,
        type: FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
      });

      // Should delete investor follow-ups
      expect(followUpRepository.delete).toHaveBeenCalledWith({
        propertyInquiry: { id: mockInquiry.id },
        status: FollowUpStatus.PENDING,
        type: FollowUpTypeEnum.POST_SHOWING_INVESTOR_FOLLOW_UP,
      });

      expect(console.log).toHaveBeenCalledWith('[Post Showing Follow ups] Deleted existing follow-ups');
    });

    it('should handle errors when deleting follow-ups', async () => {
      const error = new Error('Delete error');
      jest.spyOn(followUpRepository, 'delete').mockRejectedValue(error);
      jest.spyOn(console, 'log').mockImplementation(() => {});

      await service.deletePostShowingFollowUps(mockInquiry);

      expect(console.log).toHaveBeenCalledWith('[Post Showing Follow ups] Error deleting follow ups', error);
    });
  });

  describe('createPostShowingFollowUps', () => {
    it('should delete existing follow-ups and then use the POST_SHOWING strategy to create new ones', async () => {
      const strategy = followUpCreationStrategies.get(FollowUpCreationStrategyType.POST_SHOWING);
      jest.spyOn(strategy, 'createFollowUps').mockResolvedValue([]);
      jest.spyOn(service, 'deletePostShowingFollowUps').mockResolvedValue(undefined);
      jest.spyOn(console, 'log').mockImplementation(() => {});

      await service.createPostShowingFollowUps(mockConversation, mockInquiry);

      expect(service.deletePostShowingFollowUps).toHaveBeenCalledWith(mockInquiry);
      expect(strategy.createFollowUps).toHaveBeenCalledWith({
        conversation: mockConversation,
        inquiry: mockInquiry,
      });
      expect(console.log).toHaveBeenCalledWith('[Post Showing Follow ups] Created');
    });
  });

  describe('deleteShowingReminderFollowUps', () => {
    it('should delete showing reminder follow-ups for renter and investor', async () => {
      jest.spyOn(followUpRepository, 'delete').mockResolvedValue(undefined);
      jest.spyOn(console, 'log').mockImplementation(() => {});

      await service.deleteShowingReminderFollowUps(mockInquiry);

      expect(followUpRepository.delete).toHaveBeenCalledWith({
        propertyInquiry: { id: mockInquiry.id },
        status: FollowUpStatus.PENDING,
        type: FollowUpTypeEnum.SHOWING_REMINDER_FOLLOW_UP,
      });

      expect(followUpRepository.delete).toHaveBeenCalledWith({
        propertyInquiry: { id: mockInquiry.id },
        status: FollowUpStatus.PENDING,
        type: FollowUpTypeEnum.SHOWING_CANCELLATION_WARNING_FOLLOW_UP,
      });

      expect(followUpRepository.delete).toHaveBeenCalledWith({
        propertyInquiry: { id: mockInquiry.id },
        status: FollowUpStatus.PENDING,
        type: FollowUpTypeEnum.INVESTOR_SHOWING_REMINDER_FOLLOW_UP,
      });

      expect(console.log).toHaveBeenCalledWith('[Showing Reminder Follow ups] Deleted existing follow-ups');
    });

    it('should handle errors when deleting follow-ups', async () => {
      const error = new Error('Delete error');
      jest.spyOn(followUpRepository, 'delete').mockRejectedValue(error);
      jest.spyOn(console, 'log').mockImplementation(() => {});

      await service.deleteShowingReminderFollowUps(mockInquiry);

      expect(console.log).toHaveBeenCalledWith('[Showing Reminder Follow ups] Error deleting follow ups', error);
    });
  });

  describe('createShowingReminderFollowUps', () => {
    it('should delete existing follow-ups and then use the SHOWING_REMINDER strategy to create new ones', async () => {
      const strategy = followUpCreationStrategies.get(FollowUpCreationStrategyType.SHOWING_REMINDER);
      jest.spyOn(strategy, 'createFollowUps').mockResolvedValue([]);
      jest.spyOn(service, 'deleteShowingReminderFollowUps').mockResolvedValue(undefined);
      jest.spyOn(console, 'log').mockImplementation(() => {});

      const showingTime = new Date();
      await service.createShowingReminderFollowUps(mockConversation, mockInquiry, showingTime);

      expect(service.deleteShowingReminderFollowUps).toHaveBeenCalledWith(mockInquiry);
      expect(strategy.createFollowUps).toHaveBeenCalledWith({
        conversation: mockConversation,
        inquiry: mockInquiry,
        showingTime,
      });
      expect(console.log).toHaveBeenCalledWith('[Showing Reminder Follow ups] Created');
    });
  });

  describe('refreshFollowUpsScheduledTime', () => {
    it('should delete and recreate follow-ups for INITIAL_CONTACT stage', async () => {
      jest.spyOn(service, 'deleteRenterFollowUpsByStatusAndType').mockResolvedValue(undefined);
      jest.spyOn(service, 'createRenterFollowUps').mockResolvedValue(undefined);

      await service.refreshFollowUpsScheduledTime(mockConversation, mockInquiry);

      expect(service.deleteRenterFollowUpsByStatusAndType).toHaveBeenCalledWith(
        mockConversation.id,
        FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      );
      expect(service.createRenterFollowUps).toHaveBeenCalledWith(mockConversation, mockInquiry);
    });

    it('should delete and recreate follow-ups for SHOWING_RESCHEDULE_REQUESTED_BY_OWNER stage', async () => {
      const rescheduleInquiry = {
        ...mockInquiry,
        stage: RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER,
      };
      jest.spyOn(service, 'deleteRenterFollowUpsByStatusAndType').mockResolvedValue(undefined);
      jest.spyOn(service, 'createRenterFollowUps').mockResolvedValue(undefined);

      await service.refreshFollowUpsScheduledTime(mockConversation, rescheduleInquiry);

      expect(service.deleteRenterFollowUpsByStatusAndType).toHaveBeenCalledWith(
        mockConversation.id,
        FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      );
      expect(service.createRenterFollowUps).toHaveBeenCalledWith(mockConversation, rescheduleInquiry);
    });

    it('should not delete or recreate follow-ups for other stages', async () => {
      const otherStageInquiry = {
        ...mockInquiry,
        stage: RentStageEnum.SHOWING_CONFIRMED,
      };
      jest.spyOn(service, 'deleteRenterFollowUpsByStatusAndType').mockResolvedValue(undefined);
      jest.spyOn(service, 'createRenterFollowUps').mockResolvedValue(undefined);

      await service.refreshFollowUpsScheduledTime(mockConversation, otherStageInquiry);

      expect(service.deleteRenterFollowUpsByStatusAndType).not.toHaveBeenCalled();
      expect(service.createRenterFollowUps).not.toHaveBeenCalled();
    });
  });

  describe('deleteRenterFollowUps', () => {
    it('should delete follow-ups for a conversation', async () => {
      jest.spyOn(followUpRepository, 'delete').mockResolvedValue(undefined);
      jest.spyOn(console, 'log').mockImplementation(() => {});

      await service.deleteRenterFollowUpsByStatus(mockConversation.id);

      expect(followUpRepository.delete).toHaveBeenCalledWith({
        conversation: { id: mockConversation.id },
        status: FollowUpStatus.PENDING,
      });
      expect(console.log).toHaveBeenCalledWith('[Follow ups] Deleted');
    });

    it('should handle errors when deleting follow-ups', async () => {
      const error = new Error('Delete error');
      jest.spyOn(followUpRepository, 'delete').mockRejectedValue(error);
      jest.spyOn(console, 'log').mockImplementation(() => {});

      await service.deleteRenterFollowUpsByStatus(mockConversation.id);

      expect(console.log).toHaveBeenCalledWith('[Follow ups] Error deleting follow ups', error);
    });

    it('should allow custom status for deletion', async () => {
      jest.spyOn(followUpRepository, 'delete').mockResolvedValue(undefined);

      await service.deleteRenterFollowUpsByStatus(mockConversation.id, FollowUpStatus.SENT);

      expect(followUpRepository.delete).toHaveBeenCalledWith({
        conversation: { id: mockConversation.id },
        status: FollowUpStatus.SENT,
      });
    });
  });

  describe('deleteInvestorFollowUpsByInquiry', () => {
    it('should delete investor follow-ups for an inquiry', async () => {
      jest.spyOn(followUpRepository, 'delete').mockResolvedValue(undefined);

      await service.deleteInvestorFollowUpsByInquiry(
        mockInquiry,
        mockUser.id,
        FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
      );

      expect(followUpRepository.delete).toHaveBeenCalledWith({
        propertyInquiry: { id: mockInquiry.id },
        recipient: { id: mockUser.id },
        type: FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
      });
    });
  });

  describe('deleteFollowUpsByProperty', () => {
    // For these tests, we'll just verify the method runs without errors
    // since the query builder is complex to mock with TypeScript
    it('should call the repository methods for deleting follow-ups', async () => {
      const propertyId = 'property-id';

      // Create a spy on the method itself
      const createQueryBuilderSpy = jest.spyOn(followUpRepository, 'createQueryBuilder');
      createQueryBuilderSpy.mockImplementation(() => {
        return {
          select: jest.fn().mockReturnThis(),
          leftJoin: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getQuery: jest.fn().mockReturnValue('mock-query'),
          delete: jest.fn().mockReturnThis(),
          from: jest.fn().mockReturnThis(),
          setParameters: jest.fn().mockReturnThis(),
          execute: jest.fn().mockResolvedValue({ affected: 1 }),
        } as any;
      });

      await service.deleteFollowUpsByProperty(propertyId);

      expect(createQueryBuilderSpy).toHaveBeenCalled();
    });

    it('should handle keepInvestorFollowups parameter', async () => {
      const propertyId = 'property-id';

      // Create a spy on the method itself
      const createQueryBuilderSpy = jest.spyOn(followUpRepository, 'createQueryBuilder');
      createQueryBuilderSpy.mockImplementation(() => {
        return {
          select: jest.fn().mockReturnThis(),
          leftJoin: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getQuery: jest.fn().mockReturnValue('mock-query'),
          delete: jest.fn().mockReturnThis(),
          from: jest.fn().mockReturnThis(),
          setParameters: jest.fn().mockReturnThis(),
          execute: jest.fn().mockResolvedValue({ affected: 1 }),
        } as any;
      });

      await service.deleteFollowUpsByProperty(propertyId, true);

      expect(createQueryBuilderSpy).toHaveBeenCalled();
    });
  });

  describe('create', () => {
    it('should create and save a new follow-up', async () => {
      const followUpData = {
        scheduledTime: new Date(),
        recipient: mockUser,
        type: FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      };
      const createdFollowUp = { ...followUpData, id: 'new-follow-up-id' };

      jest.spyOn(followUpRepository, 'create').mockReturnValue(createdFollowUp as FollowUp);
      jest.spyOn(followUpRepository, 'save').mockResolvedValue(createdFollowUp as FollowUp);

      const result = await service.create(followUpData);

      expect(followUpRepository.create).toHaveBeenCalledWith(followUpData);
      expect(followUpRepository.save).toHaveBeenCalledWith(createdFollowUp);
      expect(result).toEqual(createdFollowUp);
    });

    it('should handle empty data', async () => {
      const createdFollowUp = { id: 'new-follow-up-id' };

      jest.spyOn(followUpRepository, 'create').mockReturnValue(createdFollowUp as FollowUp);
      jest.spyOn(followUpRepository, 'save').mockResolvedValue(createdFollowUp as FollowUp);

      const result = await service.create();

      expect(followUpRepository.create).toHaveBeenCalledWith({});
      expect(followUpRepository.save).toHaveBeenCalledWith(createdFollowUp);
      expect(result).toEqual(createdFollowUp);
    });
  });
});

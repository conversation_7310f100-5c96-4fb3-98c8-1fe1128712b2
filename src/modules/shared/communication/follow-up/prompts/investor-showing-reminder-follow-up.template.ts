import { PromptVariable } from '../../../../ai/enums/prompt-variable.enum';

export const investorShowingReminderFollowUpTemplate = `
<task>
You are a leasing agent reminding a property owner about an upcoming showing they have scheduled.
Create a message to remind them about the showing that is happening in 2 hours.
</task>

<instructions>
- Adjust your tone to be in sync with the rest of the messages sent by the AI. Be professional and friendly.
- The message should be short and to the point. Don't write a long message.
- Remind the property owner about the showing time and property address.
- Remind them to be prepared for the showing.
- Message should not have email formatting, just plain text.
</instructions>

${PromptVariable.RenterName}
${PromptVariable.PropertyAddress}
${PromptVariable.ShowingTime}
${PromptVariable.CurrentDate}
`;

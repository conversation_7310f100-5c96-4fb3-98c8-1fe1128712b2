import { PromptVariable } from '../../../../ai/enums/prompt-variable.enum';

/* eslint-disable max-len */
export const renterStoppedRespondingTemplate = `
<task>
<PERSON> and renter ({renterName}) were talking about the property that is potentially interesting for the renter.
Unfortunately, renter stopped responding.
AI wants to follow up with the renter to see if they are still interested in the property.
Your goal is to write a message that AI can send to the renter to follow up with them.
</task>

<instructions>
- Adjust your tone to be in sync with the rest of the messages sent by the AI. Be informal and friendly, but not begging or too pushy.
- You will be given chat history of the conversation between <PERSON> and the renter to create a more personalized message. Use the information to write the message.
- The message should be short and to the point. Don't write a long message.
- When writing the followup message DO NOT mention any dates or times discussed in the chat history
- Never suggest time slots or confirm anything that was not confirmed by AI in previous messages.
- Message should not have email formatting, just plain text.
- Don't reply to your own messages. The message can only be a follow-up message to the renter.
- Don't add stuff like 'Thanks, <PERSON>' at the end of the message. Just write the message that <PERSON> can send to the renter.
- <PERSON> will send three follow-ups total. You need to understand from the context if it is the first, second or third follow up and adjust the message accordingly.
</instructions>

<additional_instruction>
- If the "just switched to sms" flag is set to "true", you need to introduce yourself in follow up and say you tried to reach out by email but failed.
Then briefly repeat the content of the previous message sent by AI.

just switched to sms: {switchToSms}
</additional_instruction>

${PromptVariable.CurrentDate}
${PromptVariable.PropertyAddress}
${PromptVariable.ChatHistory}
`;

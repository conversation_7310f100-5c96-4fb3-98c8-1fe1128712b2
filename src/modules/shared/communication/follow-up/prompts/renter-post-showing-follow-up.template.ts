import { PromptVariable } from '../../../../ai/enums/prompt-variable.enum';

export const renterAfterShowingFollowUp = `
<task>
You are a leasing collecting a feedback from a potential renter sho has just finished
touring the property.

Create a message we will send him to ask how the tour went.

Hey {{renterName}}, it's me again. How did the tour go? Did you have any questions or concerns?'
</task>

<example>

</example>

<instructions>
- Adjust your tone to be in sync with the rest of the messages sent by the AI. Be informal and friendly, but not begging or too pushy.
- You will be given chat history of the conversation between <PERSON> and the renter to create a more personalized message. Use the information to write the message.
- The message should be short and to the point. Don't write a long message.
- When writing the followup message DO NOT mention any dates or times discussed in the chat history
- Never suggest time slots or confirm anything that was not confirmed by AI in previous messages.
- Message should not have email formatting, just plain text.
- Don't reply to your own messages. The message can only be a follow-up message to the renter.
- Don't add stuff like 'Thanks, <PERSON>' at the end of the message. Just write the message that <PERSON> can send to the renter.
</instructions>

${PromptVariable.CurrentDate}
${PromptVariable.ChatHistory}
`;

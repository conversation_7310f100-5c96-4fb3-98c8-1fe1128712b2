import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Controller, Inject, Put } from '@nestjs/common';
import { FollowUpService } from './follow-up.service';
import { BasicAuth } from '../../auth/decorators/basic-auth.decorator';

@ApiTags('follow-up')
@Controller('follow-up')
@ApiBearerAuth()
export class FollowUpController {
  @Inject(FollowUpService)
  private readonly service: FollowUpService;

  // TODO add queue for follow ups
  @Put('send')
  @ApiOkResponse({
    description: 'Sends follow ups for all the conversations that are stuck',
  })
  @BasicAuth()
  async followUpConversations(): Promise<void> {
    await this.service.followUpConversations();
  }
}

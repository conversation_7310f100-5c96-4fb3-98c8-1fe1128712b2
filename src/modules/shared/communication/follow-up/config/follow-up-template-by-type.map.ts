import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';
import { renterStoppedRespondingTemplate } from '../prompts/renter-stopped-responding.template';
import { renterAfterShowingFollowUp } from '../prompts/renter-post-showing-follow-up.template';
import { showingReminderFollowUpTemplate } from '../prompts/showing-reminder-follow-up.template';
import { showingCancellationWarningFollowUpTemplate } from '../prompts/showing-cancellation-warning-follow-up.template';
import { investorShowingReminderFollowUpTemplate } from '../prompts/investor-showing-reminder-follow-up.template';

export const followUpTemplateByTypeMap = new Map<FollowUpTypeEnum, string>([
  [FollowUpTypeEnum.RENTER_STOPPED_RESPONDING, renterStoppedRespondingTemplate],
  [FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP, renterAfterShowingFollowUp],
  [FollowUpTypeEnum.SHOWING_REMINDER_FOLLOW_UP, showingReminderFollowUpTemplate],
  [FollowUpTypeEnum.SHOWING_CANCELLATION_WARNING_FOLLOW_UP, showingCancellationWarningFollowUpTemplate],
  [FollowUpTypeEnum.INVESTOR_SHOWING_REMINDER_FOLLOW_UP, investorShowingReminderFollowUpTemplate],
]);

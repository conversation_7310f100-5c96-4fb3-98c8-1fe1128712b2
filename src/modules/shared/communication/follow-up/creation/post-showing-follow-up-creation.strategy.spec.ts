import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PostShowingFollowUpCreationStrategy } from './post-showing-follow-up-creation.strategy';
import { FollowUp } from '../follow-up.entity';
import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';
import { FollowUpCreationParams } from './follow-up-creation-strategy.interface';

describe('PostShowingFollowUpCreationStrategy', () => {
  let strategy: PostShowingFollowUpCreationStrategy;
  let followUpRepository: Repository<FollowUp>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostShowingFollowUpCreationStrategy,
        {
          provide: getRepositoryToken(FollowUp),
          useValue: {
            create: jest.fn().mockImplementation(() => ({
              scheduledTime: null,
              conversation: null,
              propertyInquiry: null,
              recipient: null,
              type: null,
            })),
            save: jest.fn().mockImplementation((data) => {
              // Create a mock follow-up with the required properties
              const mockFollowUp = {
                scheduledTime: new Date(),
                type: FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
              };
              return Array.isArray(data) ? [mockFollowUp, mockFollowUp] : mockFollowUp;
            }),
          },
        },
      ],
    }).compile();

    strategy = module.get<PostShowingFollowUpCreationStrategy>(PostShowingFollowUpCreationStrategy);
    followUpRepository = module.get<Repository<FollowUp>>(getRepositoryToken(FollowUp));
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('createFollowUps', () => {
    it('should call repository methods with correct parameters', async () => {
      // Mock data
      const mockRenterUser = {
        id: 'renter-user-id',
        name: 'Renter User',
      };

      const mockInvestorUser = {
        id: 'investor-user-id',
        name: 'Investor User',
      };

      const mockRenter = {
        id: 'renter-id',
        user: mockRenterUser,
      };

      const mockInvestor = {
        id: 'investor-id',
        user: Promise.resolve(mockInvestorUser),
      };

      const mockProperty = {
        id: 'property-id',
        owner: Promise.resolve(mockInvestor),
      };

      const mockInquiry = {
        id: 'inquiry-id',
        renter: mockRenter,
        property: Promise.resolve(mockProperty),
        stage: 'INITIAL_CONTACT',
        conversation: Promise.resolve({}),
        showingRequest: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
        isArchived: false,
        isActive: true,
      } as any;

      const mockConversation = {
        id: 'conversation-id',
        messages: [],
        isAnswered: true,
        users: [],
        emailMetadata: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
      } as any;

      const params: FollowUpCreationParams = {
        conversation: mockConversation,
        inquiry: mockInquiry,
      };

      // Reset mocks
      jest.clearAllMocks();

      // Execute the strategy
      const result = await strategy.createFollowUps(params);

      // Verify repository methods were called correctly
      expect(followUpRepository.create).toHaveBeenCalled();
      expect(followUpRepository.save).toHaveBeenCalled();

      // Verify the correct number of follow-ups were created
      expect(result.length).toBe(2);
    });

    it('should throw an error if conversation or inquiry is missing', async () => {
      // Execute the strategy with missing parameters
      await expect(strategy.createFollowUps({})).rejects.toThrow(
        'Conversation and inquiry are required for post-showing follow-ups',
      );
    });
  });
});

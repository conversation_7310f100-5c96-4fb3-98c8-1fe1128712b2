import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FollowUp } from '../follow-up.entity';
import { FollowUpCreationParams, FollowUpCreationStrategy } from './follow-up-creation-strategy.interface';
import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';
import { addMinutes } from 'date-fns';

@Injectable()
export class ShowingReminderFollowUpCreationStrategy implements FollowUpCreationStrategy {
  constructor(
    @InjectRepository(FollowUp)
    private readonly followUpRepository: Repository<FollowUp>,
  ) {}

  async createFollowUps(params: FollowUpCreationParams): Promise<FollowUp[]> {
    const { conversation, inquiry, showingTime } = params;

    if (!conversation || !inquiry || !showingTime) {
      throw new Error('Conversation, inquiry, and showingTime are required for showing reminder follow-ups');
    }

    const followUps: FollowUp[] = [];
    const property = await inquiry.property;
    const investor = await property.owner;

    // Create a follow-up for 2 hours before the showing for the renter
    const reminderTime = new Date(showingTime);
    reminderTime.setHours(reminderTime.getHours() - 2);

    const reminderFollowUp = this.followUpRepository.create({
      scheduledTime: reminderTime,
      conversation: conversation,
      propertyInquiry: inquiry,
      recipient: (await inquiry.renter).user,
      type: FollowUpTypeEnum.SHOWING_REMINDER_FOLLOW_UP,
    });
    followUps.push(reminderFollowUp);

    // Create a follow-up for 30 minutes after the reminder
    const cancellationWarningTime = addMinutes(reminderTime, 30);

    const cancellationWarningFollowUp = this.followUpRepository.create({
      scheduledTime: cancellationWarningTime,
      conversation: conversation,
      propertyInquiry: inquiry,
      recipient: (await inquiry.renter).user,
      type: FollowUpTypeEnum.SHOWING_CANCELLATION_WARNING_FOLLOW_UP,
    });
    followUps.push(cancellationWarningFollowUp);

    // Create a follow-up for 2 hours before the showing for the investor
    const investorReminderFollowUp = this.followUpRepository.create({
      scheduledTime: reminderTime,
      conversation: conversation,
      propertyInquiry: inquiry,
      recipient: await investor.user,
      type: FollowUpTypeEnum.INVESTOR_SHOWING_REMINDER_FOLLOW_UP,
    });
    followUps.push(investorReminderFollowUp);

    return await this.followUpRepository.save(followUps);
  }
}

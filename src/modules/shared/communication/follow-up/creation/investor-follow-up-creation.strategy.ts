import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FollowUp } from '../follow-up.entity';
import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';
import { FollowUpCreationStrategy, FollowUpCreationParams } from './follow-up-creation-strategy.interface';

@Injectable()
export class InvestorFollowUpCreationStrategy implements FollowUpCreationStrategy {
  constructor(
    @InjectRepository(FollowUp)
    private readonly followUpRepository: Repository<FollowUp>,
  ) {}

  async createFollowUps(params: FollowUpCreationParams): Promise<FollowUp[]> {
    const { recipient, inquiry, schedule } = params;

    if (!recipient || !schedule) {
      throw new Error('Recipient and schedule are required for investor follow-ups');
    }

    const followUps: FollowUp[] = [];
    const type = FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP;

    schedule.followUpsSchedule.forEach((date) => {
      const followUp = this.followUpRepository.create();

      if (inquiry) {
        followUp.propertyInquiry = inquiry;
      }

      followUp.scheduledTime = date;
      followUp.recipient = recipient;
      followUp.type = type;
      followUps.push(followUp);
    });

    return await this.followUpRepository.save(followUps);
  }
}

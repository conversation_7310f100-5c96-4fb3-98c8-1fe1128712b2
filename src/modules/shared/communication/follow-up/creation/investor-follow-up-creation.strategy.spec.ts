import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InvestorFollowUpCreationStrategy } from './investor-follow-up-creation.strategy';
import { FollowUp } from '../follow-up.entity';
import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';
import { FollowUpCreationParams } from './follow-up-creation-strategy.interface';
import { FollowUpsSchedule } from '../models/follow-ups-schedule';

describe('InvestorFollowUpCreationStrategy', () => {
  let strategy: InvestorFollowUpCreationStrategy;
  let followUpRepository: Repository<FollowUp>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InvestorFollowUpCreationStrategy,
        {
          provide: getRepositoryToken(FollowUp),
          useValue: {
            create: jest.fn().mockImplementation(() => ({
              scheduledTime: null,
              conversation: null,
              propertyInquiry: null,
              recipient: null,
              type: null,
            })),
            save: jest.fn().mockImplementation((data) => {
              // Create a mock follow-up with the required properties
              const mockFollowUp = {
                scheduledTime: new Date(),
                type: FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
              };
              return Array.isArray(data) ? [mockFollowUp] : mockFollowUp;
            }),
          },
        },
      ],
    }).compile();

    strategy = module.get<InvestorFollowUpCreationStrategy>(InvestorFollowUpCreationStrategy);
    followUpRepository = module.get<Repository<FollowUp>>(getRepositoryToken(FollowUp));
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('createFollowUps', () => {
    it('should call repository methods with correct parameters', async () => {
      // Mock data
      const mockInvestorUser = {
        id: 'investor-user-id',
        name: 'Investor User',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
        preferredCommunicationChannel: 'EMAIL',
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
      } as any;

      const mockInquiry = {
        id: 'inquiry-id',
        stage: 'INITIAL_CONTACT',
        renter: { id: 'renter-id' },
        property: Promise.resolve({ id: 'property-id' }),
        conversation: Promise.resolve({}),
        showingRequest: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
        isArchived: false,
        isActive: true,
      } as any;

      const mockSchedule: FollowUpsSchedule = {
        followUpsSchedule: [
          new Date('2023-01-01T10:00:00Z'),
          new Date('2023-01-02T10:00:00Z'),
          new Date('2023-01-03T10:00:00Z'),
        ],
      };

      const params: FollowUpCreationParams = {
        recipient: mockInvestorUser,
        inquiry: mockInquiry,
        schedule: mockSchedule,
      };

      // Reset mocks
      jest.clearAllMocks();

      // Execute the strategy
      const result = await strategy.createFollowUps(params);

      // Verify repository methods were called correctly
      expect(followUpRepository.create).toHaveBeenCalled();
      expect(followUpRepository.save).toHaveBeenCalled();

      // Verify the correct number of follow-ups were created
      expect(result.length).toBeGreaterThan(0);
    });

    it('should work without an inquiry', async () => {
      // Mock data
      const mockInvestorUser = {
        id: 'investor-user-id',
        name: 'Investor User',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
        preferredCommunicationChannel: 'EMAIL',
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
      } as any;

      const mockSchedule: FollowUpsSchedule = {
        followUpsSchedule: [new Date('2023-01-01T10:00:00Z')],
      };

      const params: FollowUpCreationParams = {
        recipient: mockInvestorUser,
        schedule: mockSchedule,
      };

      // Reset mocks
      jest.clearAllMocks();

      // Execute the strategy
      const result = await strategy.createFollowUps(params);

      // Verify repository methods were called correctly
      expect(followUpRepository.create).toHaveBeenCalled();
      expect(followUpRepository.save).toHaveBeenCalled();

      // Verify the correct number of follow-ups were created
      expect(result.length).toBeGreaterThan(0);
    });

    it('should throw an error if recipient or schedule is missing', async () => {
      // Execute the strategy with missing recipient
      await expect(
        strategy.createFollowUps({
          schedule: { followUpsSchedule: [new Date()] },
        }),
      ).rejects.toThrow('Recipient and schedule are required for investor follow-ups');

      // Execute the strategy with missing schedule
      await expect(
        strategy.createFollowUps({
          recipient: { id: 'user-id' } as any,
        }),
      ).rejects.toThrow('Recipient and schedule are required for investor follow-ups');
    });
  });
});

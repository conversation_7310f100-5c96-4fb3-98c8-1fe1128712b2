import { PropertyInquiry } from '../../../../investor/property-inquiry/property-inquiry.entity';
import { User } from '../../../user/entities/user.entity';
import { FollowUp } from '../follow-up.entity';
import { FollowUpsSchedule } from '../models/follow-ups-schedule';
import { Conversation } from '../../conversation/entities/conversation.entity';
import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';

export interface FollowUpCreationStrategy {
  createFollowUps(params: FollowUpCreationParams): Promise<FollowUp[]>;
}

export interface FollowUpCreationParams {
  conversation?: Conversation;
  inquiry?: PropertyInquiry;
  recipient?: User;
  schedule?: FollowUpsSchedule;
  numberOfFollowups?: number;
  followUpType?: FollowUpTypeEnum;
  showingTime?: Date;
}

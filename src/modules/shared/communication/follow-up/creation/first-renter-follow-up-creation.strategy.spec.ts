import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FirstRenterFollowUpCreationStrategy } from './first-renter-follow-up-creation.strategy';
import { FollowUp } from '../follow-up.entity';
import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';
import { FollowUpCreationParams } from './follow-up-creation-strategy.interface';
import { TimezoneUtils } from '../../../../../utils/timezone.utils';

describe('FirstRenterFollowUpCreationStrategy', () => {
  let strategy: FirstRenterFollowUpCreationStrategy;
  let followUpRepository: Repository<FollowUp>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FirstRenterFollowUpCreationStrategy,
        {
          provide: getRepositoryToken(FollowUp),
          useValue: {
            create: jest.fn().mockImplementation(() => ({
              scheduledTime: null,
              conversation: null,
              propertyInquiry: null,
              recipient: null,
              type: null,
            })),
            save: jest.fn().mockImplementation((data) => {
              // Create a mock follow-up with the required properties
              const mockFollowUp = {
                scheduledTime: new Date(),
                type: FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
              };
              return Array.isArray(data) ? [mockFollowUp] : mockFollowUp;
            }),
          },
        },
      ],
    }).compile();

    strategy = module.get<FirstRenterFollowUpCreationStrategy>(FirstRenterFollowUpCreationStrategy);
    followUpRepository = module.get<Repository<FollowUp>>(getRepositoryToken(FollowUp));
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('createFollowUps', () => {
    it('should create follow-ups with the correct timing pattern', async () => {
      // Mock data
      const mockRenterUser = {
        id: 'renter-user-id',
        name: 'Renter User',
      };

      const mockRenter = {
        id: 'renter-id',
        user: mockRenterUser,
      };

      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
      };

      const mockInquiry = {
        id: 'inquiry-id',
        renter: mockRenter,
        property: Promise.resolve(mockProperty),
        stage: 'INITIAL_CONTACT',
        conversation: Promise.resolve({}),
        showingRequest: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
        isArchived: false,
        isActive: true,
      } as any;

      const mockConversation = {
        id: 'conversation-id',
        property: Promise.resolve(mockProperty),
        messages: [],
        isAnswered: true,
        users: [],
        emailMetadata: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
      } as any;

      const params: FollowUpCreationParams = {
        conversation: mockConversation,
        inquiry: mockInquiry,
      };

      // Mock the current date to have a fixed reference point
      const now = new Date('2023-01-01T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => now as any);

      // Mock TimezoneUtils.getCurrentCityDate to return our fixed date
      jest.spyOn(TimezoneUtils, 'getCurrentCityDate').mockReturnValue(now);

      // Spy on repository methods
      const createSpy = jest.spyOn(followUpRepository, 'create');
      const saveSpy = jest.spyOn(followUpRepository, 'save');

      // Reset mocks
      jest.clearAllMocks();

      // Execute the strategy
      await strategy.createFollowUps(params);

      // Verify repository methods were called correctly
      expect(createSpy).toHaveBeenCalledTimes(3); // One for first follow-up, two for additional follow-ups
      expect(saveSpy).toHaveBeenCalledTimes(3); // First one + 2 additional

      // Verify the timing pattern for the first follow-up (1 hour after creation)
      const firstFollowUpCall = createSpy.mock.calls[0][0];
      const firstFollowUpTime = new Date(now);
      firstFollowUpTime.setHours(firstFollowUpTime.getHours() + 1);
      expect(firstFollowUpCall.scheduledTime).toEqual(firstFollowUpTime);

      // Verify the timing pattern for the second follow-up (1 day after creation at 10 AM)
      const secondFollowUpCall = createSpy.mock.calls[1][0];
      const secondFollowUpTime = new Date(now);
      secondFollowUpTime.setDate(secondFollowUpTime.getDate() + 1);
      secondFollowUpTime.setHours(10, 0, 0, 0);
      expect(secondFollowUpCall.scheduledTime).toEqual(secondFollowUpTime);

      // Verify the timing pattern for the third follow-up (2 days after creation at 10 AM)
      const thirdFollowUpCall = createSpy.mock.calls[2][0];
      const thirdFollowUpTime = new Date(now);
      thirdFollowUpTime.setDate(thirdFollowUpTime.getDate() + 2);
      thirdFollowUpTime.setHours(10, 0, 0, 0);
      expect(thirdFollowUpCall.scheduledTime).toEqual(thirdFollowUpTime);

      // Restore the mocks
      jest.spyOn(global, 'Date').mockRestore();
      jest.spyOn(TimezoneUtils, 'getCurrentCityDate').mockRestore();
    });

    it('should use custom follow-up type when provided', async () => {
      // Mock data
      const mockRenterUser = {
        id: 'renter-user-id',
        name: 'Renter User',
      };

      const mockRenter = {
        id: 'renter-id',
        user: mockRenterUser,
      };

      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
      };

      const mockInquiry = {
        id: 'inquiry-id',
        renter: mockRenter,
        property: Promise.resolve(mockProperty),
        stage: 'INITIAL_CONTACT',
        conversation: Promise.resolve({}),
        showingRequest: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
        isArchived: false,
        isActive: true,
      } as any;

      const mockConversation = {
        id: 'conversation-id',
        property: Promise.resolve(mockProperty),
        messages: [],
        isAnswered: true,
        users: [],
        emailMetadata: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
      } as any;

      const params: FollowUpCreationParams = {
        conversation: mockConversation,
        inquiry: mockInquiry,
        followUpType: FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
      };

      // Reset mocks
      jest.clearAllMocks();

      // Mock TimezoneUtils.getCurrentCityDate
      jest.spyOn(TimezoneUtils, 'getCurrentCityDate').mockReturnValue(new Date());

      // Execute the strategy
      await strategy.createFollowUps(params);

      // Verify repository methods were called
      expect(followUpRepository.create).toHaveBeenCalled();
      expect(followUpRepository.save).toHaveBeenCalled();

      // Restore mocks
      jest.spyOn(TimezoneUtils, 'getCurrentCityDate').mockRestore();
    });

    it('should throw an error if conversation or inquiry is missing', async () => {
      // Execute the strategy with missing parameters
      await expect(strategy.createFollowUps({})).rejects.toThrow(
        'Conversation and inquiry are required for first renter follow-ups',
      );
    });
  });
});

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TimezoneUtils } from '../../../../../utils/timezone.utils';
import { FollowUp } from '../follow-up.entity';
import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';
import { FollowUpCreationStrategy, FollowUpCreationParams } from './follow-up-creation-strategy.interface';

@Injectable()
export class RenterFollowUpCreationStrategy implements FollowUpCreationStrategy {
  constructor(
    @InjectRepository(FollowUp)
    private readonly followUpRepository: Repository<FollowUp>,
  ) {}

  async createFollowUps(params: FollowUpCreationParams): Promise<FollowUp[]> {
    const {
      conversation,
      inquiry,
      numberOfFollowups = 3,
      followUpType = FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
    } = params;

    if (!conversation || !inquiry) {
      throw new Error('Conversation and inquiry are required for renter follow-ups');
    }

    const followUps: FollowUp[] = [];
    const property = await conversation.property;
    const location = await property.location;
    const localDate = TimezoneUtils.getCurrentCityDate(location.city, location.timeZone);

    for (let i = 1; i <= numberOfFollowups; i++) {
      const followUpTime = TimezoneUtils.calculateNextBusinessHour(localDate, i, location.timeZone);
      const followUp = this.followUpRepository.create();
      followUp.scheduledTime = followUpTime;
      followUp.conversation = conversation;
      followUp.propertyInquiry = inquiry;
      followUp.recipient = (await inquiry.renter).user;
      followUp.type = followUpType;
      followUps.push(followUp);
    }

    return await this.followUpRepository.save(followUps);
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FollowUp } from '../follow-up.entity';
import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';
import { FollowUpCreationStrategy, FollowUpCreationParams } from './follow-up-creation-strategy.interface';

@Injectable()
export class PostShowingFollowUpCreationStrategy implements FollowUpCreationStrategy {
  constructor(
    @InjectRepository(FollowUp)
    private readonly followUpRepository: Repository<FollowUp>,
  ) {}

  async createFollowUps(params: FollowUpCreationParams): Promise<FollowUp[]> {
    const { conversation, inquiry } = params;

    if (!conversation || !inquiry) {
      throw new Error('Conversation and inquiry are required for post-showing follow-ups');
    }

    const followUps: FollowUp[] = [];
    const property = await inquiry.property;

    // Create one follow up for renter and send it after 30 minutes
    const renterFollowUpTime = new Date();
    renterFollowUpTime.setMinutes(renterFollowUpTime.getMinutes() + 30);

    const renterFollowUp = this.followUpRepository.create({
      scheduledTime: renterFollowUpTime,
      conversation: conversation,
      propertyInquiry: inquiry,
      recipient: (await inquiry.renter).user,
      type: FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
    });
    followUps.push(renterFollowUp);

    // Create one follow up for investor and send it after 1 hour
    const investorFollowUpTime = new Date();
    investorFollowUpTime.setHours(investorFollowUpTime.getHours() + 1);
    const investor = await property.owner;
    const investorFollowUp = this.followUpRepository.create({
      scheduledTime: investorFollowUpTime,
      conversation: conversation,
      propertyInquiry: inquiry,
      recipient: await investor.user,
      type: FollowUpTypeEnum.POST_SHOWING_INVESTOR_FOLLOW_UP,
    });
    followUps.push(investorFollowUp);

    return await this.followUpRepository.save(followUps);
  }
}

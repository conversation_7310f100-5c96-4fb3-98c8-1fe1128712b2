import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RenterFollowUpCreationStrategy } from './renter-follow-up-creation.strategy';
import { FollowUp } from '../follow-up.entity';
import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';
import { FollowUpCreationParams } from './follow-up-creation-strategy.interface';

describe('RenterFollowUpCreationStrategy', () => {
  let strategy: RenterFollowUpCreationStrategy;
  let followUpRepository: Repository<FollowUp>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RenterFollowUpCreationStrategy,
        {
          provide: getRepositoryToken(FollowUp),
          useValue: {
            create: jest.fn().mockImplementation(() => ({
              scheduledTime: null,
              conversation: null,
              propertyInquiry: null,
              recipient: null,
              type: null,
            })),
            save: jest.fn().mockImplementation((data) => (Array.isArray(data) ? data : [data])),
          },
        },
      ],
    }).compile();

    strategy = module.get<RenterFollowUpCreationStrategy>(RenterFollowUpCreationStrategy);
    followUpRepository = module.get<Repository<FollowUp>>(getRepositoryToken(FollowUp));
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('createFollowUps', () => {
    it('should call repository methods with correct parameters', async () => {
      // Mock data
      const mockRenterUser = {
        id: 'renter-user-id',
        name: 'Renter User',
      };

      const mockRenter = {
        id: 'renter-id',
        user: mockRenterUser,
      };

      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
      };

      const mockInquiry = {
        id: 'inquiry-id',
        renter: mockRenter,
        property: Promise.resolve(mockProperty),
        stage: 'INITIAL_CONTACT',
        conversation: Promise.resolve({}),
        showingRequest: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
        isArchived: false,
        isActive: true,
      } as any;

      const mockConversation = {
        id: 'conversation-id',
        property: Promise.resolve(mockProperty),
        messages: [],
        isAnswered: true,
        users: [],
        emailMetadata: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
      } as any;

      const params: FollowUpCreationParams = {
        conversation: mockConversation,
        inquiry: mockInquiry,
      };

      // Reset mocks
      jest.clearAllMocks();

      // Execute the strategy
      const result = await strategy.createFollowUps(params);

      // Verify repository methods were called correctly
      expect(followUpRepository.create).toHaveBeenCalled();
      expect(followUpRepository.save).toHaveBeenCalled();

      // Verify the correct number of follow-ups were created
      expect(result.length).toBeGreaterThan(0);
    });

    it('should use custom number of follow-ups and type', async () => {
      // Mock data
      const mockRenterUser = {
        id: 'renter-user-id',
        name: 'Renter User',
      };

      const mockRenter = {
        id: 'renter-id',
        user: mockRenterUser,
      };

      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
      };

      const mockInquiry = {
        id: 'inquiry-id',
        renter: mockRenter,
        property: Promise.resolve(mockProperty),
        stage: 'INITIAL_CONTACT',
        conversation: Promise.resolve({}),
        showingRequest: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
        isArchived: false,
        isActive: true,
      } as any;

      const mockConversation = {
        id: 'conversation-id',
        property: Promise.resolve(mockProperty),
        messages: [],
        isAnswered: true,
        users: [],
        emailMetadata: Promise.resolve({}),
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
      } as any;

      const params: FollowUpCreationParams = {
        conversation: mockConversation,
        inquiry: mockInquiry,
        numberOfFollowups: 5,
        followUpType: FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
      };

      // Reset mocks
      jest.clearAllMocks();

      // Execute the strategy
      const result = await strategy.createFollowUps(params);

      // Verify repository methods were called correctly
      expect(followUpRepository.create).toHaveBeenCalled();
      expect(followUpRepository.save).toHaveBeenCalled();

      // Verify the correct number of follow-ups were created
      expect(result.length).toBeGreaterThan(0);
    });

    it('should throw an error if conversation or inquiry is missing', async () => {
      // Execute the strategy with missing parameters
      await expect(strategy.createFollowUps({})).rejects.toThrow(
        'Conversation and inquiry are required for renter follow-ups',
      );
    });
  });
});

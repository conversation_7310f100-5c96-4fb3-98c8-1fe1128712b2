import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FollowUp } from '../follow-up.entity';
import { FollowUpTypeEnum } from '../enums/follow-up-type.enum';
import { FollowUpCreationStrategy, FollowUpCreationParams } from './follow-up-creation-strategy.interface';
import { TimezoneUtils } from '../../../../../utils/timezone.utils';

@Injectable()
export class FirstRenterFollowUpCreationStrategy implements FollowUpCreationStrategy {
  constructor(
    @InjectRepository(FollowUp)
    private readonly followUpRepository: Repository<FollowUp>,
  ) {}

  async createFollowUps(params: FollowUpCreationParams): Promise<FollowUp[]> {
    const { conversation, inquiry, followUpType = FollowUpTypeEnum.RENTER_STOPPED_RESPONDING } = params;

    if (!conversation || !inquiry) {
      throw new Error('Conversation and inquiry are required for first renter follow-ups');
    }

    const followUps: FollowUp[] = [];

    // Get property location and timezone
    const property = await conversation.property;
    const location = await property.location;

    // Get the local date in the property's timezone
    const localDate = TimezoneUtils.getCurrentCityDate(location.city, location.timeZone);

    // Schedule the first follow-up 1 hour after the current local time without business hour adjustment
    const firstFollowUpTime = new Date();
    firstFollowUpTime.setHours(firstFollowUpTime.getHours() + 1);

    const firstFollowUp = this.followUpRepository.create({
      scheduledTime: firstFollowUpTime,
      conversation: conversation,
      propertyInquiry: inquiry,
      recipient: (await inquiry.renter).user,
      type: followUpType,
    });

    followUps.push(firstFollowUp);
    await this.followUpRepository.save(followUps);

    // Create remaining follow-ups
    const remainingFollowUpsCount = 2;

    // Create additional follow-ups using the business hours utility
    for (let i = 1; i <= remainingFollowUpsCount; i++) {
      const followUpTime = TimezoneUtils.calculateNextBusinessHour(localDate, i, location.timeZone);

      const additionalFollowUp = this.followUpRepository.create({
        scheduledTime: followUpTime,
        conversation: conversation,
        propertyInquiry: inquiry,
        recipient: (await inquiry.renter).user,
        type: followUpType,
      });

      await this.followUpRepository.save(additionalFollowUp);
    }

    console.log('[Renter Follow ups] Created');

    return followUps;
  }
}

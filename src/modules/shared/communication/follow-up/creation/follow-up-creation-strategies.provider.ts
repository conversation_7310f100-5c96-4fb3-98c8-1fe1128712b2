import { Provider } from '@nestjs/common';
import { FollowUpCreationStrategy } from './follow-up-creation-strategy.interface';
import { RenterFollowUpCreationStrategy } from './renter-follow-up-creation.strategy';
import { FirstRenterFollowUpCreationStrategy } from './first-renter-follow-up-creation.strategy';
import { PostShowingFollowUpCreationStrategy } from './post-showing-follow-up-creation.strategy';
import { InvestorFollowUpCreationStrategy } from './investor-follow-up-creation.strategy';
import { ShowingReminderFollowUpCreationStrategy } from './showing-reminder-follow-up-creation.strategy';

export enum FollowUpCreationStrategyType {
  RENTER = 'renter',
  FIRST_RENTER = 'first_renter',
  POST_SHOWING = 'post_showing',
  INVESTOR = 'investor',
  SHOWING_REMINDER = 'showing_reminder',
}

export const FOLLOW_UP_CREATION_STRATEGIES = 'FOLLOW_UP_CREATION_STRATEGIES';

export const followUpCreationStrategiesProvider: Provider = {
  provide: FOLLOW_UP_CREATION_STRATEGIES,
  useFactory: (
    renterFollowUpCreationStrategy: RenterFollowUpCreationStrategy,
    firstRenterFollowUpCreationStrategy: FirstRenterFollowUpCreationStrategy,
    postShowingFollowUpCreationStrategy: PostShowingFollowUpCreationStrategy,
    investorFollowUpCreationStrategy: InvestorFollowUpCreationStrategy,
    showingReminderFollowUpCreationStrategy: ShowingReminderFollowUpCreationStrategy,
  ) => {
    const strategies = new Map<FollowUpCreationStrategyType, FollowUpCreationStrategy>();

    strategies.set(FollowUpCreationStrategyType.RENTER, renterFollowUpCreationStrategy);
    strategies.set(FollowUpCreationStrategyType.FIRST_RENTER, firstRenterFollowUpCreationStrategy);
    strategies.set(FollowUpCreationStrategyType.POST_SHOWING, postShowingFollowUpCreationStrategy);
    strategies.set(FollowUpCreationStrategyType.INVESTOR, investorFollowUpCreationStrategy);
    strategies.set(FollowUpCreationStrategyType.SHOWING_REMINDER, showingReminderFollowUpCreationStrategy);

    return strategies;
  },
  inject: [
    RenterFollowUpCreationStrategy,
    FirstRenterFollowUpCreationStrategy,
    PostShowingFollowUpCreationStrategy,
    InvestorFollowUpCreationStrategy,
    ShowingReminderFollowUpCreationStrategy,
  ],
};

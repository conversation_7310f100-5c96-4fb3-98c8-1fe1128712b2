import { Module } from '@nestjs/common';
import { ConversationModule } from './conversation/conversetion.module';
import { InboundCommunicationModule } from './inbound-communication/inbound-communication.module';
import { OutboundCommunicationModule } from './outbound-communication/outbound-communication.module';

@Module({
  imports: [ConversationModule, InboundCommunicationModule, OutboundCommunicationModule],
})
export class CommunicationModule {}

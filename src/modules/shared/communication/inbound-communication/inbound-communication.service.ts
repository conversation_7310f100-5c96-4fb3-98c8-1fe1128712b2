import { Injectable } from '@nestjs/common';
import { GmailService } from './gmail/gmail.service';
import { TwilioInboundSmsDto } from './models/twilio-inbound-sms.dto';
import { ConversationService } from '../conversation/conversation.service';
import { PropertyInquiryService } from '../../../investor/property-inquiry/property-inquiry.service';
import { CommunicationChannel } from '../conversation/enums/preferred-communication-channel.enum';
import { UserService } from '../../user/user.service';
import { SlackCommunicationService } from '../outbound-communication/slack/slack-communication.service';

@Injectable()
export class InboundCommunicationService {
  constructor(
    private readonly gmailService: GmailService,
    private readonly conversationService: ConversationService,
    private readonly inquiryService: PropertyInquiryService,
    private readonly userService: UserService,
    private readonly slackCommunicationService: SlackCommunicationService,
  ) {}

  async checkNewEmails(): Promise<void> {
    await this.gmailService.checkNewEmails();
  }

  async saveNewTwilioSms(body: TwilioInboundSmsDto): Promise<void> {
    const inquiry = await this.inquiryService.findLatestByRenterPhoneNumber(body.From);
    const conversation = await inquiry.conversation;
    const renter = await inquiry.renter;

    if (renter.user.preferredCommunicationChannel !== CommunicationChannel.SMS) {
      await this.userService.updateUser(renter.user.id, {
        preferredCommunicationChannel: CommunicationChannel.SMS,
      });

      this.slackCommunicationService.sendMessageToConvosChannel('📱 Switched to SMS', conversation);
    }

    await this.conversationService.saveRenterMessages(
      conversation,
      renter,
      [body.Body],
      false,
      CommunicationChannel.SMS,
    );
  }
}

import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Request } from 'express';
import * as twilio from 'twilio';

@Injectable()
export class TwilioWebhookGuard implements CanActivate {
  private readonly twilioAuthToken = process.env.TWILIO_AUTH_TOKEN;

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest<Request>();
    const twilioSignature = req.headers['x-twilio-signature'] as string;

    if (!twilioSignature) {
      throw new HttpException('Missing X-Twilio-Signature header', HttpStatus.UNAUTHORIZED);
    }

    const fullUrl = `https://${req.get('host')}${req.originalUrl}`;

    const isValid = twilio.validateRequest(this.twilioAuthToken, twilioSignature, fullUrl, req.body);

    if (!isValid) {
      throw new HttpException('Invalid Twilio Signature', HttpStatus.FORBIDDEN);
    }

    return true;
  }
}

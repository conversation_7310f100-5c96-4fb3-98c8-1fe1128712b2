import { Auth, gmail_v1, google } from 'googleapis';
import { Repository } from 'typeorm';

import { HttpException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { GaxiosResponse } from 'gaxios';
import { InboundEmailsSubjects } from '../enums/inbound-emails-subjects';
import { InboxHistoryVersion } from './inbox-history-version.entity';
import { ParsedGmailEmail } from './interfaces/parsed-gmail-email.interface';
import { PropertyInquiryService } from '../../../../investor/property-inquiry/property-inquiry.service';
import { PropertyService } from '../../../../investor/property/property/property.service';
import { RenterService } from '../../../../renter/renter/renter.service';
import ParsingUtils from '../../../../../utils/parsing.utils';
import { EmailParserService } from '../email-parser/email-parser.service';
import { ConversationService } from '../../conversation/conversation.service';
import { CommunicationChannel } from '../../conversation/enums/preferred-communication-channel.enum';
import { SlackCommunicationService } from '../../outbound-communication/slack/slack-communication.service';
import { UserService } from '../../../user/user.service';

@Injectable()
export class GmailService {
  private readonly jwtClient: Auth.JWT;
  private readonly gmail: gmail_v1.Gmail;
  private readonly trackedEmail: string;

  constructor(
    @Inject(ConfigService)
    private readonly config: ConfigService,
    @InjectRepository(InboxHistoryVersion)
    private readonly inboxHistoryRepository: Repository<InboxHistoryVersion>,
    private readonly inquiryService: PropertyInquiryService,
    private readonly propertyService: PropertyService,
    private readonly renterService: RenterService,
    private readonly emailParserService: EmailParserService,
    private readonly conversationService: ConversationService,
    private readonly slackCommunicationService: SlackCommunicationService,
    private readonly userService: UserService,
  ) {
    this.trackedEmail = this.config.get('TALLO_TRACKED_EMAIL');

    this.jwtClient = new google.auth.JWT({
      email: this.config.get('GMAIL_SERVICE_ACCOUNT_EMAIL'),
      key: this.config.get('GMAIL_SERVICE_ACCOUNT_PRIVATE_KEY'),
      scopes: ['https://www.googleapis.com/auth/gmail.readonly'],
      subject: this.trackedEmail,
    });

    try {
      this.jwtClient.authorize();
    } catch (e) {
      throw new HttpException(`Failed to authorize Gmail service: ${e.message}`, 500);
    }

    this.gmail = google.gmail({ version: 'v1', auth: this.jwtClient });
  }

  async checkNewEmails(): Promise<void> {
    try {
      const profile = await this.gmail.users.getProfile({
        userId: this.trackedEmail,
      });
      let storedInboxHistoryVersion = await this.inboxHistoryRepository.findOneBy({
        email: this.trackedEmail,
      });

      if (!storedInboxHistoryVersion) {
        storedInboxHistoryVersion = this.inboxHistoryRepository.create();

        storedInboxHistoryVersion.historyId = profile.data.historyId;
        storedInboxHistoryVersion.email = this.trackedEmail;
        await this.inboxHistoryRepository.save(storedInboxHistoryVersion);
      }

      if (storedInboxHistoryVersion.historyId !== profile.data.historyId) {
        await this.readNewEmails(storedInboxHistoryVersion);
      }
    } catch (e) {
      throw new HttpException(`Failed to check new emails: ${e.message}`, 500);
    }
  }

  private async readNewEmails(inboxHistory: InboxHistoryVersion): Promise<void> {
    const historyList = await this.gmail.users.history.list({
      userId: this.trackedEmail,
      startHistoryId: inboxHistory.historyId,
    });

    if (historyList.data.historyId) {
      await this.saveHistory(inboxHistory, historyList.data.historyId);
    }

    for (const history of historyList.data.history || []) {
      if (history.messagesAdded) {
        for (const added of history.messagesAdded) {
          const email = await this.gmail.users.messages.get({
            userId: this.trackedEmail,
            id: added.message.id,
            format: 'full',
          });

          const parsedEmail = await this.parseGmailEmail(email);

          if (parsedEmail.subject.includes(InboundEmailsSubjects.NEW_PROPERTY_MESSAGE)) {
            await this.saveRenterEmail(parsedEmail);
          } else if (parsedEmail.subject.includes(InboundEmailsSubjects.NEW_SMS_MESSAGE)) {
            await this.saveRenterSms(parsedEmail);
          }
        }
      }
    }
  }

  private async saveRenterEmail(email: ParsedGmailEmail): Promise<void> {
    try {
      const renter = await this.renterService.findByEmail(email.emailAddress);
      if (!renter) {
        throw new Error('Renter not found');
      }

      const { street, apartmentNumber } = ParsingUtils.parseAddress(email.subject);
      const property = await this.propertyService.findByAddress(street, apartmentNumber);

      if (!property) {
        throw new Error(`Property ${street} not found`);
      }
      const inquiry = await this.inquiryService.findLatestByRenterAndProperty(renter.id, property.id);
      const conversation = await inquiry.conversation;

      const messageText = await this.emailParserService.extractRenterMessageFromRawEmail(email.emailText);

      if (renter.user.preferredCommunicationChannel !== CommunicationChannel.EMAIL) {
        await this.userService.updateUser(renter.user.id, {
          preferredCommunicationChannel: CommunicationChannel.EMAIL,
        });

        await this.slackCommunicationService.sendMessageToConvosChannel('📧 Switched to EMAIL', conversation);
      }

      await this.conversationService.saveRenterMessages(
        conversation,
        renter,
        [messageText],
        false,
        CommunicationChannel.EMAIL,
      );

      // Update email metadata
      await this.conversationService.updateEmailMetadata(conversation, {
        subject: email.subject,
        lastMessageId: email.messageId,
        references: email.references,
        previousText: messageText,
      });
    } catch (e) {
      console.error('Failed to save renter email:', e);
      throw new HttpException(`Failed to save renter email: ${e.message}`, 500);
    }
  }

  private async saveRenterSms(smsEmail: ParsedGmailEmail): Promise<void> {
    try {
      const { phoneNumber, messageText } = await this.emailParserService.getRenterPhoneAndTextContentFromBrevoEmail(
        smsEmail.emailText,
      );

      const inquiry = await this.inquiryService.findLatestByRenterPhoneNumber(phoneNumber);
      const conversation = await inquiry.conversation;
      const renter = await inquiry.renter;

      await this.conversationService.saveRenterMessages(
        conversation,
        renter,
        [messageText],
        false,
        CommunicationChannel.SMS,
      );

      if (renter.user.preferredCommunicationChannel !== CommunicationChannel.SMS) {
        await this.userService.updateUser(renter.user.id, {
          preferredCommunicationChannel: CommunicationChannel.SMS,
        });

        await this.slackCommunicationService.sendMessageToConvosChannel('📱 Switched to SMS', conversation);
      }
    } catch (e) {
      console.error(`Failed to save renter SMS: ${e.message}`);
    }
  }

  private async saveHistory(inboxHistory: InboxHistoryVersion, historyId: string) {
    inboxHistory.historyId = historyId;
    await this.inboxHistoryRepository.save(inboxHistory);
  }

  private async parseGmailEmail(email: GaxiosResponse): Promise<ParsedGmailEmail> {
    const headers = email.data.payload.headers;

    // Extract Subject
    const subjectHeader = headers.find((header) => header.name === 'Subject');
    const subject = subjectHeader ? subjectHeader.value : 'No Subject';

    // Extract From (Sender's Email)
    const fromHeader = headers.find((header) => header.name === 'From');
    let emailAddress = fromHeader ? fromHeader.value : undefined;

    if (emailAddress) {
      const match = emailAddress.match(/<(.+)>/);
      if (match && match[1]) {
        emailAddress = match[1];
      }
    }

    // Extract Message-ID
    const messageIdHeader = headers.find((header) => header.name === 'Message-ID');
    const messageId = messageIdHeader ? messageIdHeader.value : undefined;

    // Extract References
    const referencesHeader = headers.find((header) => header.name === 'References');
    let references: string[] = [];
    if (referencesHeader && referencesHeader.value) {
      references = referencesHeader.value.split(' ').map((ref) => ref.trim());
    }

    // Extract Email Text
    const emailText = await this.getEmailText(email.data.payload.parts);

    return {
      emailText,
      subject,
      emailAddress,
      messageId,
      references,
    };
  }

  private async getEmailText(parts: gmail_v1.Schema$MessagePart[]): Promise<string> {
    const text = [];
    parts.forEach((part) => {
      if (part.mimeType === 'text/plain') {
        text.push(this.decodeBodyData(part.body.data));
      }

      if (part.mimeType === 'multipart/alternative') {
        part?.parts.forEach((alternativePart) => {
          if (alternativePart.mimeType === 'text/plain') {
            text.push(this.decodeBodyData(alternativePart.body.data));
          }
        });
      }
    });

    return text.join();
  }

  private decodeBodyData(data: string): string {
    return Buffer.from(data, 'base64').toString('utf8');
  }
}

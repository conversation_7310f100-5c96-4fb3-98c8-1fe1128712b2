import { Module } from '@nestjs/common';
import { GmailService } from './gmail.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InboxHistoryVersion } from './inbox-history-version.entity';
import { RenterModule } from '../../../../renter/renter/renter.module';
import { PropertyModule } from '../../../../investor/property/property/property.module';
import { PropertyInquiryModule } from '../../../../investor/property-inquiry/property-inquiry.module';
import { EmailParserModule } from '../email-parser/email-parser.module';
import { ConversationModule } from '../../conversation/conversetion.module';
import { SlackCommunicationModule } from '../../outbound-communication/slack/slack-communication.module';
import { UserModule } from '../../../user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([InboxHistoryVersion]),
    RenterModule,
    PropertyModule,
    PropertyInquiryModule,
    ConversationModule,
    EmailParserModule,
    SlackCommunicationModule,
    UserModule,
  ],
  providers: [GmailService],
  exports: [GmailService],
})
export class GmailModule {}

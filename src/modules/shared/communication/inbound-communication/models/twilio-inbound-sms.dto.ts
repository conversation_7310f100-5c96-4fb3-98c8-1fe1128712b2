import { IsString } from 'class-validator';

export class TwilioInboundSmsDto {
  @IsString()
  ToCountry: string;

  @IsString()
  ToState: string;

  @IsString()
  SmsMessageSid: string;

  @IsString()
  NumMedia: string;

  @IsString()
  ToCity: string;

  @IsString()
  FromZip: string;

  @IsString()
  SmsSid: string;

  @IsString()
  FromState: string;

  @IsString()
  SmsStatus: string;

  @IsString()
  FromCity: string;

  @IsString()
  Body: string;

  @IsString()
  FromCountry: string;

  @IsString()
  To: string;

  @IsString()
  MessagingServiceSid: string;

  @IsString()
  ToZip: string;

  @IsString()
  NumSegments: string;

  @IsString()
  MessageSid: string;

  @IsString()
  AccountSid: string;

  @IsString()
  From: string;

  @IsString()
  ApiVersion: string;
}

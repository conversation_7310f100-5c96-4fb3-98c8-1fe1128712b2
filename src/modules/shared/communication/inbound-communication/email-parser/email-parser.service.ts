import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { z } from 'zod';
import { parseBrevoNewTextEmailTemplate } from './prompts/parse-brevo-new-text-email.template';
import { LanguageModelsEnum } from '../../../../ai/enums/language-models.enum';
import { emailParserTemplate } from './prompts/email-parser.template';
import { AiService } from '../../../../ai/ai.service';
import { StructuredOutputParser } from '@langchain/core/output_parsers';

@Injectable()
export class EmailParserService {
  constructor(private readonly aiService: AiService) {}

  async getRenterPhoneAndTextContentFromBrevoEmail(emailText: string): Promise<{
    phoneNumber: string;
    messageText: string;
  }> {
    const parser = StructuredOutputParser.fromZodSchema(
      z.object({
        phoneNumber: z.string().describe('Phone number of the user'),
        messageText: z.string().describe('Text of the message sent by user'),
      }),
    );

    let parsedEmail: { messageText: string; phoneNumber: string };

    try {
      parsedEmail = await this.aiService.getResponse(
        {
          input: emailText,
        },
        parseBrevoNewTextEmailTemplate,
        null,
        LanguageModelsEnum.GPT_4_MINI,
        parser,
      );

      console.log(
        '[Email parsing result]',
        `Incoming email: "${emailText}". AI response: "${parsedEmail.messageText}"`,
      );
    } catch (error) {
      console.error(`Failed to parse email: ${emailText}`, error);

      parsedEmail = await this.aiService.getResponse(
        {
          input: emailText,
        },
        parseBrevoNewTextEmailTemplate,
        null,
        LanguageModelsEnum.GPT_4,
        parser,
      );

      console.log(
        '[Retried parsing email with GPT-4]',
        `Incoming email: "${emailText}". AI response: "${parsedEmail.messageText}"`,
      );
    }

    return parsedEmail;
  }

  async extractRenterMessageFromRawEmail(rawEmailText: string): Promise<string> {
    const parser = StructuredOutputParser.fromZodSchema(
      z.object({
        renterMessage: z.string().describe("User's message from the email"),
      }),
    );

    let parsedEmail: { renterMessage: string };

    try {
      parsedEmail = <{ renterMessage: string }>await this.aiService.getResponse(
        {
          input: rawEmailText,
        },
        emailParserTemplate,
        null,
        LanguageModelsEnum.GPT_4_MINI,
        parser,
      );

      console.log(
        '[Email parsing result]',
        `Incoming email: "${rawEmailText}". AI response: "${parsedEmail.renterMessage}"`,
      );
    } catch (error) {
      console.error(`Failed to parse email: ${rawEmailText}`, error);

      parsedEmail = <{ renterMessage: string }>await this.aiService.getResponse(
        {
          input: rawEmailText,
        },
        emailParserTemplate,
        null,
        LanguageModelsEnum.GPT_4,
        parser,
      );

      console.log(
        '[Retried parsing email with GPT-4]',
        `Incoming email: "${rawEmailText}". AI response: "${parsedEmail.renterMessage}"`,
      );
    }

    if (!parsedEmail) {
      throw new InternalServerErrorException(`Could not parse email: ${rawEmailText}`);
    }

    return parsedEmail.renterMessage;
  }
}

/* eslint-disable max-len */
export const emailParserTemplate = `
<task>
Your task is to extract the latest user's message from an email body provided in "email_to_parse" xml tags.
</task>

<instructions>
- Extract user message sent via email and return as output. Do not make any modifications except removing line breaks, just return text as is.
- If you are not sure what the user's message is, return the whole text from the "email_to_parse" as is without modifications.
- Never make anything up in the output, your output should be the exact text written by the user.
</instructions>

<handling_previous_messages_history>
Email usually contains previous messages history. In case if it's obvious that part of email is history, do not include it in the output.
History might be separated by certain formatting, like arrows (>), symbols (-, _, etc), dates, tags, etc and usually placed at the bottom of the email.
Pay special attention to dates and special formatting that might indicate the start of previous message history (it usually starts with text similar to "On [DATE] at [TIME] [PERSON] wrote:").
The latest message is usually above such formatting. Important: user message might be a date or contain a date, it could be a number or short answer like "yes", "no", etc. Do not confuse it with previous messages history.
</handling_previous_messages_history>

<output_format>
- Never wrap the output in the markdown code block like "\`\`\`json.
- Use the provided format for the output: {format_instructions}
</output_format>

<email_to_parse>
{input}
</email_to_parse>
`;

// Test these corner cases to make sure it still works:

// "Greetings, my friend! I would like to schedule a tour. Hi I'm looking to move in June. I have a cat. Could I tour Thursday or next week?"
// This one is tough for ai because double greetings makes it think that the first sentence is a previous message.
// So it might cut it off. But it should return the whole string as a message

// "Also it says that’s it’s a 4 bedroom house and I only see 3 rooms with 2
// beds in one. Am I missing another room or is that correct?"
// Messages like this are tricky to parse because of the 2nd part is moved to the next line. So it might cut it off.

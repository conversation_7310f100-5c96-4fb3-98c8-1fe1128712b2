/* eslint-disable max-len */
export const parseBrevoNewTextEmailTemplate = `
<task>
Extract the user's message and phone number from an email body provided in "email_to_parse" xml tags and map it to the database entity.
Parse the email content to extract and return the user's message and phone number in a format suitable for database storage.
</task>

<key_points>
- The user's message usually starts with "The mobile phone +*********** replied to your SMS transactional:" and ends with "Note - Replying to this notification email...".
- Return the exact text written by the user, without modifications.
- Focus on accurately extracting the user's message from the email text.
- Handle different variations in email formatting and content.
- Do not use examples as your output; only use the actual email provided in "email_to_parse" tags.
</key_points>

<examples>
1. If the email contains: "The mobile phone +123456789 replied to your SMS transactional: Hello!
    Note - Replying to this notification email will not send the response to your contact."
The answer should include the phone number (+123456789) and the message text (Hello!)

2. If the email contains: "The mobile phone +98764321 replied to your SMS transactional: When can I see the property?
    Note - Replying to this notification email will not send the response to your contact."
The answer should include the phone number (+98764321) and the message text (When can I see the property?)
</examples>

<output_format>
Use the provided format for the output: "{format_instructions}"
</output_format>

<email_to_parse>
{input}
</email_to_parse>
`;

import { Body, Controller, Post, Put, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { BasicAuth } from '../../auth/decorators/basic-auth.decorator';
import { InboundCommunicationService } from './inbound-communication.service';
import { Public } from '../../auth/decorators/public-access.decorator';
import { TwilioInboundSmsDto } from './models/twilio-inbound-sms.dto';
import { TwilioWebhookGuard } from './guards/twilio-webhook.guard';

@ApiTags('inbound-communication')
@Controller('inbound-communication')
export class InboundCommunicationController {
  constructor(private readonly inboundCommunicationService: InboundCommunicationService) {}

  @Put('gmail/check-new-emails')
  @ApiOkResponse({ description: 'Returns 200 status if successful' })
  @BasicAuth()
  async checkNewEmails(): Promise<void> {
    return this.inboundCommunicationService.checkNewEmails();
  }

  @Post('twilio/sms')
  @ApiOkResponse({ description: 'Returns 200 status if successful' })
  @Public()
  @UseGuards(TwilioWebhookGuard)
  async receiveSms(@Body() body: TwilioInboundSmsDto): Promise<void> {
    await this.inboundCommunicationService.saveNewTwilioSms(body);
  }
}

import { Modu<PERSON> } from '@nestjs/common';
import { GmailModule } from './gmail/gmail.module';
import { InboundCommunicationController } from './inbound-communication.controller';
import { InboundCommunicationService } from './inbound-communication.service';
import { PropertyInquiryModule } from '../../../investor/property-inquiry/property-inquiry.module';
import { ConversationModule } from '../conversation/conversetion.module';
import { UserModule } from '../../user/user.module';
import { SlackCommunicationModule } from '../outbound-communication/slack/slack-communication.module';

@Module({
  imports: [GmailModule, PropertyInquiryModule, ConversationModule, UserModule, SlackCommunicationModule],
  providers: [InboundCommunicationService],
  controllers: [InboundCommunicationController],
})
export class InboundCommunicationModule {}

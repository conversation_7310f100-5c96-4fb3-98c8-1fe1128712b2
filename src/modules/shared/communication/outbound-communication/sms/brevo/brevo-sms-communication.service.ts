import { Injectable } from '@nestjs/common';
import * as SibApiV3Sdk from 'sib-api-v3-sdk';
import { ConfigService } from '@nestjs/config';
import ParsingUtils from '../../../../../../utils/parsing.utils';
import { SmsType } from '../models/sms-type.interface';

@Injectable()
export class BrevoSmsCommunicationService {
  private readonly senderNumber: string;
  private readonly senderName: string;
  private readonly transactionalSmsApiInstance: SibApiV3Sdk.TransactionalSMSApi;

  constructor(private readonly config: ConfigService) {
    const apiKey = this.config.get('BREVO_API_KEY');
    const defaultClient = SibApiV3Sdk.ApiClient.instance;
    defaultClient.authentications['api-key'].apiKey = apiKey;

    this.transactionalSmsApiInstance = new SibApiV3Sdk.TransactionalSMSApi();
    this.senderNumber = this.config.get('TALLO_SENDER_NUMBER');
    this.senderName = this.config.get('TALLO_SENDER_NAME');
  }

  async sendSms(phoneNumber: string, content: string, type: SmsType): Promise<void> {
    const sendTransactSms = new SibApiV3Sdk.SendTransacSms();

    if (type === SmsType.TRANSACTIONAL) {
      sendTransactSms.sender = this.senderName;
    } else {
      sendTransactSms.sender = this.senderNumber;
    }

    sendTransactSms.recipient = ParsingUtils.formatPhoneNumber(phoneNumber);
    sendTransactSms.content = content;
    sendTransactSms.type = 'transactional';

    try {
      if (this.config.get('SMS_NOTIFICATIONS_ACTIVE')) {
        await this.transactionalSmsApiInstance.sendTransacSms(sendTransactSms);
      }
    } catch (error) {
      console.error('Error sending SMS:', error.response);
    }
  }
}

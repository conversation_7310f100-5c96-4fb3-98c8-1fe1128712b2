import { Module } from '@nestjs/common';
import { SmsCommunicationService } from './sms-communication.service';
import { BrevoSmsCommunicationService } from './brevo/brevo-sms-communication.service';
import { TwilioSmsCommunicationService } from './twilio/twilio-sms-communication.service';

@Module({
  providers: [SmsCommunicationService, BrevoSmsCommunicationService, TwilioSmsCommunicationService],
  exports: [SmsCommunicationService],
})
export class SmsCommunicationModule {}

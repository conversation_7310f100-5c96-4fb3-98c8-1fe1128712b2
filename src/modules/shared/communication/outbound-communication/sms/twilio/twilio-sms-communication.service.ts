import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Twilio } from 'twilio';
import ParsingUtils from '../../../../../../utils/parsing.utils';

@Injectable()
export class TwilioSmsCommunicationService {
  private readonly client: Twilio;

  constructor(private readonly config: ConfigService) {
    const accountSid = this.config.get('TWILIO_ACCOUNT_SID');
    const authToken = this.config.get('TWILIO_AUTH_TOKEN');
    this.client = new Twilio(accountSid, authToken);
  }

  async sendSms(phoneNumber: string, content: string): Promise<void> {
    try {
      const formattedPhoneNumber = ParsingUtils.formatPhoneNumber(phoneNumber);
      if (this.config.get('SMS_NOTIFICATIONS_ACTIVE')) {
        await this.client.messages.create({
          body: content,
          to: formattedPhoneNumber,
          shortenUrls: true,
          messagingServiceSid: this.config.get('TWILIO_MESSAGING_SERVICE_SID'),
        });
      }
    } catch (error) {
      console.error('Error sending SMS via Twilio:', error.message);
    }
  }
}

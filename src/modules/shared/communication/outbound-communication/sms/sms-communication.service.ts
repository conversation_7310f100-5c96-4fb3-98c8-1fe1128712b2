import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { BrevoSmsCommunicationService } from './brevo/brevo-sms-communication.service';
import { TwilioSmsCommunicationService } from './twilio/twilio-sms-communication.service';
import { SmsType } from './models/sms-type.interface';
import { TourType } from '../../../../investor/showing/enums/tour-type.enum';

@Injectable()
export class SmsCommunicationService {
  constructor(
    private readonly brevoSmsCommunicationService: BrevoSmsCommunicationService,
    private readonly twilioSmsCommunicationService: TwilioSmsCommunicationService,
    private readonly config: ConfigService,
  ) {}

  private async sendViaTwilio(phoneNumber: string, message: string): Promise<void> {
    this.twilioSmsCommunicationService.sendSms(phoneNumber, message).then(
      () => {
        console.log('Message sent via <PERSON><PERSON><PERSON>');
      },
      (error) => {
        console.error('Error sending message via Twi<PERSON>', error);
      },
    );
  }

  private async sendViaBrevo(phoneNumber: string, message: string, type: SmsType): Promise<void> {
    await this.brevoSmsCommunicationService.sendSms(phoneNumber, message, type).then(
      () => {
        console.log('Message sent via Brevo');
      },
      (error) => {
        console.error('Error sending message via Brevo', error);
      },
    );
  }

  async sendMessage(phoneNumber: string, message: string, type: SmsType): Promise<void> {
    try {
      await this.sendViaTwilio(phoneNumber, message);
    } catch (twilioError) {
      console.warn('Twilio failed, fallback to Brevo:', twilioError.message);
      await this.sendViaBrevo(phoneNumber, message, type);
    }
  }

  async sendNewPropertyQuestionNotification(
    propertyQuestionUrl: string,
    phoneNumber: string,
    username: string,
    propertyAddress: string,
  ) {
    const message = `Hi ${username}, new Intelligent Escalation for ${propertyAddress}. Tallo needs your input to provide accurate info to a renter. Respond here: ${propertyQuestionUrl} - Tallo Team`;

    this.sendMessage(phoneNumber, message, SmsType.TRANSACTIONAL).then();
  }

  async sendRenterCancelledShowingNotification(
    phoneNumber: string,
    username: string,
    showingDate: string,
    propertyAddress: string,
  ) {
    const message = `Hi ${username}, the showing for ${propertyAddress} on ${showingDate} was canceled by the renter. We'll work on rescheduling. View details: ${this.config.get('INVESTOR_APP_URL')}/showings - Tallo Team`;

    this.sendMessage(phoneNumber, message, SmsType.TRANSACTIONAL).then();
  }

  async sendNewShowingRequestNotification(
    propertyId: string,
    phoneNumber: string,
    username: string,
    propertyAddress: string,
    showingDate: string,
    tourType: TourType,
  ) {
    const baseUrl = this.config.get('INVESTOR_APP_URL');
    const link = `${baseUrl}/property/${propertyId}/dashboard#showing-requests-section`;
    const tourTypeText = tourType === TourType.VIRTUAL ? 'virtual tour' : 'showing';
    const message = `Hi ${username}, you have a new ${tourTypeText} request for ${propertyAddress} on ${showingDate}. Review and confirm here: ${link} - Tallo Team`;

    this.sendMessage(phoneNumber, message, SmsType.TRANSACTIONAL).then();
  }

  async sendShowingRequestRescheduledNotification(
    phoneNumber: string,
    username: string,
    renterName: string,
    showingDate: string,
    showingId: string,
  ): Promise<void> {
    const baseUrl = this.config.get('INVESTOR_APP_URL');
    const link = `${baseUrl}/property/showings/${showingId}`;
    // eslint-disable-next-line max-len
    const message = `Hi ${username}, good news! Your showing with ${renterName} has been rescheduled to ${showingDate}. View details here: ${link} - Tallo Team`;

    this.sendMessage(phoneNumber, message, SmsType.TRANSACTIONAL).then();
  }

  async sendUpcomingShowingReminderToInvestor(
    phoneNumber: string,
    ownerName: string,
    showingDate: string,
    showingUrl: string,
    propertyAddress: string,
    tourType: TourType,
  ): Promise<void> {
    const tourTypeText = tourType === TourType.VIRTUAL ? 'virtual tour' : 'showing';
    // eslint-disable-next-line max-len
    const message = `Hi ${ownerName}, just a quick reminder: your ${tourTypeText} at ${propertyAddress} is coming up on ${showingDate}. View details or make changes here: ${showingUrl} - Tallo Team`;

    this.sendMessage(phoneNumber, message, SmsType.TRANSACTIONAL);
  }

  async sendRenterConfirmedShowingNotification(
    phoneNumber: string,
    recipientName: string,
    renterName: string,
    showingDate: string,
    propertyAddress: string,
  ): Promise<void> {
    const message = `Hi ${recipientName}, good news! ${renterName} has confirmed their attendance for the showing at ${propertyAddress} on ${showingDate}. - Tallo Team`;

    this.sendMessage(phoneNumber, message, SmsType.TRANSACTIONAL);
  }

  async sendPendingShowingRequestReminderToInvestor(
    phoneNumber: string,
    ownerName: string,
    renterProfileUrl: string,
    propertyAddress: string,
  ): Promise<void> {
    // eslint-disable-next-line max-len
    const message = `Hi ${ownerName}, pending showing request for ${propertyAddress}. Please confirm or cancel within 4 hours to avoid automatic cancellation: ${renterProfileUrl} - Tallo Team`;

    this.sendMessage(phoneNumber, message, SmsType.TRANSACTIONAL);
  }

  async sendPendingShowingRequestExpiredNotification(
    phoneNumber: string,
    ownerName: string,
    renterProfileUrl: string,
    propertyAddress: string,
  ): Promise<void> {
    // eslint-disable-next-line max-len
    const message = `Hi ${ownerName}, the showing request for ${propertyAddress} was canceled due to no response. View renter's profile: ${renterProfileUrl}. Let us know if you have any questions. - Tallo Team`;

    this.sendMessage(phoneNumber, message, SmsType.TRANSACTIONAL);
  }

  async sendApplicationSubmittedNotification(
    phoneNumber: string,
    ownerFirstName: string,
    renterName: string,
    renterProfileUrl: string,
    propertyAddress: string,
  ): Promise<void> {
    // eslint-disable-next-line max-len
    const message = `Hi ${ownerFirstName}. ${renterName} has submitted an application for the property located at ${propertyAddress}. Review the application here: ${renterProfileUrl} - Tallo Team`;

    this.sendMessage(phoneNumber, message, SmsType.TRANSACTIONAL);
  }
}

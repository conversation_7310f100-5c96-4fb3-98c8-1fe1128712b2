import axios from 'axios';

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { Conversation } from '../../conversation/entities/conversation.entity';
import { PostedSlackMessageData } from './posted-slack-message-data.interface';
import { slackChannelsIds } from './slack-channels-ids.const';
import { SlackConvoMessageBuilder } from './slack-convo-message-builder.class';
import { SlackConvoMessageParams } from './slack-convo-message-params.interface';
import { slackWebhooks } from './slack-webhooks.const';

@Injectable()
export class SlackCommunicationService {
  slackNotificationsEnabled = true;

  constructor(private readonly config: ConfigService) {
    this.slackNotificationsEnabled = this.config.get('SLACK_NOTIFICATIONS_ACTIVE');
  }

  async sendNewUserRegisteredMessage(message: string): Promise<void> {
    if (this.slackNotificationsEnabled) {
      try {
        await axios.post(this.getWebhookUrl(slackWebhooks.newUsersNotificationsChannel), { text: message });
        console.log('Message sent to Slack successfully');
      } catch (error) {
        console.error('Error sending message to Slack:', error);
      }
    }
  }

  async logError(error: string): Promise<void> {
    if (this.slackNotificationsEnabled) {
      const message = {
        text: error,
      };

      try {
        await axios.post(this.getWebhookUrl(slackWebhooks.alertsProdChannel), message);
        console.log('Error logged to Slack successfully');
      } catch (error) {
        console.error('Error logging error to Slack:', error);
      }
    }
  }

  async buildAndSendMessageToConvosChannel(params: SlackConvoMessageParams): Promise<PostedSlackMessageData> {
    const slackMessage = await this.buildConvoMessageNotification(params);

    return this.sendMessageToConvosChannel(slackMessage, params.conversation);
  }

  async sendMessageToConvosChannel(message: string, conversation?: Conversation): Promise<PostedSlackMessageData> {
    if (this.slackNotificationsEnabled) {
      try {
        const response = await this.sendConvosMessageToSlack(message, conversation?.slackLoggingThreadId);
        console.log('Message sent to Slack successfully');

        return { threadId: response.data.ts };
      } catch (error) {
        console.error('Error sending message to Slack:', error);
        return { threadId: null };
      }
    }

    return { threadId: null };
  }

  private async buildConvoMessageNotification(params: SlackConvoMessageParams): Promise<string> {
    if (params.conversation.slackLoggingThreadId) {
      return this.buildMessageForExistingThread(params);
    }

    return this.buildNewLeadMessage(params);
  }

  private async buildNewLeadMessage(params: SlackConvoMessageParams): Promise<string> {
    const propertyOwner = await params.property.owner;
    const ownerUser = await propertyOwner.user;
    const renterProfileUrl = this.getRenterProfileUrl(params.property.id, params.renter.id);

    return new SlackConvoMessageBuilder()
      .appendTextLine(
        // eslint-disable-next-line max-len
        `💬 <mailto:${ownerUser.email}|${ownerUser.name}'s> property | <${renterProfileUrl}|Conversation>`,
      )
      .appendEmptyLine()
      .appendRenterMessageAsBullet(params.renterMessages)
      .appendTalloMessageAsBullet(params.aiResponse)
      .build();
  }

  private async buildMessageForExistingThread(params: SlackConvoMessageParams): Promise<string> {
    return new SlackConvoMessageBuilder()
      .appendRenterMessageAsBullet(params.renterMessages)
      .appendTalloMessageAsBullet(params.aiResponse)
      .build();
  }

  private getRenterProfileUrl(propertyId: string, renterId: string): string {
    return `${this.config.get('INVESTOR_APP_URL')}/property/${propertyId}/renter/profile/${renterId}`;
  }

  private async sendConvosMessageToSlack(message: string, threadId?: string): Promise<{ data: { ts: string } }> {
    const body = {
      text: message,
      channel: this.config.get('IS_LOCALHOST') ? slackChannelsIds.testSlackNotifications : slackChannelsIds.convos,
      unfurl_links: false,
    };

    if (threadId) {
      body['thread_ts'] = threadId;
    }

    const response = await axios.post('https://slack.com/api/chat.postMessage', body, {
      headers: {
        Authorization: `Bearer ${this.config.get('SLACK_CONVO_LOGGER_BOT_TOKEN')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.data.ok) {
      throw new Error(`Error sending message to Slack (${response.data.error})`);
    }

    return response;
  }

  private getWebhookUrl(webhookUrl: string): string {
    if (this.config.get('IS_LOCALHOST')) {
      return slackWebhooks.testSlackNotificationsChannel;
    }

    return webhookUrl;
  }
}

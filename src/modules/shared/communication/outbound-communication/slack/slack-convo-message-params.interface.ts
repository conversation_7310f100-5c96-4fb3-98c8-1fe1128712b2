import { Property } from '../../../../investor/property/property/entities/property.entity';
import { Renter } from '../../../../renter/renter/renter.entity';
import { Conversation } from '../../conversation/entities/conversation.entity';

export interface SlackConvoMessageParams {
  renterMessages: string[];
  aiResponse: string;
  property: Property;
  renter: Renter;
  conversation: Conversation;
}

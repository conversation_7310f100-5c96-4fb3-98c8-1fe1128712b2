export class SlackConvoMessageBuilder {
  private readonly textLines: string[] = [];

  appendTextLine(text: string): SlackConvoMessageBuilder {
    if (text) {
      this.textLines.push(text);
    }

    return this;
  }

  appendBulletItem(text: string): SlackConvoMessageBuilder {
    return this.appendTextLine(`• ${text}`);
  }

  appendEmptyLine(): SlackConvoMessageBuilder {
    this.textLines.push('');
    return this;
  }

  appendTalloMessageAsBullet(text: string): SlackConvoMessageBuilder {
    this.appendAuthoredBulletItem('Tallo', text);

    return this;
  }

  appendRenterMessageAsBullet(text: string): SlackConvoMessageBuilder;
  appendRenterMessageAsBullet(texts: string[]): SlackConvoMessageBuilder;
  appendRenterMessageAsBullet(textOrTexts: string | string[]): SlackConvoMessageBuilder {
    if (Array.isArray(textOrTexts)) {
      textOrTexts.forEach((text) => this.appendAuthoredBulletItem('Renter', text));
    } else {
      this.appendAuthoredBulletItem('Renter', textOrTexts);
    }

    return this;
  }

  appendOwnerMessageAsBullet(text: string): SlackConvoMessageBuilder {
    this.appendAuthoredBulletItem('Owner', text);

    return this;
  }

  appendTalloMessage(text: string): SlackConvoMessageBuilder {
    this.appendAuthoredTextLine('Tallo', text);

    return this;
  }

  appendRenterMessage(text: string): SlackConvoMessageBuilder {
    this.appendAuthoredTextLine('Renter', text);

    return this;
  }

  appendOwnerMessage(text: string): SlackConvoMessageBuilder {
    this.appendAuthoredTextLine('Owner', text);

    return this;
  }

  appendAuthoredBulletItem(author: string, text: string): SlackConvoMessageBuilder {
    return this.appendBulletItem(this.formatAuthoredMessage(author, text));
  }

  appendAuthoredTextLine(author: string, text: string): SlackConvoMessageBuilder {
    return this.appendTextLine(this.formatAuthoredMessage(author, text));
  }

  build(): string {
    return this.textLines.join('\n');
  }

  private formatAuthoredMessage(author: string, text: string): string {
    return `_\`${author}:\`_ ${text}`;
  }
}

import { Injectable } from '@nestjs/common';

import { ConfigService } from '@nestjs/config';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { PropertyLocation } from '../../../investor/property/property/property-details/location/property-location.entity';
import { Renter } from '../../../renter/renter/renter.entity';
import { Role } from '../../auth/models/roles-enum';
import { CompanySignUpDto } from '../../company/model/company-sign-up.dto';
import { User } from '../../user/entities/user.entity';
import { EmailMetadata } from '../conversation/entities/email.metadata.entity';
import { CommunicationChannel } from '../conversation/enums/preferred-communication-channel.enum';
import { EmailNotificationService } from './email/email-notification.service';
import { SlackCommunicationService } from './slack/slack-communication.service';
import { SmsCommunicationService } from './sms/sms-communication.service';
import { SmsType } from './sms/models/sms-type.interface';
import { AnalyticsService } from '../../../analytics/analytics.service';
import { AnalyticsEvent } from '../../../analytics/analytics.constants';
import { TourType } from '../../../investor/showing/enums/tour-type.enum';

@Injectable()
export class OutboundCommunicationService {
  senderNumber: string;
  senderName: string;

  constructor(
    private readonly emailNotificationService: EmailNotificationService,
    private readonly smsNotificationService: SmsCommunicationService,
    private readonly slackNotificationService: SlackCommunicationService,
    private readonly analyticsService: AnalyticsService,
    private readonly config: ConfigService,
  ) {
    this.senderNumber = this.config.get('TALLO_SENDER_NUMBER');
    this.senderName = this.config.get('TALLO_SENDER_NAME');
  }
  async sendMessage(
    recipient: User,
    messageContent: string,
    emailMetadata: EmailMetadata,
    chanel: CommunicationChannel,
  ) {
    this.analyticsService.increment(AnalyticsEvent.MESSAGE_SENT);
    if (chanel === CommunicationChannel.SMS && recipient.phoneNumber) {
      this.analyticsService.increment(AnalyticsEvent.MESSAGE_SENT_SMS);
      let smsType: SmsType;

      if (recipient.roles.includes(Role.INVESTOR)) {
        smsType = SmsType.NOTIFICATION;
      } else {
        smsType = SmsType.TRANSACTIONAL;
      }
      this.smsNotificationService
        .sendMessage(recipient.phoneNumber, messageContent, smsType)
        .catch((error) => console.error('SMS sending error', error));
    } else {
      this.analyticsService.increment(AnalyticsEvent.MESSAGE_SENT_EMAIL);
      this.emailNotificationService
        .sendMessage(recipient.email, messageContent, this.getUserName(recipient), emailMetadata)
        .catch((error) => console.error('Email sending error', error));
    }
  }

  async sendPasswordResetLink(passwordResetToken: string, recipient: User, role: Role): Promise<void> {
    const websiteDomain = role === Role.INVESTOR ? this.baseInvestorWsUrl : this.baseRenterWsUrl;
    const resetLink = `${websiteDomain}/auth/reset-password/confirm?token=${passwordResetToken}`;

    // For devs local testing purposes, so you don't have to find code or links in DB
    if (this.config.get('IS_LOCALHOST')) {
      console.log('[Password reset link is sent]', resetLink);
    }

    await this.emailNotificationService.sendPasswordResetLink(resetLink, recipient.email, this.getUserName(recipient));
  }

  async sendEmailConfirmationCode(email: string, userFirstName: string, emailConfirmationCode: string): Promise<void> {
    // For devs local testing purposes, so you don't have to find code or links in DB
    if (this.config.get('IS_LOCALHOST')) {
      console.log('[Email confirmation code is sent]', emailConfirmationCode);
    }

    await this.emailNotificationService.sendEmailConfirmationCode(email, userFirstName, emailConfirmationCode);
  }

  async sendApplicationInviteToNonActivatedPrimaryApplicant(
    inviteToken: string,
    renterUser: User,
    ownerUser: User,
    location: PropertyLocation,
    propertyCoverImageUrl: string,
  ): Promise<void> {
    await this.emailNotificationService.sendApplicationInviteToNonActivatedPrimaryApplicant(
      renterUser.email,
      this.getUserName(renterUser),
      ownerUser.name,
      location,
      this.getRenterActivationUrl(inviteToken, renterUser.email, renterUser.name, propertyCoverImageUrl),
    );
  }

  async sendApplicationInviteToActivatedPrimaryApplicant(
    renterUser: User,
    ownerUser: User,
    location: PropertyLocation,
    propertyCoverImageUrl: string,
  ): Promise<void> {
    await this.emailNotificationService.sendApplicationInviteToActivatedPrimaryApplicant(
      renterUser.email,
      this.getUserName(renterUser),
      ownerUser.name,
      location,
      this.getRenterAppSignInUrl(renterUser.email, propertyCoverImageUrl),
    );
  }

  async sendApplicationInviteToNonActivatedCoApplicant(
    inviteToken: string,
    coApplicantUser: User,
    primaryApplicantUser: User,
    location: PropertyLocation,
    propertyCoverImageUrl: string,
  ): Promise<void> {
    await this.emailNotificationService.sendApplicationInviteToNonActivatedCoApplicant(
      coApplicantUser.email,
      this.getUserName(coApplicantUser),
      primaryApplicantUser.name,
      location,
      this.getRenterActivationUrl(inviteToken, coApplicantUser.email, coApplicantUser.name, propertyCoverImageUrl),
    );
  }

  async sendApplicationInviteToActivatedCoApplicant(
    coApplicantUser: User,
    primaryApplicantUser: User,
    location: PropertyLocation,
    propertyCoverImageUrl: string,
  ): Promise<void> {
    await this.emailNotificationService.sendApplicationInviteToActivatedCoApplicant(
      coApplicantUser.email,
      this.getUserName(coApplicantUser),
      primaryApplicantUser.name,
      location,
      this.getRenterAppSignInUrl(coApplicantUser.email, propertyCoverImageUrl),
    );
  }

  async sendApplicationInviteToNonActivatedCoSigner(
    inviteToken: string,
    coSignerUser: User,
    inviterUser: User,
    location: PropertyLocation,
    propertyCoverImageUrl: string,
  ): Promise<void> {
    await this.emailNotificationService.sendApplicationInviteToNonActivatedCoSigner(
      coSignerUser.email,
      this.getUserName(coSignerUser),
      inviterUser.name,
      location,
      this.getRenterActivationUrl(inviteToken, coSignerUser.email, coSignerUser.name, propertyCoverImageUrl),
    );
  }

  async sendApplicationInviteToActivatedCoSigner(
    coSignerUser: User,
    inviterUser: User,
    location: PropertyLocation,
    propertyCoverImageUrl: string,
  ): Promise<void> {
    await this.emailNotificationService.sendApplicationInviteToActivatedCoSigner(
      coSignerUser.email,
      this.getUserName(coSignerUser),
      inviterUser.name,
      location,
      this.getRenterAppSignInUrl(coSignerUser.email, propertyCoverImageUrl),
    );
  }

  async sendApplicationSubmittedNotification(
    recipient: User,
    renter: Renter,
    property: Property,
    location: PropertyLocation,
  ): Promise<void> {
    if (User.isSmsComms(recipient)) {
      await this.smsNotificationService.sendApplicationSubmittedNotification(
        recipient.phoneNumber,
        this.getUserName(recipient),
        renter.user.name,
        this.getRenterProfileUrl(property.id, renter.id),
        location.address,
      );
    } else {
      await this.emailNotificationService.sendApplicationSubmittedNotification(
        recipient.email,
        this.getUserName(recipient),
        renter.user.name,
        this.getRenterProfileUrl(property.id, renter.id),
        location,
      );
    }
  }

  async sendManualTransUnionVerificationIsCompletedNotification(
    applicationInviteId: string,
    recipient: User,
    location: PropertyLocation,
  ): Promise<void> {
    await this.emailNotificationService.sendManualTransUnionVerificationIsCompletedNotification(
      recipient.email,
      this.getUserName(recipient),
      `${this.config.get('RENTER_APP_URL')}/application-invite/${applicationInviteId}`,
      location,
    );
  }

  async sendBundleIsReadyForSubmissionNotification(
    recipient: User,
    bundleId: string,
    location: PropertyLocation,
  ): Promise<void> {
    await this.emailNotificationService.sendBundleIsReadyForSubmissionNotification(
      recipient.email,
      this.getUserName(recipient),
      `${this.config.get('RENTER_APP_URL')}/application-bundle/${bundleId}`,
      location,
    );
  }

  async sendOneTimeSignInCode(email: string, ownerFirstName: string, oneTimeCode: string): Promise<void> {
    if (this.config.get('IS_LOCALHOST')) {
      console.log('[One time code for sign in', oneTimeCode);
    }

    await this.emailNotificationService.sendOneTimeSignInCode(email, ownerFirstName, oneTimeCode);
  }

  async sendNewPropertyQuestionNotification(
    questionId: string,
    propertyId: string,
    recipient: User,
    renter: Renter,
    location: PropertyLocation,
  ) {
    const propertyQuestionUrl = this.getPropertyQuestionUrl(propertyId, questionId);

    if (recipient.preferredCommunicationChannel === CommunicationChannel.SMS && recipient.phoneNumber) {
      await this.smsNotificationService.sendNewPropertyQuestionNotification(
        propertyQuestionUrl,
        recipient.phoneNumber,
        this.getUserName(recipient),
        location.address,
      );
    } else {
      await this.emailNotificationService.sendNewPropertyQuestionNotification(
        propertyQuestionUrl,
        recipient.email,
        this.getUserName(recipient),
        renter,
        location,
      );
    }
  }

  async sendNewShowingRequestNotification(
    property: Property,
    location: PropertyLocation,
    recipient: User,
    showingDate: string,
    renter: Renter,
    chanel: CommunicationChannel,
    tourType: TourType,
  ) {
    if (chanel === CommunicationChannel.SMS && recipient.phoneNumber) {
      await this.smsNotificationService.sendNewShowingRequestNotification(
        property.id,
        recipient.phoneNumber,
        this.getUserName(recipient),
        location.address,
        showingDate,
        tourType,
      );
    } else {
      await this.emailNotificationService.sendNewShowingRequestNotification(
        property,
        location,
        recipient.email,
        this.getUserName(recipient),
        showingDate,
        renter,
        tourType,
      );
    }
  }

  async sendShowingRequestRescheduledNotification(
    showingId: string,
    propertyLocation: PropertyLocation,
    recipient: User,
    showingDate: string,
    renterName: string,
  ) {
    if (recipient.preferredCommunicationChannel === CommunicationChannel.SMS && recipient.phoneNumber) {
      await this.smsNotificationService.sendShowingRequestRescheduledNotification(
        recipient.phoneNumber,
        this.getUserName(recipient),
        renterName,
        showingDate,
        showingId,
      );
    } else {
      await this.emailNotificationService.sendShowingRequestRescheduledNotification(
        recipient.email,
        this.getUserName(recipient),
        renterName,
        showingDate,
        showingId,
        propertyLocation,
      );
    }
  }

  async sendRenterCancelledShowingNotification(
    propertyId: string,
    recipient: User,
    renter: Renter,
    showingDate: string,
    propertyAddress: string,
    chanel: CommunicationChannel,
  ) {
    if (chanel === CommunicationChannel.SMS && recipient.phoneNumber) {
      await this.smsNotificationService.sendRenterCancelledShowingNotification(
        recipient.phoneNumber,
        this.getUserName(recipient),
        showingDate,
        propertyAddress,
      );
    } else {
      await this.emailNotificationService.sendRenterCancelledShowingNotification(
        propertyId,
        recipient.email,
        this.getUserName(recipient),
        renter,
        showingDate,
        propertyAddress,
      );
    }
  }

  async sendUpcomingShowingReminderToInvestor(
    showingId: string,
    propertyLocation: PropertyLocation,
    recipient: User,
    showingDate: string,
    tourType: TourType,
  ): Promise<void> {
    if (recipient.preferredCommunicationChannel === CommunicationChannel.SMS && recipient.phoneNumber) {
      this.smsNotificationService.sendUpcomingShowingReminderToInvestor(
        recipient.phoneNumber,
        this.getUserName(recipient),
        showingDate,
        this.getShowingUrl(showingId),
        propertyLocation.address,
        tourType,
      );
    } else {
      this.emailNotificationService.sendUpcomingShowingReminderToInvestor(
        recipient.email,
        this.getUserName(recipient),
        showingDate,
        this.getShowingUrl(showingId),
        propertyLocation,
        tourType,
      );
    }
  }

  async sendNewUserRegisteredNotification(companySignUpDto: CompanySignUpDto, userEmail: string) {
    const notificationMessage = `[New Company Form Fill In] ${userEmail} filled in company form 🔥.
    Company data: ${JSON.stringify(companySignUpDto)}`;
    console.log(notificationMessage);
    await this.slackNotificationService.sendNewUserRegisteredMessage(notificationMessage);
  }

  async sendPendingShowingRequestReminderNotification(
    ownerUser: User,
    property: Property,
    renter: Renter,
  ): Promise<void> {
    const propertyLocation = await property.location;
    const renterProfileUrl = this.getRenterProfileUrl(property.id, renter.id);

    if (ownerUser.phoneNumber) {
      this.smsNotificationService.sendPendingShowingRequestReminderToInvestor(
        ownerUser.phoneNumber,
        this.getUserName(ownerUser),
        renterProfileUrl,
        propertyLocation.address,
      );
    } else {
      this.emailNotificationService.sendPendingShowingRequestReminderToInvestor(
        ownerUser.email,
        this.getUserName(ownerUser),
        renterProfileUrl,
        propertyLocation,
      );
    }
  }

  async sendPendingShowingRequestExpiredNotification(
    ownerUser: User,
    property: Property,
    renter: Renter,
  ): Promise<void> {
    const propertyLocation = await property.location;
    const renterProfileUrl = this.getRenterProfileUrl(property.id, renter.id);

    if (ownerUser.preferredCommunicationChannel === CommunicationChannel.SMS && ownerUser.phoneNumber) {
      this.smsNotificationService.sendPendingShowingRequestExpiredNotification(
        ownerUser.phoneNumber,
        this.getUserName(ownerUser),
        renterProfileUrl,
        propertyLocation.address,
      );
    } else {
      this.emailNotificationService.sendPendingShowingRequestExpiredNotification(
        ownerUser.email,
        this.getUserName(ownerUser),
        renterProfileUrl,
        propertyLocation.address,
      );
    }
  }

  async sendApplicationDeclinedNotification(renterUser: User, location: PropertyLocation): Promise<void> {
    await this.emailNotificationService.sendApplicationDeclinedNotification(
      renterUser.email,
      this.getUserName(renterUser),
      location,
    );
  }

  async sendAfterShowingInvestorFollowUp(
    property: Property,
    location: PropertyLocation,
    recipient: User,
    renter: Renter,
  ) {
    const renterProfileUrl = this.getRenterProfileUrl(property.id, renter.id);
    const message = `Hi ${this.getUserName(recipient)}, hope the showing at ${location.address} went well. Want to send ${renter.user.name} an application? Tap below to review their profile and take the next steps. ${renterProfileUrl}`;
    const emailMetadata = new EmailMetadata();
    emailMetadata.subject = `Application for ${renter.user.name}`;

    if (recipient.preferredCommunicationChannel === CommunicationChannel.SMS && recipient.phoneNumber) {
      await this.smsNotificationService.sendMessage(recipient.phoneNumber, message, SmsType.NOTIFICATION);
    } else {
      await this.emailNotificationService.sendMessage(
        recipient.email,
        message,
        this.getUserName(recipient),
        emailMetadata,
      );
    }
  }

  async sendRenterWantsToApplyNotification(
    property: Property,
    location: PropertyLocation,
    recipient: User,
    renter: Renter,
  ) {
    const renterProfileUrl = this.getRenterProfileUrl(property.id, renter.id);
    const messageText = `Hi ${this.getUserName(recipient)}, ${renter.user.name} wants to apply to your property located at ${location.address}. You can review their profile here: ${renterProfileUrl}`;

    if (recipient.preferredCommunicationChannel === CommunicationChannel.SMS && recipient.phoneNumber) {
      await this.smsNotificationService.sendMessage(recipient.phoneNumber, messageText, SmsType.NOTIFICATION);
    } else {
      await this.emailNotificationService.sendRenterWantsToApplyNotification(
        recipient.email,
        messageText,
        renter.user.name,
        {
          subject: 'Renter wants to apply',
        },
      );
    }
  }

  async sendRenterConfirmedShowingNotification(
    recipientPhone: string,
    recipientName: string,
    renterName: string,
    showingDate: string,
    propertyAddress: string,
  ): Promise<void> {
    await this.smsNotificationService.sendRenterConfirmedShowingNotification(
      recipientPhone,
      recipientName,
      renterName,
      showingDate,
      propertyAddress,
    );
  }

  private getUserName(user: User): string {
    return user.name.split(' ')[0];
  }

  private getShowingUrl(showingId: string): string {
    return `${this.baseInvestorWsUrl}/property/showings/${showingId}`;
  }

  private getRenterProfileUrl(propertyId: string, renterId: string): string {
    return `${this.baseInvestorWsUrl}/property/${propertyId}/renter/profile/${renterId}`;
  }

  private getPropertyQuestionUrl(propertyId: string, questionId: string): string {
    return `${this.baseInvestorWsUrl}/property/${propertyId}/status?questionId=${questionId}`;
  }

  private get baseInvestorWsUrl(): string {
    return this.config.get('INVESTOR_APP_URL');
  }

  private get baseRenterWsUrl(): string {
    return this.config.get('RENTER_APP_URL');
  }

  getRenterAppSignInUrl(autofillEmail?: string, propertyCoverImageUrl?: string): string {
    const baseUrl = `${this.config.get('RENTER_APP_URL')}/auth/sign-in`;
    const params = new URLSearchParams();

    if (propertyCoverImageUrl) {
      params.append('coverImage', propertyCoverImageUrl);
    }

    if (autofillEmail) {
      params.append('email', autofillEmail);
    }

    const queryString = params.toString();
    return queryString ? `${baseUrl}?${queryString}` : baseUrl;
  }

  private getRenterActivationUrl(
    inviteToken: string,
    email: string,
    renterFullName: string,
    propertyCoverImageUrl: string,
  ): string {
    // using URLSearchParams prevents any issues with special characters in the url
    const params = new URLSearchParams({
      email,
      name: renterFullName,
      token: inviteToken,
      coverImage: propertyCoverImageUrl,
    });

    const url = `${this.config.get('RENTER_APP_URL')}/auth/activation?${params.toString()}`;

    // For devs local testing purposes, so you don't have to find code or links in DB
    if (this.config.get('IS_LOCALHOST')) {
      console.log('[Renter activation link]', url);
    }

    return url;
  }
}

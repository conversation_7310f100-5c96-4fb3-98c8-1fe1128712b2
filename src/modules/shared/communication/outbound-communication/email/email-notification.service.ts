import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { Property } from '../../../../investor/property/property/entities/property.entity';
import { PropertyLocation } from '../../../../investor/property/property/property-details/location/property-location.entity';
import { Renter } from '../../../../renter/renter/renter.entity';
import { EmailMetadata } from '../../conversation/entities/email.metadata.entity';
import { BrevoEmailCommunicationService } from './brevo/brevo-email-communication.service';
import { BrevoTemplates } from './brevo/brevo-templates.enum';
import { talloProductPricesMap } from '../../../checkout/tallo-product-prices.map';
import { TalloProductsEnum } from '../../../checkout/tallo-products.enum';
import { SendGridEmailCommunicationService } from './sendgrid/sengrid-email-communication.service';
import { TourType } from '../../../../investor/showing/enums/tour-type.enum';

@Injectable()
export class EmailNotificationService {
  constructor(
    @Inject(ConfigService)
    private readonly config: ConfigService,
    private readonly brevoNotificationService: BrevoEmailCommunicationService,
    private readonly sendGridNotificationService: SendGridEmailCommunicationService,
  ) {}

  async sendEmailConfirmationCode(email: string, ownerFirstName: string, emailConfirmationCode: string): Promise<void> {
    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.INVESTOR_SIGN_UP_EMAIL_CONFIRMATION,
      { emailConfirmationCode, ownerFirstName },
    );
  }

  async sendOneTimeSignInCode(email: string, ownerFirstName: string, oneTimeCode: string): Promise<void> {
    await this.brevoNotificationService.sendEmailFromTemplate(email, BrevoTemplates.INVESTOR_SIGN_IN_ONE_TIME_CODE, {
      oneTimeCode,
      ownerFirstName,
    });
  }

  async sendPasswordResetLink(resetLink: string, email: string, userFirstName: string): Promise<void> {
    const params = {
      userFirstName,
      resetLink,
    };

    await this.brevoNotificationService.sendEmailFromTemplate(email, BrevoTemplates.RESET_PASSWORD, params);
  }

  async sendApplicationInviteToNonActivatedPrimaryApplicant(
    email: string,
    renterFirstName: string,
    ownerName: string,
    propertyLocation: PropertyLocation,
    invitationLink: string,
  ): Promise<void> {
    const params = {
      renterFirstName,
      ownerName,
      invitationLink,
      propertyAddress: propertyLocation.address,
      propertyAddressFull: this.buildPropertyAddressForEmail(propertyLocation),
      applicationFullPackagePrice: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_FULL_PACKAGE),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.RENTER_APPLICATION_INVITE_NON_ACTIVATED_PRIMARY_APPLICANT,
      params,
    );
  }

  async sendApplicationInviteToActivatedPrimaryApplicant(
    email: string,
    renterFirstName: string,
    ownerName: string,
    propertyLocation: PropertyLocation,
    invitationLink: string,
  ): Promise<void> {
    const params = {
      renterFirstName,
      ownerName,
      invitationLink,
      propertyAddress: propertyLocation.address,
      propertyAddressFull: this.buildPropertyAddressForEmail(propertyLocation),
      applicationFullPackagePrice: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_FULL_PACKAGE),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.RENTER_APPLICATION_INVITE_ACTIVATED_PRIMARY_APPLICANT,
      params,
    );
  }

  async sendApplicationInviteToNonActivatedCoApplicant(
    email: string,
    coApplicantFirstName: string,
    primaryApplicantName: string,
    propertyLocation: PropertyLocation,
    invitationLink: string,
  ): Promise<void> {
    const params = {
      renterFirstName: coApplicantFirstName,
      primaryApplicantName,
      invitationLink,
      propertyAddress: propertyLocation.address,
      propertyAddressFull: this.buildPropertyAddressForEmail(propertyLocation),
      applicationFullPackagePrice: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_FULL_PACKAGE),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.RENTER_APPLICATION_INVITE_NON_ACTIVATED_CO_APPLICANT,
      params,
    );
  }

  async sendApplicationInviteToActivatedCoApplicant(
    email: string,
    coApplicantFirstName: string,
    primaryApplicantName: string,
    propertyLocation: PropertyLocation,
    invitationLink: string,
  ): Promise<void> {
    const params = {
      renterFirstName: coApplicantFirstName,
      primaryApplicantName,
      invitationLink,
      propertyAddress: propertyLocation.address,
      propertyAddressFull: this.buildPropertyAddressForEmail(propertyLocation),
      applicationFullPackagePrice: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_FULL_PACKAGE),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.RENTER_APPLICATION_INVITE_ACTIVATED_CO_APPLICANT,
      params,
    );
  }

  async sendRenterWantsToApplyNotification(
    email: string,
    text: string,
    recipientName: string,
    emailMetadata: Partial<EmailMetadata>,
  ): Promise<void> {
    // TODO replace with template
    await this.sendGridNotificationService.sendMessage(email, text, recipientName, emailMetadata);
  }

  async sendApplicationInviteToNonActivatedCoSigner(
    email: string,
    coSignerFirstName: string,
    inviterUserName: string,
    propertyLocation: PropertyLocation,
    invitationLink: string,
  ): Promise<void> {
    const params = {
      renterFirstName: coSignerFirstName,
      inviterUserName,
      invitationLink,
      propertyAddress: propertyLocation.address,
      applicationBasePackagePrice: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_BASE_PACKAGE),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.RENTER_APPLICATION_INVITE_NON_ACTIVATED_CO_SIGNER,
      params,
    );
  }

  async sendApplicationInviteToActivatedCoSigner(
    email: string,
    coSignerFirstName: string,
    inviterUserName: string,
    propertyLocation: PropertyLocation,
    invitationLink: string,
  ): Promise<void> {
    const params = {
      renterFirstName: coSignerFirstName,
      inviterUserName,
      invitationLink,
      propertyAddress: propertyLocation.address,
      applicationBasePackagePrice: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_BASE_PACKAGE),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.RENTER_APPLICATION_INVITE_ACTIVATED_CO_SIGNER,
      params,
    );
  }

  async sendApplicationSubmittedNotification(
    email: string,
    ownerFirstName: string,
    renterName: string,
    renterProfileUrl: string,
    location: PropertyLocation,
  ): Promise<void> {
    const params = {
      ownerFirstName,
      renterProfileUrl,
      renterName,
      propertyAddress: location.address,
      propertyAddressFull: this.buildPropertyAddressForEmail(location),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.INVESTOR_APPLICATION_SUBMITTED,
      params,
    );
  }

  async sendApplicationDeclinedNotification(
    email: string,
    renterFirstName: string,
    location: PropertyLocation,
  ): Promise<void> {
    const params = {
      renterFirstName,
      propertyAddress: location.address,
      propertyAddressFull: this.buildPropertyAddressForEmail(location),
      applicationFullPackagePrice: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_FULL_PACKAGE),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.RENTER_APPLICATION_DECLINED,
      params,
    );
  }

  async sendManualTransUnionVerificationIsCompletedNotification(
    email: string,
    renterFirstName: string,
    continueApplicationUrl: string,
    location: PropertyLocation,
  ): Promise<void> {
    const params = {
      renterFirstName,
      continueApplicationUrl,
      propertyAddress: location.address,
      propertyAddressFull: this.buildPropertyAddressForEmail(location),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.RENTER_TU_VERIFICATION_SUCCESS,
      params,
    );
  }

  async sendBundleIsReadyForSubmissionNotification(
    email: string,
    renterFirstName: string,
    submitBundleUrl: string,
    location: PropertyLocation,
  ): Promise<void> {
    const params = {
      renterFirstName,
      submitBundleUrl,
      propertyAddress: location.address,
      propertyAddressFull: this.buildPropertyAddressForEmail(location),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.RENTER_BUNDLE_IS_READY_FOR_SUBMISSION,
      params,
    );
  }

  async sendNewPropertyQuestionNotification(
    propertyQuestionUrl: string,
    email: string,
    ownerFirstName: string,
    renter: Renter,
    location: PropertyLocation,
  ): Promise<void> {
    const params = {
      ownerFirstName,
      escalationUrl: propertyQuestionUrl,
      renterName: renter.user.name,
      propertyAddress: this.buildPropertyAddressForEmail(location),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.INVESTOR_NEW_PROPERTY_QUESTION,
      params,
    );
  }

  async sendNewShowingRequestNotification(
    property: Property,
    location: PropertyLocation,
    email: string,
    ownerFirstName: string,
    showingDate: string,
    renter: Renter,
    tourType: TourType,
  ): Promise<void> {
    const renterProfileUrl = `${this.config.get('INVESTOR_APP_URL')}/property/${property.id}/renter/profile/${renter.id}`;
    const showingRequestsUrl = `${this.config.get('INVESTOR_APP_URL')}/property/${property.id}/dashboard#showing-requests-section`;
    const tourTypeText = tourType === TourType.VIRTUAL ? 'virtual tour' : 'showing';

    const params = {
      ownerFirstName,
      renterProfileUrl,
      showingsPageUrl: showingRequestsUrl,
      showingDate,
      renterName: renter.user.name,
      propertyAddress: this.buildPropertyAddressForEmail(location),
      tourTypeText,
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.INVESTOR_NEW_SHOWING_REQUEST,
      params,
    );
  }

  async sendShowingRequestRescheduledNotification(
    email: string,
    ownerFirstName: string,
    renterName: string,
    showingDate: string,
    showingId: string,
    location: PropertyLocation,
  ): Promise<void> {
    const params = {
      ownerFirstName,
      renterName,
      showingDate,
      propertyAddress: this.buildPropertyAddressForEmail(location),
      showingUrl: `${this.config.get('INVESTOR_APP_URL')}/property/showings/${showingId}`,
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.INVESTOR_SHOWING_REQUEST_RESCHEDULED,
      params,
    );
  }

  async sendRenterCancelledShowingNotification(
    propertyId: string,
    email: string,
    ownerFirstName: string,
    renter: Renter,
    showingDate: string,
    propertyAddress: string,
  ): Promise<void> {
    const params = {
      ownerFirstName,
      renterProfileUrl: `${this.config.get('INVESTOR_APP_URL')}/property/${propertyId}/renter/profile/${renter.id}`,
      renterName: renter.user.name,
      showingDate,
      propertyAddress,
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      email,
      BrevoTemplates.INVESTOR_RENTER_CANCELLED_SHOWING,
      params,
    );
  }

  async sendUpcomingShowingReminderToInvestor(
    ownerEmail: string,
    ownerFirstName: string,
    showingDate: string,
    showingUrl: string,
    location: PropertyLocation,
    tourType: TourType,
  ): Promise<void> {
    const tourTypeText = tourType === TourType.VIRTUAL ? 'virtual tour' : 'property showing';
    const params = {
      ownerFirstName,
      showingDate,
      propertyAddress: this.buildPropertyAddressForEmail(location),
      showingUrl,
      tourTypeText,
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      ownerEmail,
      BrevoTemplates.INVESTOR_UPCOMING_SHOWING_REMINDER,
      params,
    );
  }

  async sendPendingShowingRequestReminderToInvestor(
    ownerEmail: string,
    ownerFirstName: string,
    renterProfileUrl: string,
    location: PropertyLocation,
  ): Promise<void> {
    const params = {
      ownerFirstName,
      renterProfileUrl,
      propertyAddress: this.buildPropertyAddressForEmail(location),
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      ownerEmail,
      BrevoTemplates.INVESTOR_PENDING_SHOWING_REQUEST_REMINDER,
      params,
    );
  }

  async sendPendingShowingRequestExpiredNotification(
    ownerEmail: string,
    ownerFirstName: string,
    renterProfileUrl: string,
    propertyAddress: string,
  ): Promise<void> {
    const params = {
      ownerFirstName,
      renterProfileUrl,
      propertyAddress,
    };

    await this.brevoNotificationService.sendEmailFromTemplate(
      ownerEmail,
      BrevoTemplates.INVESTOR_PENDING_SHOWING_REQUEST_EXPIRED_AND_CANCELED,
      params,
    );
  }

  async sendMessage(
    recipientEmail: string,
    emailText: string,
    recipientName: string,
    emailMetadata: EmailMetadata,
  ): Promise<void> {
    await this.sendGridNotificationService.sendMessage(recipientEmail, emailText, recipientName, emailMetadata);
  }

  private buildPropertyAddressForEmail(location: PropertyLocation): string {
    return `${location.address}, ${location.city}, ${location.state}, ${location.zip}`;
  }
}

import { Module } from '@nestjs/common';
import { EmailNotificationService } from './email-notification.service';
import { BrevoEmailCommunicationService } from './brevo/brevo-email-communication.service';
import { SendGridEmailCommunicationService } from './sendgrid/sengrid-email-communication.service';

@Module({
  providers: [EmailNotificationService, BrevoEmailCommunicationService, SendGridEmailCommunicationService],
  exports: [EmailNotificationService],
})
export class EmailCommunicationModule {}

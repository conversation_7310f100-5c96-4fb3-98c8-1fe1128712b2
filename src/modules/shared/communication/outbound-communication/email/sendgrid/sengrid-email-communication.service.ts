// sendgrid-email-communication.service.ts
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as sgMail from '@sendgrid/mail';
import { EmailCommunicationService } from '../email-communication-service.interface';
import { DateUtils } from '../../../../../../utils/date.utils';
import { EmailMetadata } from '../../../conversation/entities/email.metadata.entity';

@Injectable()
export class SendGridEmailCommunicationService implements EmailCommunicationService {
  constructor(private readonly config: ConfigService) {
    const sendgridApiKey = this.config.get('SENDGRID_API_KEY');
    sgMail.setApiKey(sendgridApiKey);
  }

  async sendMessage(
    recipientEmail: string,
    emailText: string,
    recipientName: string,
    emailMetadata: Partial<EmailMetadata>,
  ): Promise<void> {
    if (!this.isEmailSendingAvailable(recipientEmail)) {
      return;
    }

    const msg: sgMail.MailDataRequired = {
      to: {
        email: recipientEmail,
        name: recipientName,
      },
      from: {
        email: this.config.get('TALLO_SENDER_EMAIL'),
        name: this.config.get('TALLO_SENDER_NAME'),
      },
      subject: emailMetadata.subject,
      html: this.prepareEmailContent(emailText, recipientName, recipientEmail),
      replyTo: {
        email: this.config.get('TALLO_SENDER_EMAIL'),
        name: this.config.get('TALLO_SENDER_NAME'),
      },
      headers: {},
    };

    if (emailMetadata.lastMessageId) {
      msg.headers['In-Reply-To'] = emailMetadata.lastMessageId;
    }
    if (emailMetadata.references) {
      msg.headers['References'] = emailMetadata.references.join(' ');
    }

    try {
      await sgMail.send(msg);
    } catch (e) {
      console.error('Failed to send email with SendGrid', e);
    }
  }

  async sendEmailFromTemplate(
    email: string,
    templateId: string | number,
    params?: Record<string, unknown>,
  ): Promise<void> {
    if (!this.isEmailSendingAvailable(email)) {
      return;
    }

    const msg: sgMail.MailDataRequired = {
      to: email,
      from: {
        email: this.config.get('TALLO_SENDER_EMAIL'),
        name: this.config.get('TALLO_SENDER_NAME'),
      },
      templateId: typeof templateId === 'string' ? templateId : templateId.toString(),
      dynamicTemplateData: params || {},
    };

    try {
      await sgMail.send(msg);
    } catch (e) {
      console.error('Failed to send email template with SendGrid', e);
    }
  }

  private isEmailSendingAvailable(recipientEmail: string): boolean {
    if (!this.config.get('EMAIL_NOTIFICATIONS_ACTIVE')) {
      return false;
    }

    if (this.config.get('IS_PRODUCTION')) {
      return true;
    }

    return this.isEmailWhiteListed(recipientEmail);
  }

  private isEmailWhiteListed(email: string): boolean {
    const allowedDomains = this.config.get('EMAIL_WHITELIST_DOMAINS') || [];
    const allowedEmails = this.config.get('EMAIL_WHITELIST_ADDRESSES') || [];
    const [localPart, domain] = email.toLowerCase().split('@');

    if (allowedDomains.includes(domain)) {
      return true;
    }

    const baseEmail = localPart.split('+')[0] + '@' + domain;

    return allowedEmails.some((allowedEmail: string) => allowedEmail.toLowerCase() === baseEmail);
  }

  private prepareEmailContent(
    emailText: string,
    recipientName: string,
    recipientAddress: string,
    previousText?: string,
  ): string {
    const formattedEmailText = emailText.replace(/\n/g, '<br>').replace(/  /g, '&nbsp;&nbsp;');

    let fullHtmlContent = '<html lang="en"><body>';
    fullHtmlContent += `<p>${formattedEmailText}</p><br>`;

    if (previousText) {
      const timestamp = DateUtils.formatDateToGmailFormat(new Date());
      const quotedPreviousText = previousText
        .split('\n')
        .map((line) => `> ${line}`)
        .join('<br>');
      fullHtmlContent += `<br>On ${timestamp} ${recipientName} &lt;${recipientAddress}&gt; wrote:<br>`;
      fullHtmlContent += '<blockquote style="border-left: 1px solid #ccc; margin-left: 1em; padding-left: 1em;">';
      fullHtmlContent += quotedPreviousText;
      fullHtmlContent += '</blockquote>';
    }

    fullHtmlContent += '</body></html>';

    return fullHtmlContent;
  }
}

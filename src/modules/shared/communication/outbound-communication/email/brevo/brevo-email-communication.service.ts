import * as SibApiV3Sdk from 'sib-api-v3-sdk';

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { DateUtils } from '../../../../../../utils/date.utils';
import { EmailMetadata } from '../../../conversation/entities/email.metadata.entity';
import { BrevoTemplates } from './brevo-templates.enum';

@Injectable()
export class BrevoEmailCommunicationService {
  private readonly transactionalEmailsApiInstance: typeof SibApiV3Sdk.TransactionalEmailsApi;

  constructor(private readonly config: ConfigService) {
    const defaultClient = SibApiV3Sdk.ApiClient.instance;

    // Configure API key authorization: api-key
    const apiKeyAuth = defaultClient.authentications['api-key'];
    apiKeyAuth.apiKey = this.config.get('BREVO_API_KEY');

    this.transactionalEmailsApiInstance = new SibApiV3Sdk.TransactionalEmailsApi();
  }

  async sendMessage(
    recipientEmail: string,
    emailText: string,
    recipientName: string,
    emailMetadata: EmailMetadata,
  ): Promise<void> {
    const smtpEmail = this.prepareSmtpEmail(emailMetadata, recipientEmail, recipientName);
    smtpEmail.htmlContent = this.prepareEmailContent(emailText, recipientName, recipientEmail);

    if (this.isEmailSendingAvailable(recipientEmail)) {
      try {
        this.transactionalEmailsApiInstance.sendTransacEmail(smtpEmail);
      } catch (e) {
        console.error('Failed to send email', e);
      }
    }
  }

  private prepareSmtpEmail(
    emailMetadata: EmailMetadata,
    recipientAddress: string,
    recipientName: string,
  ): SibApiV3Sdk.SendSmtpEmail {
    const smtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    smtpEmail.subject = emailMetadata.subject;

    smtpEmail.sender = {
      name: this.config.get('TALLO_SENDER_NAME'),
      email: this.config.get('TALLO_SENDER_EMAIL'),
    };
    smtpEmail.to = [
      {
        email: recipientAddress,
        name: recipientName,
      },
    ];

    smtpEmail.replyTo = {
      name: this.config.get('TALLO_SENDER_NAME'),
      email: this.config.get('TALLO_SENDER_EMAIL'),
    };

    if (emailMetadata.lastMessageId || emailMetadata.references) {
      smtpEmail.headers = {};
      if (emailMetadata.lastMessageId) {
        smtpEmail.headers['In-Reply-To'] = emailMetadata.lastMessageId;
      }
      if (emailMetadata.references) {
        smtpEmail.headers['References'] = emailMetadata.references.join(' ');
      }
    }

    return smtpEmail;
  }

  private prepareEmailContent(
    emailText: string,
    recipientName: string,
    recipientAddress: string,
    previousText?: string,
  ): string {
    // Replace new lines with <br> and spaces with &nbsp;
    const formattedEmailText = emailText.replace(/\n/g, '<br>').replace(/  /g, '&nbsp;&nbsp;');

    let fullHtmlContent = '<html lang="en"><body>';
    fullHtmlContent += `<p>${formattedEmailText}</p><br>`;

    if (previousText) {
      const timestamp = DateUtils.formatDateToGmailFormat(new Date());
      const quotedPreviousText = previousText
        .split('\n')
        .map((line) => `> ${line}`)
        .join('<br>');
      fullHtmlContent += `<br>On ${timestamp} ${recipientName} <${recipientAddress}> wrote:<br>`;
      fullHtmlContent += '<blockquote style="border-left: 1px solid #ccc; margin-left: 1em; padding-left: 1em;">';
      fullHtmlContent += quotedPreviousText;
      fullHtmlContent += '</blockquote>';
    }

    fullHtmlContent += '</body></html>';

    return fullHtmlContent;
  }

  async sendEmailFromTemplate(email: string, templateId: BrevoTemplates, params?: Record<string, unknown>) {
    const sendSmtpEmail = {
      to: [
        {
          email,
        },
      ],
      templateId,
      params,
    };

    if (this.isEmailSendingAvailable(email)) {
      try {
        this.transactionalEmailsApiInstance.sendTransacEmail(sendSmtpEmail);
      } catch (e) {
        console.error('Failed to send email', e);
      }
    }
  }

  private isEmailSendingAvailable(recipientEmail: string): boolean {
    if (!this.config.get('EMAIL_NOTIFICATIONS_ACTIVE')) {
      return false;
    }

    if (this.config.get('IS_PRODUCTION')) {
      return true;
    }

    return this.isEmailWhiteListed(recipientEmail);
  }

  private isEmailWhiteListed(email: string): boolean {
    const allowedDomains = this.config.get('EMAIL_WHITELIST_DOMAINS') || [];
    const [localPart, domain] = email.toLowerCase().split('@');

    if (allowedDomains.includes(domain)) {
      return true;
    }

    // For specific email whitelist, handle Gmail + suffixes
    const allowedEmails = this.config.get('EMAIL_WHITELIST_ADDRESSES') || [];
    const baseEmail = localPart.split('+')[0] + '@' + domain;

    // Check if base email is in allowed list
    return allowedEmails.some((allowedEmail) => allowedEmail.toLowerCase() === baseEmail);
  }
}

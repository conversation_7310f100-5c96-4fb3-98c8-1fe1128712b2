/**
 * First word is the user type that the email is sent to (INVESTOR, RENTER, etc.)
 */
export enum BrevoTemplates {
  INVESTOR_NEW_SHOWING_REQUEST = 13,
  INVESTOR_SIGN_UP_EMAIL_CONFIRMATION = 14,
  INVESTOR_SIGN_IN_ONE_TIME_CODE = 15,
  INVESTOR_SHOWING_REQUEST_RESCHEDULED = 16,
  INVESTOR_RENTER_CANCELLED_SHOWING = 17,
  INVESTOR_NEW_PROPERTY_QUESTION = 19,
  INVESTOR_UPCOMING_SHOWING_REMINDER = 20,
  INVESTOR_PENDING_SHOWING_REQUEST_REMINDER = 21,
  INVESTOR_PENDING_SHOWING_REQUEST_EXPIRED_AND_CANCELED = 22,
  INVESTOR_APPLICATION_SUBMITTED = 30,

  RESET_PASSWORD = 18,

  RENTER_APPLICATION_INVITE_NON_ACTIVATED_PRIMARY_APPLICANT = 23,
  RENTER_APPLICATION_INVITE_ACTIVATED_PRIMARY_APPLICANT = 24,
  RENTER_APPLICATION_INVITE_NON_ACTIVATED_CO_APPLICANT = 27,
  RENTER_APPLICATION_INVITE_ACTIVATED_CO_APPLICANT = 25,
  RENTER_APPLICATION_INVITE_NON_ACTIVATED_CO_SIGNER = 29,
  RENTER_APPLICATION_INVITE_ACTIVATED_CO_SIGNER = 28,
  RENTER_APPLICATION_DECLINED = 31,
  RENTER_TU_VERIFICATION_SUCCESS = 32,
  RENTER_BUNDLE_IS_READY_FOR_SUBMISSION = 34,
}

import { Module } from '@nestjs/common';
import { OutboundCommunicationService } from './outbound-communication.service';
import { SmsCommunicationModule } from './sms/sms-communication.module';
import { EmailCommunicationModule } from './email/email-communication.module';

@Module({
  imports: [EmailCommunicationModule, SmsCommunicationModule],
  providers: [OutboundCommunicationService],
  exports: [OutboundCommunicationService],
})
export class OutboundCommunicationModule {}

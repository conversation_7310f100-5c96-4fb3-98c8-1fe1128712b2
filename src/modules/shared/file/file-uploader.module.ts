import { Module } from '@nestjs/common';
import { S3Module } from './s3/s3.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FileService } from './file.service';
import { File } from './entities/file.entity';
import { ImageAnalysisModule } from './image-analysis/image-analysis.module';

@Module({
  imports: [TypeOrmModule.forFeature([File]), S3Module, ImageAnalysisModule],
  controllers: [],
  providers: [FileService],
  exports: [FileService],
})
export class FileUploaderModule {}

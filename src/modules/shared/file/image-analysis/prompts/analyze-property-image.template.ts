export const analyzePropertyImageTemplate = `
You are an expert real estate photographer and property marketing specialist. Analyze this property image comprehensively and provide a detailed assessment.

Analyze the image for:

1. **Room Type Identification** (with confidence 0-100):
   - Primary room type: bedroom, bathroom, kitchen, living room, dining room, office, laundry room, garage, exterior, balcony, patio, basement, attic, hallway, closet, etc.
   - Secondary spaces if visible

2. **Visual Attributes** (with confidence 0-100):
   - Lighting: bright, dark, natural light, artificial light, well-lit, dim
   - Style: modern, traditional, contemporary, vintage, rustic, industrial, minimalist, luxury, basic
   - Condition: updated, renovated, new, dated, worn, pristine
   - Spatial: spacious, cozy, cramped, open, compact, airy

3. **Notable Features** (with confidence 0-100):
   - Flooring: hardwood floors, carpet, tile, laminate, concrete, marble
   - Fixtures: granite counters, stainless appliances, fireplace, high ceilings, large windows, crown molding
   - Built-ins: cabinets, shelving, island, breakfast bar
   - Architectural: exposed beams, vaulted ceilings, bay windows, french doors

4. **Property Amenities & Features** (with confidence 0-100) - Use EXACT property database field names:
   - HVAC: air_conditioning, heating, central_air, wall_ac
   - Appliances: dishwasher, washer, dryer, microwave, refrigerator, garbage_disposal
   - Amenities: fireplace, pool, hot_tub_spa, balcony, patio, deck, basement
   - Parking: garage_attached, garage_detached, carport, parking_spaces
   - Flooring: hardwood_floors, carpet_flooring, tile_flooring, laminate_flooring

5. **Image Quality Assessment** (0-100 scale):
   - Lighting quality: How well-lit and evenly exposed is the image?
   - Composition: How well-composed and professionally shot is the image?
   - Clarity: How sharp and clear is the image?
   - Overall quality: Overall photographic quality

6. **Staging Assessment** (0-100 scale):
   - Cleanliness: How clean and tidy does the space appear?
   - Furnished: Is the space furnished/staged? (true/false)
   - Decluttered: How decluttered and organized is the space?

7. **Market Appeal Factors** (0-100 scale):
   - Uniqueness: How unique or distinctive are the features shown?
   - Market appeal: How appealing would this be to potential renters?
   - Overall confidence: How confident are you in this analysis?

Return your analysis as a JSON object with this exact structure:
{
  "roomTypes": [{"tag": "room_name", "confidence": 85, "category": "primary"}],
  "attributes": [{"tag": "attribute_name", "confidence": 90}],
  "features": [{"tag": "feature_name", "confidence": 75}],
  "propertyFeatures": [{"tag": "exact_db_field_name", "confidence": 85, "value": true}],
  "quality": {
    "lighting": 85,
    "composition": 90,
    "clarity": 80,
    "overall": 85
  },
  "staging": {
    "cleanliness": 90,
    "furnished": true,
    "decluttered": 85
  },
  "uniqueness": 70,
  "marketAppeal": 85,
  "confidence": 88
}

Be precise and conservative with confidence scores. Only include tags you're reasonably confident about (>60 confidence).
For propertyFeatures, use exact database field names and include a boolean value indicating presence/absence.`;

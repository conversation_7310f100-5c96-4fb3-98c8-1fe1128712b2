import { Test, TestingModule } from '@nestjs/testing';
import { ImageAnalysisService } from './image-analysis.service';
import { AiService } from '../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../ai/enums/language-models.enum';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Conversation } from '../../../shared/communication/conversation/entities/conversation.entity';
import { Message } from '../../../shared/communication/conversation/message/message.entity';

describe('ImageAnalysisService', () => {
  let service: ImageAnalysisService;
  let aiService: jest.Mocked<AiService>;

  beforeEach(async () => {
    const mockAiService = {
      analyzeImageWithVision: jest.fn(),
    };

    const mockConversationRepository = {
      find: jest.fn(),
      findOne: jest.fn(),
      save: jest.fn(),
      create: jest.fn(),
    };

    const mockMessageRepository = {
      find: jest.fn(),
      findOne: jest.fn(),
      save: jest.fn(),
      create: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImageAnalysisService,
        {
          provide: AiService,
          useValue: mockAiService,
        },
        {
          provide: getRepositoryToken(Conversation),
          useValue: mockConversationRepository,
        },
        {
          provide: getRepositoryToken(Message),
          useValue: mockMessageRepository,
        },
      ],
    }).compile();

    service = module.get<ImageAnalysisService>(ImageAnalysisService);
    aiService = module.get(AiService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return empty array for non-image files', async () => {
    const buffer = Buffer.from('test');
    const result = await service.analyzeImage(buffer, 'application/pdf');

    expect(result).toEqual({ tags: [] });
    expect(aiService.analyzeImageWithVision).not.toHaveBeenCalled();
  });

  it('should return empty array for unsupported image types', async () => {
    const buffer = Buffer.from('test');
    const result = await service.analyzeImage(buffer, 'image/svg+xml');

    expect(result).toEqual({ tags: [] });
    expect(aiService.analyzeImageWithVision).not.toHaveBeenCalled();
  });

  it('should analyze image and return tags', async () => {
    const mockResponse = JSON.stringify({
      roomTypes: [{ tag: 'bedroom', confidence: 85 }],
      attributes: [
        { tag: 'bright', confidence: 80 },
        { tag: 'modern', confidence: 90 },
      ],
      features: [
        { tag: 'hardwood floors', confidence: 75 },
        { tag: 'large windows', confidence: 85 },
      ],
      propertyFeatures: [
        { tag: 'hasFireplace', confidence: 85, value: true },
        { tag: 'hasAirConditioning', confidence: 80, value: true },
      ],
      quality: { lighting: 85, composition: 80, clarity: 90, overall: 85 },
      staging: { cleanliness: 90, furnished: true, decluttered: 85 },
      uniqueness: 75,
      marketAppeal: 80,
      confidence: 85,
    });
    aiService.analyzeImageWithVision.mockResolvedValue(mockResponse);

    const buffer = Buffer.from('fake-image-data');
    const result = await service.analyzeImage(buffer, 'image/jpeg');

    expect(result.tags).toEqual([
      'bedroom',
      'bright',
      'modern',
      'hardwood floors',
      'large windows',
      'hasfireplace',
      'hasairconditioning',
    ]);
    expect(result.propertyFeatures).toEqual([
      { tag: 'hasfireplace', confidence: 85, value: true },
      { tag: 'hasairconditioning', confidence: 80, value: true },
    ]);
    expect(result.qualityLighting).toBe(85);
    expect(result.stagingFurnished).toBe(true);
    expect(result.analysisVersion).toBe('2.1');
    expect(aiService.analyzeImageWithVision).toHaveBeenCalledWith(
      expect.stringContaining('data:image/jpeg;base64,'),
      expect.stringContaining('Analyze this property image'),
      LanguageModelsEnum.GPT_4,
    );
  });

  it('should handle AI service errors gracefully', async () => {
    aiService.analyzeImageWithVision.mockRejectedValue(new Error('AI service error'));

    const buffer = Buffer.from('fake-image-data');
    const result = await service.analyzeImage(buffer, 'image/jpeg');

    expect(result).toEqual({ tags: [] });
  });

  it('should limit tags to 10 and filter out invalid ones', async () => {
    const mockResponse = JSON.stringify({
      roomTypes: [{ tag: 'bedroom', confidence: 85 }],
      attributes: [
        { tag: 'bright', confidence: 80 },
        { tag: 'modern', confidence: 90 },
        { tag: 'spacious', confidence: 85 },
        { tag: 'updated', confidence: 80 },
        { tag: 'luxury', confidence: 85 },
      ],
      features: [
        { tag: 'hardwood floors', confidence: 75 },
        { tag: 'large windows', confidence: 85 },
        { tag: 'granite counters', confidence: 80 },
        { tag: 'stainless appliances', confidence: 85 },
        { tag: 'fireplace', confidence: 75 },
        { tag: 'high ceilings', confidence: 80 },
      ],
      propertyFeatures: [
        { tag: 'hasFireplace', confidence: 85, value: true },
        { tag: 'hasAirConditioning', confidence: 80, value: true },
      ],
      quality: { lighting: 85, composition: 80, clarity: 90, overall: 85 },
      staging: { cleanliness: 90, furnished: true, decluttered: 85 },
      uniqueness: 75,
      marketAppeal: 80,
      confidence: 85,
    });
    aiService.analyzeImageWithVision.mockResolvedValue(mockResponse);

    const buffer = Buffer.from('fake-image-data');
    const result = await service.analyzeImage(buffer, 'image/jpeg');

    // Should limit to 10 tags total (including propertyFeatures added to tags)
    expect(result.tags).toHaveLength(10);
    expect(result.tags).toEqual([
      'bedroom',
      'bright',
      'modern',
      'spacious',
      'updated',
      'luxury',
      'hardwood floors',
      'large windows',
      'granite counters',
      'stainless appliances',
    ]);
    expect(result.propertyFeatures).toEqual([
      { tag: 'hasfireplace', confidence: 85, value: true },
      { tag: 'hasairconditioning', confidence: 80, value: true },
    ]);
    expect(result.analysisVersion).toBe('2.1');
  });

  it('should clean up response with Tags: prefix', async () => {
    const mockResponse = 'Tags: bedroom, bright, modern';
    aiService.analyzeImageWithVision.mockResolvedValue(mockResponse);

    const buffer = Buffer.from('fake-image-data');
    const result = await service.analyzeImage(buffer, 'image/jpeg');

    expect(result).toEqual({ tags: [] });
  });
});

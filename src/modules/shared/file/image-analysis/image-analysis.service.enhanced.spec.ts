import { Test, TestingModule } from '@nestjs/testing';
import { ImageAnalysisService } from './image-analysis.service';
import { AiService } from '../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../ai/enums/language-models.enum';

describe('ImageAnalysisService - Enhanced Analysis', () => {
  let service: ImageAnalysisService;
  let aiService: jest.Mocked<AiService>;

  beforeEach(async () => {
    const mockAiService = {
      analyzeImageWithVision: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImageAnalysisService,
        {
          provide: AiService,
          useValue: mockAiService,
        },
      ],
    }).compile();

    service = module.get<ImageAnalysisService>(ImageAnalysisService);
    aiService = module.get(AiService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should analyze image with enhanced analysis and return structured data', async () => {
    const mockResponse = JSON.stringify({
      roomTypes: [
        { tag: 'living room', confidence: 95, category: 'primary' },
        { tag: 'kitchen', confidence: 30, category: 'secondary' },
      ],
      attributes: [
        { tag: 'modern', confidence: 85 },
        { tag: 'bright', confidence: 90 },
        { tag: 'spacious', confidence: 75 },
      ],
      features: [
        { tag: 'hardwood floors', confidence: 80 },
        { tag: 'large windows', confidence: 85 },
      ],
      quality: {
        lighting: 85,
        composition: 90,
        clarity: 80,
        overall: 85,
      },
      staging: {
        cleanliness: 90,
        furnished: true,
        decluttered: 85,
      },
      uniqueness: 70,
      marketAppeal: 85,
      confidence: 88,
    });

    aiService.analyzeImageWithVision.mockResolvedValue(mockResponse);

    const buffer = Buffer.from('fake-image-data');
    const result = await service.analyzeImage(buffer, 'image/jpeg');

    expect(result.tags).toEqual(['living room', 'modern', 'bright', 'spacious', 'hardwood floors', 'large windows']);
    expect(result.qualityLighting).toBe(85);
    expect(result.qualityComposition).toBe(90);
    expect(result.qualityClarity).toBe(80);
    expect(result.qualityOverall).toBe(85);
    expect(result.stagingCleanliness).toBe(90);
    expect(result.stagingFurnished).toBe(true);
    expect(result.stagingDecluttered).toBe(85);
    expect(result.uniqueness).toBe(70);
    expect(result.marketAppeal).toBe(85);
    expect(result.analysisConfidence).toBe(88);
    expect(result.analysisVersion).toBe('2.1');

    expect(aiService.analyzeImageWithVision).toHaveBeenCalledWith(
      expect.stringContaining('data:image/jpeg;base64,'),
      expect.stringContaining('You are an expert real estate photographer'),
      LanguageModelsEnum.GPT_4,
    );
  });

  it('should filter out low confidence tags', async () => {
    const mockResponse = JSON.stringify({
      roomTypes: [
        { tag: 'living room', confidence: 95 },
        { tag: 'kitchen', confidence: 50 }, // Below 70 threshold
      ],
      attributes: [
        { tag: 'modern', confidence: 85 },
        { tag: 'dark', confidence: 60 }, // Below 75 threshold for attributes
      ],
      features: [
        { tag: 'hardwood floors', confidence: 80 },
        { tag: 'carpet', confidence: 65 }, // Below 70 threshold
      ],
      quality: { lighting: 85, composition: 90, clarity: 80, overall: 85 },
      staging: { cleanliness: 90, furnished: true, decluttered: 85 },
      uniqueness: 70,
      marketAppeal: 85,
      confidence: 88,
    });

    aiService.analyzeImageWithVision.mockResolvedValue(mockResponse);

    const buffer = Buffer.from('fake-image-data');
    const result = await service.analyzeImage(buffer, 'image/jpeg');

    // Should only include high-confidence tags
    expect(result.tags).toEqual(['living room', 'modern', 'hardwood floors']);
    expect(result.tags).not.toContain('kitchen');
    expect(result.tags).not.toContain('dark');
    expect(result.tags).not.toContain('carpet');
  });

  it('should handle malformed JSON response gracefully', async () => {
    const mockResponse = 'Invalid JSON response';
    aiService.analyzeImageWithVision.mockResolvedValue(mockResponse);

    const buffer = Buffer.from('fake-image-data');
    const result = await service.analyzeImage(buffer, 'image/jpeg');

    // Should fallback to basic parsing
    expect(result.tags).toEqual([]);
    expect(result.analysisVersion).toBeUndefined();
  });

  it('should clamp scores to valid ranges', async () => {
    const mockResponse = JSON.stringify({
      roomTypes: [{ tag: 'living room', confidence: 95 }],
      attributes: [],
      features: [],
      quality: {
        lighting: 150, // Above 100
        composition: -10, // Below 0
        clarity: 80,
        overall: 85,
      },
      staging: {
        cleanliness: 200, // Above 100
        furnished: true,
        decluttered: -5, // Below 0
      },
      uniqueness: 120, // Above 100
      marketAppeal: -20, // Below 0
      confidence: 88,
    });

    aiService.analyzeImageWithVision.mockResolvedValue(mockResponse);

    const buffer = Buffer.from('fake-image-data');
    const result = await service.analyzeImage(buffer, 'image/jpeg');

    expect(result.qualityLighting).toBe(100); // Clamped to max
    expect(result.qualityComposition).toBe(0); // Clamped to min
    expect(result.stagingCleanliness).toBe(100); // Clamped to max
    expect(result.stagingDecluttered).toBe(0); // Clamped to min
    expect(result.uniqueness).toBe(100); // Clamped to max
    expect(result.marketAppeal).toBe(0); // Clamped to min
  });

  it('should handle AI service errors gracefully', async () => {
    aiService.analyzeImageWithVision.mockRejectedValue(new Error('AI service error'));

    const buffer = Buffer.from('fake-image-data');
    const result = await service.analyzeImage(buffer, 'image/jpeg');

    expect(result.tags).toEqual([]);
    expect(result.analysisVersion).toBeUndefined();
  });
});

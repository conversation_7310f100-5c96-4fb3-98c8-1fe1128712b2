import { Injectable } from '@nestjs/common';
import { AiService } from '../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../ai/enums/language-models.enum';
import { analyzePropertyImageTemplate } from './prompts/analyze-property-image.template';
import { ImageAnalysis } from './interfaces/image-analysis.intefrace';

@Injectable()
export class ImageAnalysisService {
  constructor(private readonly aiService: AiService) {}

  async analyzeImage(imageBuffer: Buffer, mimeType: string): Promise<ImageAnalysis> {
    if (!this.isAnalyzableImageType(mimeType)) {
      return { tags: [] };
    }

    try {
      const base64Image = imageBuffer.toString('base64');
      const dataUrl = `data:${mimeType};base64,${base64Image}`;

      const response = await this.aiService.analyzeImageWithVision(
        dataUrl,
        analyzePropertyImageTemplate,
        LanguageModelsEnum.GPT_4,
      );

      return this.parseEnhancedResponse(response);
    } catch (error) {
      console.error('Error analyzing image:', error);
      return { tags: [] };
    }
  }

  private parseEnhancedResponse(response: string): ImageAnalysis {
    try {
      let cleanResponse = response.trim();

      const jsonMatch = cleanResponse.match(/\{[\s\S]*}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }

      const parsed = JSON.parse(cleanResponse);

      const allTags: string[] = [];

      // Room types (highest priority)
      if (parsed.roomTypes && Array.isArray(parsed.roomTypes)) {
        parsed.roomTypes.forEach((item: any) => {
          if (item.tag && item.confidence >= 70) {
            allTags.push(item.tag.toLowerCase().trim());
          }
        });
      }

      // Attributes
      if (parsed.attributes && Array.isArray(parsed.attributes)) {
        parsed.attributes.forEach((item: any) => {
          if (item.tag && item.confidence >= 75) {
            allTags.push(item.tag.toLowerCase().trim());
          }
        });
      }

      // Features
      if (parsed.features && Array.isArray(parsed.features)) {
        parsed.features.forEach((item: any) => {
          if (item.tag && item.confidence >= 70) {
            allTags.push(item.tag.toLowerCase().trim());
          }
        });
      }

      const propertyFeatures: Array<{ tag: string; confidence: number; value: boolean }> = [];
      if (parsed.propertyFeatures && Array.isArray(parsed.propertyFeatures)) {
        parsed.propertyFeatures.forEach((item: any) => {
          if (item.tag && item.confidence >= 70) {
            propertyFeatures.push({
              tag: item.tag.toLowerCase().trim(),
              confidence: item.confidence,
              value: Boolean(item.value),
            });
            allTags.push(item.tag.toLowerCase().trim());
          }
        });
      }

      // Remove duplicates and limit to 10 tags total
      const tags = [...new Set(allTags)].slice(0, 10);

      return {
        tags,
        propertyFeatures,
        qualityLighting: this.clampScore(parsed.quality?.lighting),
        qualityComposition: this.clampScore(parsed.quality?.composition),
        qualityClarity: this.clampScore(parsed.quality?.clarity),
        qualityOverall: this.clampScore(parsed.quality?.overall),
        stagingCleanliness: this.clampScore(parsed.staging?.cleanliness),
        stagingFurnished: Boolean(parsed.staging?.furnished),
        stagingDecluttered: this.clampScore(parsed.staging?.decluttered),
        uniqueness: this.clampScore(parsed.uniqueness),
        marketAppeal: this.clampScore(parsed.marketAppeal),
        analysisConfidence: this.clampScore(parsed.confidence),
        analysisVersion: '2.1',
      };
    } catch (error) {
      console.error('Error parsing enhanced analysis response:', error);
      return { tags: [] };
    }
  }

  private clampScore(score: any): number | undefined {
    if (typeof score !== 'number') return undefined;
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private isAnalyzableImageType(mimeType: string): boolean {
    const supportedImageTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
      'image/gif',
      'image/bmp',
      'image/tiff',
    ];

    return supportedImageTypes.includes(mimeType.toLowerCase());
  }
}

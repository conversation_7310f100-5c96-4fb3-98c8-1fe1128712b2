import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { FileDto } from '../models/file.dto';

@Entity()
export class File {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Expose()
  @Column({ type: 'varchar', length: 512 })
  url!: string;

  @Expose()
  @Column({ type: 'varchar', length: 512, nullable: true })
  thumbnailUrl: string;

  @Expose()
  @Column({ type: 'varchar', length: 512 })
  mimeType: string;

  @Expose()
  @Column({ type: 'int' })
  fileSize: number;

  @Expose()
  @Column({ type: 'varchar', length: 512 })
  fileName: string;

  @Expose()
  @Column({ type: 'int', default: 0 })
  order: number;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  static convertToDto(image: File): FileDto {
    return <FileDto>instanceToPlain(image, { excludeExtraneousValues: true });
  }
}

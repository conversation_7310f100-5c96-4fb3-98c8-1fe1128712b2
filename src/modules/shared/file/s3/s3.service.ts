import { Injectable } from '@nestjs/common';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import * as process from 'process';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class S3Service {
  private readonly s3: S3Client;
  private readonly environment;

  constructor(private readonly config: ConfigService) {
    this.environment = config.get('ENV');
    this.s3 = new S3Client({
      region: 'us-east-1',
      credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY_ID,
        secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
      },
    });
  }

  async uploadFile(buffer: Buffer | Uint8Array, mimeType: string, fileName: string): Promise<string> {
    const params = {
      Bucket: process.env.S3_TARGET_BUCKET,
      Key: `images/${this.environment}/${fileName}`,
      Body: buffer,
      ContentType: mimeType,
    };

    await this.s3.send(new PutObjectCommand(params));

    return `https://files.tallo.ai/images/${this.environment}/${fileName}`;
  }
}

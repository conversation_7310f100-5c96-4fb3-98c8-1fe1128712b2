import { Injectable } from '@nestjs/common';
import { Enhancer } from '../enhancer.interface';
import { S3Service } from '../../../file/s3/s3.service';

@Injectable()
export class LetsEnhanceIoEnhancerService implements Enhancer {
  constructor(private readonly s3Service: S3Service) {}
  async enhance(image: Buffer, mimeType: string, fileName: string): Promise<string> {
    return await this.s3Service.uploadFile(image, mimeType, fileName);
  }
}

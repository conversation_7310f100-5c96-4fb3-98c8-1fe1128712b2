import { Injectable } from '@nestjs/common';
import { Enhancer } from '../enhancer.interface';
import { S3Service } from '../../../file/s3/s3.service';
import { HttpService } from '@nestjs/axios';
import FormData from 'form-data';

@Injectable()
export class VanceAiEnhancerService implements Enhancer {
  readonly apiToken = 'ad739c28a7c62b3cf4c82a93205d0d01';
  constructor(
    private readonly s3Service: S3Service,
    private httpService: HttpService,
  ) {}
  async enhance(image: Buffer, mimeType: string, fileName: string): Promise<string> {
    const uploadedFile = await this.uploadImage(image, fileName);
    const enhancedFileUrl = await this.transformImage(uploadedFile.data.uid);
    await this.checkImageProgress(enhancedFileUrl.data.trans_id);
    const enhancedImage = await this.downloadImage(enhancedFileUrl.data.trans_id);
    return await this.s3Service.uploadFile(enhancedImage, mimeType, fileName);
  }

  async uploadImage(fileBuffer: Buffer, fileName: string): Promise<any> {
    const apiUrl = 'https://api-service.vanceai.com/web_api/v1/upload';

    const form = new FormData();
    form.append('api_token', this.apiToken);
    form.append('file', fileBuffer, { filename: fileName });

    const { data } = await this.httpService
      .post(apiUrl, form, {
        headers: form.getHeaders(),
      })
      .toPromise();

    return data;
  }

  async transformImage(uid: string): Promise<any> {
    const apiUrl = 'https://api-service.vanceai.com/web_api/v1/transform';

    const jconfig = {
      job: 'enlarge',
      config: {
        module: 'enlarge',
        module_params: {
          model_name: 'EnlargeStable',
          suppress_noise: 26,
          remove_blur: 26,
          scale: '2x',
        },
        out_params: {},
      },
    };

    const { data } = await this.httpService
      .post(apiUrl, null, {
        params: {
          api_token: this.apiToken,
          uid: uid,
          jconfig: JSON.stringify(jconfig),
        },
      })
      .toPromise();

    return data;
  }

  async checkImageProgress(transId: string): Promise<any> {
    const apiUrl = 'https://api-service.vanceai.com/web_api/v1/progress';

    const pollProgress = async (resolve: any, reject: any): Promise<void> => {
      try {
        const { data } = await this.httpService
          .post(apiUrl, null, {
            params: {
              api_token: this.apiToken,
              trans_id: transId,
            },
          })
          .toPromise();

        if (data.data.status === 'finish') {
          resolve(data);
        } else if (data.data.status === 'fatal') {
          reject(new Error(`Error processing image: ${data.error}`));
        } else {
          // Retry polling after a delay (e.g., 5 seconds)
          setTimeout(() => pollProgress(resolve, reject), 5000);
        }
      } catch (error) {
        reject(error);
      }
    };

    return new Promise((resolve, reject) => {
      pollProgress(resolve, reject);
    });
  }

  async downloadImage(transId: string): Promise<Buffer> {
    const apiUrl = 'https://api-service.vanceai.com/web_api/v1/download';

    const { data } = await this.httpService
      .post(apiUrl, null, {
        responseType: 'arraybuffer',
        params: {
          api_token: this.apiToken,
          trans_id: transId,
        },
      })
      .toPromise();

    return data;
  }
}

import { Modu<PERSON> } from '@nestjs/common';
import { ImageEnhancerService } from './image-enhancer.service';
import { VanceAiEnhancerService } from './enhancers/vance-ai-enhancer.service';
import { EnhancerController } from './enhancer.controller';
import { LetsEnhanceIoEnhancerService } from './enhancers/lets-enhance-io-enhancer.service';
import { EnhancerStrategiesEnum } from './enhancer-strategies.enum';
import { Enhancer } from './enhancer.interface';
import { HttpModule } from '@nestjs/axios';
import { S3Module } from '../../file/s3/s3.module';

@Module({
  providers: [
    ImageEnhancerService,
    LetsEnhanceIoEnhancerService,
    VanceAiEnhancerService,
    {
      provide: 'Enhancers',
      useFactory: (
        vanceAiEnhancerService: VanceAiEnhancerService,
        letsEnhanceIoEnhancerService: LetsEnhanceIoEnhancerService,
      ) => {
        const enhancers = new Map<EnhancerStrategiesEnum, Enhancer>();
        enhancers.set(EnhancerStrategiesEnum.Vance_Ai, vanceAiEnhancerService);
        enhancers.set(EnhancerStrategiesEnum.Lets_Enhance, letsEnhanceIoEnhancerService);

        return enhancers;
      },
      inject: [VanceAiEnhancerService, LetsEnhanceIoEnhancerService],
    },
  ],
  imports: [HttpModule, S3Module],
  controllers: [EnhancerController],
  exports: [ImageEnhancerService],
})
export class ImageEnhancerModule {}

import { Inject, Injectable } from '@nestjs/common';
import { Enhancer } from './enhancer.interface';
import { EnhancerStrategiesEnum } from './enhancer-strategies.enum';

@Injectable()
export class ImageEnhancerService {
  constructor(
    @Inject('Enhancers')
    private readonly enhancers: Map<EnhancerStrategiesEnum, Enhancer>,
  ) {}

  async enhance(
    image: Buffer,
    enhancerStrategy: EnhancerStrategiesEnum,
    mimeType: string,
    fileName: string,
  ): Promise<string> {
    return this.enhancers.get(enhancerStrategy).enhance(image, mimeType, fileName);
  }
}

import { Controller, Post, UploadedFile, UseInterceptors, Param, Req } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ImageEnhancerService } from './image-enhancer.service';
import { EnhancerStrategiesEnum } from './enhancer-strategies.enum';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BasicAuth } from '../../auth/decorators/basic-auth.decorator';

@ApiTags('image-enhancer')
@Controller('image-enhancer')
export class EnhancerController {
  constructor(private readonly enhancerService: ImageEnhancerService) {}

  @Post(':strategy')
  @ApiResponse({ status: 200, description: 'Enhanced image', type: String })
  @UseInterceptors(FileInterceptor('image'))
  @BasicAuth()
  @ApiBearerAuth()
  async enhanceImage(
    @UploadedFile() file: Express.Multer.File,
    @Req() request: any,
    @Param('strategy') strategy: EnhancerStrategiesEnum,
  ): Promise<string> {
    if (!Object.values(EnhancerStrategiesEnum).includes(strategy)) {
      throw new Error('Invalid enhancer strategy');
    }

    return await this.enhancerService.enhance(file.buffer, strategy, request.body.mimeType, request.body.fileName);
  }
}

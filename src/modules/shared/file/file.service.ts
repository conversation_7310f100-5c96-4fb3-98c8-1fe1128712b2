import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { instanceToPlain } from 'class-transformer';
import * as convert from 'heic-convert';
import { v4 as uuid } from 'uuid';
import * as sharp from 'sharp';
import { S3Service } from './s3/s3.service';
import { FileDto } from './models/file.dto';
import { File } from './entities/file.entity';

@Injectable()
export class FileService {
  constructor(
    @InjectRepository(File)
    private readonly fileRepository: Repository<File>,
    private readonly s3Service: S3Service,
  ) {}

  async save(file: File): Promise<File> {
    return this.fileRepository.save(file);
  }

  async uploadFile(file: Express.Multer.File, order?: number): Promise<File> {
    const processedFile = await this.processFile(file);
    let thumbnail = null;

    if (this.isThumbnailSupported(processedFile.mimeType)) {
      thumbnail = await this.createThumbnail(processedFile);
    }

    const fileUrl = await this.s3Service.uploadFile(
      processedFile.buffer,
      processedFile.mimeType,
      processedFile.fileName,
    );

    let thumbnailUrl = null;
    if (thumbnail) {
      thumbnailUrl = await this.s3Service.uploadFile(thumbnail.buffer, thumbnail.mimeType, thumbnail.fileName);
    }

    const fileEntity = new File();
    fileEntity.url = fileUrl;
    fileEntity.thumbnailUrl = thumbnailUrl;
    fileEntity.order = order;
    fileEntity.fileName = processedFile.fileName;
    fileEntity.mimeType = processedFile.mimeType;
    fileEntity.fileSize = file.size;

    const result = await this.fileRepository.createQueryBuilder().insert().values(fileEntity).returning('*').execute();

    return Object.assign(new File(), { ...result.raw[0] });
  }

  async deleteFile(fileId: string): Promise<void> {
    await this.fileRepository.softDelete(fileId);
  }

  async updateFileOrder(files: FileDto[]): Promise<File[]> {
    files.forEach((file, index) => {
      file.order = index;
    });

    return this.fileRepository.save(files);
  }

  async convertToDto(file: File): Promise<FileDto> {
    return instanceToPlain(file, { excludeExtraneousValues: true }) as FileDto;
  }

  private async processFile(
    file: Express.Multer.File,
  ): Promise<{ buffer: Buffer; mimeType: string; fileName: string }> {
    let buffer: Buffer = file.buffer;
    let fileName = this.createUniqueFileName(file.originalname);
    let mimeType = file.mimetype;

    if (mimeType.includes('heic')) {
      const arrayBuffer = await convert({
        buffer: file.buffer,
        format: 'PNG',
      });

      buffer = Buffer.from(arrayBuffer);
      fileName = fileName.replace(/\.[^.]+$/, '.png');
      mimeType = 'image/png';
    }

    return { buffer, mimeType, fileName };
  }

  private async createThumbnail(processedFile: {
    buffer: Buffer;
    mimeType: string;
    fileName: string;
  }): Promise<{ buffer: Buffer; mimeType: string; fileName: string }> {
    const { buffer, mimeType } = processedFile;

    let thumbnailBuffer: Buffer;
    const thumbnailMimeType = 'image/webp';
    const thumbnailFileName = this.createUniqueFileName('thumbnail.webp');

    if (mimeType.startsWith('image/')) {
      thumbnailBuffer = await sharp(buffer)
        .rotate()
        .resize({ width: 640, withoutEnlargement: true })
        .toFormat('webp', { quality: 100 })
        .toBuffer();
    } else {
      return null;
    }

    return {
      buffer: thumbnailBuffer,
      fileName: thumbnailFileName,
      mimeType: thumbnailMimeType,
    };
  }

  private createUniqueFileName(originalName: string): string {
    const extension = originalName.slice(originalName.lastIndexOf('.'));
    return `${uuid()}${extension}`;
  }

  private isThumbnailSupported(mimeType: string): boolean {
    return mimeType.startsWith('image/') || mimeType === 'application/pdf';
  }
}

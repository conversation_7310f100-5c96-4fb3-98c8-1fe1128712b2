import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserMarketingCampaign } from '../entities/user-marketing-campaign.entity';
import { User } from '../entities/user.entity';
import { MarketingSignUpParamsDto } from './marketing-signup-params.dto';

@Injectable()
export class UserMarketingCampaignService {
  constructor(
    @InjectRepository(UserMarketingCampaign)
    private readonly userMarketingCampaignRepository: Repository<UserMarketingCampaign>,
  ) {}

  async createUserMarketingCampaign(
    user: User,
    marketingParams: MarketingSignUpParamsDto | null = null,
  ): Promise<UserMarketingCampaign | null> {
    if (!user || !marketingParams) {
      return null;
    }

    try {
      const campaign = new UserMarketingCampaign();

      campaign.user = user;
      campaign.utm_source = marketingParams.utm_source || 'unknown';
      campaign.utm_medium = marketingParams.utm_medium || 'unknown';
      campaign.utm_campaign = marketingParams.utm_campaign || 'unknown';

      return this.userMarketingCampaignRepository.save(campaign);
    } catch (error) {
      console.error('Error creating user marketing campaign:', error);
      return null;
    }
  }
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserMarketingCampaign } from '../entities/user-marketing-campaign.entity';
import { UserMarketingCampaignService } from './user-marketing-campaign.service';

@Module({
  imports: [TypeOrmModule.forFeature([UserMarketingCampaign])],
  providers: [UserMarketingCampaignService],
  exports: [UserMarketingCampaignService],
})
export class UserMarketingCampaignModule {}

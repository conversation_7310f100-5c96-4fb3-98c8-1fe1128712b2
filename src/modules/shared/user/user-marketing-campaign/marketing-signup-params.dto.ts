import { IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class MarketingSignUpParamsDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  utm_source?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  utm_medium?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  utm_campaign?: string;
}

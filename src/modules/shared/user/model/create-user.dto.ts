import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON>ptional, IsString, IsStrongPassword, Length } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

import { Renter } from '../../../renter/renter/renter.entity';
import { Role } from '../../auth/models/roles-enum';
import { CommunicationChannel } from '../../communication/conversation/enums/preferred-communication-channel.enum';
import { IsUSPhoneNumber } from '../../../../utils/validators/us-phone-number.validator';
import { MarketingSignUpParamsDto } from '../user-marketing-campaign/marketing-signup-params.dto';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  public name: string;

  @IsEmail()
  public email: string;

  @IsOptional()
  @IsStrongPassword(null, {
    message:
      'Password is too weak. It must contain at least 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character and be at least 8 characters long.',
  })
  public password?: string;

  @IsOptional()
  @IsUSPhoneNumber()
  public phoneNumber?: string;

  @IsOptional()
  public renter?: Renter;

  @IsOptional()
  @IsArray({ each: true })
  public roles?: Role[];

  @IsOptional()
  public preferredCommunicationChannel?: CommunicationChannel;
}

export class CreateInvestorUserDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  public name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  public email: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsStrongPassword(null, {
    message:
      'Password is too weak. It must contain at least 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character and be at least 8 characters long.',
  })
  public password: string;
}

export class InvestorCredentialsSignUpDto {
  @ApiProperty({ type: () => CreateInvestorUserDto })
  @IsNotEmpty()
  public user: CreateInvestorUserDto;

  @ApiProperty({ required: false, nullable: true, type: () => MarketingSignUpParamsDto })
  @IsOptional()
  public marketingParams: MarketingSignUpParamsDto | null;
}

export class InvestorUserCreatedDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  public userId: string;
}

export class ConfirmUserEmailDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  public userId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Length(6, 6)
  public code: string;
}

export class RequestOneTimeSignInCodeDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  public email: string;
}

export class SignInWithOneTimeCodeDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  public email: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Length(6, 6)
  public code: string;
}

export class ResendEmailConfirmationCodeDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  public userId: string;
}

export class InvestorGoogleSignInDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  public googleJwt: string;
}

export class InvestorGoogleSignUpDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  public googleJwt: string;

  @ApiProperty({ required: false, nullable: true, type: () => MarketingSignUpParamsDto })
  @IsOptional()
  public marketingParams: MarketingSignUpParamsDto | null;
}

export class RenterActivationWithGoogleDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  accountActivationToken: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  googleJwt: string;
}

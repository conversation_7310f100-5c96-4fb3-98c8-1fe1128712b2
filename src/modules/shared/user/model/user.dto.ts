import { instanceTo<PERSON><PERSON> } from 'class-transformer';
import { User } from '../entities/user.entity';
import { ApiProperty } from '@nestjs/swagger';
import { CommunicationChannel } from '../../communication/conversation/enums/preferred-communication-channel.enum';
import { CalendarProvider } from '../../calendar/models/calendar-provider.enum';
import { CalendarIntegration } from '../../calendar/entities/calendar-integration.entity';

export class UserDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  preferredCommunicationChannel: CommunicationChannel;

  calendars: {
    provider: CalendarProvider;
    userEmail: string;
  };
}

export async function convertUserToDto(user: User): Promise<UserDto> {
  const userDto = <UserDto>instanceToPlain(user, { strategy: 'excludeAll' });

  userDto.calendars = <CalendarIntegration>instanceToPlain(await user.calendars, { strategy: 'excludeAll' });

  return userDto;
}

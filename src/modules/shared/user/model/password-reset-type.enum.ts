export enum UserVerificationType {
  PasswordResetToken = 'Password reset token',
  EmailConfirmationCode = 'Email confirmation code',
  OneTimeSignInCode = 'One time sign in code',
  RenterActivationToken = 'Renter activation token',
}

export const userVerificationLablesMap = new Map<UserVerificationType, string>([
  [UserVerificationType.PasswordResetToken, 'Password reset link'],
  [UserVerificationType.EmailConfirmationCode, 'Email confirmation code'],
  [UserVerificationType.OneTimeSignInCode, 'One time sign in code'],
  [UserVerificationType.RenterActivationToken, 'Account activation link'],
]);

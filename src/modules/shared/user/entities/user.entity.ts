import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Role } from '../../auth/models/roles-enum';
import { SignInMethod } from '../../auth/models/sign-in-method.enum';
import { CommunicationChannel } from '../../communication/conversation/enums/preferred-communication-channel.enum';
import { CalendarIntegration } from '../../calendar/entities/calendar-integration.entity';

@Entity()
export class User {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: true })
  name: string;

  @Expose()
  @Column({ type: 'text', nullable: true })
  avatar: string;

  @Expose()
  @Index()
  @Column({ type: 'varchar', length: 120, unique: true, nullable: true })
  email: string;

  @Expose()
  @Index()
  @Column({ type: 'varchar', length: 120, nullable: true })
  phoneNumber?: string;

  @Expose()
  @Column({
    type: 'enum',
    enum: CommunicationChannel,
    default: CommunicationChannel.EMAIL,
  })
  preferredCommunicationChannel?: CommunicationChannel;

  @Column({ type: 'varchar', length: 120, nullable: true })
  password: string;

  @DeleteDateColumn()
  deletedAt?: Date;

  /** For investor - activated when account is created. For renter will be false until we have portal */
  @Column({ type: 'boolean', default: false })
  isActivated: boolean;

  @Column({ type: 'simple-array', default: Role.INVESTOR })
  roles: Role[];

  @Column('enum', { enum: SignInMethod, array: true, default: [] })
  signInMethods: SignInMethod[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  @OneToMany(() => CalendarIntegration, (calendarIntegration) => calendarIntegration.user, {
    lazy: true,
  })
  calendars: Promise<CalendarIntegration[]> | CalendarIntegration[];

  static isRenter(user: User): boolean {
    return user.roles.includes(Role.RENTER);
  }

  static isSmsComms(user: User): boolean {
    return user.preferredCommunicationChannel === CommunicationChannel.SMS && Boolean(user.phoneNumber);
  }
}

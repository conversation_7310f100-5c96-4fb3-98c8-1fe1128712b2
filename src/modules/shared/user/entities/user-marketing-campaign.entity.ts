import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../user/entities/user.entity';

@Entity()
export class UserMarketingCampaign {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => User, { nullable: false })
  @JoinColumn()
  user: User;

  /**
   * "Who" is sending the traffic. Used by marketers, for example "google", "facebook", "newsletter"
   */
  @Column({ type: 'varchar', length: 120, default: 'unknown' })
  utm_source: string;

  /**
   * "How" the traffic is coming. For example "phone_call", "email", "social", "paid_social", "referral"
   */
  @Column({ type: 'varchar', length: 120, default: 'unknown' })
  utm_medium: string;

  /**
   * Campaign name. Used to identify the campaign, for example "chris_s_campaign", "ads_agency", "artem_campaign_may"
   */
  @Column({ type: 'varchar', length: 120, default: 'unknown' })
  utm_campaign: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

import { <PERSON>umn, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

import { UserVerificationType } from '../model/password-reset-type.enum';
import { User } from './user.entity';

@Entity()
export class UserVerification {
  @PrimaryGeneratedColumn('uuid')
  public id!: string;

  @Column({ type: 'boolean', default: false })
  public isUsed: boolean;

  @Column({
    type: 'enum',
    enum: UserVerificationType,
  })
  type: UserVerificationType;

  @ManyToOne(() => User)
  user: User;

  @Column({ type: 'varchar', length: 120 })
  public code: string;

  @Column({ type: 'timestamp' })
  public expiresAt: Date;

  @Column({ type: 'boolean', default: false })
  public isDeleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;
}

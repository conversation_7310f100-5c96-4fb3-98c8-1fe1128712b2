import * as bcrypt from 'bcrypt';
import { QueryFailedError, Repository } from 'typeorm';

import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Role } from '../auth/models/roles-enum';
import { SignInMethod } from '../auth/models/sign-in-method.enum';
import { CommunicationChannel } from '../communication/conversation/enums/preferred-communication-channel.enum';
import { User } from './entities/user.entity';
import { CreateInvestorUserDto, CreateUserDto } from './model/create-user.dto';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  private readonly saltOrRounds = 10;

  public findById(id: string): Promise<User> {
    return this.userRepository.findOneByOrFail({ id });
  }

  async findByEmail(email: string): Promise<User> {
    return this.userRepository.findOneBy({ email });
  }

  async updateUser(userId: string, updateBody: Partial<User>): Promise<void> {
    await this.userRepository.update(userId, updateBody);
  }

  async createUser(body: CreateUserDto): Promise<User> {
    const user: User = new User();

    const hash = body.password ? await this.hashPassword(body.password) : null;

    user.name = body.name;
    user.email = body.email;
    user.password = hash;
    user.phoneNumber = body.phoneNumber;
    user.roles = body.roles?.length ? body.roles : [Role.INVESTOR];
    user.isActivated = this.determineIfUserIsActivated(user);
    user.signInMethods = body.roles?.includes(Role.INVESTOR) ? [SignInMethod.Credentials] : [];
    user.preferredCommunicationChannel = body.preferredCommunicationChannel;

    try {
      return await this.userRepository.save(user);
    } catch (error) {
      if (
        error instanceof QueryFailedError &&
        error.message.includes('duplicate key value violates unique constraint')
      ) {
        throw new HttpException('Email already exists', HttpStatus.CONFLICT);
      }
      throw new HttpException(`Internal server error: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async createNotActiveInvestorFromCreds(body: CreateInvestorUserDto): Promise<User> {
    const user: User = new User();

    user.name = body.name;
    user.email = body.email;
    user.password = await this.hashPassword(body.password);
    user.roles = [Role.INVESTOR];
    user.isActivated = false;
    user.signInMethods = [SignInMethod.Credentials];
    user.preferredCommunicationChannel = CommunicationChannel.SMS;

    try {
      return this.userRepository.save(user);
    } catch (error) {
      if (
        error instanceof QueryFailedError &&
        error.message.includes('duplicate key value violates unique constraint')
      ) {
        throw new HttpException('Email already exists', HttpStatus.CONFLICT);
      }

      throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Adds required stuff to user entity in case if non active
   * renter signs up with credentials on the investor portal
   */
  async addInvestorRoleToExistingNonActiveUserWhoSignsUpWithCreds(
    user: User,
    signUpDto: CreateInvestorUserDto,
  ): Promise<void> {
    if (!user.roles.includes(Role.INVESTOR)) {
      const updatedSignInMethods = user.signInMethods.includes(SignInMethod.Credentials)
        ? user.signInMethods
        : [...user.signInMethods, SignInMethod.Credentials];

      await this.updateUser(user.id, {
        name: signUpDto.name,
        password: await this.hashPassword(signUpDto.password),
        roles: [...user.roles, Role.INVESTOR],
        signInMethods: updatedSignInMethods,
      });
    }
  }

  async createUserFromGoogleJwt(
    name: string,
    email: string,
    avatarUrl: string,
    role: Role,
    communicationChannel: CommunicationChannel,
  ): Promise<User> {
    const user: User = new User();

    user.name = name;
    user.email = email;
    user.roles = [role];
    user.signInMethods = [SignInMethod.Google];
    user.isActivated = true;
    user.preferredCommunicationChannel = communicationChannel;

    if (avatarUrl) {
      user.avatar = avatarUrl;
    }

    try {
      return this.userRepository.save(user);
    } catch (error) {
      if (
        error instanceof QueryFailedError &&
        error.message.includes('duplicate key value violates unique constraint')
      ) {
        throw new HttpException('Email already exists', HttpStatus.CONFLICT);
      }

      throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateUserProfileVerifiedByGoogle(user: User, avatar: string): Promise<void> {
    const userUpdatePayload: Partial<User> = {};

    if (!user.isActivated) {
      userUpdatePayload.isActivated = true;
    }

    if (!user.avatar && avatar) {
      userUpdatePayload.avatar = avatar;
    }

    if (!user.signInMethods.includes(SignInMethod.Google)) {
      userUpdatePayload.signInMethods = [...user.signInMethods, SignInMethod.Google];
    }

    if (Object.keys(userUpdatePayload).length) {
      await this.userRepository.update(user.id, userUpdatePayload);
    }
  }

  hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.saltOrRounds);
  }

  private determineIfUserIsActivated(user: User): boolean {
    const userHasPassword = Boolean(user.password);
    const userIsInvestor = user.roles.includes(Role.INVESTOR);
    const userHasEmail = Boolean(user.email);
    const userHasName = Boolean(user.name);

    return userHasPassword && userIsInvestor && userHasEmail && userHasName;
  }
}

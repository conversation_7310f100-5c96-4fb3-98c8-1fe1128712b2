import { Controller, Get, Inject, Req, UseGuards } from '@nestjs/common';
import { UserService } from './user.service';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { UserDto, convertUserToDto } from './model/user.dto';
import { Request } from 'express';
import { RolesGuard } from '../auth/guards/roles.guard';

@ApiTags('user')
@Controller('user')
@UseGuards(RolesGuard)
@ApiBearerAuth()
export class UserController {
  @Inject(UserService)
  private readonly service: UserService;

  @Get('me')
  @ApiOkResponse({ description: 'Returns a user by ID', type: UserDto })
  public async getMyProfile(@Req() req: Request): Promise<UserDto> {
    const user = await this.service.findById(req.user.id);
    return convertUserToDto(user);
  }
}

import { Column, CreateDate<PERSON>olumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { Application } from '../../application.entity';
import { ApplicationRenterReferenceDto } from './application-renter-reference.dto';

@Entity()
export class ApplicationRenterReference {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Application, (application) => application.references, {
    nullable: false,
    lazy: true,
  })
  application: Application;

  @Expose()
  @Column({ type: 'varchar', length: 512, nullable: true })
  fullName: string;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: true })
  phoneNumber: string;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: true })
  email: string;

  @Expose()
  @Column({ type: 'text', nullable: true })
  relationship: string;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  agreedToDisclosures: boolean;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  static convertToDto(renterReference: ApplicationRenterReference): ApplicationRenterReferenceDto {
    if (!renterReference || !renterReference.id) {
      return null;
    }

    return <ApplicationRenterReferenceDto>instanceToPlain(renterReference, {
      excludeExtraneousValues: true,
    });
  }
}

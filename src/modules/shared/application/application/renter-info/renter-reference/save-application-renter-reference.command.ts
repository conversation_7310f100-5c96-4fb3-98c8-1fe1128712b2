import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsEmail } from 'class-validator';

import { IsUSPhoneNumber } from '../../../../../../utils/validators/us-phone-number.validator';

export class SaveApplicationRenterReferenceCommand {
  @IsString()
  @ApiProperty({ description: 'Full name of the reference' })
  fullName: string;

  @IsUSPhoneNumber()
  @ApiProperty({ example: '8003434343', description: '10 digits (US phone number)' })
  phoneNumber: string;

  @IsEmail()
  @ApiProperty({ description: 'Email address of the reference' })
  email: string;

  @IsString()
  @ApiProperty({ description: 'Relationship of the reference to the applicant' })
  relationship: string;

  @IsBoolean()
  @ApiProperty({ description: 'Whether the reference agreed to disclosures' })
  agreedToDisclosures: boolean;
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationRenterReference } from './application-renter-reference.entity';

@Injectable()
export class ApplicationRenterReferenceService {
  constructor(
    @InjectRepository(ApplicationRenterReference)
    private referencesRepository: Repository<ApplicationRenterReference>,
  ) {}

  async create(payload: Partial<ApplicationRenterReference>): Promise<ApplicationRenterReference> {
    const result = await this.referencesRepository
      .createQueryBuilder()
      .insert()
      .values({ ...payload })
      .returning('*')
      .execute();

    return Object.assign(new ApplicationRenterReference(), { ...result.raw[0] });
  }

  async update(referenceId: string, payload: Partial<ApplicationRenterReference>): Promise<ApplicationRenterReference> {
    const result = await this.referencesRepository
      .createQueryBuilder()
      .update(ApplicationRenterReference)
      .set({ ...payload })
      .where({ id: referenceId })
      .returning('*')
      .execute();

    return Object.assign(new ApplicationRenterReference(), { ...result.raw[0] });
  }

  async delete(referenceId: string): Promise<void> {
    await this.referencesRepository.delete({ id: referenceId });
  }
}

import { Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { ResidenceStatus } from './application-renter-residence-status.enum';
import { Application } from '../../application.entity';
import { ApplicationRenterResidenceInfoDto } from './application-renter-residence-info.dto';
import { ApplicationRenterResidenceInfoType } from './application-renter-residence-info-type.enum';

@Entity()
export class ApplicationRenterResidenceInfo {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Application, (application) => application.residenceInfo, {
    nullable: false,
    lazy: true,
  })
  application: Application;

  @Expose()
  @Column({
    type: 'enum',
    enum: ApplicationRenterResidenceInfoType,
  })
  type: ApplicationRenterResidenceInfoType;

  @Expose()
  @Column({
    type: 'enum',
    enum: ResidenceStatus,
    nullable: true,
  })
  status: ResidenceStatus;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  approvesToContactLandlord: boolean;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  propertyAddress: string;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  landlordName: string;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  landlordPhone: string;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  landlordEmail: string;

  @Expose()
  @Column({ type: 'integer', nullable: true })
  monthlyRent: number;

  @Expose()
  @Column({ type: 'date', nullable: true })
  moveInDate: Date;

  @Expose()
  @Column({ type: 'date', nullable: true })
  moveOutDate: Date;

  @Expose()
  @Column({ type: 'text', nullable: true })
  moveOutReason: string;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  static convertToDto(renterResidenceInfo: ApplicationRenterResidenceInfo): ApplicationRenterResidenceInfoDto {
    if (!renterResidenceInfo || !renterResidenceInfo.id) {
      return null;
    }

    return <ApplicationRenterResidenceInfoDto>instanceToPlain(renterResidenceInfo, {
      excludeExtraneousValues: true,
    });
  }
}

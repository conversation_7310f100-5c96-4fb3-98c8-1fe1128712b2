import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationRenterResidenceInfo } from './application-renter-residence-info.entity';

@Injectable()
export class ApplicationRenterResidenceInfoService {
  constructor(
    @InjectRepository(ApplicationRenterResidenceInfo)
    private residenceInfoRepository: Repository<ApplicationRenterResidenceInfo>,
  ) {}

  async create(payload: Partial<ApplicationRenterResidenceInfo>): Promise<ApplicationRenterResidenceInfo> {
    const result = await this.residenceInfoRepository
      .createQueryBuilder()
      .insert()
      .values({ ...payload })
      .returning('*')
      .execute();

    return Object.assign(new ApplicationRenterResidenceInfo(), { ...result.raw[0] });
  }

  async update(
    residenceInfoId: string,
    payload: Partial<ApplicationRenterResidenceInfo>,
  ): Promise<ApplicationRenterResidenceInfo> {
    const result = await this.residenceInfoRepository
      .createQueryBuilder()
      .update(ApplicationRenterResidenceInfo)
      .set({ ...payload })
      .where({ id: residenceInfoId })
      .returning('*')
      .execute();

    return Object.assign(new ApplicationRenterResidenceInfo(), { ...result.raw[0] });
  }

  async delete(residenceInfoId: string): Promise<void> {
    await this.residenceInfoRepository.delete({ id: residenceInfoId });
  }
}

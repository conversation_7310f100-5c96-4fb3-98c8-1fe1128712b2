import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean, IsDate, IsInt, IsEnum, IsNotEmpty, IsEmail } from 'class-validator';
import { ResidenceStatus } from './application-renter-residence-status.enum';
import { Type } from 'class-transformer';
import { ApplicationRenterResidenceInfoType } from './application-renter-residence-info-type.enum';
import { IsUSPhoneNumber } from '../../../../../../utils/validators/us-phone-number.validator';

export class SaveApplicationRenterResidenceInfoCommand {
  @IsNotEmpty()
  @ApiProperty({ description: 'Type of residence', enum: ApplicationRenterResidenceInfoType })
  @IsEnum(ApplicationRenterResidenceInfoType)
  type: ApplicationRenterResidenceInfoType;

  @ApiPropertyOptional({ description: 'Status of residence (Rental or Own)', enum: ResidenceStatus })
  @IsOptional()
  @IsEnum(ResidenceStatus)
  status?: ResidenceStatus;

  @ApiPropertyOptional({ description: 'Indicates if the applicant agrees to contact the landlord' })
  @IsOptional()
  @IsBoolean()
  approvesToContactLandlord?: boolean;

  @ApiPropertyOptional({ description: 'Property address of the applicant' })
  @IsOptional()
  @IsString()
  propertyAddress?: string;

  @ApiPropertyOptional({ description: 'Name of the landlord' })
  @IsOptional()
  @IsString()
  landlordName?: string;

  @ApiPropertyOptional({ description: 'Phone number of the landlord' })
  @IsOptional()
  @IsUSPhoneNumber()
  landlordPhone?: string;

  @ApiPropertyOptional({ description: 'Email address of the landlord' })
  @IsOptional()
  @IsEmail()
  landlordEmail?: string;

  @ApiPropertyOptional({ description: 'Monthly rent paid by the applicant' })
  @IsOptional()
  @IsInt()
  monthlyRent?: number;

  @ApiPropertyOptional({ description: 'Move-in date of the applicant' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  moveInDate?: Date;

  @ApiPropertyOptional({ description: 'Move-out date of the applicant' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  moveOutDate?: Date;

  @ApiPropertyOptional({ description: 'Reason for moving out' })
  @IsOptional()
  @IsString()
  moveOutReason?: string;
}

import { Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Expose, instanceToPlain, Type } from 'class-transformer';
import { Application } from '../../application.entity';
import { ApplicationRenterEmploymentInfoType } from './application-renter-employment-info-type.enum';
import { ApplicationRenterEmploymentInfoDto } from './application-renter-employment-info.dto';

@Entity()
export class ApplicationRenterEmploymentInfo {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Application, (application) => application.employmentInfo, {
    nullable: false,
    lazy: true,
  })
  application: Application;

  @Expose()
  @Column({
    type: 'enum',
    enum: ApplicationRenterEmploymentInfoType,
  })
  type: ApplicationRenterEmploymentInfoType;

  @Expose()
  @Column({ type: 'varchar', length: 512, nullable: true })
  jobTitle: string;

  @Expose()
  @Column({ type: 'varchar', length: 512, nullable: true })
  companyName: string;

  @Expose()
  @Column({ type: 'float', nullable: true })
  netIncome: number;

  @Expose()
  @Column({ type: 'date', nullable: true })
  @Type(() => Date)
  startDate: Date;

  @Expose()
  @Column({ type: 'date', nullable: true })
  @Type(() => Date)
  endDate: Date;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  isCurrent: boolean;

  @Expose()
  @Column({ type: 'varchar', length: 128, nullable: true })
  managerName: string;

  @Expose()
  @Column({ type: 'varchar', length: 128, nullable: true })
  managerPhone: string;

  @Expose()
  @Column({ type: 'varchar', length: 128, nullable: true })
  managerEmail: string;

  @CreateDateColumn({ type: 'timestamp' })
  @Type(() => Date)
  public createdAt!: Date;

  static convertToDto(renterEmploymentInfo: ApplicationRenterEmploymentInfo): ApplicationRenterEmploymentInfoDto {
    if (!renterEmploymentInfo || !renterEmploymentInfo.id) {
      return null;
    }

    return <ApplicationRenterEmploymentInfoDto>instanceToPlain(renterEmploymentInfo, {
      excludeExtraneousValues: true,
    });
  }
}

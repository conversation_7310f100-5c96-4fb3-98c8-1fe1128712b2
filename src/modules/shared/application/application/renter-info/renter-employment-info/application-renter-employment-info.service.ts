import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationRenterEmploymentInfo } from './application-renter-employment-info.entity';

@Injectable()
export class ApplicationRenterEmploymentInfoService {
  constructor(
    @InjectRepository(ApplicationRenterEmploymentInfo)
    private employmentInfoRepository: Repository<ApplicationRenterEmploymentInfo>,
  ) {}

  async create(payload: Partial<ApplicationRenterEmploymentInfo>): Promise<ApplicationRenterEmploymentInfo> {
    const result = await this.employmentInfoRepository
      .createQueryBuilder()
      .insert()
      .values({ ...payload })
      .returning('*')
      .execute();

    return Object.assign(new ApplicationRenterEmploymentInfo(), { ...result.raw[0] });
  }

  async update(
    employmentInfoId: string,
    payload: Partial<ApplicationRenterEmploymentInfo>,
  ): Promise<ApplicationRenterEmploymentInfo> {
    const result = await this.employmentInfoRepository
      .createQueryBuilder()
      .update(ApplicationRenterEmploymentInfo)
      .set({ ...payload })
      .where({ id: employmentInfoId })
      .returning('*')
      .execute();

    return Object.assign(new ApplicationRenterEmploymentInfo(), { ...result.raw[0] });
  }

  async delete(employmentInfoId: string): Promise<void> {
    await this.employmentInfoRepository.delete({ id: employmentInfoId });
  }
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsDate, IsBoolean, IsNotEmpty, IsEnum, IsEmail } from 'class-validator';
import { Type } from 'class-transformer';
import { ApplicationRenterEmploymentInfoType } from './application-renter-employment-info-type.enum';
import { IsUSPhoneNumber } from 'src/utils/validators/us-phone-number.validator';

export class SaveApplicationRenterEmploymentInfoCommand {
  @IsNotEmpty()
  @ApiProperty({ description: 'Type of employment', enum: ApplicationRenterEmploymentInfoType })
  @IsEnum(ApplicationRenterEmploymentInfoType)
  type: ApplicationRenterEmploymentInfoType;

  @ApiPropertyOptional({ description: 'Job title of the applicant' })
  @IsOptional()
  @IsString()
  jobTitle?: string;

  @ApiPropertyOptional({ description: 'Company name where the applicant works' })
  @IsOptional()
  @IsString()
  companyName?: string;

  @ApiPropertyOptional({ description: 'Net income of the applicant' })
  @IsOptional()
  @IsNumber()
  netIncome?: number;

  @ApiPropertyOptional({ description: "Start date of the applicant's employment" })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDate?: Date;

  @ApiPropertyOptional({ description: "End date of the applicant's employment" })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate?: Date;

  @ApiPropertyOptional({ description: 'Indicates if the applicant is currently employed' })
  @IsOptional()
  @IsBoolean()
  isCurrent?: boolean;

  @ApiPropertyOptional({ description: "Manager's name" })
  @IsOptional()
  @IsString()
  managerName?: string;

  @ApiPropertyOptional({ description: "Manager's phone number" })
  @IsOptional()
  @IsUSPhoneNumber()
  managerPhone?: string;

  @ApiPropertyOptional({ description: "Manager's email address" })
  @IsOptional()
  @IsEmail()
  managerEmail?: string;
}

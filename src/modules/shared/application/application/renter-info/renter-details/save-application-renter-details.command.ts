import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean, IsDate, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export class SaveApplicationRenterDetailsCommand {
  @ApiPropertyOptional({ description: 'Information about the renter' })
  @IsOptional()
  @IsString()
  aboutYou?: string;

  @ApiPropertyOptional({ description: 'Indicates if the renter is a smoker' })
  @IsOptional()
  @IsBoolean()
  isSmoker?: boolean;

  @ApiPropertyOptional({ description: 'Desired move-in date of the renter' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  desiredMoveInDate?: Date;

  @ApiPropertyOptional({ description: 'Indicates if the move-in date is flexible' })
  @IsOptional()
  @IsBoolean()
  isMoveInDateFlexible?: boolean;

  @ApiPropertyOptional({ description: 'Indicates if the renter has pets' })
  @IsOptional()
  @IsBoolean()
  hasPets?: boolean;

  @ApiPropertyOptional({ description: 'Number of small dogs the renter has' })
  @IsOptional()
  @IsInt()
  numberOfSmallDogs?: number;

  @ApiPropertyOptional({ description: 'Number of medium dogs the renter has' })
  @IsOptional()
  @IsInt()
  numberOfMediumDogs?: number;

  @ApiPropertyOptional({ description: 'Number of large dogs the renter has' })
  @IsOptional()
  @IsInt()
  numberOfLargeDogs?: number;

  @ApiPropertyOptional({ description: 'Number of cats the renter has' })
  @IsOptional()
  @IsInt()
  numberOfCats?: number;

  @ApiPropertyOptional({ description: 'Number of other pets the renter has' })
  @IsOptional()
  @IsInt()
  numberOfOtherPets?: number;

  @ApiPropertyOptional({ description: "Details about the renter's pets" })
  @IsOptional()
  @IsString()
  petDetails?: string;
}

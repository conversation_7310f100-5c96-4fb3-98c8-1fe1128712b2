import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationRenterDetails } from './application-renter-details.entity';
import { FileService } from 'src/modules/shared/file/file.service';
import { File } from 'src/modules/shared/file/entities/file.entity';

@Injectable()
export class ApplicationRenterDetailsService {
  constructor(
    @InjectRepository(ApplicationRenterDetails)
    private renterDetailsRepository: Repository<ApplicationRenterDetails>,
    private readonly fileService: FileService,
  ) {}

  async findById(renterDetailsId: string): Promise<ApplicationRenterDetails> {
    return this.renterDetailsRepository.findOneOrFail({ where: { id: renterDetailsId } });
  }

  async create(payload: Partial<ApplicationRenterDetails>): Promise<ApplicationRenterDetails> {
    const result = await this.renterDetailsRepository
      .createQueryBuilder()
      .insert()
      .values({ ...payload })
      .returning('*')
      .execute();

    return Object.assign(new ApplicationRenterDetails(), { ...result.raw[0] });
  }

  async update(renterDetailsId: string, payload: Partial<ApplicationRenterDetails>): Promise<ApplicationRenterDetails> {
    const result = await this.renterDetailsRepository
      .createQueryBuilder()
      .update(ApplicationRenterDetails)
      .set({ ...payload })
      .where({ id: renterDetailsId })
      .returning('*')
      .execute();

    return Object.assign(new ApplicationRenterDetails(), { ...result.raw[0] });
  }

  async uploadPetPhoto(renterDetailsId: string, file: Express.Multer.File): Promise<File> {
    const renterDetails = await this.findById(renterDetailsId);
    const uploadedFile = await this.fileService.uploadFile(file, 0);

    await this.renterDetailsRepository
      .createQueryBuilder()
      .relation(ApplicationRenterDetails, 'petPhotos')
      .of(renterDetails)
      .add(uploadedFile);

    return uploadedFile;
  }

  async deletePetPhoto(renterDetailsId: string, fileId: string): Promise<void> {
    await this.findById(renterDetailsId);
    await this.fileService.deleteFile(fileId);
  }
}

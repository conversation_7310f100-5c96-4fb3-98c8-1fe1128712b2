import { Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { ApplicationRenterDetailsDto } from './application-renter-details.dto';
import { Application } from '../../application.entity';

@Entity()
export class ApplicationRenterDetails {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Application, (application) => application.renterDetails, {
    nullable: false,
    lazy: true,
  })
  application: Promise<Application> | Application;

  @Expose()
  @Column({ type: 'text', nullable: true })
  aboutYou: string;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  isSmoker: boolean;

  @Expose()
  @Column({ type: 'date', nullable: true })
  desiredMoveInDate: Date;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  isMoveInDateFlexible: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasPets: boolean;

  @Expose()
  @Column({ type: 'integer', nullable: true })
  numberOfSmallDogs: number;

  @Expose()
  @Column({ type: 'integer', nullable: true })
  numberOfMediumDogs: number;

  @Expose()
  @Column({ type: 'integer', nullable: true })
  numberOfLargeDogs: number;

  @Expose()
  @Column({ type: 'integer', nullable: true })
  numberOfCats: number;

  @Expose()
  @Column({ type: 'integer', nullable: true })
  numberOfOtherPets: number;

  @Expose()
  @Column({ type: 'text', nullable: true })
  petDetails: string;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  public static convertToDto(renterDetails: ApplicationRenterDetails): ApplicationRenterDetailsDto {
    if (!renterDetails || !renterDetails.id) {
      return null;
    }

    return <ApplicationRenterDetailsDto>instanceToPlain(renterDetails, {
      excludeExtraneousValues: true,
    });
  }
}

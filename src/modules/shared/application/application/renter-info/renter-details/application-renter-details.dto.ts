import { ApiProperty } from '@nestjs/swagger';
import { SaveApplicationRenterDetailsCommand } from './save-application-renter-details.command';
import { FileDto } from 'src/modules/shared/file/models/file.dto';

export class ApplicationRenterDetailsDto extends SaveApplicationRenterDetailsCommand {
  @ApiProperty()
  id: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  petPhotos: FileDto[];
}

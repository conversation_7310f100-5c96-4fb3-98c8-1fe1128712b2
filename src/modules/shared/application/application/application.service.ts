import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Application } from './application.entity';
import { Repository } from 'typeorm';
import { ApplicationRenterEmploymentInfoService } from './renter-info/renter-employment-info/application-renter-employment-info.service';
import { ApplicationRenterReferenceService } from './renter-info/renter-reference/application-renter-reference.service';
import { ApplicationRenterResidenceInfoService } from './renter-info/renter-residence-info/application-renter-residence-info.service';
import { ApplicationRenterDetails } from './renter-info/renter-details/application-renter-details.entity';
import { ApplicationRenterDetailsService } from './renter-info/renter-details/application-renter-details.service';
import { ApplicationRenterEmploymentInfo } from './renter-info/renter-employment-info/application-renter-employment-info.entity';
import { ApplicationRenterReference } from './renter-info/renter-reference/application-renter-reference.entity';
import { ApplicationRenterResidenceInfo } from './renter-info/renter-residence-info/application-renter-residence-info.entity';
import { FileService } from '../../file/file.service';
import { File } from '../../file/entities/file.entity';
import { BackgroundCheckStatusEnum } from './enums/background-check-status.enum';
import { ApplicantType } from './enums/applicant-type.enum';

@Injectable()
export class ApplicationService {
  constructor(
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    private readonly renterDetailsService: ApplicationRenterDetailsService,
    private readonly renterEmploymentInfoService: ApplicationRenterEmploymentInfoService,
    private readonly renterReferenceService: ApplicationRenterReferenceService,
    private readonly renterResidenceInfoService: ApplicationRenterResidenceInfoService,
    private readonly fileService: FileService,
  ) {}

  async findByScreeningRequestRenterId(screeningRequestRenterId: string): Promise<Application> {
    return this.applicationRepository.findOne({
      where: {
        screeningRequestRenterId,
      },
    });
  }

  async findApplicationById(applicationId: string, withRelations = false): Promise<Application> {
    const relations = [];

    if (withRelations) {
      relations.push('applicationBundle', 'applicationBundle.property');
    }

    return this.applicationRepository.findOneOrFail({
      where: {
        id: applicationId,
      },
      relations,
    });
  }

  async createApplication(application: Partial<Application>): Promise<Application> {
    const createdApplication = this.applicationRepository.create();
    Object.assign(createdApplication, application);

    return this.applicationRepository.save(createdApplication);
  }

  async createRenterDetails(
    applicationId: string,
    renterDetails: Partial<ApplicationRenterDetails>,
  ): Promise<ApplicationRenterDetails> {
    const createdRenterDetails = await this.renterDetailsService.create(renterDetails);

    await this.applicationRepository.update(applicationId, {
      renterDetailsId: createdRenterDetails.id,
    });

    return createdRenterDetails;
  }

  async updateApplication(applicationId: string, application: Partial<Application>): Promise<void> {
    await this.applicationRepository.update(applicationId, application);
  }

  async updateRenterDetails(
    renterDetailsId: string,
    renterDetails: Partial<ApplicationRenterDetails>,
  ): Promise<ApplicationRenterDetails> {
    return this.renterDetailsService.update(renterDetailsId, renterDetails);
  }

  async createRenterEmploymentInfo(
    applicationId: string,
    renterEmploymentInfo: Partial<ApplicationRenterEmploymentInfo>,
  ): Promise<ApplicationRenterEmploymentInfo> {
    renterEmploymentInfo.application = await this.findApplicationById(applicationId);
    return await this.renterEmploymentInfoService.create(renterEmploymentInfo);
  }

  async updateRenterEmploymentInfo(
    renterEmploymentInfoId: string,
    renterEmploymentInfo: Partial<ApplicationRenterEmploymentInfo>,
  ): Promise<ApplicationRenterEmploymentInfo> {
    return this.renterEmploymentInfoService.update(renterEmploymentInfoId, renterEmploymentInfo);
  }

  async deleteRenterEmploymentInfo(renterEmploymentInfoId: string): Promise<void> {
    return this.renterEmploymentInfoService.delete(renterEmploymentInfoId);
  }

  async createRenterReference(
    applicationId: string,
    renterReference: Partial<ApplicationRenterReference>,
  ): Promise<ApplicationRenterReference> {
    renterReference.application = await this.findApplicationById(applicationId);
    return this.renterReferenceService.create(renterReference);
  }

  async updateRenterReference(
    renterReferenceId: string,
    renterReference: Partial<ApplicationRenterReference>,
  ): Promise<ApplicationRenterReference> {
    return this.renterReferenceService.update(renterReferenceId, renterReference);
  }

  async deleteRenterReference(renterReferenceId: string): Promise<void> {
    return this.renterReferenceService.delete(renterReferenceId);
  }

  async createRenterResidenceInfo(
    applicationId: string,
    renterResidenceInfo: Partial<ApplicationRenterResidenceInfo>,
  ): Promise<ApplicationRenterResidenceInfo> {
    renterResidenceInfo.application = await this.findApplicationById(applicationId);
    return this.renterResidenceInfoService.create(renterResidenceInfo);
  }

  async updateRenterResidenceInfo(
    renterResidenceInfoId: string,
    renterResidenceInfo: Partial<ApplicationRenterResidenceInfo>,
  ): Promise<ApplicationRenterResidenceInfo> {
    return this.renterResidenceInfoService.update(renterResidenceInfoId, renterResidenceInfo);
  }

  async deleteRenterResidenceInfo(renterResidenceInfoId: string): Promise<void> {
    return this.renterResidenceInfoService.delete(renterResidenceInfoId);
  }

  async savePhotoId(applicationId: string, file: Express.Multer.File): Promise<File> {
    const application = await this.findApplicationById(applicationId);
    const uploadedFile = await this.fileService.uploadFile(file, 0);

    await this.applicationRepository
      .createQueryBuilder()
      .relation(Application, 'photoIds')
      .of(application)
      .add(uploadedFile);

    return uploadedFile;
  }

  async deletePhotoId(applicationId: string, fileId: string): Promise<void> {
    await this.findApplicationById(applicationId);
    await this.fileService.deleteFile(fileId);
  }

  async saveProofOfIncome(applicationId: string, file: Express.Multer.File): Promise<File> {
    const application = await this.findApplicationById(applicationId);
    const uploadedFile = await this.fileService.uploadFile(file, 0);

    await this.applicationRepository
      .createQueryBuilder()
      .relation(Application, 'proofsOfIncome')
      .of(application)
      .add(uploadedFile);

    return uploadedFile;
  }

  async deleteProofOfIncome(applicationId: string, fileId: string): Promise<void> {
    await this.findApplicationById(applicationId);
    await this.fileService.deleteFile(fileId);
  }

  async saveAdditionalDocument(applicationId: string, file: Express.Multer.File): Promise<File> {
    const application = await this.findApplicationById(applicationId);
    const uploadedFile = await this.fileService.uploadFile(file, 0);

    await this.applicationRepository
      .createQueryBuilder()
      .relation(Application, 'additionalDocuments')
      .of(application)
      .add(uploadedFile);

    return uploadedFile;
  }

  async deleteAdditionalDocument(applicationId: string, fileId: string): Promise<void> {
    await this.findApplicationById(applicationId);
    await this.fileService.deleteFile(fileId);
  }

  async savePetPhoto(applicationId: string, file: Express.Multer.File): Promise<File> {
    const application = await this.findApplicationById(applicationId);
    const uploadedFile = await this.fileService.uploadFile(file, 0);

    await this.applicationRepository
      .createQueryBuilder()
      .relation(Application, 'petPhotos')
      .of(application)
      .add(uploadedFile);

    return uploadedFile;
  }

  async deletePetPhoto(applicationId: string, fileId: string): Promise<void> {
    await this.findApplicationById(applicationId);
    await this.fileService.deleteFile(fileId);
  }

  async isApplicationReadyToBeCompleted(application: Application): Promise<boolean> {
    if (application.backgroundCheckStatus !== BackgroundCheckStatusEnum.FINISHED) {
      return false;
    }

    if (application.type !== ApplicantType.CO_SIGNER) {
      return this.isSelfReportFilled(application);
    }

    return true;
  }

  async isSelfReportFilled(application: Application): Promise<boolean> {
    const hasRenterDetails = Boolean(application.renterDetailsId);
    const hasReferences = (await application.references).length > 0;
    const hasEmploymentInfo = (await application.employmentInfo).length > 0;
    const hasResidenceInfo = (await application.residenceInfo).length > 0;
    const hasPhotoIds = (await application.photoIds).length > 0;

    return hasRenterDetails && hasReferences && hasEmploymentInfo && hasResidenceInfo && hasPhotoIds;
  }
}

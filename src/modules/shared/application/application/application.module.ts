import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { Application } from './application.entity';
import { ApplicationService } from './application.service';
import { ApplicationRenterDetails } from './renter-info/renter-details/application-renter-details.entity';
import { ApplicationRenterEmploymentInfo } from './renter-info/renter-employment-info/application-renter-employment-info.entity';
import { ApplicationRenterReference } from './renter-info/renter-reference/application-renter-reference.entity';
import { ApplicationRenterEmploymentInfoService } from './renter-info/renter-employment-info/application-renter-employment-info.service';
import { ApplicationRenterReferenceService } from './renter-info/renter-reference/application-renter-reference.service';
import { ApplicationRenterResidenceInfoService } from './renter-info/renter-residence-info/application-renter-residence-info.service';
import { ApplicationRenterResidenceInfo } from './renter-info/renter-residence-info/application-renter-residence-info.entity';
import { ApplicationRenterDetailsService } from './renter-info/renter-details/application-renter-details.service';
import { FileModule } from '../../file/file.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Application,
      Property,
      ApplicationRenterDetails,
      ApplicationRenterEmploymentInfo,
      ApplicationRenterReference,
      ApplicationRenterResidenceInfo,
    ]),
    FileModule,
  ],
  providers: [
    ApplicationService,
    ApplicationRenterDetailsService,
    ApplicationRenterEmploymentInfoService,
    ApplicationRenterReferenceService,
    ApplicationRenterResidenceInfoService,
  ],
  exports: [ApplicationService],
})
export class ApplicationModule {}

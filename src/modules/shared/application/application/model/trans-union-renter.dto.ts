import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Length,
  MaxDate,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum IncomeFrequency {
  PerMonth = 'PerMonth',
  PerYear = 'PerYear',
}

export enum EmploymentStatus {
  NotEmployed = 'NotEmployed',
  Employed = 'Employed',
  SelfEmployed = 'SelfEmployed',
  Student = 'Student',
}

export class TransUnionRenterDto {
  @IsNumber()
  @ApiPropertyOptional()
  @IsOptional()
  income?: number;

  @IsEnum(IncomeFrequency)
  @ApiPropertyOptional()
  @IsOptional()
  incomeFrequency?: IncomeFrequency;

  @IsNumber()
  @ApiPropertyOptional()
  @IsOptional()
  otherIncome?: number;

  @IsEnum(IncomeFrequency)
  @ApiPropertyOptional()
  @IsOptional()
  otherIncomeFrequency?: IncomeFrequency;

  @IsNumber()
  @ApiPropertyOptional()
  @IsOptional()
  assets?: number;

  @IsEnum(EmploymentStatus)
  @ApiPropertyOptional()
  @IsOptional()
  employmentStatus?: EmploymentStatus;

  @IsString()
  @ApiProperty({
    description: "The first line of the Person's street address",
  })
  addressLine1: string;

  @IsString()
  @ApiPropertyOptional()
  phoneNumber: string;

  @IsNotEmpty()
  @IsBoolean()
  @ApiProperty()
  acceptedTermsAndConditions: boolean;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  lastName: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  middleName?: string;

  @IsNotEmpty()
  @IsDate()
  @ApiProperty()
  @Type(() => Date)
  @MaxDate(new Date(), { message: 'Date of birth cannot be in the future' })
  dateOfBirth: Date;

  @IsNotEmpty()
  @IsString()
  @Length(9, 9, { message: 'SSN must be exactly 9 characters long' })
  @ApiProperty()
  nationalId: string;

  @IsString()
  @ApiProperty()
  city: string;

  @IsString()
  @ApiProperty()
  state: string;

  @IsString()
  @Length(4, 5)
  @ApiProperty()
  postalCode: string;
}

import { SaveApplicationCommand } from '../commands/save-application.command';
import { ApplicationRenterDetailsDto } from '../renter-info/renter-details/application-renter-details.dto';
import { ApplicationRenterEmploymentInfoDto } from '../renter-info/renter-employment-info/application-renter-employment-info.dto';
import { ApplicationRenterReferenceDto } from '../renter-info/renter-reference/application-renter-reference.dto';
import { ApplicationRenterResidenceInfoDto } from '../renter-info/renter-residence-info/application-renter-residence-info.dto';
import { ApplicantType } from '../enums/applicant-type.enum';
import { BackgroundCheckStatusEnum } from '../enums/background-check-status.enum';
import { ApiProperty } from '@nestjs/swagger';
import { RenterDto } from '../../../../renter/renter/renter.dto';
import { FileDto } from '../../../file/models/file.dto';

export class ApplicationDto extends SaveApplicationCommand {
  @ApiProperty()
  id: string;

  @ApiProperty({ type: ApplicationRenterDetailsDto, nullable: true })
  renterDetails: ApplicationRenterDetailsDto;

  @ApiProperty({ type: ApplicationRenterEmploymentInfoDto, isArray: true })
  employmentInfo: ApplicationRenterEmploymentInfoDto[];

  @ApiProperty({ type: ApplicationRenterReferenceDto, isArray: true })
  references: ApplicationRenterReferenceDto[];

  @ApiProperty({ type: ApplicationRenterResidenceInfoDto, isArray: true })
  residenceInfo: ApplicationRenterResidenceInfoDto[];

  @ApiProperty({ enum: ApplicantType })
  type: ApplicantType;

  @ApiProperty({ enum: BackgroundCheckStatusEnum })
  backgroundCheckStatus: BackgroundCheckStatusEnum;

  @ApiProperty({ type: RenterDto })
  renter: RenterDto;

  @ApiProperty({ isArray: true })
  photoIds: FileDto[];

  @ApiProperty({ isArray: true })
  proofsOfIncome: FileDto[];

  @ApiProperty({ isArray: true })
  additionalDocuments: FileDto[];

  @ApiProperty({ isArray: true })
  petPhotos: FileDto[];

  @ApiProperty()
  expiresAt: Date;

  @ApiProperty()
  isPaid: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

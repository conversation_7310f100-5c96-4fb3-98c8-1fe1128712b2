import { Expose, instanceToPlain } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinC<PERSON>umn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApplicationStatus } from './enums/application-status.enum';
import { Renter } from '../../../renter/renter/renter.entity';
import { BackgroundCheckStatusEnum } from './enums/background-check-status.enum';
import { ApplicationDto } from './model/application.dto';
import { ApplicantType } from './enums/applicant-type.enum';
import { ApplicationRenterDetails } from './renter-info/renter-details/application-renter-details.entity';
import { ApplicationRenterReference } from './renter-info/renter-reference/application-renter-reference.entity';
import { ApplicationRenterEmploymentInfo } from './renter-info/renter-employment-info/application-renter-employment-info.entity';
import { ApplicationRenterResidenceInfo } from './renter-info/renter-residence-info/application-renter-residence-info.entity';
import { convertToRenterToDto } from '../../../renter/renter/renter.dto';
import { File } from '../../file/entities/file.entity';

@Entity()
export class Application {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Renter, {
    nullable: false,
    lazy: true,
  })
  renter: Promise<Renter> | Renter;

  @Expose()
  @Column({ type: 'varchar', length: 512, nullable: true })
  screeningRequestRenterId: string;

  @Expose()
  @Column({
    type: 'enum',
    enum: BackgroundCheckStatusEnum,
    default: BackgroundCheckStatusEnum.NOT_STARTED,
  })
  backgroundCheckStatus: BackgroundCheckStatusEnum;

  @Expose()
  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Expose()
  @JoinColumn()
  @OneToOne(() => ApplicationRenterDetails, {
    lazy: true,
    nullable: true,
  })
  renterDetails: Promise<ApplicationRenterDetails> | ApplicationRenterDetails;

  @Column({ nullable: true })
  renterDetailsId: string | null;

  @Expose()
  @OneToMany(() => ApplicationRenterReference, (reference) => reference.application, {
    nullable: true,
    lazy: true,
  })
  references: Promise<ApplicationRenterReference[]> | ApplicationRenterReference[];

  @Expose()
  @OneToMany(() => ApplicationRenterEmploymentInfo, (employmentInfo) => employmentInfo.application, {
    nullable: true,
    lazy: true,
  })
  employmentInfo: Promise<ApplicationRenterEmploymentInfo[]> | ApplicationRenterEmploymentInfo[];

  @Expose()
  @OneToMany(() => ApplicationRenterResidenceInfo, (residenceInfo) => residenceInfo.application, {
    nullable: true,
    lazy: true,
  })
  residenceInfo: Promise<ApplicationRenterResidenceInfo[]> | ApplicationRenterResidenceInfo[];

  @Expose()
  @ManyToMany(() => File, { lazy: true })
  @JoinTable()
  photoIds: Promise<File[]> | File[];

  @Expose()
  @ManyToMany(() => File, { lazy: true })
  @JoinTable()
  proofsOfIncome: Promise<File[]> | File[];

  @Expose()
  @ManyToMany(() => File, { lazy: true })
  @JoinTable()
  additionalDocuments: Promise<File[]> | File[];

  @Expose()
  @ManyToMany(() => File, { lazy: true })
  @JoinTable()
  petPhotos: Promise<File[]> | File[];

  @Expose()
  @Column({
    type: 'enum',
    enum: ApplicationStatus,
    default: ApplicationStatus.IN_PROGRESS,
  })
  status: ApplicationStatus;

  @Expose()
  @Column({
    type: 'enum',
    enum: ApplicantType,
  })
  type: ApplicantType;

  @Expose()
  @Column({ type: 'boolean', default: false })
  isPaid: boolean;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  public static async convertToDto(
    application: Application,
    extendedApplicationDetails = false,
  ): Promise<ApplicationDto> {
    if (!application || !application.id) {
      return null;
    }

    const applicationDto = <ApplicationDto>instanceToPlain(application, {
      excludeExtraneousValues: true,
    });

    if (extendedApplicationDetails) {
      applicationDto.renterDetails = ApplicationRenterDetails.convertToDto(await application.renterDetails);
      applicationDto.employmentInfo = (await application.employmentInfo).map((employmentInfo) =>
        ApplicationRenterEmploymentInfo.convertToDto(employmentInfo),
      );
      applicationDto.references = (await application.references).map((reference) =>
        ApplicationRenterReference.convertToDto(reference),
      );
      applicationDto.residenceInfo = (await application.residenceInfo).map((residence) =>
        ApplicationRenterResidenceInfo.convertToDto(residence),
      );
      applicationDto.renter = convertToRenterToDto(await application.renter);
      applicationDto.photoIds = (await application.photoIds).map((photoId) => File.convertToDto(photoId));
      applicationDto.petPhotos = (await application.petPhotos).map((petPhoto) => File.convertToDto(petPhoto));
      applicationDto.proofsOfIncome = (await application.proofsOfIncome).map((proofsOfIncome) =>
        File.convertToDto(proofsOfIncome),
      );
      applicationDto.additionalDocuments = (await application.additionalDocuments).map((additionalDocument) =>
        File.convertToDto(additionalDocument),
      );
    }

    return applicationDto;
  }
}

import { Expose, instanceTo<PERSON>lain } from 'class-transformer';
import {
  PrimaryGeneratedColumn,
  ManyToOne,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Entity,
  DeleteDateColumn,
} from 'typeorm';
import { ApplicationBundle } from '../application-bundle/application-bundle.entity';
import { Renter } from '../../../renter/renter/renter.entity';
import { ApplicationInviteStatus } from './application-invite-status.enum';
import { ApplicantType } from '../application/enums/applicant-type.enum';
import { ApplicationInviteDto } from './application-invite.dto';
import { Application } from '../application/application.entity';
import { User } from '../../user/entities/user.entity';

@Entity()
export class ApplicationInvite {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => ApplicationBundle, {
    nullable: false,
    lazy: true,
  })
  applicationBundle: Promise<ApplicationBundle> | ApplicationBundle;

  @ManyToOne(() => Renter, {
    nullable: false,
    lazy: true,
  })
  renter: Promise<Renter> | Renter;

  @ManyToOne(() => Application, {
    nullable: true,
    lazy: true,
  })
  application: Promise<Application> | Application;

  @ManyToOne(() => User, {
    nullable: true,
    lazy: true,
  })
  invitedBy: Promise<User> | User;

  @Expose()
  @Column({
    type: 'enum',
    enum: ApplicationInviteStatus,
    default: ApplicationInviteStatus.PENDING,
  })
  status: ApplicationInviteStatus;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  transUnionScreeningRequestId: string;

  @Expose()
  @Column({
    type: 'enum',
    enum: ApplicantType,
  })
  type: ApplicantType;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  public static async convertToDto(
    applicationInvite: ApplicationInvite,
    withApplicationBundle = false,
    withRenter = false,
    withApplication = false,
  ): Promise<ApplicationInviteDto> {
    const bundle = await applicationInvite.applicationBundle;

    const applicationInviteDto = <ApplicationInviteDto>instanceToPlain(applicationInvite, {
      excludeExtraneousValues: true,
    });

    const invitedBy = await applicationInvite.invitedBy;

    applicationInviteDto.invitedBy = {
      id: invitedBy.id,
      name: invitedBy.name,
    };

    if (withApplicationBundle) {
      applicationInviteDto.applicationBundle = await ApplicationBundle.convertToDto(bundle, true);
    }

    if (withApplication) {
      const application = await applicationInvite.application;
      applicationInviteDto.application = await Application.convertToDto(application, true);
    }

    if (withRenter) {
      const renter = await applicationInvite.renter;
      applicationInviteDto.renter = {
        id: renter.user.id,
        name: renter.user.name,
        email: renter.user.email,
      };
    }

    return applicationInviteDto;
  }
}

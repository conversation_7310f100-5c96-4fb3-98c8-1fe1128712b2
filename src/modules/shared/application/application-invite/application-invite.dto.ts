import { ApiProperty } from '@nestjs/swagger';
import { IsObject } from 'class-validator';
import { ApplicationBundleDto } from '../application-bundle/application-bundle.dto';
import { ApplicationInviteStatus } from './application-invite-status.enum';
import { ApplicationDto } from '../application/model/application.dto';
import { ApplicantType } from '../application/enums/applicant-type.enum';

class ApplicationInviteRenterDto {
  @ApiProperty({
    description: 'user id',
    type: String,
  })
  id: string;

  @ApiProperty({
    description: 'Name of the renter',
    type: String,
  })
  name: string;

  @ApiProperty({
    description: 'Email of the renter',
    type: String,
  })
  email: string;
}

class ApplicationInviteInvitedByUserDto {
  @ApiProperty({
    description: 'Id of the user',
    type: String,
  })
  id: string;

  @ApiProperty({
    description: 'Name of the user',
    type: String,
  })
  name: string;
}

export class ApplicationInviteDto {
  @ApiProperty({
    description: 'Unique identifier for the application invite',
    type: String,
  })
  id: string;

  @IsObject()
  @ApiProperty({
    description: 'The user who sent the invite',
    type: ApplicationInviteInvitedByUserDto,
  })
  invitedBy: ApplicationInviteInvitedByUserDto;

  @IsObject()
  @ApiProperty({
    description: 'Application bundle for the application invite',
    type: ApplicationBundleDto,
  })
  applicationBundle: ApplicationBundleDto;

  @IsObject()
  @ApiProperty({
    description: 'Application for the application invite',
    type: ApplicationDto,
  })
  application: ApplicationDto;

  // TODO: rename to user
  @IsObject()
  @ApiProperty({
    description: 'Renter for the application invite',
    type: ApplicationInviteRenterDto,
  })
  renter: ApplicationInviteRenterDto;

  @ApiProperty({
    description: 'Current status of the application invite',
    enum: ApplicationInviteStatus,
  })
  status: ApplicationInviteStatus;

  @ApiProperty({
    description: 'Type of the applicant',
    enum: ApplicantType,
  })
  type: ApplicantType;

  transUnionScreeningRequestId: string;

  @ApiProperty({
    description: 'Timestamp when the application invite was created',
    type: Date,
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp when the application invite was last updated',
    type: Date,
  })
  updatedAt: Date;
}

import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuthService } from '../../auth/auth.service';
import { ApplicationInvite } from './application-invite.entity';
import { ApplicantType } from '../application/enums/applicant-type.enum';
import { ApplicationBundle } from '../application-bundle/application-bundle.entity';
import { ApplicationBundleStatus } from '../application-bundle/application-bundle-status.enum';

@Injectable()
export class ApplicationInviteService {
  constructor(
    @InjectRepository(ApplicationInvite)
    private readonly applicationInviteRepository: Repository<ApplicationInvite>,
    private readonly authService: AuthService,
  ) {}

  findById(id: string): Promise<ApplicationInvite> {
    return this.applicationInviteRepository.findOneByOrFail({ id });
  }

  findByApplicationId(applicationId: string): Promise<ApplicationInvite> {
    return this.applicationInviteRepository.findOneByOrFail({ application: { id: applicationId } });
  }

  findByBundleIdAndUserId(applicationBundleId: string, userId: string): Promise<ApplicationInvite> {
    return this.applicationInviteRepository.findOneByOrFail({
      applicationBundle: { id: applicationBundleId },
      renter: {
        user: {
          id: userId,
        },
      },
    });
  }

  findBundleIdAndEmail(applicationBundleId: string, email: string): Promise<ApplicationInvite> {
    return this.applicationInviteRepository.findOneBy({
      applicationBundle: { id: applicationBundleId },
      renter: {
        user: { email },
      },
    });
  }

  findAllApplicationInvitesByRenter(renterId: string, withRelations = false): Promise<ApplicationInvite[]> {
    const relations = withRelations
      ? [
          'applicationBundle',
          'applicationBundle.property',
          'applicationBundle.property.leaseConditions',
          'applicationBundle.property.location',
          'application',
        ]
      : [];

    return this.applicationInviteRepository.find({
      where: {
        renter: { id: renterId },
      },
      relations,
    });
  }

  findAllApplicationInvitesByBundle(applicationBundleId: string, withRelations = false): Promise<ApplicationInvite[]> {
    const relations = withRelations ? ['renter', 'application'] : [];

    return this.applicationInviteRepository.find({
      where: {
        applicationBundle: { id: applicationBundleId },
      },
      relations,
    });
  }

  async save(applicationInvite: Partial<ApplicationInvite>): Promise<void> {
    const createdApplicationInvite = this.applicationInviteRepository.create();
    Object.assign(createdApplicationInvite, applicationInvite);

    await this.applicationInviteRepository.save(createdApplicationInvite);
  }

  async createApplicationInvite(applicationInvite: Partial<ApplicationInvite>): Promise<ApplicationInvite> {
    const createdApplicationInvite = this.applicationInviteRepository.create();
    Object.assign(createdApplicationInvite, applicationInvite);

    return this.applicationInviteRepository.save(createdApplicationInvite);
  }

  async resendApplicationInviteToPrimaryApplicant(bundle: ApplicationBundle): Promise<void> {
    if (bundle.status !== ApplicationBundleStatus.SENT) {
      throw new BadRequestException('Application bundle is already in progress');
    }

    const primaryApplicant = await bundle.primaryApplicant;
    const property = await bundle.property;
    const propertyLocation = await property.location;
    const propertyOwner = await property.owner;
    const ownerUser = await propertyOwner.user;

    await this.authService.invitePrimaryApplicantToApply(
      primaryApplicant.user,
      ownerUser,
      propertyLocation,
      property.coverImage,
    );
  }

  async sendApplicationInvite(applicationInvite: ApplicationInvite, bundle: ApplicationBundle): Promise<void> {
    const renter = await applicationInvite.renter;
    const property = await bundle.property;
    const propertyLocation = await property.location;
    const invitedBy = await applicationInvite.invitedBy;

    switch (applicationInvite.type) {
      case ApplicantType.APPLICANT:
        await this.authService.invitePrimaryApplicantToApply(
          renter.user,
          invitedBy,
          propertyLocation,
          property.coverImage,
        );
        break;
      case ApplicantType.CO_APPLICANT:
        await this.authService.inviteCoApplicantToApply(renter.user, invitedBy, propertyLocation, property.coverImage);
        break;
      case ApplicantType.CO_SIGNER:
        await this.authService.inviteCoSignerToApply(renter.user, invitedBy, propertyLocation, property.coverImage);
        break;
    }
  }

  async updateApplication(applicationInviteId: string, applicationInvite: Partial<ApplicationInvite>): Promise<void> {
    await this.applicationInviteRepository.update(applicationInviteId, applicationInvite);
  }

  async delete(applicationInviteId: string): Promise<void> {
    await this.applicationInviteRepository.softDelete(applicationInviteId);
  }
}

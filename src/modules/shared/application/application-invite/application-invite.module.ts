import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicationInvite } from './application-invite.entity';
import { ApplicationInviteService } from './application-invite.service';
import { RenterModule } from '../../../renter/renter/renter.module';
import { AuthModule } from '../../auth/auth.module';

@Module({
  imports: [TypeOrmModule.forFeature([ApplicationInvite]), RenterModule, AuthModule],
  providers: [ApplicationInviteService],
  exports: [ApplicationInviteService],
})
export class ApplicationInviteModule {}

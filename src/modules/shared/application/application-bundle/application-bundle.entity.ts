import { Expose, instanceToPlain } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { ApplicationBundleStatus } from './application-bundle-status.enum';
import { ApplicationBundleDto } from './application-bundle.dto';
import { Renter } from '../../../renter/renter/renter.entity';
import { Company } from '../../company/entities/company.entity';
import { LeaseConditions } from '../../../investor/property/property/property-details/lease-conditions/lease-conditions.entity';
import { PropertyLocation } from '../../../investor/property/property/property-details/location/property-location.entity';
import { ApplicationInvite } from '../application-invite/application-invite.entity';

@Entity()
export class ApplicationBundle {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Property, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @Index()
  @ManyToOne(() => Renter, {
    nullable: false,
    lazy: true,
  })
  primaryApplicant: Promise<Renter> | Renter;

  @Index()
  @ManyToOne(() => Company, {
    nullable: false,
    lazy: true,
  })
  company: Promise<Company> | Company;

  @OneToMany(() => ApplicationInvite, (applicationInvite) => applicationInvite.applicationBundle, {
    nullable: true,
    lazy: true,
  })
  applicationInvites: Promise<ApplicationInvite[]> | ApplicationInvite[];

  @Expose()
  @Column({ type: 'integer', nullable: true })
  numberOfOccupantsUnderEighteen: number;

  @Expose()
  @Column({
    type: 'enum',
    enum: ApplicationBundleStatus,
    default: ApplicationBundleStatus.SENT,
  })
  status: ApplicationBundleStatus;

  @DeleteDateColumn()
  deletedAt?: Date;

  @Expose()
  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  @Expose()
  @Column({ type: 'timestamp', nullable: true })
  submittedAt: Date;

  public static async convertToDto(
    applicationBundle: ApplicationBundle,
    extendedPropertyDetails = false,
  ): Promise<ApplicationBundleDto> {
    if (!applicationBundle || !applicationBundle.id) {
      return null;
    }

    const applicationBundleDto = <ApplicationBundleDto>instanceToPlain(applicationBundle, {
      excludeExtraneousValues: true,
    });

    const property = await applicationBundle.property;
    const ownerUser = await (await property.owner).user;

    applicationBundleDto.property = {
      id: property.id,
      displayName: property.displayName,
      coverImage: property.coverImage,
      owner: {
        name: ownerUser.name,
        email: ownerUser.email,
        phoneNumber: ownerUser.phoneNumber,
      },
    };

    if (extendedPropertyDetails) {
      applicationBundleDto.property.leaseConditions = LeaseConditions.convertToDto(await property.leaseConditions);
      applicationBundleDto.property.location = PropertyLocation.convertToDto(await property.location);
    }

    return applicationBundleDto;
  }
}

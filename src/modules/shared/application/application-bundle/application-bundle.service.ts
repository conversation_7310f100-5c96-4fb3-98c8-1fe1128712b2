import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OutboundCommunicationService } from '../../communication/outbound-communication/outbound-communication.service';
import { ApplicationStatus } from '../application/enums/application-status.enum';
import { ApplicationBundleStatus } from './application-bundle-status.enum';
import { ApplicationBundle } from './application-bundle.entity';

@Injectable()
export class ApplicationBundleService {
  constructor(
    @InjectRepository(ApplicationBundle)
    private readonly applicationBundleRepository: Repository<ApplicationBundle>,
    private outboundCommunicationService: OutboundCommunicationService,
  ) {}

  async getAllApplicationBundlesByRenterAndCompany(renterId: string, companyId: string): Promise<ApplicationBundle[]> {
    return this.applicationBundleRepository.find({
      where: {
        primaryApplicant: { id: renterId },
        company: { id: companyId },
      },
      relations: ['property'],
    });
  }

  async isBundleExists(renterId: string, companyId: string, propertyId: string): Promise<boolean> {
    const applicationBundlesCount = await this.applicationBundleRepository.count({
      where: {
        primaryApplicant: { id: renterId },
        company: { id: companyId },
        property: { id: propertyId },
      },
    });

    return applicationBundlesCount > 0;
  }

  async findById(applicationBundleId: string, withRelations = false): Promise<ApplicationBundle> {
    return this.applicationBundleRepository.findOne({
      where: {
        id: applicationBundleId,
      },
      relations: withRelations ? ['property', 'applicationInvites', 'applicationInvites.renter'] : [],
    });
  }

  async findByApplicationId(applicationId: string): Promise<ApplicationBundle> {
    return this.applicationBundleRepository.findOneOrFail({
      where: {
        applicationInvites: { application: { id: applicationId } },
      },
    });
  }

  async createApplicationBundle(applicationBundle: Partial<ApplicationBundle>): Promise<ApplicationBundle> {
    const createdApplicationBundle = this.applicationBundleRepository.create();
    Object.assign(createdApplicationBundle, applicationBundle);

    return this.applicationBundleRepository.save(createdApplicationBundle);
  }

  async update(applicationBundleId: string, payload: Partial<ApplicationBundle>): Promise<ApplicationBundle> {
    const result = await this.applicationBundleRepository
      .createQueryBuilder()
      .update({ ...payload })
      .where({ id: applicationBundleId })
      .returning('*')
      .execute();

    return Object.assign(new ApplicationBundle(), { ...result.raw[0] });
  }

  async acceptApplicationBundle(applicationBundleId: string): Promise<ApplicationBundle> {
    return this.update(applicationBundleId, { status: ApplicationBundleStatus.ACCEPTED });
  }

  async declineApplicationBundle(applicationBundleId: string): Promise<ApplicationBundle> {
    return this.update(applicationBundleId, { status: ApplicationBundleStatus.DECLINED });
  }

  async sendBundleIsReadyForSubmissionEmailIfAllApplicationsCompleted(bundle: ApplicationBundle): Promise<void> {
    const areAllApplicationsCompleted = await this.areAllApplicationsInTheBundleCompleted(bundle);

    if (areAllApplicationsCompleted) {
      const primaryRenter = await bundle.primaryApplicant;
      const property = await bundle.property;

      await this.outboundCommunicationService.sendBundleIsReadyForSubmissionNotification(
        primaryRenter.user,
        bundle.id,
        await property.location,
      );
    }
  }

  async areAllApplicationsInTheBundleCompleted(bundle: ApplicationBundle): Promise<boolean> {
    const invites = await bundle.applicationInvites;
    const applications = await Promise.all(invites.map(async (invite) => await invite.application));

    const validApplications = applications.filter((application) => application !== null);

    if (validApplications.length === 0 || validApplications.length < invites.length) {
      return false;
    }

    const areAllCompleted = validApplications.every(
      (application) => application.status === ApplicationStatus.COMPLETED,
    );

    return areAllCompleted;
  }
}

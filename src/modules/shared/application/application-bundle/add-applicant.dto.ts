import { IsEmail, <PERSON>E<PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ApplicantType } from '../application/enums/applicant-type.enum';

export class AddApplicantDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  lastName: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  @IsEmail()
  email: string;

  @IsEnum(ApplicantType)
  @ApiProperty({ enum: ApplicantType, enumName: 'ApplicantType' })
  type: ApplicantType;
}

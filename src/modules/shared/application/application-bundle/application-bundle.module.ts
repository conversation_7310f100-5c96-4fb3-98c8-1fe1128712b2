import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicationBundle } from './application-bundle.entity';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { ApplicationBundleService } from './application-bundle.service';
import { OutboundCommunicationModule } from '../../communication/outbound-communication/outbound-communication.module';

@Module({
  imports: [TypeOrmModule.forFeature([ApplicationBundle, Property]), OutboundCommunicationModule],
  providers: [ApplicationBundleService],
  exports: [ApplicationBundleService],
})
export class ApplicationBundleModule {}

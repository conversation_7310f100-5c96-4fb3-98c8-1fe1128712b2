import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';
import { LeaseConditionsDto } from '../../../investor/property/property/property-details/lease-conditions/lease-conditions.dto';
import { PropertyLocationDto } from '../../../investor/property/property/property-details/location/property-location.dto';
import { RenterDto } from '../../../renter/renter/renter.dto';
import { ApplicationBundleStatus } from './application-bundle-status.enum';

class ApplicationBundlePropertyOwnerDto {
  @ApiProperty()
  name: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  phoneNumber: string;
}

class ApplicationBundlePropertyDto {
  @IsString()
  @ApiProperty()
  id: string;

  @IsString()
  @ApiProperty()
  displayName: string;

  @IsString()
  @ApiProperty()
  coverImage: string;

  @ApiProperty()
  owner: ApplicationBundlePropertyOwnerDto;

  @ApiPropertyOptional()
  leaseConditions?: LeaseConditionsDto;

  @ApiPropertyOptional()
  location?: PropertyLocationDto;
}

export class ApplicationBundleDto {
  @IsString()
  @ApiProperty()
  id: string;

  @IsObject()
  @ApiProperty()
  property: ApplicationBundlePropertyDto;

  @IsObject()
  @ApiProperty()
  primaryApplicant: RenterDto;

  @IsNumber()
  @IsOptional()
  @ApiProperty()
  numberOfOccupantsUnderEighteen: number;

  @IsEnum(ApplicationBundleStatus)
  @ApiProperty({ enum: ApplicationBundleStatus, enumName: 'ApplicationBundleStatus' })
  status: ApplicationBundleStatus;

  @IsDate()
  @ApiProperty()
  @Type(() => Date)
  submittedAt: Date;

  @IsDate()
  @ApiProperty()
  @Type(() => Date)
  expiresAt: Date;

  @IsDate()
  @ApiProperty()
  @Type(() => Date)
  createdAt: Date;

  @IsDate()
  @ApiProperty()
  updatedAt: Date;
}

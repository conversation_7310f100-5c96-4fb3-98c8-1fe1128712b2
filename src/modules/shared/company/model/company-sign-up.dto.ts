import { <PERSON><PERSON><PERSON>, <PERSON>N<PERSON><PERSON>, IsOptional, IsString, Min } from 'class-validator';

import { ApiPropertyOptional } from '@nestjs/swagger';

import { CompanyNumberOfEmployees } from '../enums/company-number-of-employees.enum';
import { IsUSPhoneNumber } from '../../../../utils/validators/us-phone-number.validator';
import { CompanyType } from '../enums/company-type.enum';
import { ScreeningSensitivity } from '../../../investor/property/property/property-details/renter-requirements/screening-sensitivity.enum';
import { CommunicationChannel } from '../../communication/conversation/enums/preferred-communication-channel.enum';

export class CompanySignUpDto {
  @IsString()
  @IsOptional()
  @IsUSPhoneNumber()
  @ApiPropertyOptional({ example: '8003434343', description: '10 digits (US phone number w/o "+1"' })
  phoneNumber?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ example: 'Burbuleh' })
  companyName?: string;

  @IsEnum(CompanyNumberOfEmployees)
  @IsOptional()
  @ApiPropertyOptional({
    enum: [
      CompanyNumberOfEmployees.OneTo50,
      CompanyNumberOfEmployees.FiftyOneTo200,
      CompanyNumberOfEmployees.TwoHundredOneTo2000,
      CompanyNumberOfEmployees.TwoThousandPlus,
    ],
    enumName: 'CompanyNumberOfEmployees',
    example: CompanyNumberOfEmployees.OneTo50,
  })
  numberOfEmployees?: CompanyNumberOfEmployees;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional({ example: 5 })
  numberOfUnitsOverseeing?: number;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ example: 'Help to find renters' })
  perceivedProductValueProposition?: string;

  @IsEnum(CompanyType)
  @IsOptional()
  @ApiPropertyOptional({ enum: CompanyType, enumName: 'CompanyType', example: CompanyType.Individual })
  accountType?: CompanyType;

  @IsEnum(ScreeningSensitivity)
  @IsOptional()
  @ApiPropertyOptional({
    enum: ScreeningSensitivity,
    enumName: 'ScreeningSensitivity',
    example: ScreeningSensitivity.MODERATE,
  })
  screeningSensitivity?: ScreeningSensitivity;

  @IsEnum(CommunicationChannel)
  @IsOptional()
  @ApiPropertyOptional({
    enum: CommunicationChannel,
    enumName: 'CommunicationChannel',
    example: CommunicationChannel.EMAIL,
  })
  preferredCommunicationChannel?: CommunicationChannel;
}

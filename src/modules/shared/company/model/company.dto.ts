import { instanceToPlain } from 'class-transformer';
import { Company } from '../entities/company.entity';
import { CompanyNumberOfEmployees } from '../enums/company-number-of-employees.enum';

export class CompanyDto {
  id: string;
  name: string;
  numberOfEmployees: CompanyNumberOfEmployees;
  integrations: {
    aptly: {
      feedUrl: string | null;
    };
  };
}

export async function convertCompanyToDto(company: Company, withRelations = false): Promise<CompanyDto> {
  const companyDto = <CompanyDto>instanceToPlain(company, { strategy: 'excludeAll' });

  if (withRelations) {
    companyDto.integrations = {
      aptly: {
        feedUrl: company.settings.aptlyFeedUrl,
      },
    };
  }

  return companyDto;
}

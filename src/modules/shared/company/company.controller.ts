import { Request } from 'express';

import { Controller, Get, Inject, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';

import { HasRoles } from '../auth/decorators/role-access.decorator';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Role } from '../auth/models/roles-enum';
import { CompanyService } from './company.service';
import { CompanyDto, convertCompanyToDto } from './model/company.dto';

@ApiTags('company')
@Controller('company')
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@ApiBearerAuth()
export class CompanyController {
  @Inject(CompanyService)
  private readonly companyService: CompanyService;

  @Get()
  @ApiOkResponse({ description: "Returns user's company" })
  public async getMyCompany(@Req() req: Request): Promise<CompanyDto> {
    const company = await this.companyService.getCompanyByUser(req.user.id, true);
    return convertCompanyToDto(company, true);
  }
}

import { Repository } from 'typeorm';

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { OutboundCommunicationService } from '../communication/outbound-communication/outbound-communication.service';
import { CompanyPersonaService } from './company-persona.service';
import { CompanySettingsService } from './company-settings.service';
import { Company } from './entities/company.entity';

@Injectable()
export class CompanyService {
  constructor(
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    private readonly companyPersonaService: CompanyPersonaService,
    private readonly outboundCommunicationService: OutboundCommunicationService,
    private readonly companySettingsService: CompanySettingsService,
  ) {}

  async createCompany(companyData: Partial<Company> = {}): Promise<Company> {
    const company: Company = this.companyRepository.create();
    Object.assign(company, companyData);
    company.settings = this.companySettingsService.createInstance();

    return this.companyRepository.save(company);
  }

  async updateCompany(companyId: string, updateBody: Partial<Company> = {}): Promise<void> {
    await this.companyRepository.update(companyId, updateBody);
  }

  async getCompanyByUser(userId: string, withRelations: boolean = false): Promise<Company> {
    const relations = [];
    if (withRelations) {
      relations.push('settings');
    }

    return this.companyRepository.findOneOrFail({
      where: {
        investors: {
          user: {
            id: userId,
          },
        },
      },
      relations,
    });
  }

  async getCompanyByUserAndCompanyId(companyId: string, userId: string): Promise<Company> {
    return this.companyRepository.findOneOrFail({
      where: {
        id: companyId,
        investors: {
          user: {
            id: userId,
          },
        },
      },
    });
  }
}

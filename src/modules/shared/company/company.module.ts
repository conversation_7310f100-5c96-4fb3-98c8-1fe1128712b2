import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { UserModule } from '../user/user.module';
import { CompanyPersonaService } from './company-persona.service';
import { CompanyController } from './company.controller';
import { CompanyService } from './company.service';
import { CompanyPersona } from './entities/company-persona.entity';
import { Company } from './entities/company.entity';
import { OutboundCommunicationModule } from '../communication/outbound-communication/outbound-communication.module';
import { InvestorModule } from '../../investor/investor.module';
import { CompanySettings } from './entities/company-settings.entity';
import { CompanySettingsService } from './company-settings.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Company, CompanyPersona, CompanySettings]),
    forwardRef(() => InvestorModule),
    UserModule,
    OutboundCommunicationModule,
  ],
  controllers: [CompanyController],
  providers: [CompanyService, CompanyPersonaService, CompanySettingsService],
  exports: [CompanyService, CompanyPersonaService, CompanySettingsService],
})
export class CompanyModule {}

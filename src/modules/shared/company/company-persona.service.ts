import { Repository } from 'typeorm';

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CompanyPersona } from './entities/company-persona.entity';

@Injectable()
export class CompanyPersonaService {
  constructor(
    @InjectRepository(CompanyPersona)
    private readonly companyPersonaRepository: Repository<CompanyPersona>,
  ) {}

  async findByCompanyId(companyId: string): Promise<CompanyPersona> {
    return this.companyPersonaRepository.findOneBy({
      company: {
        id: companyId,
      },
    });
  }

  async create(companyPersonaData: Partial<CompanyPersona> = {}): Promise<CompanyPersona> {
    const companyPersona: CompanyPersona = this.companyPersonaRepository.create();
    Object.assign(companyPersona, companyPersonaData);

    return await this.companyPersonaRepository.save(companyPersona);
  }

  async update(companyPersonaId: string, updateBody: Partial<CompanyPersona> = {}): Promise<void> {
    await this.companyPersonaRepository.update(companyPersonaId, updateBody);
  }
}

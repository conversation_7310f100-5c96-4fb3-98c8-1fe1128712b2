import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CompanySettings } from './entities/company-settings.entity';
import { Repository } from 'typeorm';

@Injectable()
export class CompanySettingsService {
  constructor(
    @InjectRepository(CompanySettings)
    private readonly settingsRepository: Repository<CompanySettings>,
  ) {}

  createInstance(): CompanySettings {
    return this.settingsRepository.create();
  }

  async updateSettings(settingsId: string, updates: Partial<CompanySettings>): Promise<void> {
    await this.settingsRepository.update(settingsId, updates);
  }

  async findByCompanyId(companyId: string): Promise<CompanySettings | null> {
    return this.settingsRepository
      .createQueryBuilder('settings')
      .leftJoin('settings.company', 'company')
      .where('company.id = :companyId', { companyId })
      .getOne();
  }
}

import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Company } from './company.entity';

@Entity()
export class CompanyPersona {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Company, {
    nullable: false,
    lazy: true,
  })
  @JoinColumn()
  company: Company;

  @Column({ type: 'text', nullable: true })
  perceivedProductValueProposition: string;

  @Column({ type: 'int', nullable: true })
  numberOfUnitsOverseeing: number;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

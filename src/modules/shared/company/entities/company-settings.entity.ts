import { Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

import { Company } from './company.entity';

@Entity()
export class CompanySettings {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Company, (company) => company.settings)
  company: Promise<Company> | Company;

  @Column({ type: 'boolean', default: true })
  advancedRenterScreening: boolean;

  @Column({ type: 'varchar', length: 512, nullable: true })
  aptlyFeedUrl: string;

  @Column({ type: 'boolean', default: false })
  syndicateAsAgent: boolean;

  @Column({ type: 'boolean', default: false })
  syndicateAgentNumber: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

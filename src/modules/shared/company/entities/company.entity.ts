import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Investor } from '../../../investor/investor/investor.entity';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { CompanyNumberOfEmployees } from '../enums/company-number-of-employees.enum';
import { CompanySettings } from './company-settings.entity';
import { CompanyType } from '../enums/company-type.enum';

@Entity()
export class Company {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: true })
  name: string;

  @OneToMany(() => Property, (property) => property.company, {
    lazy: true,
  })
  properties: Promise<Property[]> | Property[];

  @OneToMany(() => Investor, (investor) => investor.company, {
    lazy: true,
  })
  investors: Promise<Investor[]> | Investor[];

  @OneToOne(() => CompanySettings, (settings) => settings.company, {
    nullable: false,
    eager: true,
    cascade: true,
  })
  @JoinColumn()
  settings: CompanySettings;

  @Column({ type: 'enum', enum: CompanyNumberOfEmployees, nullable: true })
  numberOfEmployees: CompanyNumberOfEmployees;

  @Column({ type: 'enum', enum: CompanyType, nullable: true })
  companyType: CompanyType;

  @DeleteDateColumn()
  deletedAt?: Date;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

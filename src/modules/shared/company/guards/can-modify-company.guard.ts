import { BadRequestException, ExecutionContext, Injectable, NotFoundException } from '@nestjs/common';

import { CompanyService } from '../company.service';

@Injectable()
export class CanModifyCompanyGuard {
  constructor(private companyService: CompanyService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const companyId = request.params.companyId;
    const userId = request.user.id;

    if (!companyId || !userId) {
      throw new BadRequestException('Company or user ID not provided');
    }

    const company = await this.companyService.getCompanyByUserAndCompanyId(companyId, userId);

    if (!company) {
      throw new NotFoundException('Company not found');
    }

    request.company = company;

    return true;
  }
}

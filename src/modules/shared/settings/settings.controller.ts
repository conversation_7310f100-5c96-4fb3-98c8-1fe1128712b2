import { Controller, Get, Query, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { Public } from '../auth/decorators/public-access.decorator';
import { ConfigService } from '@nestjs/config';

@ApiTags('settings')
@Controller('settings')
export class SettingsController {
  constructor(private readonly configService: ConfigService) {}

  @Public()
  @Get('calendar-callback')
  @ApiOperation({ summary: 'Handle Google Calendar OAuth callback' })
  @ApiResponse({ status: 200, description: 'Redirects to the frontend with the auth code' })
  async handleCalendarCallback(@Query('code') code: string, @Res() res: Response): Promise<void> {
    if (!code) {
      return res.redirect(`${this.configService.get('INVESTOR_APP_URL')}/settings?error=missing_code`);
    }

    return res.redirect(`${this.configService.get('INVESTOR_APP_URL')}/settings/calendar?code=${code}`);
  }
}

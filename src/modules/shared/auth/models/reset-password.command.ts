import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, IsStrongPassword } from 'class-validator';
import { Role } from './roles-enum';

export class PasswordSetupDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsStrongPassword(null, {
    message:
      'Password is too weak. It must contain at least 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character and be at least 8 characters long.',
  })
  newPassword: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  token: string;
}

export class PasswordSetupWithRoleDto extends PasswordSetupDto {
  @ApiProperty({ enum: Role, enumName: 'Role' })
  @IsEnum(Role)
  @IsNotEmpty()
  forRole: Role;
}

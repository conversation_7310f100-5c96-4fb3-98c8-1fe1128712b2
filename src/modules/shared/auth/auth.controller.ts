import { Body, ConflictException, Controller, Patch, Post, Request, UseGuards } from '@nestjs/common';
import { ApiCreatedResponse, ApiOkResponse, ApiTags } from '@nestjs/swagger';

import { User } from '../user/entities/user.entity';
import {
  ConfirmUserEmailDto,
  InvestorCredentialsSignUpDto,
  InvestorGoogleSignInDto,
  InvestorGoogleSignUpDto,
  InvestorUserCreatedDto,
  RenterActivationWithGoogleDto,
  RequestOneTimeSignInCodeDto,
  ResendEmailConfirmationCodeDto,
  SignInWithOneTimeCodeDto,
} from '../user/model/create-user.dto';
import { UserVerificationType } from '../user/model/password-reset-type.enum';
import { AuthService } from './auth.service';
import { Public } from './decorators/public-access.decorator';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { AccessToken } from './models/access-token';
import { PasswordResetDto } from './models/password-reset.dto';
import { PasswordSetupDto, PasswordSetupWithRoleDto } from './models/reset-password.command';
import { Role } from './models/roles-enum';
import { SignInWithCredsDto } from './models/sign-in-with-creds.dto';
import { SignedUpGoogleInvestorDto, SignedUpInvestorDto } from './models/signed-up-user.dto';

@Public()
@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('sign-in-credentials')
  @UseGuards(LocalAuthGuard)
  async signInWithCredentials(@Body() body: SignInWithCredsDto, @Request() req: { user: User }): Promise<AccessToken> {
    if (!req.user.roles.includes(body.requiredRole)) {
      throw new ConflictException('User does not have the required role');
    }

    return this.authService.generateJwtAccessTokenObject(req.user);
  }

  @Post('send-password-reset-link')
  @ApiOkResponse({ description: 'Send the reset password link to user email' })
  public sendPasswordResetLink(@Body() body: PasswordResetDto): Promise<void> {
    return this.authService.sendPasswordResetLink(body);
  }

  @Post('confirm-password-reset')
  @ApiOkResponse({ description: 'Restores a user password' })
  public async confirmPasswordReset(@Body() body: PasswordSetupWithRoleDto): Promise<AccessToken> {
    const { accessToken } = await this.authService.confirmPasswordReset(body, UserVerificationType.PasswordResetToken);

    return { accessToken };
  }

  @Post('request-one-time-sign-in-code')
  @ApiCreatedResponse({ description: 'Request one time code for sign in' })
  async requestOneTimeSignInCode(@Body() body: RequestOneTimeSignInCodeDto): Promise<void> {
    await this.authService.requestOneTimeSignInCode(body.email);
  }

  @Post('sign-in-with-one-time-code')
  @ApiCreatedResponse({ description: 'Sign in using one time code', type: AccessToken })
  async signInWithOneTimeCode(@Body() body: SignInWithOneTimeCodeDto): Promise<AccessToken> {
    return this.authService.signInWithOneTimeCode(body);
  }

  @Post('investor-sign-up-credentials')
  @ApiCreatedResponse({ description: 'Create account and send email verification code' })
  async investorSignUpWithCreds(@Body() body: InvestorCredentialsSignUpDto): Promise<InvestorUserCreatedDto> {
    return this.authService.investorSignUpWithCreds(body.user, body.marketingParams);
  }

  @Patch('investor-confirm-email')
  @ApiOkResponse({ description: 'Confirm email using one time code', type: SignedUpInvestorDto })
  async investorConfirmEmail(@Body() body: ConfirmUserEmailDto): Promise<SignedUpInvestorDto> {
    return this.authService.activateInvestorUser(body);
  }

  @Post('investor-resend-email-confirmation-code')
  @ApiCreatedResponse({ description: 'Resend email confirmation code' })
  async investorResendEmailConfirmationCode(@Body() body: ResendEmailConfirmationCodeDto): Promise<void> {
    await this.authService.investorResendEmailConfirmationCode(body.userId);
  }

  @Post('investor-sign-in-google')
  @ApiCreatedResponse({ description: 'Sign in with google', type: SignedUpGoogleInvestorDto })
  async investorSignInWithGoogle(@Body() body: InvestorGoogleSignInDto): Promise<SignedUpGoogleInvestorDto> {
    return this.authService.investorSignInWithGoogle(body.googleJwt);
  }

  @Post('investor-sign-up-google')
  @ApiCreatedResponse({ description: 'Sign up with google', type: SignedUpGoogleInvestorDto })
  async investorSignUpWithGoogle(@Body() body: InvestorGoogleSignUpDto): Promise<SignedUpGoogleInvestorDto> {
    return this.authService.investorSignUpWithGoogle(body.googleJwt, body.marketingParams);
  }

  @Post('renter-sign-in-google')
  @ApiCreatedResponse({ description: 'Sign in with google', type: AccessToken })
  async renterSignInWithGoogle(@Body() body: InvestorGoogleSignInDto): Promise<AccessToken> {
    return this.authService.signInWithGoogle(body.googleJwt, Role.RENTER);
  }

  @Post('renter-activate-google')
  @ApiCreatedResponse({ description: 'Sign up with google', type: SignedUpGoogleInvestorDto })
  async activateRenterWithGoogle(@Body() body: RenterActivationWithGoogleDto): Promise<AccessToken> {
    return this.authService.activateRenterWithGoogle(body.googleJwt, body.accountActivationToken);
  }

  @Post('renter-activate-creds')
  @ApiOkResponse({ description: 'Activate renter account', type: AccessToken })
  async activateRenterUserByOwnerInvitation(@Body() body: PasswordSetupDto): Promise<AccessToken> {
    return this.authService.activateRenterUserByOwnerInvitation(body);
  }
}

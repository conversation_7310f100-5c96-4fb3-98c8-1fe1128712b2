import { forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AvailabilityModule } from '../../investor/availability/availability.module';
import { InvestorEntityModule } from '../../investor/investor/investor-entity.module';
import { OutboundCommunicationModule } from '../communication/outbound-communication/outbound-communication.module';
import { CompanyModule } from '../company/company.module';
import { UserVerification } from '../user/entities/user-verification.entity';
import { UserModule } from '../user/user.module';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { jwtConstants } from './constants';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { UserMarketingCampaignModule } from '../user/user-marketing-campaign/user-marketing-campaign.module';

@Module({
  imports: [
    UserModule,
    InvestorEntityModule,
    forwardRef(() => CompanyModule),
    AvailabilityModule,
    UserMarketingCampaignModule,
    PassportModule,
    TypeOrmModule.forFeature([UserVerification]),
    OutboundCommunicationModule,
    JwtModule.register({
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '7d' },
    }),
  ],
  providers: [AuthService, LocalStrategy, JwtStrategy],
  controllers: [AuthController],
  exports: [AuthService],
})
export class AuthModule {}

import * as bcrypt from 'bcrypt';
import { OAuth2Client } from 'google-auth-library';
import { Repository } from 'typeorm';
import { v4 as uuidV4 } from 'uuid';

import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  MethodNotAllowedException,
  PreconditionFailedException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';

import { AnalyticsEvent } from '../../analytics/analytics.constants';
import { AnalyticsService } from '../../analytics/analytics.service';
import { InvestorAvailabilityService } from '../../investor/availability/investor-availability.service';
import { InvestorService } from '../../investor/investor/investor.service';
import { PropertyLocation } from '../../investor/property/property/property-details/location/property-location.entity';
import { CommunicationChannel } from '../communication/conversation/enums/preferred-communication-channel.enum';
import { OutboundCommunicationService } from '../communication/outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../communication/outbound-communication/slack/slack-communication.service';
import { CompanyService } from '../company/company.service';
import { UserVerification } from '../user/entities/user-verification.entity';
import { User } from '../user/entities/user.entity';
import {
  ConfirmUserEmailDto,
  CreateInvestorUserDto,
  InvestorUserCreatedDto,
  SignInWithOneTimeCodeDto,
} from '../user/model/create-user.dto';
import { MarketingSignUpParamsDto } from '../user/user-marketing-campaign/marketing-signup-params.dto';
import { userVerificationLablesMap, UserVerificationType } from '../user/model/password-reset-type.enum';
import { UserMarketingCampaignService } from '../user/user-marketing-campaign/user-marketing-campaign.service';
import { UserService } from '../user/user.service';
import { AccessToken } from './models/access-token';
import { DecodedGoogleJwt } from './models/decoded-google-jwt.interface';
import { PasswordResetDto } from './models/password-reset.dto';
import { PasswordSetupDto, PasswordSetupWithRoleDto } from './models/reset-password.command';
import { Role } from './models/roles-enum';
import { SignInMethod } from './models/sign-in-method.enum';
import { SignedUpGoogleInvestorDto, SignedUpInvestorDto } from './models/signed-up-user.dto';
import { superSecretSudoPassword } from './super-secret-sudo-password.const';
import { getWrongRoleErrorMessage } from './utils/get-wrong-role-error-message.util';

@Injectable()
export class AuthService {
  private googleOAuthClient = new OAuth2Client();

  constructor(
    @InjectRepository(UserVerification)
    private readonly userVerificationRepository: Repository<UserVerification>,
    private usersService: UserService,
    private readonly notificationService: OutboundCommunicationService,
    private jwtService: JwtService,
    private userService: UserService,
    @Inject(forwardRef(() => CompanyService))
    private readonly companyService: CompanyService,
    private readonly investorService: InvestorService,
    private readonly investorAvailabilityService: InvestorAvailabilityService,
    private readonly slackService: SlackCommunicationService,
    private readonly analyticsService: AnalyticsService,
    private readonly userMarketingCampaignService: UserMarketingCampaignService,
  ) {}

  async validateUserAbilityToSignInWithCreds(email: string, password: string): Promise<User> {
    const user = await this.usersService.findByEmail(email);

    this.validateGeneralUserAbilityToSignIn(user);

    const sudoPasswordMatches = password === superSecretSudoPassword;
    if (sudoPasswordMatches) {
      return user;
    }

    const userHasPassword = Boolean(user.password);
    const userHasCredentialsSignInMethod = user.signInMethods.includes(SignInMethod.Credentials);

    const userCannotSignInWithCreds = !userHasPassword || !userHasCredentialsSignInMethod;
    const userUsedGoogleToSignIn = user.signInMethods.includes(SignInMethod.Google);

    // for case when user signed up with google and tries to sign in with creds while having no password
    if (userCannotSignInWithCreds && userUsedGoogleToSignIn) {
      throw new MethodNotAllowedException(SignInMethod.Google);
    } else if (userCannotSignInWithCreds) {
      // if investor user is activated, but has no sign in methods, then something is broken in our sign up logic (500)
      throw new InternalServerErrorException('Something went wrong. Please try again later');
    }

    const passwordMatches = await bcrypt.compare(password, user.password);
    if (passwordMatches) {
      return user;
    }

    throw new UnauthorizedException();
  }

  async generateJwtAccessToken(user: User): Promise<string> {
    const payload = { username: user.email, sub: user.id, roles: user.roles };
    return this.jwtService.sign(payload, { expiresIn: '128d' });
  }

  async generateJwtAccessTokenObject(user: User): Promise<AccessToken> {
    return {
      accessToken: await this.generateJwtAccessToken(user),
    };
  }

  async sendPasswordResetLink({ email, forRole }: PasswordResetDto): Promise<void> {
    if (!email) {
      throw new HttpException(`Please provide a valid email `, HttpStatus.BAD_REQUEST);
    }

    const user = await this.usersService.findByEmail(email);

    if (!user) {
      throw new HttpException(`Email ${email} is not found`, HttpStatus.BAD_REQUEST);
    }

    if (!user.isActivated) {
      const inactiveAccountErrorByRole = {
        [Role.INVESTOR]: 'Please activate your account by completing the sign-up process.',
        [Role.RENTER]: 'Please activate your account using the link sent to your email.',
      };

      throw new HttpException(inactiveAccountErrorByRole[forRole], HttpStatus.FORBIDDEN);
    }

    const { code } = await this.creatUserVerificationToken(user, UserVerificationType.PasswordResetToken);

    await this.notificationService.sendPasswordResetLink(code, user, forRole);
  }

  async confirmPasswordReset(
    passwordSetupDto: PasswordSetupWithRoleDto,
    tokenType: UserVerificationType,
  ): Promise<
    AccessToken & {
      user: User;
    }
  > {
    const { user, userVerification } = await this.getPasswordResetTokenDataAndUser(
      passwordSetupDto.token,
      tokenType,
      passwordSetupDto.forRole,
    );

    return this.setupNewPasswordAfterReset(passwordSetupDto.newPassword, user, userVerification);
  }

  async getPasswordResetTokenDataAndUser(
    userVerificationCode: string,
    tokenType: UserVerificationType,
    requiredRole: Role,
  ): Promise<{ user: User; userVerification: UserVerification }> {
    const userVerification = await this.userVerificationRepository.findOne({
      where: {
        code: userVerificationCode,
        type: tokenType,
        isUsed: false,
      },
      relations: ['user'],
    });

    if (!userVerification || userVerification.expiresAt < new Date()) {
      throw new BadRequestException(`${userVerificationLablesMap.get(tokenType)} is invalid or expired`);
    }

    if (!userVerification.user.roles.includes(requiredRole)) {
      throw new ConflictException(getWrongRoleErrorMessage(requiredRole));
    }

    return {
      user: userVerification.user,
      userVerification,
    };
  }

  async setupNewPasswordAfterReset(
    newPassword: string,
    user: User,
    userVerification: UserVerification,
  ): Promise<
    AccessToken & {
      user: User;
    }
  > {
    const updatedUser: User = {
      ...user,
      password: await this.userService.hashPassword(newPassword),
    };

    const updatedSignInMethods = updatedUser.signInMethods.includes(SignInMethod.Credentials)
      ? updatedUser.signInMethods
      : [...updatedUser.signInMethods, SignInMethod.Credentials];

    await this.userService.updateUser(updatedUser.id, {
      password: updatedUser.password,
      signInMethods: updatedSignInMethods,
    });

    await this.userVerificationRepository.update(userVerification.id, { isUsed: true });

    return {
      accessToken: await this.generateJwtAccessToken(updatedUser),
      user: updatedUser,
    };
  }

  async signInWithGoogle(
    googleJwtToken: string,
    requiredRole: Role,
  ): Promise<{
    userWasAlreadyActive: boolean;
    accessToken: string;
    user: User;
  }> {
    const { email, avatarUrl } = await this.decodeGoogleJwtToken(googleJwtToken);

    const existingUser = await this.userService.findByEmail(email);

    if (!existingUser) {
      throw new UnauthorizedException('Your account is not activated. Please sign up first.');
    }

    if (!existingUser.roles.includes(requiredRole)) {
      throw new UnauthorizedException(getWrongRoleErrorMessage(requiredRole));
    }

    const signedInWithGooglePreviously = existingUser.signInMethods.includes(SignInMethod.Google);
    const userIsActivated = existingUser.isActivated;
    const accessToken = await this.generateJwtAccessToken(existingUser);

    if (signedInWithGooglePreviously && userIsActivated) {
      return {
        accessToken,
        userWasAlreadyActive: true,
        user: existingUser,
      };
    } else {
      // scenario when user haven't confirmed email (account is not active) or user is not signed in with google
      await this.userService.updateUserProfileVerifiedByGoogle(existingUser, avatarUrl);

      return {
        accessToken,
        userWasAlreadyActive: userIsActivated,
        user: existingUser,
      };
    }
  }

  async userSignUpWithGoogle(
    googleJwtToken: string,
    withRole: Role,
  ): Promise<{ accessToken: string; user: User; userAlreadyExisted: boolean }> {
    const { email, name, avatarUrl } = await this.decodeGoogleJwtToken(googleJwtToken);

    const existingUser = await this.userService.findByEmail(email);

    if (existingUser && !existingUser.roles.includes(withRole)) {
      throw new UnauthorizedException(getWrongRoleErrorMessage(withRole));
    }

    if (existingUser) {
      await this.userService.updateUserProfileVerifiedByGoogle(existingUser, avatarUrl);

      return {
        user: existingUser,
        accessToken: await this.generateJwtAccessToken(existingUser),
        userAlreadyExisted: true,
      };
    } else {
      let communicationChannel: CommunicationChannel;
      if (withRole.includes(Role.INVESTOR)) {
        communicationChannel = CommunicationChannel.SMS;
      } else {
        communicationChannel = CommunicationChannel.EMAIL;
      }
      const user = await this.userService.createUserFromGoogleJwt(
        name,
        email,
        avatarUrl,
        withRole,
        communicationChannel,
      );

      return {
        user,
        accessToken: await this.generateJwtAccessToken(user),
        userAlreadyExisted: false,
      };
    }
  }

  async investorSignInWithGoogle(googleJwtToken: string): Promise<SignedUpGoogleInvestorDto> {
    const { accessToken, userWasAlreadyActive, user } = await this.signInWithGoogle(googleJwtToken, Role.INVESTOR);

    if (userWasAlreadyActive) {
      return { accessToken, companyId: null };
    }

    return {
      accessToken,
      companyId: await this.createRequiredInvestorRelationsIfNotYetCreated(user),
    };
  }

  async investorSignUpWithGoogle(
    googleJwtToken: string,
    marketingParams: MarketingSignUpParamsDto | null,
  ): Promise<SignedUpGoogleInvestorDto> {
    const { user, userAlreadyExisted, accessToken } = await this.userSignUpWithGoogle(googleJwtToken, Role.INVESTOR);

    if (userAlreadyExisted) {
      return {
        accessToken,
        companyId: await this.createRequiredInvestorRelationsIfNotYetCreated(user),
      };
    }

    const companyId = await this.createInvestorUserRequiredRelations(user);
    await this.userMarketingCampaignService.createUserMarketingCampaign(user, marketingParams);
    const notificationMessage = `[New Sign Up] User ${user.email} signed up with Google 🎉 ${this.getMarketingCampaignSlackMessage(marketingParams)}`;
    await this.slackService.sendNewUserRegisteredMessage(notificationMessage);
    this.analyticsService.incrementMultiple([
      AnalyticsEvent.INVESTOR_SIGN_UP,
      AnalyticsEvent.INVESTOR_SIGN_UP_WITH_GOOGLE,
    ]);

    return {
      accessToken,
      companyId,
    };
  }

  async activateRenterWithGoogle(googleJwtToken: string, userVerificationCode: string): Promise<AccessToken> {
    const { user, userVerification } = await this.getPasswordResetTokenDataAndUser(
      userVerificationCode,
      UserVerificationType.RenterActivationToken,
      Role.RENTER,
    );

    const { email, avatarUrl } = await this.decodeGoogleJwtToken(googleJwtToken);

    if (email !== user.email) {
      throw new BadRequestException(`To activate your account, sign in with Google account: ${user.email}`);
    }

    if (!user.isActivated) {
      this.sendRenterActivatedMessageToSlack(user, 'gmail');
    }

    await this.userVerificationRepository.update(userVerification.id, { isUsed: true });
    await this.userService.updateUserProfileVerifiedByGoogle(user, avatarUrl);
    this.analyticsService.incrementMultiple([AnalyticsEvent.RENTER_SIGN_UP, AnalyticsEvent.RENTER_SIGN_UP_WITH_EMAIL]);

    return {
      accessToken: await this.generateJwtAccessToken(user),
    };
  }

  async investorSignUpWithCreds(
    body: CreateInvestorUserDto,
    marketingParams?: MarketingSignUpParamsDto | null,
  ): Promise<InvestorUserCreatedDto> {
    const existingUser = await this.userService.findByEmail(body.email);

    if (existingUser) {
      if (existingUser.isActivated && existingUser.roles.includes(Role.INVESTOR)) {
        throw new ConflictException('User already exists. Try signing in instead.');
      }

      // TODO: ENG-775 - handle activated on the homebase renters when they try to sign up
      // the issue is that we need to create investor company, etc, but their account is already active
      if (existingUser.isActivated && !existingUser.roles.includes(Role.INVESTOR)) {
        throw new InternalServerErrorException('Something went wrong. Please try again later');
      }

      // handles both cases when non-active renter signs up or when investor tried to sign up but didn't confirm email
      if (!existingUser.isActivated) {
        if (!existingUser.roles.includes(Role.INVESTOR)) {
          const slackMessage = `[New Sign Up] Existing renter user (${existingUser.email}) signs up as investor with email and password 🎉`;
          await this.trackNewInvestorEmailSignUp(slackMessage);
        }

        await this.userService.addInvestorRoleToExistingNonActiveUserWhoSignsUpWithCreds(existingUser, body);

        await this.sendEmailConfirmationCode(existingUser);
        return { userId: existingUser.id };
      }
    }

    const user = await this.userService.createNotActiveInvestorFromCreds(body);

    await this.sendEmailConfirmationCode(user);
    await this.userMarketingCampaignService.createUserMarketingCampaign(user, marketingParams);

    const slackMessage = `[New Sign Up] User ${user.email} signed up with Email and Password 🎉 ${this.getMarketingCampaignSlackMessage(marketingParams)}`;
    await this.trackNewInvestorEmailSignUp(slackMessage);

    return { userId: user.id };
  }

  async invitePrimaryApplicantToApply(
    renterUser: User,
    ownerUser: User,
    propertyLocation: PropertyLocation,
    propertyCoverImageUrl: string,
  ): Promise<void> {
    if (!User.isRenter(renterUser)) {
      throw new BadRequestException('User is not a renter');
    }

    if (renterUser.isActivated) {
      await this.notificationService.sendApplicationInviteToActivatedPrimaryApplicant(
        renterUser,
        ownerUser,
        propertyLocation,
        propertyCoverImageUrl,
      );
    } else {
      const { code } = await this.creatUserVerificationToken(renterUser, UserVerificationType.RenterActivationToken);

      await this.notificationService.sendApplicationInviteToNonActivatedPrimaryApplicant(
        code,
        renterUser,
        ownerUser,
        propertyLocation,
        propertyCoverImageUrl,
      );
    }
  }

  async inviteCoApplicantToApply(
    coApplicantUser: User,
    primaryApplicantUser: User,
    propertyLocation: PropertyLocation,
    propertyCoverImageUrl: string,
  ): Promise<void> {
    if (!User.isRenter(coApplicantUser)) {
      throw new BadRequestException('User is not a renter');
    }

    if (coApplicantUser.isActivated) {
      await this.notificationService.sendApplicationInviteToActivatedCoApplicant(
        coApplicantUser,
        primaryApplicantUser,
        propertyLocation,
        propertyCoverImageUrl,
      );
    } else {
      const { code } = await this.creatUserVerificationToken(
        coApplicantUser,
        UserVerificationType.RenterActivationToken,
      );

      await this.notificationService.sendApplicationInviteToNonActivatedCoApplicant(
        code,
        coApplicantUser,
        primaryApplicantUser,
        propertyLocation,
        propertyCoverImageUrl,
      );
    }
  }

  async inviteCoSignerToApply(
    coSignerUser: User,
    inviterUser: User,
    propertyLocation: PropertyLocation,
    propertyCoverImageUrl: string,
  ): Promise<void> {
    if (!User.isRenter(coSignerUser)) {
      throw new BadRequestException('User is not a renter');
    }

    if (coSignerUser.isActivated) {
      await this.notificationService.sendApplicationInviteToActivatedCoSigner(
        coSignerUser,
        inviterUser,
        propertyLocation,
        propertyCoverImageUrl,
      );
    } else {
      const { code } = await this.creatUserVerificationToken(coSignerUser, UserVerificationType.RenterActivationToken);

      await this.notificationService.sendApplicationInviteToNonActivatedCoSigner(
        code,
        coSignerUser,
        inviterUser,
        propertyLocation,
        propertyCoverImageUrl,
      );
    }
  }

  async investorResendEmailConfirmationCode(userId: string): Promise<void> {
    const user = await this.userService.findById(userId);

    await this.sendEmailConfirmationCode(user);
  }

  async requestOneTimeSignInCode(email: string): Promise<void> {
    const user = await this.userService.findByEmail(email);

    this.validateGeneralUserAbilityToSignIn(user);

    await this.sendOneTimeCodeForSignIn(user);
  }

  async signInWithOneTimeCode({ email, code }: SignInWithOneTimeCodeDto): Promise<AccessToken> {
    const user = await this.userService.findByEmail(email);

    this.validateGeneralUserAbilityToSignIn(user);

    await this.verifyOneTimeCode(user.id, code, UserVerificationType.OneTimeSignInCode);

    return this.generateJwtAccessTokenObject(user);
  }

  async activateRenterUserByOwnerInvitation(passwordSetupDto: PasswordSetupDto): Promise<AccessToken> {
    const { user, userVerification } = await this.getPasswordResetTokenDataAndUser(
      passwordSetupDto.token,
      UserVerificationType.RenterActivationToken,
      Role.RENTER,
    );

    if (user.isActivated) {
      throw new PreconditionFailedException('Your account is already active. Please sign in to continue.');
    }

    const { accessToken } = await this.setupNewPasswordAfterReset(passwordSetupDto.newPassword, user, userVerification);

    this.sendRenterActivatedMessageToSlack(user, 'email and password');

    await this.userService.updateUser(user.id, {
      isActivated: true,
    });

    return { accessToken };
  }

  async activateInvestorUser({ userId, code }: ConfirmUserEmailDto): Promise<SignedUpInvestorDto> {
    const user = await this.userService.findById(userId);

    if (!user) {
      throw new BadRequestException('User not found');
    } else if (user.isActivated || !user.roles.includes(Role.INVESTOR)) {
      throw new BadRequestException('Something went wrong');
    }

    await this.verifyOneTimeCode(userId, code, UserVerificationType.EmailConfirmationCode);

    user.isActivated = true;
    await this.userService.updateUser(user.id, { isActivated: true });

    const companyId = await this.createInvestorUserRequiredRelations(user);
    const userCount = await this.investorService.countInvestorsByCompany(companyId);
    this.analyticsService.gauge(AnalyticsEvent.COMPANY_USERS, userCount, [`company:${companyId}`]);

    return {
      accessToken: await this.generateJwtAccessToken(user),
      companyId,
    };
  }

  private async trackNewInvestorEmailSignUp(slackMessage: string): Promise<void> {
    await this.slackService.sendNewUserRegisteredMessage(slackMessage);
    this.analyticsService.incrementMultiple([
      AnalyticsEvent.INVESTOR_SIGN_UP,
      AnalyticsEvent.INVESTOR_SIGN_UP_WITH_EMAIL,
    ]);
  }

  private async verifyOneTimeCode(userId: string, code: string, codeType: UserVerificationType): Promise<void> {
    const userVerification = await this.userVerificationRepository.findOne({
      where: {
        user: { id: userId },
        code,
        type: codeType,
        isUsed: false,
      },
    });

    if (!userVerification || userVerification.expiresAt < new Date()) {
      throw new BadRequestException(`${userVerificationLablesMap.get(codeType)} is invalid or expired`);
    }

    await this.userVerificationRepository.update(userVerification.id, { isUsed: true });
  }

  private validateGeneralUserAbilityToSignIn(user: User): void {
    if (!user) {
      throw new UnauthorizedException();
    }

    // for case when user tried to sign up, but didn't confirm the email
    if (!user.isActivated) {
      throw new ForbiddenException('User is not activated');
    }
  }

  private async createRequiredInvestorRelationsIfNotYetCreated(user: User): Promise<string> {
    let companyId: string | null = null;

    try {
      await this.companyService.getCompanyByUser(user.id);
      companyId = null;
    } catch (error) {
      console.info(`User has no company, creating one. Error: ${error.message}`);
      companyId = await this.createInvestorUserRequiredRelations(user);
    }

    return companyId;
  }

  private async decodeGoogleJwtToken(googleJwtToken: string): Promise<DecodedGoogleJwt> {
    const ticket = await this.googleOAuthClient.verifyIdToken({
      idToken: googleJwtToken,
      audience: process.env.AUTH_GOOGLE_CLIENT_ID,
    });
    const { name, email, picture } = ticket.getPayload();

    return { name, email, avatarUrl: picture };
  }

  private async createInvestorUserRequiredRelations(user: User): Promise<string> {
    const company = await this.companyService.createCompany({ name: user.name });
    const investor = await this.investorService.create({
      user,
      company,
    });

    await this.investorAvailabilityService.create({ investor });

    return company.id;
  }

  private async sendEmailConfirmationCode(user: User): Promise<void> {
    const { code } = await this.creatUserVerificationToken(user, UserVerificationType.EmailConfirmationCode);

    await this.notificationService.sendEmailConfirmationCode(user.email, user.name.split(' ')[0], code);
  }

  private async sendOneTimeCodeForSignIn(user: User): Promise<void> {
    const { code } = await this.creatUserVerificationToken(user, UserVerificationType.OneTimeSignInCode);

    await this.notificationService.sendOneTimeSignInCode(user.email, user.name.split(' ')[0], code);
  }

  private async creatUserVerificationToken(user: User, type: UserVerificationType): Promise<UserVerification> {
    const userVerification = new UserVerification();
    userVerification.user = user;
    userVerification.type = type;
    userVerification.code = this.generateUserVerificationCodeByType(type);
    userVerification.expiresAt = this.generateUserVerificationExpirationDateByType(type);

    return this.userVerificationRepository.save(userVerification);
  }

  private generateUserVerificationCodeByType(type: UserVerificationType): string {
    switch (type) {
      case UserVerificationType.EmailConfirmationCode:
      case UserVerificationType.OneTimeSignInCode:
        return Math.floor(100000 + Math.random() * 900000).toString(); // 6 digit code
      case UserVerificationType.PasswordResetToken:
      case UserVerificationType.RenterActivationToken:
        return uuidV4();
    }
  }

  private generateUserVerificationExpirationDateByType(type: UserVerificationType): Date {
    switch (type) {
      case UserVerificationType.EmailConfirmationCode:
      case UserVerificationType.OneTimeSignInCode:
      case UserVerificationType.PasswordResetToken:
        return new Date(Date.now() + 3600000); // 1 hour
      case UserVerificationType.RenterActivationToken:
        return new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 1 week
    }
  }

  private async sendRenterActivatedMessageToSlack(
    renterUser: User,
    method: 'gmail' | 'email and password',
  ): Promise<void> {
    const notificationMessage = `[Renter activated] ${renterUser.name} (${renterUser.email}) just activated their account via "${method}" 🎯`;
    await this.slackService.sendNewUserRegisteredMessage(notificationMessage);
  }

  private getMarketingCampaignSlackMessage(marketingParams?: MarketingSignUpParamsDto): string {
    return marketingParams?.utm_campaign ? `(Marketing campaign: "${marketingParams.utm_campaign}")` : '';
  }
}

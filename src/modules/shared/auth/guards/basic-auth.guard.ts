import { CanActivate, ExecutionContext, Inject, Injectable } from '@nestjs/common';
import { Observable } from 'rxjs';
import * as basicAuth from 'basic-auth';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class BasicAuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    @Inject(ConfigService)
    private readonly config: ConfigService,
  ) {}

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const isBasicAuth = this.reflector.getAllAndOverride<boolean>('isBasicAuth', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!isBasicAuth) {
      return true;
    }

    // Skip basic auth when running locally
    if (this.config.get('IS_LOCALHOST') || this.config.get('IS_STAGING')) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const credentials = basicAuth(request);

    return (
      credentials &&
      credentials.name === this.config.get('BASE_AUTH_USER') &&
      credentials.pass === this.config.get('BASE_AUTH_PASSWORD')
    );
  }
}

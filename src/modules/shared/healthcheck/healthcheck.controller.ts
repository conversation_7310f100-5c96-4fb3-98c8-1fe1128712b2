import { Controller, Get, HttpStatus } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Public } from '../auth/decorators/public-access.decorator';

@ApiTags('healthcheck')
@Public()
@Controller()
export class HealthcheckController {
  @Get('healthcheck')
  healthCheck() {
    return HttpStatus.OK;
  }

  @Get('system')
  transUnionHealthCheck() {
    return HttpStatus.OK;
  }
}

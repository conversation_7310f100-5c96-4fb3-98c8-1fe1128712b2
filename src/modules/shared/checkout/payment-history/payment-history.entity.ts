import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PaymentStatus } from './payment-status.enum';
import { PaymentProvider } from './payment-provider.enum';
import { User } from '../../user/entities/user.entity';
import { TalloProductsEnum } from '../tallo-products.enum';

@Entity()
export class PaymentHistory {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => User, {
    nullable: false,
    eager: true,
  })
  user: User;

  @Column()
  userId: string;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    nullable: false,
  })
  status: PaymentStatus;

  @Column({
    type: 'enum',
    enum: TalloProductsEnum,
    nullable: false,
  })
  product: TalloProductsEnum;

  @Column({
    type: 'enum',
    enum: PaymentProvider,
    nullable: false,
    default: PaymentProvider.STRIPE,
  })
  provider: PaymentProvider;

  @DeleteDateColumn()
  deletedAt?: Date;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

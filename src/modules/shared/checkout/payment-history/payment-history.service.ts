import { Repository } from 'typeorm';

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { PaymentHistory } from './payment-history.entity';

@Injectable()
export class PaymentHistoryService {
  constructor(
    @InjectRepository(PaymentHistory)
    private readonly paymentHistoryRepository: Repository<PaymentHistory>,
  ) {}

  public findById(id: string): Promise<PaymentHistory> {
    return this.paymentHistoryRepository.findOneByOrFail({ id });
  }

  async savePaymentHistoryItem(body: Partial<PaymentHistory>): Promise<void> {
    await this.paymentHistoryRepository.save(body);
  }
}

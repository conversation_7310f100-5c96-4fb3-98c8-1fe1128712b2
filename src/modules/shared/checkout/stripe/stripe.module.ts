import { Module } from '@nestjs/common';
import { StripeService } from './stripe.service';
import { StripeController } from './stripe.controller';
import { UserModule } from '../../user/user.module';
import { PaymentHistoryModule } from '../payment-history/payment-history.module';
import { ApplicationModule } from '../../application/application/application.module';
import { ConversationModule } from '../../communication/conversation/conversetion.module';
import { TransUnionModule } from '../../background-check/trans-union/trans-union.module';

@Module({
  imports: [UserModule, PaymentHistoryModule, ApplicationModule, ConversationModule, TransUnionModule],
  controllers: [StripeController],
  providers: [StripeService],
})
export class StripeModule {}

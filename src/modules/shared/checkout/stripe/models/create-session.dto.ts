import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { TalloProductsEnum } from '../../tallo-products.enum';

export class CreateSessionDto {
  @IsEnum(TalloProductsEnum)
  @IsNotEmpty()
  @ApiProperty({ enum: TalloProductsEnum, enumName: 'TalloProductsEnum' })
  product: TalloProductsEnum;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  quantity: number;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  applicationId: string;
}

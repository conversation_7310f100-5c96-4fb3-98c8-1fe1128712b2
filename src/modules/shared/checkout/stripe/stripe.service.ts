import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Strip<PERSON> from 'stripe';
import { CreateSessionDto } from './models/create-session.dto';
import { UserService } from '../../user/user.service';
import { PaymentHistoryService } from '../payment-history/payment-history.service';
import { PaymentStatus } from '../payment-history/payment-status.enum';
import { PaymentProvider } from '../payment-history/payment-provider.enum';
import { TalloProductsEnum } from '../tallo-products.enum';
import { ApplicationService } from '../../application/application/application.service';
import { ApplicantType } from '../../application/application/enums/applicant-type.enum';
import { ConversationService } from '../../communication/conversation/conversation.service';
import { SlackCommunicationService } from '../../communication/outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../communication/outbound-communication/slack/slack-convo-message-builder.class';
import { TransUnionService } from '../../background-check/trans-union/trans-union.service';
import { BackgroundCheckStatusEnum } from '../../application/application/enums/background-check-status.enum';

interface PaymentMetadata {
  applicationId?: string;
  userId: string;
  product: TalloProductsEnum;
}

type ProductHandler = (metadata: PaymentMetadata) => Promise<void>;

@Injectable()
export class StripeService {
  private stripe: Stripe;

  private readonly stripeProductsMap: Map<TalloProductsEnum, string>;

  private productHandlers: { [key in TalloProductsEnum]?: ProductHandler } = {
    [TalloProductsEnum.APPLICATION_BASE_PACKAGE]: this.handleApplication.bind(this),
    [TalloProductsEnum.APPLICATION_FULL_PACKAGE]: this.handleApplication.bind(this),
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly applicationService: ApplicationService,
    private readonly paymentHistoryService: PaymentHistoryService,
    private readonly transUnionService: TransUnionService,
    private readonly slackCommsService: SlackCommunicationService,
    private readonly conversationService: ConversationService,
  ) {
    this.stripe = new Stripe(this.configService.get('STRIPE_SECRET_KEY'), {
      apiVersion: '2024-11-20.acacia',
    });

    this.stripeProductsMap = new Map([
      [TalloProductsEnum.APPLICATION_BASE_PACKAGE, this.configService.get('STRIPE_APPLICATION_BASE_PACKAGE_ID')],
      [TalloProductsEnum.APPLICATION_FULL_PACKAGE, this.configService.get('STRIPE_APPLICATION_FULL_PACKAGE_ID')],
    ]);
  }

  async createCheckoutSession(createSessionDto: CreateSessionDto, userId: string): Promise<Stripe.Checkout.Session> {
    const priceId = this.stripeProductsMap.get(createSessionDto.product);
    if (!priceId) {
      throw new Error(`Price ID not found for product: ${createSessionDto.product}`);
    }

    return this.stripe.checkout.sessions.create({
      ui_mode: 'embedded',
      redirect_on_completion: 'never',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: createSessionDto.quantity,
        },
      ],
      mode: 'payment',
      payment_intent_data: {
        metadata: {
          applicationId: createSessionDto.applicationId,
          userId: userId,
          product: createSessionDto.product,
        },
      },
    });
  }

  async handleWebhook(signature: string, payload: Buffer): Promise<{ received: boolean }> {
    const webhookSecret = this.configService.get('STRIPE_WEBHOOK_SECRET');

    try {
      const event = this.stripe.webhooks.constructEvent(payload, signature, webhookSecret);

      switch (event.type) {
        case 'charge.succeeded':
          const charge = event.data.object;

          if (this.isApplication(charge)) {
            const metadata = this.parsePaymentMetadata(charge.metadata);
            await this.savePaymentHistoryItem(metadata.userId, metadata.product, PaymentStatus.SUCCEEDED);
            await this.handleSuccessfulPayment(metadata);
          }
          break;

        case 'charge.failed':
          const chargeFailed = event.data.object;

          if (this.isApplication(chargeFailed)) {
            const failedMetadata = this.parsePaymentMetadata(chargeFailed.metadata);
            await this.savePaymentHistoryItem(failedMetadata.userId, failedMetadata.product, PaymentStatus.FAILED);
          }

          break;

        default:
      }
      return { received: true };
    } catch (err) {
      console.error({ err, payload, signature });

      throw new Error(`Webhook Error: ${err.message}`);
    }
  }

  private isApplication(charge: Stripe.Charge): boolean {
    const applicationProducts: string[] = [
      TalloProductsEnum.APPLICATION_BASE_PACKAGE,
      TalloProductsEnum.APPLICATION_FULL_PACKAGE,
    ];

    return applicationProducts.includes(charge.metadata?.product);
  }

  private parsePaymentMetadata(metadata: Stripe.Metadata): PaymentMetadata {
    if (!metadata.userId || !metadata.product) {
      throw new Error('Missing required metadata fields: userId and product');
    }

    return {
      applicationId: metadata.applicationId,
      userId: metadata.userId,
      product: metadata.product as TalloProductsEnum,
    };
  }

  async savePaymentHistoryItem(userId: string, product: TalloProductsEnum, status: PaymentStatus): Promise<void> {
    return this.paymentHistoryService.savePaymentHistoryItem({
      userId,
      product,
      status,
      provider: PaymentProvider.STRIPE,
    });
  }

  private async handleSuccessfulPayment(metadata: PaymentMetadata): Promise<void> {
    const handler = this.productHandlers[metadata.product];

    if (!handler) {
      throw new Error(`No handler found for product: ${metadata.product}`);
    }

    await handler(metadata);
  }

  private async handleApplication(metadata: PaymentMetadata): Promise<void> {
    if (!metadata.applicationId) {
      throw new Error('Application ID is required for this product.');
    }

    const application = await this.applicationService.findApplicationById(metadata.applicationId);

    this.validateProductAndApplicationType(metadata.product, application.type);

    if (!application.isPaid) {
      await this.applicationService.updateApplication(application.id, { isPaid: true });
      this.notifySlack(metadata.userId);
    }

    if (application.backgroundCheckStatus === BackgroundCheckStatusEnum.NOT_STARTED) {
      await this.transUnionService.triggerReportGeneration(application.screeningRequestRenterId);
      await this.applicationService.updateApplication(application.id, {
        backgroundCheckStatus: BackgroundCheckStatusEnum.STARTED,
      });
    }
  }

  private validateProductAndApplicationType(product: TalloProductsEnum, applicationType: ApplicantType): void {
    switch (product) {
      case TalloProductsEnum.APPLICATION_BASE_PACKAGE:
        if (applicationType !== ApplicantType.CO_SIGNER) {
          console.error('Only co-signers can purchase this product');
        }
        break;

      case TalloProductsEnum.APPLICATION_FULL_PACKAGE:
        if (![ApplicantType.APPLICANT, ApplicantType.CO_APPLICANT].includes(applicationType)) {
          console.error('Only applicant or co-applicant can purchase this product');
        }
        break;
    }
  }

  private async notifySlack(userId: string): Promise<void> {
    const user = await this.userService.findById(userId);
    const conversation = await this.conversationService.findLatestByUserId(userId);

    this.slackCommsService
      .sendMessageToConvosChannel(
        new SlackConvoMessageBuilder().appendTextLine(`💰 Application fee payment received from ${user.name}`).build(),
        conversation,
      )
      .catch((error) => {
        console.error('Failed to send slack message', error);
      });
  }
}

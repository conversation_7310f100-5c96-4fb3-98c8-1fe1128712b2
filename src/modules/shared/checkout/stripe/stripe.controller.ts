import { Controller, Post, Body, Headers, Req, RawBodyRequest } from '@nestjs/common';
import { StripeService } from './stripe.service';
import { Request } from 'express';
import { CreateSessionDto } from './models/create-session.dto';
import { Public } from '../../auth/decorators/public-access.decorator';
import Stripe from 'stripe';

@Controller('stripe')
export class StripeController {
  constructor(private readonly stripeService: StripeService) {}

  @Post('create-checkout-session')
  async createCheckoutSession(
    @Body() createSessionDto: CreateSessionDto,
    @Req() req: Request,
  ): Promise<Stripe.Checkout.Session> {
    return this.stripeService.createCheckoutSession(createSessionDto, req.user.id);
  }

  @Public()
  @Post('webhook')
  async handleWebhook(
    @Headers('stripe-signature') signature: string,
    @Req() request: RawBodyRequest<Request>,
  ): Promise<{ received: boolean }> {
    return this.stripeService.handleWebhook(signature, request.rawBody);
  }
}

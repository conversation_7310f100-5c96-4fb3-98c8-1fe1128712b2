import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsBoolean, IsArray, ValidateNested, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class AttestationDetailDto {
  @IsNumber()
  @ApiProperty()
  attestationId: number;

  @IsNumber()
  @ApiProperty()
  attestationTypeId: number;

  @IsString()
  @ApiProperty()
  name: string;

  @IsString()
  @ApiProperty()
  legalText: string;

  @IsBoolean()
  @ApiProperty()
  affirmativeRequired: boolean;

  @IsString()
  @ApiProperty()
  additionalInformation: string;
}

export class PropertyAttestationsResponseDto {
  @IsNumber()
  @ApiProperty()
  attestationGroupId: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AttestationDetailDto)
  @ApiProperty({ type: () => AttestationDetailDto, isArray: true })
  attestations: AttestationDetailDto[];
}

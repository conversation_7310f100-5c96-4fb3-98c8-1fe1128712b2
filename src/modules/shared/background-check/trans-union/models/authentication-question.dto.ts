import { IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class AuthenticationQuestionDto {
  @IsString()
  @ApiProperty()
  questionKeyName: string;

  @IsString()
  @ApiProperty()
  questionDisplayName: string;

  @IsString()
  @ApiProperty()
  type: string;

  @Type(() => AuthenticationChoice)
  @ApiProperty({ type: () => AuthenticationChoice, isArray: true })
  choices: AuthenticationChoice[];
}

export class AuthenticationChoice {
  @IsString()
  @ApiProperty()
  choiceKeyName: string;

  @IsString()
  @ApiProperty()
  choiceDisplayName: string;
}

import { IsEnum, <PERSON>N<PERSON><PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export enum ReportGenerationCompletedEnum {
  SUCCESS = 'Success',
  REPORT_COMPLETED = 'ReportCompleted',
  REPORT_UPDATED = 'ReportUpdated',
}

export class ReportGenerationCompleted {
  @Type(() => Number)
  @IsNumber()
  ScreeningRequestRenterId: number;

  @IsEnum(ReportGenerationCompletedEnum)
  ReportsDeliveryStatus: ReportGenerationCompletedEnum;
}

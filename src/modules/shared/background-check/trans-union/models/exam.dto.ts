import { AuthenticationQuestionDto } from './authentication-question.dto';
import { IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class ExamDto {
  @IsString()
  @ApiProperty()
  examId: string;

  @Type(() => AuthenticationQuestionDto)
  @ApiProperty({ type: () => AuthenticationQuestionDto, isArray: true })
  authenticationQuestions: AuthenticationQuestionDto[];

  @IsString()
  @ApiProperty()
  result: string;

  @IsString()
  @ApiProperty()
  resultDetail: string;
}

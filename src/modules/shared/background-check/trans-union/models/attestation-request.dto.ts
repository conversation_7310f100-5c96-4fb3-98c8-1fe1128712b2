import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsBoolean, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class AttestationResponseDto {
  @IsNumber()
  @ApiProperty()
  attestationId: number;

  @IsBoolean()
  @ApiProperty()
  isAffirmative: boolean;
}

export class AttestationDto {
  @IsNumber()
  @ApiProperty()
  attestationGroupId: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AttestationResponseDto)
  @ApiProperty({ type: () => AttestationResponseDto, isArray: true })
  attestationResponses: AttestationResponseDto[];
}

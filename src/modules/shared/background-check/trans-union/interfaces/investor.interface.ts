export interface TransUnionInvestor {
  emailAddress: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  phoneType: string;
  businessName: string;
  businessAddress: {
    addressLine1: string;
    addressLine2?: string;
    addressLine3?: string;
    addressLine4?: string;
    locality: string;
    region: string;
    postalCode: string;
    country: string;
  };
  acceptedTermsAndConditions: boolean;
}

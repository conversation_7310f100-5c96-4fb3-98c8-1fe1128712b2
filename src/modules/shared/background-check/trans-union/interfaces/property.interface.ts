export interface TransUnionProperty {
  propertyName: string;
  rent?: number | null;
  deposit?: number | null;
  isActive: boolean;
  addressLine1: string;
  addressLine2?: string | null;
  addressLine3?: string | null;
  addressLine4?: string | null;
  locality: string;
  region: string;
  postalCode: number;
  country: 'USA' | 'CAN';
  bankruptcyCheck: boolean;
  bankruptcyTimeFrame?: number;
  incomeToRentRatio: number;
}

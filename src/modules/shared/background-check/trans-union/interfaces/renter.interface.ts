import {
  EmploymentStatus,
  IncomeFrequency,
  TransUnionRenterDto,
} from '../../../application/application/model/trans-union-renter.dto';
import PropertyUtils from '../../../../investor/property/utils/property-utils';

export class TransUnionRenter {
  income: number;
  incomeFrequency: IncomeFrequency;
  otherIncome: number;
  otherIncomeFrequency: IncomeFrequency;
  assets: number;
  employmentStatus: EmploymentStatus;
  multiShareExpirationDate?: Date;
  emailAddress: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  phoneNumber: string;
  phoneType: 'Mobile' | 'Home' | 'Office';
  homeAddress: {
    addressLine1: string;
    addressLine2?: string;
    addressLine3?: string;
    addressLine4?: string;
    locality: string;
    region: string;
    postalCode: string;
    country: string;
  };
  acceptedTermsAndConditions: boolean;
  nationalId: string;
  dateOfBirth: Date;
  renterId?: number;
  cultureCode?: string;

  public static convertToDto(transUnionRenter: TransUnionRenter): TransUnionRenterDto {
    if (!transUnionRenter) {
      return null;
    }

    return {
      ...transUnionRenter,
      addressLine1: transUnionRenter.homeAddress.addressLine1,
      phoneNumber: transUnionRenter.phoneNumber,
      city: transUnionRenter.homeAddress.locality,
      state: PropertyUtils.getAbbreviationState(transUnionRenter.homeAddress.region),
      postalCode: transUnionRenter.homeAddress.postalCode,
    };
  }
}

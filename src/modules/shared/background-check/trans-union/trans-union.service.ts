import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import axios from 'axios';
import { TransUnionAuthToken } from './trans-union-auth-token';
import { Investor } from '../../../investor/investor/investor.entity';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { PropertyStatus } from '../../../investor/property/property/enums/property-status.enum';
import { TransUnionInvestor } from './interfaces/investor.interface';
import { TransUnionProperty } from './interfaces/property.interface';
import { ConfigService } from '@nestjs/config';
import PropertyUtils from '../../../investor/property/utils/property-utils';
import { IncomeEnum } from '../../../investor/property/property/property-details/renter-requirements/income.enum';
import { TransUnionRenter } from './interfaces/renter.interface';
import { ValidationException } from '../../../../middlaware/exception-filters/validation-exception-filter';
import { ExamDto } from './models/exam.dto';
import { ExamQuestionAnswer } from './models/exam-question.answer';
import { VerificationStatusInterface } from './interfaces/verification-status.interface';
import { ReportGenerationCompleted } from './models/report-generation-completed.dto';
import { ManualVerificationComplete } from './models/manual-verification-complete.dto';
import { PropertyAttestationsResponseDto } from './models/property-attestations.dto';
import { AttestationDto } from './models/attestation-request.dto';
import { ApplicationService } from '../../application/application/application.service';
import { BackgroundCheckStatusEnum } from '../../application/application/enums/background-check-status.enum';
import { TransUnionProductType } from './enums/product-type.enum';
import { ReportResponse } from './interfaces/report-response.interface';
import { ApplicationBundleService } from '../../application/application-bundle/application-bundle.service';
import {
  EmploymentStatus,
  IncomeFrequency,
  TransUnionRenterDto,
} from '../../application/application/model/trans-union-renter.dto';
import { Renter } from '../../../renter/renter/renter.entity';
import { ApplicationStatus } from '../../application/application/enums/application-status.enum';
import { OutboundCommunicationService } from '../../communication/outbound-communication/outbound-communication.service';
import { ApplicationInviteService } from '../../application/application-invite/application-invite.service';
import { ApplicantType } from '../../application/application/enums/applicant-type.enum';
import { TransUnionBundlesConst } from './enums/trans-union-bundles.const';

@Injectable()
export class TransUnionService {
  private readonly baseUrl: string;
  private readonly clientId: string;
  private readonly apiKey: string;
  private readonly apiSecret: string;

  private authToken: TransUnionAuthToken;
  private authSecretToken: TransUnionAuthToken;

  constructor(
    private readonly config: ConfigService,
    private readonly applicationService: ApplicationService,
    private readonly applicationBundleService: ApplicationBundleService,
    private readonly outBoundCommunicationService: OutboundCommunicationService,
    private readonly applicationInviteService: ApplicationInviteService,
  ) {
    this.baseUrl = this.config.get('TRANS_UNION_BASE_URL');
    this.clientId = this.config.get('TRANS_UNION_CLIENT_ID');
    this.apiKey = this.config.get('TRANS_UNION_API_KEY');
    this.apiSecret = this.config.get('TRANS_UNION_API_SECRET');
  }

  async createProperty(property: Property): Promise<string> {
    if (!this.apiKey) {
      return;
    }
    const owner = await property.owner;

    if (!owner.transUnionLandlordId) {
      console.error(`Can't create property for landlord ${owner.id} because they don't have a TransUnion ID`);
    }

    const tuProperty = await this.convertToTuProperty(property);

    try {
      const response = await this.retryRequest(async () =>
        axios.post(`${this.baseUrl}/landlords/${owner.transUnionLandlordId}/properties`, tuProperty, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data.propertyId;
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async manuallyAuthenticateRenter(verificationStatus: ManualVerificationComplete): Promise<void> {
    const application = await this.applicationService.findByScreeningRequestRenterId(
      verificationStatus.ScreeningRequestRenterId.toString(),
    );

    if (!application) {
      throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
    }

    const applicationInvite = await this.applicationInviteService.findByApplicationId(application.id);
    const bundle = await applicationInvite.applicationBundle;
    const property = await bundle.property;
    const location = await property.location;

    await this.outBoundCommunicationService.sendManualTransUnionVerificationIsCompletedNotification(
      applicationInvite.id,
      (await applicationInvite.renter).user,
      location,
    );
  }

  async completeReportGeneration(reportStatus: ReportGenerationCompleted): Promise<void> {
    const application = await this.applicationService.findByScreeningRequestRenterId(
      reportStatus.ScreeningRequestRenterId.toString(),
    );

    if (!application) {
      throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
    }

    const bundle = await this.applicationBundleService.findByApplicationId(application.id);

    const month = 30 * 24 * 60 * 60 * 1000;
    const expiresAt = new Date(new Date().getTime() + month);
    if (!bundle.expiresAt) {
      await this.applicationBundleService.update(bundle.id, { expiresAt });
    }

    application.backgroundCheckStatus = BackgroundCheckStatusEnum.FINISHED;

    if (await this.applicationService.isApplicationReadyToBeCompleted(application)) {
      await this.applicationService.updateApplication(application.id, {
        expiresAt,
        backgroundCheckStatus: application.backgroundCheckStatus,
        status: ApplicationStatus.COMPLETED,
      });
      await this.applicationBundleService.sendBundleIsReadyForSubmissionEmailIfAllApplicationsCompleted(bundle);
    } else {
      await this.applicationService.updateApplication(application.id, {
        expiresAt,
        backgroundCheckStatus: application.backgroundCheckStatus,
      });
    }
  }

  async updateProperty(property: Property): Promise<void> {
    if (!property.transUnionPropertyId) {
      return;
    }

    const tuProperty = await this.convertToTuProperty(property);
    const owner = await property.owner;

    try {
      await this.retryRequest(async () =>
        axios.put(
          `${this.baseUrl}/landlords/${owner.transUnionLandlordId}/properties/${property.transUnionPropertyId}`,
          tuProperty,
          {
            headers: await this.getAuthHeader(),
          },
        ),
      );
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async createLandlord(investor: Investor): Promise<string> {
    if (!this.apiKey) {
      return;
    }
    const landlord = await this.convertToLandlord(investor);

    try {
      const response = await this.retryRequest(async () =>
        axios.post(`${this.baseUrl}/landlords`, landlord, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data.landlordId;
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async updateLandlord(investor: Investor): Promise<void> {
    if (!this.apiKey) {
      return;
    }
    const landlord = await this.convertToLandlord(investor);

    try {
      await this.retryRequest(async () =>
        axios.put(`${this.baseUrl}/landlords/${investor.transUnionLandlordId}`, landlord, {
          headers: await this.getAuthHeader(),
        }),
      );
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async createScreeningRequest(
    landlordId: string,
    transUnionPropertyId: string,
    applicationType: ApplicantType,
    attestation?: AttestationDto,
  ): Promise<string> {
    let bundleId: number;

    if (applicationType === ApplicantType.CO_SIGNER) {
      bundleId = TransUnionBundlesConst.CreditCheck;
    } else {
      bundleId = TransUnionBundlesConst.CreditCriminalEvictionCheck;
    }

    const requestBody: any = {
      initialBundleId: bundleId,
    };

    if (attestation) {
      requestBody.attestation = attestation;
    }

    try {
      const response = await this.retryRequest(async () =>
        axios.post(
          `${this.baseUrl}/landlords/${landlordId}/properties/${transUnionPropertyId}/screeningRequests`,
          requestBody,
          {
            headers: await this.getAuthHeader(),
          },
        ),
      );

      return response.data.screeningRequestId;
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async createScreeningRequestRenter(screeningRequestId: string, tuRenterId: string): Promise<string> {
    try {
      const response = await this.retryRequest(async () =>
        axios.post(
          `${this.baseUrl}/screeningRequests/${screeningRequestId}/renters/${tuRenterId}/screeningRequestRenters`,
          {},
          {
            headers: await this.getAuthHeader(),
          },
        ),
      );

      return response.data.screeningRequestRenterId;
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async cancelScreeningRequestRenters(screeningRequestRenterId: string): Promise<void> {
    try {
      await this.retryRequest(async () =>
        axios.delete(`${this.baseUrl}/screeningRequestRenters/${screeningRequestRenterId}/cancel`, {
          headers: await this.getAuthHeader(),
        }),
      );
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async getAllScreeningRequestRenters(screeningRequestId: string): Promise<any> {
    try {
      const response = await this.retryRequest(async () =>
        axios.get(`${this.baseUrl}/screeningRequests/${screeningRequestId}/screeningRequestRenters`, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data;
    } catch (error) {
      throw new HttpException(this.getErrorText(error), error.status);
    }
  }

  async validateScreeningRequestRenter(
    screeningRequestRenterId: string,
    tuRenterId: string,
  ): Promise<VerificationStatusInterface> {
    const renter = await this.getRenterDetails(tuRenterId);
    try {
      const response = await this.retryRequest(async () =>
        axios.post(`${this.baseUrl}/screeningRequestRenters/${screeningRequestRenterId}/validate`, renter, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data;
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async getScreeningRequestRenterDetails(screeningRequestRenterId: string): Promise<any> {
    try {
      const response = await this.retryRequest(async () =>
        axios.get(`${this.baseUrl}/screeningRequestRenters/${screeningRequestRenterId}`, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data;
    } catch (error) {
      throw new HttpException(this.getErrorText(error), error.status);
    }
  }

  async createScreeningRequestRenterExam(screeningRequestRenterId: string): Promise<ExamDto> {
    const screeningRequestRenter = await this.getScreeningRequestRenterDetails(screeningRequestRenterId);
    const renter = await this.getRenterDetails(screeningRequestRenter.renterId);
    renter.cultureCode = 'en-US';
    try {
      const response = await this.retryRequest(async () =>
        axios.post(`${this.baseUrl}/screeningRequestRenters/${screeningRequestRenterId}/exams`, renter, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data;
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async answerExam(screeningRequestRenterId: string, examId: string, answers: ExamQuestionAnswer[]): Promise<any> {
    try {
      const response = await this.retryRequest(async () =>
        axios.post(
          `${this.baseUrl}/screeningRequestRenters/${screeningRequestRenterId}/exams/${examId}/answers`,
          {
            answers,
            cultureCode: 'en-US',
          },
          {
            headers: await this.getAuthHeader(),
          },
        ),
      );

      return response.data;
    } catch (error) {
      throw new HttpException(this.getErrorText(error), error.status);
    }
  }

  async createRenter(body: TransUnionRenterDto, renter: Renter): Promise<string> {
    const tuRenter = await this.convertToTuRenter(body, renter);

    try {
      const response = await this.retryRequest(async () =>
        axios.post(`${this.baseUrl}/renters`, tuRenter, {
          headers: await this.getAuthHeader(),
        }),
      );
      return response.data.renterId;
    } catch (error) {
      console.error(
        'Could not create TransUnion renter: ' + JSON.stringify(error.response.data) + JSON.stringify(tuRenter),
      );
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async updateRenter(renterId: string, payload: TransUnionRenterDto, renter: Renter): Promise<void> {
    const tuRenter = await this.convertToTuRenter(payload, renter);

    try {
      await this.retryRequest(async () =>
        axios.put(`${this.baseUrl}/renters/${renterId}`, tuRenter, {
          headers: await this.getAuthHeader(),
        }),
      );
    } catch (error) {
      console.error(
        'Could not update TransUnion renter: ' + JSON.stringify(error.response.data) + JSON.stringify(tuRenter),
      );
      if (error.status === HttpStatus.BAD_REQUEST || error.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async getRenterDetails(renterId: string): Promise<TransUnionRenter> {
    try {
      const response = await this.retryRequest(async () =>
        axios.get(`${this.baseUrl}/renters/${renterId}`, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data;
    } catch (error) {
      throw new HttpException(this.getErrorText(error), error.status);
    }
  }

  async triggerReportGeneration(screeningRequestRenterId: string): Promise<void> {
    const screeningRequestRenter = await this.getScreeningRequestRenterDetails(screeningRequestRenterId);
    const renter = await this.getRenterDetails(screeningRequestRenter.renterId);
    renter.cultureCode = 'en-US';
    delete renter.dateOfBirth;
    delete renter.nationalId;
    delete renter.renterId;

    try {
      await this.retryRequest(async () =>
        axios.post(`${this.baseUrl}/renters/screeningRequestRenters/${screeningRequestRenterId}/reports`, renter, {
          headers: await this.getAuthHeader(),
        }),
      );
    } catch (error) {
      throw new HttpException(this.getErrorText(error), error.status);
    }
  }

  async getScreeningRequestRenterReport(
    screeningRequestRenterId: string,
    productType: TransUnionProductType,
  ): Promise<ReportResponse> {
    try {
      const response = await this.retryRequest(async () =>
        axios.get(
          `${this.baseUrl}/renters/screeningRequestRenters/${screeningRequestRenterId}/reports?requestedProduct=${productType}&reportType=Html`,
          {
            headers: await this.getAuthHeader(),
          },
        ),
      );

      return response.data;
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async getScreeningRequestLandlordReport(
    screeningRequestRenterId: string,
    productType: TransUnionProductType,
  ): Promise<ReportResponse> {
    try {
      const response = await this.retryRequest(async () =>
        axios.get(
          `${this.baseUrl}/landlords/screeningRequestRenters/${screeningRequestRenterId}/reports?requestedProduct=${productType}&reportType=Html`,
          {
            headers: await this.getAuthHeader(),
          },
        ),
      );

      return response.data;
    } catch (error) {
      if (error.status === HttpStatus.BAD_REQUEST) {
        throw new ValidationException(error.response.data.errors);
      } else {
        throw new HttpException(this.getErrorText(error), error.status);
      }
    }
  }

  async getScreeningRequestDetails(screeningRequestId: string): Promise<any> {
    try {
      const response = await this.retryRequest(async () =>
        axios.get(`${this.baseUrl}/screeningRequests/${screeningRequestId}`, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data;
    } catch (error) {
      throw new HttpException(this.getErrorText(error), error.status);
    }
  }

  private async getAuthHeader(): Promise<{
    Authorization: string;
    MFAAuthorized: string;
  }> {
    return {
      Authorization: await this.getAuthToken(),
      MFAAuthorized: await this.getSecretToken(),
    };
  }

  private async getAuthToken(): Promise<string> {
    if (!this.isAuthTokenValid()) await this.refreshAuthToken();
    return this.authToken.token;
  }

  private async getSecretToken(): Promise<string> {
    if (!this.isAuthTokenValid()) await this.refreshAuthToken();
    return this.authSecretToken.token;
  }

  private async refreshAuthToken(): Promise<void> {
    const response = await axios.post(`${this.baseUrl}/tokens`, {
      clientId: this.clientId,
      apiKey: this.apiKey,
    });

    const secretResponse = await axios.post(`${this.baseUrl}/tokens`, {
      clientId: this.clientId,
      apiKey: this.apiSecret,
    });

    this.authToken = {
      token: response.data.token,
      expires: new Date(response.data.expires),
    };

    this.authSecretToken = {
      token: secretResponse.data.token,
      expires: new Date(secretResponse.data.expires),
    };
  }

  async isRenterVerified(tuRenterId: string): Promise<boolean> {
    if (!tuRenterId) {
      return false;
    }

    try {
      const response = await this.retryRequest(async () =>
        axios.get(`${this.baseUrl}/screeningRequests/${tuRenterId}`, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data;
    } catch (error) {
      throw new HttpException(this.getErrorText(error), error.status);
    }
  }

  async getBundles(): Promise<{ bundleId: number; name: string }[]> {
    try {
      const response = await this.retryRequest(async () =>
        axios.get(`${this.baseUrl}/bundles`, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async getPropertyAttestations(landlordId: string, propertyId: string): Promise<PropertyAttestationsResponseDto> {
    try {
      const response = await this.retryRequest(async () =>
        axios.post(`${this.baseUrl}/landlords/${landlordId}/properties/${propertyId}/attestations`, null, {
          headers: await this.getAuthHeader(),
        }),
      );

      return response.data;
    } catch (error) {
      throw new HttpException(this.getErrorText(error), error.status);
    }
  }

  private isAuthTokenValid(): boolean {
    return (
      this.authToken &&
      this.authSecretToken &&
      !this.isTokenExpired(this.authToken) &&
      !this.isTokenExpired(this.authSecretToken)
    );
  }

  private isTokenExpired(token: TransUnionAuthToken): boolean {
    return token.expires <= new Date();
  }

  private async convertToTuRenter(body: TransUnionRenterDto, renter: Renter): Promise<TransUnionRenter> {
    return {
      firstName: body.firstName,
      lastName: body.lastName,
      middleName: body.middleName ? body.middleName : null,
      acceptedTermsAndConditions: true,
      nationalId: body.nationalId,
      income: renter.monthlyIncome ? renter.monthlyIncome : 0,
      incomeFrequency: IncomeFrequency.PerMonth,
      otherIncome: 0,
      otherIncomeFrequency: IncomeFrequency.PerMonth,
      assets: 0,
      employmentStatus: EmploymentStatus.Employed,
      emailAddress: renter.user.email,
      phoneType: 'Mobile',
      phoneNumber: body.phoneNumber.replace('+1', ''),
      homeAddress: {
        country: 'USA',
        locality: body.city,
        region: PropertyUtils.getStateAbbreviation(body.state),
        postalCode: PropertyUtils.transformZip(body.postalCode),
        addressLine1: body.addressLine1,
        addressLine2: '',
        addressLine3: '',
        addressLine4: '',
      },
      dateOfBirth: body.dateOfBirth,
    };
  }

  private async convertToLandlord(investor: Investor): Promise<TransUnionInvestor> {
    const user = await investor.user;
    const company = await investor.company;
    const nameParts = user.name.trim().split(' ');
    const firstName = nameParts.shift();
    const lastName = nameParts.join(' ');
    return {
      emailAddress: user.email,
      firstName,
      lastName,
      phoneNumber: user.phoneNumber ? user.phoneNumber.replace('+1', '') : this.config.get('TALLO_SENDER_NUMBER'),
      phoneType: 'Mobile',
      businessName: company.name,
      businessAddress: {
        addressLine1: investor.address,
        locality: 'US',
        region: PropertyUtils.getStateAbbreviation(investor.state),
        postalCode: investor.zip.toString(),
        country: 'USA',
      },
      acceptedTermsAndConditions: true,
    };
  }

  private async convertToTuProperty(property: Property): Promise<TransUnionProperty> {
    const location = await property.location;
    const leaseConditions = await property.leaseConditions;
    const renterRequirements = await property.renterRequirements;

    return {
      propertyName: property.displayName,
      rent: leaseConditions?.rent,
      deposit: leaseConditions ? PropertyUtils.getDeposit(leaseConditions.securityDeposit, leaseConditions.rent) : null,
      isActive: property.status === PropertyStatus.LISTED,
      addressLine1: location.address,
      addressLine2: null,
      addressLine3: null,
      addressLine4: null,
      locality: location.city,
      region: PropertyUtils.getStateAbbreviation(location.state),
      postalCode: +PropertyUtils.transformZip(location.zip?.toString()),
      country: 'USA',
      bankruptcyCheck: false,
      incomeToRentRatio: this.getIncomeRatio(renterRequirements?.minimumIncome),
    };
  }

  private async retryRequest<T>(fn: () => Promise<T>, retries = 3, delay = 1000): Promise<T> {
    let attempt = 0;
    while (attempt < retries) {
      try {
        return await fn();
      } catch (error) {
        attempt++;
        if (attempt >= retries) {
          throw error;
        }
        await this.sleep(delay);
      }
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private getErrorText(tuError: any): string {
    return tuError.response?.data?.message ? tuError.response.data.message : tuError.response?.statusText;
  }

  private getIncomeRatio(incomeEnum: IncomeEnum): number | null {
    if (!incomeEnum) {
      return 0;
    }

    switch (incomeEnum) {
      case IncomeEnum.X0_5_RENT:
        return 1;
      case IncomeEnum.X1_RENT:
        return 1;
      case IncomeEnum.X2_RENT:
        return 2;
      case IncomeEnum.X2_5_RENT:
        return 2;
      case IncomeEnum.X3_RENT:
        return 3;
      case IncomeEnum.X3_5_RENT:
        return 3;
      case IncomeEnum.NO_REQUIREMENT:
        return 0;
      default:
        return 0;
    }
  }
}

import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';

@Injectable()
export class TransUnionGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const headers = request.headers;

    // Get the IP from headers or connection info
    let requestIP = headers['x-forwarded-for'] || request.connection.remoteAddress;

    // If there's a list of IPs (e.g., "client, proxy1, proxy2"), take the first one
    if (typeof requestIP === 'string' && requestIP.includes(',')) {
      requestIP = requestIP.split(',')[0].trim();
    }

    // Remove IPv6 prefix if present (e.g., "::ffff:127.0.0.1")
    if (typeof requestIP === 'string' && requestIP.includes('::ffff:')) {
      requestIP = requestIP.split('::ffff:')[1];
    }

    // Convert dotted IP string to a numeric representation
    const ipToLong = (ip: string): number => {
      return (
        ip.split('.').reduce((acc, octet) => {
          return (acc << 8) + parseInt(octet, 10);
        }, 0) >>> 0
      );
    };

    // Define all allowed ranges
    // Each element is [startIP, endIP]
    const allowedRanges: [string, string][] = [
      // Range #1: ************ -> ************
      ['************', '************'],

      // Range #2 (part A): ************ -> ************
      ['************', '************'],

      // Range #2 (part B): ************ -> ************
      ['************', '************'],
    ];

    const ipNum = ipToLong(requestIP);

    // Helper to determine if ipNum is within a start->end range
    const isWithinRange = (ip: number, start: string, end: string): boolean => {
      const startNum = ipToLong(start);
      const endNum = ipToLong(end);
      return ip >= startNum && ip <= endNum;
    };

    // Check if the request IP is in any allowed range or is localhost
    // IPv4 localhost (optional if you want to allow it)
    return (
      allowedRanges.some(([start, end]) => isWithinRange(ipNum, start, end)) ||
      requestIP === '::1' || // IPv6 localhost
      requestIP === '127.0.0.1'
    );
  }
}

import { ApiBearerAuth, <PERSON>piBody, ApiTags } from '@nestjs/swagger';
import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { Public } from '../../auth/decorators/public-access.decorator';
import { TransUnionService } from './trans-union.service';
import { TransUnionGuard } from './guards/trans-union-guard';
import { ReportGenerationCompleted, ReportGenerationCompletedEnum } from './models/report-generation-completed.dto';
import { ManualVerificationComplete } from './models/manual-verification-complete.dto';

@ApiTags('trans-union')
@Controller('trans-union')
@ApiBearerAuth()
@Public()
@UseGuards(TransUnionGuard)
export class TransUnionController {
  constructor(private readonly transUnionService: TransUnionService) {}

  @Post('manualauthentication/status')
  @ApiBody({
    type: ManualVerificationComplete,
    description: 'Result of the manual verification',
    examples: {
      default: {
        value: {
          ScreeningRequestRenterId: 12345,
          ManualAuthenticationStatus: 'Verified',
        },
      },
    },
  })
  async manualVerificationComplete(@Body() manualVerificationCompleted: ManualVerificationComplete): Promise<void> {
    await this.transUnionService.manuallyAuthenticateRenter(manualVerificationCompleted);
  }

  @Post('reports/status')
  @ApiBody({
    type: ReportGenerationCompleted,
    description: 'Result of the report generation',
    examples: {
      default: {
        value: {
          ScreeningRequestRenterId: 12345,
          ReportsDeliveryStatus: ReportGenerationCompletedEnum.SUCCESS,
        },
      },
    },
  })
  async reportRegenerationComplete(@Body() reportGenerationCompleted: ReportGenerationCompleted): Promise<void> {
    await this.transUnionService.completeReportGeneration(reportGenerationCompleted);
  }
}

import { Modu<PERSON> } from '@nestjs/common';
import { TransUnionService } from './trans-union.service';
import { TransUnionController } from './trans-union.controller';
import { TransUnionGuard } from './guards/trans-union-guard';
import { ApplicationModule } from '../../application/application/application.module';
import { ApplicationBundleModule } from '../../application/application-bundle/application-bundle.module';
import { OutboundCommunicationModule } from '../../communication/outbound-communication/outbound-communication.module';
import { ApplicationInviteModule } from '../../application/application-invite/application-invite.module';

@Module({
  imports: [ApplicationModule, ApplicationBundleModule, ApplicationInviteModule, OutboundCommunicationModule],
  controllers: [TransUnionController],
  providers: [TransUnionService, TransUnionGuard],
  exports: [TransUnionService],
})
export class TransUnionModule {}

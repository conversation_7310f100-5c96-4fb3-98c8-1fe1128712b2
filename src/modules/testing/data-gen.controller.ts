import { <PERSON>, Controller, Param, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiCreatedResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IntelligentEscalation } from '../investor/property-question/entities/property-question.entity';
import { User } from '../shared/user/entities/user.entity';
import { DataGenService } from './data-gen.service';
import { DataGenCreateConversationDto } from './dto/data-gen-create-conversation.dto';
import { DataGenDeleteMessages } from './dto/data-gen-delete-messages.dto';
import { DataGenRefreshFollowupTimeDto } from './dto/datagen-refresh-followup-time.dto';
import { ReplyToEmailDto } from './dto/reply-to-email.dto';
import { BasicAuth } from '../shared/auth/decorators/basic-auth.decorator';

@ApiTags('data-gen')
@Controller('data-gen')
@BasicAuth()
@ApiBearerAuth()
export class DataGenController {
  constructor(private dataGenService: DataGenService) {}

  @Post('owner-with-all-entities')
  @ApiCreatedResponse({
    description: 'Add rich investor account with all data. Account password: "123qweQWE))"',
  })
  async ownerWithAllEntities(): Promise<string> {
    const data = await this.dataGenService.generateOwnerWithAllEntities();

    return data.email;
  }

  @Post('owner-with-single-ready-for-listing-property')
  @ApiCreatedResponse({
    description: 'Add use with the single "ready for listing" property. Account password: "123qweQWE))"',
  })
  async ownerWithSingleReadyForListingProperty(): Promise<string> {
    const data = await this.dataGenService.generateOwnerWithSingleReadyForListingProperty();

    return data.email;
  }

  @Post('owner')
  @ApiCreatedResponse({
    description: 'Add randomly generated owner. Account password: "123qweQWE))"',
  })
  async generateOwner(): Promise<User> {
    return this.dataGenService.generateOwnerUser();
  }

  @Post('reply-to-email')
  @ApiCreatedResponse({
    description: 'Send an email reply',
  })
  @ApiBody({
    type: ReplyToEmailDto,
  })
  async replyToEmail(@Body() body: ReplyToEmailDto): Promise<void> {
    await this.dataGenService.replyToEmail(body);
  }

  @Post('conversation')
  @ApiCreatedResponse({
    description: 'Create new conversation',
  })
  async createConversation(@Body() body: DataGenCreateConversationDto): Promise<void> {
    await this.dataGenService.createConversation(body);
  }

  @Post('delete-messages')
  @ApiCreatedResponse({
    description: 'Delete the message from the convo',
  })
  async deleteMessage(@Body() body: DataGenDeleteMessages): Promise<void> {
    await this.dataGenService.deleteConvoMessages(body.messagesIds);
  }

  @Post('follow-up')
  @ApiCreatedResponse({
    description: 'Trigger follow-ups',
  })
  async triggerFollowUps(): Promise<void> {
    await this.dataGenService.triggerFollowUps();
  }

  @Post('follow-up/reset')
  @ApiCreatedResponse({
    description: 'Reset follow-ups',
  })
  async resetFollowUps(@Body() body: DataGenRefreshFollowupTimeDto): Promise<void> {
    await this.dataGenService.resetFollowUps(body);
  }

  @Post('new-renter')
  @ApiCreatedResponse({
    description: 'Create new renter',
  })
  async generateNewRenter(): Promise<void> {
    await this.dataGenService.generateNewRenter();
  }

  @Post('property/:propertyId/question')
  @ApiParam({
    name: 'propertyId',
    description: 'The ID of the property. It is a required argument when calling this endpoint.',
    required: true,
  })
  @ApiCreatedResponse({
    description: 'Add randomly generated property question',
  })
  async generatePropertyQuestion(@Param('propertyId') propertyId: string): Promise<IntelligentEscalation> {
    return this.dataGenService.generatePropertyQuestion(propertyId);
  }

  @Post('migrate-investor-availability-to-property')
  @ApiCreatedResponse({
    description: 'Migrate all investor availabilities to property availabilities',
  })
  async migrateInvestorAvailabilityToProperty(): Promise<{ message: string; migratedCount: number }> {
    return this.dataGenService.migrateInvestorAvailabilityToProperty();
  }
}

import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ReplyToEmailDto {
  @IsNotEmpty()
  @ApiProperty()
  email: string;

  @IsNotEmpty()
  @ApiProperty({
    example: 'Hi! I would like to schedule a property tour.',
    description:
      'First message from the renter. Use "text2" and "text3" for additional messages. "text1" is sent first, "text3" is sent last.',
  })
  text1: string;

  @IsOptional()
  @ApiPropertyOptional({ example: null })
  text2: string;

  @IsOptional()
  @ApiPropertyOptional({ example: null })
  text3: string;

  @IsNotEmpty()
  @ApiProperty()
  propertyAddress: string;
}

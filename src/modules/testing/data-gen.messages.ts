export interface MockedMessagesList {
  text: string;
  isSystem: boolean;
}

export const mockMessages: MockedMessagesList[] = [
  {
    text: 'Good morning! How can I assist you today?',
    isSystem: true,
  },
  {
    text: "Hi there! I'm interested in renting a one-bedroom apartment in the city. Can you tell me more about the options available?",
    isSystem: false,
  },
  {
    text: 'Of course! We have a few one-bedroom apartments available that might suit your needs. Would you prefer a specific location within the city?',
    isSystem: true,
  },
  {
    text: "I'm looking for something that's relatively close to public transportation and has some grocery stores and restaurants nearby.",
    isSystem: false,
  },
  {
    text: "That's great to hear! We have two options that fit your criteria. One is located downtown, just a couple of blocks away from the main subway station. It's surrounded by a variety of restaurants and shops, and there's a grocery store right across the street.",
    isSystem: true,
  },
  {
    text: "That sounds perfect! What's the other option?",
    isSystem: false,
  },
  {
    text: 'We are done talking',
    isSystem: true,
  },
];

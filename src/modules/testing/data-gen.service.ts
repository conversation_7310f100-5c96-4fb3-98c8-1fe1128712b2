import { addMinutes, subDays } from 'date-fns';

import { faker } from '@faker-js/faker';
import { Body, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AvailabilitySlot } from '../investor/availability/availability-slot.entity';
import { AvailabilitySlotsService } from '../investor/availability/availability-slot.service';
import { InvestorAvailabilityService } from '../investor/availability/investor-availability.service';
import { Investor } from '../investor/investor/investor.entity';
import { InvestorService } from '../investor/investor/investor.service';
import { PropertyAvailabilityService } from '../investor/property/availability/property-availability.service';
import { PropertyInquiryService } from '../investor/property-inquiry/property-inquiry.service';
import { IntelligentEscalation } from '../investor/property-question/entities/property-question.entity';
import { IntelligentEscalationService } from '../investor/property-question/intelligent-escalation.service';
import { Property } from '../investor/property/property/entities/property.entity';
import { LeaseTermEnum } from '../investor/property/property/enums/lease-term.enum';
import { PropertyStatus } from '../investor/property/property/enums/property-status.enum';
import { PropertyTypeEnum } from '../investor/property/property/enums/property-type.enum';
import { LeaseConditions } from '../investor/property/property/property-details/lease-conditions/lease-conditions.entity';
import { PropertyLocation } from '../investor/property/property/property-details/location/property-location.entity';
import { IncomeEnum } from '../investor/property/property/property-details/renter-requirements/income.enum';
import { PropertySpecifications } from '../investor/property/property/property-details/specifications/property-specifications.entity';
import { PropertyService } from '../investor/property/property/property.service';
import { ShowingRequestStatus } from '../investor/showing-request/enums/showing-request-status.enum';
import { ShowingRequestService } from '../investor/showing-request/showing-request.service';
import { ShowingStatus } from '../investor/showing/enums/showing-status.enum';
import { Showing } from '../investor/showing/showing.entity';
import { ShowingService } from '../investor/showing/showing.service';
import { Renter } from '../renter/renter/renter.entity';
import { Role } from '../shared/auth/models/roles-enum';
import { ConversationService } from '../shared/communication/conversation/conversation.service';
import { Conversation } from '../shared/communication/conversation/entities/conversation.entity';
import { CommunicationChannel } from '../shared/communication/conversation/enums/preferred-communication-channel.enum';
import { MessageType } from '../shared/communication/conversation/message/message-type.enum';
import { MessageService } from '../shared/communication/conversation/message/message.service';
import { FollowUpService } from '../shared/communication/follow-up/follow-up.service';
import { InboundEmailsSubjects } from '../shared/communication/inbound-communication/enums/inbound-emails-subjects';
import { ParsedGmailEmail } from '../shared/communication/inbound-communication/gmail/interfaces/parsed-gmail-email.interface';
import { CompanyService } from '../shared/company/company.service';
import { Company } from '../shared/company/entities/company.entity';
import { User } from '../shared/user/entities/user.entity';
import { UserService } from '../shared/user/user.service';
import { mockMessages } from './data-gen.messages';
import { DataGenCreateConversationDto } from './dto/data-gen-create-conversation.dto';
import { DataGenRefreshFollowupTimeDto } from './dto/datagen-refresh-followup-time.dto';
import { ReplyToEmailDto } from './dto/reply-to-email.dto';
import { RenterService } from '../renter/renter/renter.service';
import { RenterCommunicationService } from '../renter/renter-communication/renter-communication.service';
import ParsingUtils from '../../utils/parsing.utils';

export interface GeneratePropertyPayload {
  investor: Investor;
  company: Company;
  status: PropertyStatus;
}

@Injectable()
export class DataGenService {
  constructor(
    private readonly showingRequestService: ShowingRequestService,
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly propertyService: PropertyService,
    private readonly showingService: ShowingService,
    private readonly propertyQuestionService: IntelligentEscalationService,
    private readonly userService: UserService,
    private readonly conversationService: ConversationService,
    private readonly messageService: MessageService,
    private readonly renterService: RenterService,
    private readonly renterCommunicationService: RenterCommunicationService,
    private readonly companyService: CompanyService,
    private readonly followUpService: FollowUpService,
    private readonly investorAvailabilityService: InvestorAvailabilityService,
    private readonly investorService: InvestorService,
    private readonly propertyAvailabilityService: PropertyAvailabilityService,
    private readonly availabilitySlotsService: AvailabilitySlotsService,
    @InjectRepository(AvailabilitySlot)
    private readonly availabilitySlotRepository: Repository<AvailabilitySlot>,
  ) {}

  async generateOwnerWithAllEntities(): Promise<User> {
    const user = await this.generateOwnerUser();

    const company = await this.companyService.createCompany({
      name: faker.company.name(),
    });

    const investor = await this.investorService.create({
      user,
      company,
    });

    await this.investorAvailabilityService.create({ investor });

    await this.generateListedPropertyWithShowings(investor, company);
    await this.generateListedPropertyWithShowings(investor, company);
    await this.generateBunchOfOtherProperties(investor, company);

    return user;
  }

  async generateOwnerWithSingleReadyForListingProperty(): Promise<User> {
    const user = await this.generateOwnerUser();

    const company = await this.companyService.createCompany({
      name: faker.company.name(),
    });

    const investor = await this.investorService.create({
      user,
      company,
    });

    await this.investorAvailabilityService.create({ investor });

    await this.generateProperty({
      investor,
      company,
      status: PropertyStatus.READY_FOR_LISTING,
    });

    return user;
  }

  async generateOwnerUser(): Promise<User> {
    return this.userService.createUser({
      name: faker.person.fullName(),
      email: faker.internet.email(),
      password: '123qweQWE))',
      roles: [Role.INVESTOR],
    });
  }

  async generateListedPropertyWithShowings(investor: Investor, company: Company): Promise<void> {
    const listedProperty = await this.generateProperty({
      investor,
      company,
      status: PropertyStatus.LISTED,
    });

    await this.createShowingWithPendingShowingReq(listedProperty);
    await this.createShowingWithTwoConfirmedAndTwoPendingShowingReqs(listedProperty);
    await this.createUpcomingConfirmedShowing(listedProperty);
    await this.createUpcomingConfirmedShowing(listedProperty);
    await this.createCompletedShowing(listedProperty);
  }

  async generateBunchOfOtherProperties(investor: Investor, company: Company): Promise<void> {
    await this.generateProperty({
      investor,
      company,
      status: PropertyStatus.DRAFT,
    });
    await this.generateProperty({
      investor,
      company,
      status: PropertyStatus.UNLISTED,
    });
    await this.generateProperty({
      investor,
      company,
      status: PropertyStatus.LISTING_IN_PROGRESS,
    });
    await this.generateProperty({
      investor,
      company,
      status: PropertyStatus.READY_FOR_LISTING,
    });
    await this.generateProperty({
      investor,
      company,
      status: PropertyStatus.RENTED_OUT,
    });
  }

  async createShowingWithPendingShowingReq(property: Property): Promise<Showing> {
    const pendingShowing = await this.generateShowing(property, ShowingStatus.PENDING);
    const { renter } = await this.generateShowingRequest(pendingShowing, ShowingRequestStatus.PENDING);

    await this.addPropertyQuestion(property, [renter]);

    return pendingShowing;
  }

  async createShowingWithTwoConfirmedAndTwoPendingShowingReqs(property: Property): Promise<void> {
    const showing = await this.generateShowing(property, ShowingStatus.CONFIRMED);
    const renter1 = await this.generateShowingRequest(showing, ShowingRequestStatus.PENDING);
    const renter2 = await this.generateShowingRequest(showing, ShowingRequestStatus.PENDING);
    const renter3 = await this.generateShowingRequest(showing, ShowingRequestStatus.ACCEPTED);
    const renter4 = await this.generateShowingRequest(showing, ShowingRequestStatus.ACCEPTED);

    await this.addPropertyQuestion(property, [renter1.renter, renter2.renter, renter3.renter, renter4.renter]);
  }

  async createUpcomingConfirmedShowing(property: Property): Promise<void> {
    const showing = await this.generateShowing(property, ShowingStatus.CONFIRMED);
    await this.generateShowingRequest(showing, ShowingRequestStatus.ACCEPTED);
    await this.generateShowingRequest(showing, ShowingRequestStatus.ACCEPTED);
  }

  async createCompletedShowing(property: Property): Promise<void> {
    const showing = await this.generateCompletedShowing(property);
    await this.generateShowingRequest(showing, ShowingRequestStatus.ACCEPTED);
    await this.generateShowingRequest(showing, ShowingRequestStatus.PENDING);
    await this.generateShowingRequest(showing, ShowingRequestStatus.DECLINED);
  }

  async addPropertyQuestion(property: Property, renters: Renter[]): Promise<IntelligentEscalation> {
    return await this.propertyQuestionService.create({
      property,
      renters,
      title: faker.word.adjective(),
      questionText: faker.lorem.paragraph(),
    });
  }

  async generateNewRenter(): Promise<void> {
    await this.renterService.create(
      faker.person.fullName(),
      faker.internet.email(),
      faker.phone.number({
        style: 'international',
      }),
    );
  }

  async createConversation(createConversation: DataGenCreateConversationDto): Promise<void> {
    const renter = await this.renterService.findById(createConversation.renterId, false);
    const property = await this.propertyService.findById(createConversation.propertyId);

    await this.conversationService.create([renter.user], property);
  }

  async triggerFollowUps(): Promise<void> {
    await this.followUpService.followUpConversations();
  }

  async resetFollowUps(@Body() body: DataGenRefreshFollowupTimeDto): Promise<void> {
    const conversation = await this.conversationService.findById(body.conversationId);
    const property = await conversation.property;
    const renterUser = (await conversation.users).find((user) => user.roles.includes(Role.RENTER));
    const renter = await this.renterService.findByEmail(renterUser.email);
    const propertyInquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, property.id);

    await this.followUpService.refreshFollowUpsScheduledTime(conversation, propertyInquiry);
  }

  async replyToEmail(replyDto: ReplyToEmailDto): Promise<void> {
    const emailSubject = InboundEmailsSubjects.NEW_PROPERTY_MESSAGE + ' ' + replyDto.propertyAddress;

    const renter = await this.renterService.findByEmail(replyDto.email);

    if (!renter) {
      throw new Error('Renter not found');
    }

    const { street, apartmentNumber } = ParsingUtils.parseAddress(emailSubject);

    let property = await this.propertyService.findByAddress(street, apartmentNumber);

    if (!property) {
      property = await this.propertyService.findByDisplayName(street);
    }

    const inquiry = await this.propertyInquiryService.findLatestByRenterAndProperty(renter.id, property.id);
    const conversation = await inquiry.conversation;
    const messages = [replyDto.text1, replyDto.text2, replyDto.text3].filter(Boolean);

    await Promise.all(
      messages.map((message) =>
        this.messageService.create({
          user: renter.user,
          conversations: [conversation],
          communicationChannel: CommunicationChannel.EMAIL,
          content: message,
          type: MessageType.TEXT,
          isAnswered: true,
        }),
      ),
    );

    // Update conversation status to ANSWERED since messages are marked as answered
    await this.conversationService.markConversationAsAnswered(conversation.id);

    if (renter.user.preferredCommunicationChannel === CommunicationChannel.SMS && renter.user.phoneNumber) {
      await this.renterCommunicationService.answerToRenter(messages, inquiry, CommunicationChannel.SMS);
    } else {
      const messagesForEmail: ParsedGmailEmail[] = messages.map((message) => ({
        emailText: message,
        emailAddress: replyDto.email,
        subject: emailSubject,
        messageId: undefined,
        references: [],
      }));

      await this.renterCommunicationService.answerToRenterEmails(inquiry, messagesForEmail);
    }
  }

  async generateProperty(propertyPayload: GeneratePropertyPayload): Promise<Property> {
    const createdAt = faker.date.past();
    const address = faker.location.streetAddress();

    const location = new PropertyLocation();
    location.address = address;
    location.city = 'Pittsburgh';
    location.state = faker.location.state();
    location.zip = 10001;
    location.latitude = faker.location.latitude();
    location.longitude = faker.location.longitude();

    const leaseConditions = new LeaseConditions();
    leaseConditions.rent = faker.number.int({ min: 1500, max: 5000 });
    leaseConditions.securityDeposit = IncomeEnum.X1_RENT;
    leaseConditions.lastMonthRent = IncomeEnum.X1_RENT;
    leaseConditions.possibleLeaseTerms = [LeaseTermEnum.MONTHLY];

    const specifications = new PropertySpecifications();
    specifications.propertyType = PropertyTypeEnum.HOUSE;
    specifications.squareFeet = faker.number.int({ min: 500, max: 1000 });
    specifications.bedrooms = faker.number.int({ min: 1, max: 3 });
    specifications.fullBathrooms = faker.number.int({ min: 1, max: 2 });
    specifications.halfBathrooms = faker.number.int({ min: 1, max: 2 });
    specifications.yearBuilt = faker.number.int({ min: 1990, max: 2020 });
    specifications.numberOfFloors = 1;

    return this.propertyService.createNewProperty({
      createdAt,
      owner: propertyPayload.investor,
      company: propertyPayload.company,
      updatedAt: faker.date.future({ refDate: createdAt }),
      conversations: [],
      showingRequests: [],
      showings: [],
      status: propertyPayload.status,
      petPolicy: null,
      includedUtilities: null,
      accessibility: null,
      parking: null,
      amenities: null,
      renterRequirements: null,
      location,
      specifications,
      leaseConditions,
      displayName: address,
      questions: [],
      allowsSmoking: null,
      maximumOccupancy: null,
      description: faker.lorem.paragraphs(5),
    });
  }

  async generatePropertyQuestion(propertyId: string): Promise<IntelligentEscalation> {
    const property = await this.propertyService.findById(propertyId);

    return await this.propertyQuestionService.create({
      property,
      title: faker.lorem.sentence(),
      questionText: faker.lorem.paragraph(),
    });
  }

  async generateRenterConversation(user: User, property: Property): Promise<void> {
    const conversation = new Conversation();
    conversation.users = [user];
    conversation.property = property;

    const savedConversation = await this.conversationService.save(conversation);
    await this.conversationService.updateEmailMetadata(conversation, {
      subject: 'Data gen service: reach out about showing',
    });

    await Promise.all([
      ...mockMessages.map(async (message) => {
        return await this.messageService.create({
          user,
          conversations: [savedConversation],
          content: message.text,
          type: MessageType.TEXT,
          isSystem: message.isSystem,
          isAnswered: true,
          isSeen: true,
        });
      }),
    ]);

    // Update conversation status to ANSWERED since messages are marked as answered
    await this.conversationService.markConversationAsAnswered(savedConversation.id);
  }

  async deleteConvoMessages(messagesIds: number[]): Promise<void> {
    await this.messageService.deleteMultiple(messagesIds);
  }

  private async generateShowing(
    property: Property,
    showingStatus: ShowingStatus = ShowingStatus.PENDING,
  ): Promise<Showing> {
    const createdAt = faker.date.past();
    const startTime = faker.date.future();
    const endTime = new Date();
    endTime.setMinutes(startTime.getMinutes() + 30);

    return this.showingService.create({
      startTime,
      createdAt,
      endTime,
      updatedAt: faker.date.past({ refDate: createdAt }),
      property,
      showingRequests: [],
      status: showingStatus,
    });
  }

  private async generateCompletedShowing(property: Property): Promise<Showing> {
    const createdAt = subDays(new Date(), 2);
    const startTime = subDays(new Date(), 1);
    const endTime = addMinutes(startTime, 15);

    return this.showingService.create({
      startTime,
      createdAt,
      endTime,
      updatedAt: faker.date.past({ refDate: createdAt }),
      property,
      showingRequests: [],
      status: ShowingStatus.COMPLETED,
    });
  }

  private async generateShowingRequest(
    showing: Showing,
    showingRequestStatus: ShowingRequestStatus = ShowingRequestStatus.PENDING,
  ): Promise<{ renter: Renter }> {
    const renter = await this.generateRenter(await showing.property);

    await this.showingRequestService.create({
      showing,
      renter: renter,
      property: await showing.property,
      status: showingRequestStatus,
      createdAt: faker.date.past(),
      updatedAt: faker.date.past(),
    });

    return { renter };
  }

  private async generateRenter(property: Property): Promise<Renter> {
    const renter = await this.renterService.create(
      faker.person.fullName(),
      faker.internet.email(),
      faker.phone.number({
        style: 'international',
      }),
      CommunicationChannel.EMAIL,
      {
        occupation: faker.person.jobTitle(),
        createdAt: faker.date.past(),
        monthlyIncome: faker.number.int({ min: 1500, max: 10000 }),
        creditScore: `${faker.number.int({
          min: 600,
          max: 650,
        })}-${faker.number.int({ min: 651, max: 700 })}`,
      },
    );

    await this.generateRenterConversation(renter.user, property);

    return renter;
  }

  async migrateInvestorAvailabilityToProperty(): Promise<{ message: string; migratedCount: number }> {
    console.log('Starting migration from investor-availability to property-availability...');

    let migratedCount = 0;

    try {
      // 1. Get all investor availabilities
      const investorAvailabilities = await this.investorAvailabilityService.findAll();

      console.log(`Found ${investorAvailabilities.length} investor availabilities to migrate`);

      for (const investorAvailability of investorAvailabilities) {
        const investor = await investorAvailability.investor;
        const company = await investor.company;

        // 2. Find all properties of this investor
        const properties = await this.propertyService.findAllByCompany(company.id);

        console.log(`Found ${properties.length} properties for investor ${investor.id}`);

        // 3. For each property, create a property availability
        for (const property of properties) {
          try {
            // Check if property already has availability
            const existingAvailability = await this.propertyAvailabilityService.findByPropertyId(property.id);

            if (existingAvailability) {
              console.log(`Property ${property.id} already has availability, skipping...`);
              continue;
            }

            // Create property availability
            const propertyAvailability = await this.propertyAvailabilityService.create({
              showingDurationInMinutes: investorAvailability.showingDurationInMinutes,
              property: property,
            });

            // 4. Copy all availability slots
            const investorSlots = await investorAvailability.availabilitySlots;

            for (const slot of investorSlots) {
              const newSlot = new AvailabilitySlot();
              newSlot.weekday = slot.weekday;
              newSlot.startTime = slot.startTime;
              newSlot.endTime = slot.endTime;
              newSlot.propertyAvailability = propertyAvailability;

              await this.availabilitySlotRepository.save(newSlot);
            }

            console.log(`Migrated availability for property ${property.id} with ${investorSlots.length} slots`);
            migratedCount++;
          } catch (error) {
            console.error(`Error migrating property ${property.id}:`, error.message);
          }
        }
      }

      console.log(`Migration completed. Total properties migrated: ${migratedCount}`);

      return {
        message: `Successfully migrated ${migratedCount} properties from investor availability to property availability`,
        migratedCount,
      };
    } catch (error) {
      console.error('Migration failed:', error);
      throw new Error(`Migration failed: ${error.message}`);
    }
  }
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AvailabilitySlot } from '../investor/availability/availability-slot.entity';
import { PropertyModule } from '../investor/property/property/property.module';
import { PropertyAvailabilityModule } from '../investor/property/availability/property-availability-module';
import { IntelligentEscalationModule } from '../investor/property-question/intelligent-escalation.module';
import { ShowingRequestModule } from '../investor/showing-request/showing-request.module';
import { ShowingModule } from '../investor/showing/showing.module';
import { ConversationModule } from '../shared/communication/conversation/conversetion.module';
import { UserModule } from '../shared/user/user.module';
import { DataGenController } from './data-gen.controller';
import { DataGenService } from './data-gen.service';
import { CompanyModule } from '../shared/company/company.module';
import { FollowUpModule } from '../shared/communication/follow-up/follow-up.module';
import { AvailabilityModule } from '../investor/availability/availability.module';
import { InvestorEntityModule } from '../investor/investor/investor-entity.module';
import { PropertyInquiryModule } from '../investor/property-inquiry/property-inquiry.module';
import { AuthModule } from '../shared/auth/auth.module';
import { RenterModule } from '../renter/renter/renter.module';
import { RenterCommunicationModule } from '../renter/renter-communication/renter-communication.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AvailabilitySlot]),
    RenterModule,
    PropertyModule,
    PropertyAvailabilityModule,
    ShowingModule,
    PropertyModule,
    ShowingRequestModule,
    IntelligentEscalationModule,
    UserModule,
    ConversationModule,
    CompanyModule,
    FollowUpModule,
    AvailabilityModule,
    InvestorEntityModule,
    PropertyInquiryModule,
    RenterCommunicationModule,
    AuthModule,
  ],
  providers: [DataGenService],
  controllers: [DataGenController],
})
export class DataGenModule {}

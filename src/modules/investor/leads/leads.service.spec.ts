import { Test, TestingModule } from '@nestjs/testing';
import { LeadsService } from './leads.service';
import { RenterService } from '../../renter/renter/renter.service';
import { PropertyService } from '../property/property/property.service';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import { RenterCommunicationService } from '../../renter/renter-communication/renter-communication.service';
import { ConversationService } from '../../shared/communication/conversation/conversation.service';
import { FollowUpService } from '../../shared/communication/follow-up/follow-up.service';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { InvestorApplicationService } from '../application/investor-application.service';
import { PropertyInquirySource } from '../property-inquiry/property-inquiry-source.enum';
import { CommunicationChannel } from '../../shared/communication/conversation/enums/preferred-communication-channel.enum';
import { EmploymentStatus } from '../../renter/renter/enums/employment-status.enum';
import { LeadDto } from './models/lead.dto';
import { ManualLeadDto } from './models/manual-lead.dto';

describe('LeadsService', () => {
  let service: LeadsService;
  let renterService: jest.Mocked<RenterService>;
  let propertyService: jest.Mocked<PropertyService>;
  let propertyInquiryService: jest.Mocked<PropertyInquiryService>;
  let renterCommunicationService: jest.Mocked<RenterCommunicationService>;
  let conversationService: jest.Mocked<ConversationService>;
  let followUpService: jest.Mocked<FollowUpService>;

  const mockProperty = {
    id: 'property-1',
    displayName: 'Test Property',
  };

  const mockRenter = {
    id: 'renter-1',
    user: {
      preferredCommunicationChannel: CommunicationChannel.EMAIL,
    },
  };

  const mockInquiry = {
    id: 'inquiry-1',
    conversation: Promise.resolve({
      id: 'conversation-1',
      messages: Promise.resolve([]),
    }),
    renter: Promise.resolve(mockRenter),
    property: Promise.resolve(mockProperty),
    source: PropertyInquirySource.ZILLOW,
    updatedAt: new Date(),
    createdAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LeadsService,
        {
          provide: RenterService,
          useValue: {
            findByEmailOrPhoneAndAddRenterRoleIfNeeded: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: PropertyService,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: PropertyInquiryService,
          useValue: {
            findByRenterAndProperty: jest.fn(),
            createNewInquiry: jest.fn(),
          },
        },
        {
          provide: RenterCommunicationService,
          useValue: {
            answerToRenter: jest.fn(),
            welcomeRenter: jest.fn(),
          },
        },
        {
          provide: ConversationService,
          useValue: {
            saveRenterMessages: jest.fn(),
          },
        },
        {
          provide: FollowUpService,
          useValue: {
            createFirstFollowUpsForRenter: jest.fn(),
          },
        },
        {
          provide: SlackCommunicationService,
          useValue: {
            sendMessageToConvosChannel: jest.fn(),
          },
        },
        {
          provide: InvestorApplicationService,
          useValue: {
            sendApplicationToRenter: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<LeadsService>(LeadsService);
    renterService = module.get(RenterService);
    propertyService = module.get(PropertyService);
    propertyInquiryService = module.get(PropertyInquiryService);
    renterCommunicationService = module.get(RenterCommunicationService);
    conversationService = module.get(ConversationService);
    followUpService = module.get(FollowUpService);

    // Reset mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('addLead', () => {
    const mockLead: LeadDto = {
      propertyId: 'property-1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+12125551234', // formatted phone number as it would come from ZillowService
      message: 'Hello I am interested in this property',
      employmentStatus: EmploymentStatus.EMPLOYED,
      jobTitle: 'Software Engineer',
      monthlyIncome: 10000,
      creditScore: '700 - 750',
      hasPets: true,
      petDescription: 'Friendly golden retriever',
      petType: 'dog',
      source: PropertyInquirySource.ZILLOW,
    };

    beforeEach(() => {
      propertyService.findById.mockResolvedValue(mockProperty as any);
      renterService.findByEmailOrPhoneAndAddRenterRoleIfNeeded.mockResolvedValue(null);
      renterService.create.mockResolvedValue(mockRenter as any);
      propertyInquiryService.findByRenterAndProperty.mockResolvedValue(null);
      propertyInquiryService.createNewInquiry.mockResolvedValue(mockInquiry as any);
    });

    it('should process lead with message and create inquiry', async () => {
      await service.addLead(mockLead);

      expect(propertyService.findById).toHaveBeenCalledWith('property-1', false);
      expect(renterService.create).toHaveBeenCalledWith(
        'John Doe',
        '<EMAIL>',
        '+12125551234', // formatted phone
        CommunicationChannel.EMAIL,
        {
          employmentStatus: EmploymentStatus.EMPLOYED,
          occupation: 'Software Engineer',
          monthlyIncome: 10000,
          creditScore: '700 - 750',
          hasPets: true,
          petDescription: 'Friendly golden retriever',
          petType: 'dog',
        },
      );
      expect(propertyInquiryService.createNewInquiry).toHaveBeenCalledWith(
        mockProperty,
        mockRenter,
        'Hello I am interested in this property',
        PropertyInquirySource.ZILLOW,
      );
      expect(conversationService.saveRenterMessages).toHaveBeenCalled();
      expect(renterCommunicationService.answerToRenter).toHaveBeenCalled();
    });

    it('should process lead without message and welcome renter', async () => {
      const leadWithoutMessage = { ...mockLead, message: undefined };

      await service.addLead(leadWithoutMessage);

      expect(renterCommunicationService.welcomeRenter).toHaveBeenCalled();
      expect(followUpService.createFirstFollowUpsForRenter).toHaveBeenCalled();
      expect(conversationService.saveRenterMessages).not.toHaveBeenCalled();
      expect(renterCommunicationService.answerToRenter).not.toHaveBeenCalled();
    });

    it('should handle existing renter', async () => {
      renterService.findByEmailOrPhoneAndAddRenterRoleIfNeeded.mockResolvedValue(mockRenter as any);

      await service.addLead(mockLead);

      expect(renterService.create).not.toHaveBeenCalled();
      expect(propertyInquiryService.createNewInquiry).toHaveBeenCalledWith(
        mockProperty,
        mockRenter,
        expect.any(String),
        PropertyInquirySource.ZILLOW,
      );
    });

    it('should handle existing non-expired inquiry', async () => {
      const recentInquiry = {
        ...mockInquiry,
        updatedAt: new Date(), // recent update
        createdAt: new Date(), // recent creation
      };
      propertyInquiryService.findByRenterAndProperty.mockResolvedValue(recentInquiry as any);

      await service.addLead(mockLead);

      expect(propertyInquiryService.createNewInquiry).not.toHaveBeenCalled();
      expect(conversationService.saveRenterMessages).toHaveBeenCalled();
    });
  });

  describe('addSilent', () => {
    const mockSilentLead: ManualLeadDto = {
      propertyId: 'property-1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+12125551234',
      action: undefined, // action is ignored in silent mode
    };

    beforeEach(() => {
      propertyService.findById.mockResolvedValue(mockProperty as any);
      renterService.findByEmailOrPhoneAndAddRenterRoleIfNeeded.mockResolvedValue(null);
      renterService.create.mockResolvedValue(mockRenter as any);
      propertyInquiryService.findByRenterAndProperty.mockResolvedValue(null);
      propertyInquiryService.createNewInquiry.mockResolvedValue(mockInquiry as any);
    });

    it('should create user and inquiry without sending messages', async () => {
      const result = await service.addSilent(mockSilentLead);

      expect(result).toBe('inquiry-1');
      expect(propertyService.findById).toHaveBeenCalledWith('property-1', false);
      expect(renterService.create).toHaveBeenCalledWith(
        'John Doe',
        '<EMAIL>',
        '+12125551234',
        CommunicationChannel.EMAIL,
        {},
      );
      expect(propertyInquiryService.createNewInquiry).toHaveBeenCalledWith(
        mockProperty,
        mockRenter,
        null,
        PropertyInquirySource.MANUALLY_ADDED,
      );

      // Verify no communication services are called
      expect(renterCommunicationService.welcomeRenter).not.toHaveBeenCalled();
      expect(renterCommunicationService.answerToRenter).not.toHaveBeenCalled();
      expect(followUpService.createFirstFollowUpsForRenter).not.toHaveBeenCalled();
      expect(conversationService.saveRenterMessages).not.toHaveBeenCalled();
    });

    it('should handle existing renter', async () => {
      renterService.findByEmailOrPhoneAndAddRenterRoleIfNeeded.mockResolvedValue(mockRenter as any);

      const result = await service.addSilent(mockSilentLead);

      expect(result).toBe('inquiry-1');
      expect(renterService.create).not.toHaveBeenCalled();
      expect(propertyInquiryService.createNewInquiry).toHaveBeenCalledWith(
        mockProperty,
        mockRenter,
        null,
        PropertyInquirySource.MANUALLY_ADDED,
      );
    });

    it('should throw error if renter already has non-expired inquiry', async () => {
      const recentInquiry = {
        ...mockInquiry,
        updatedAt: new Date(),
        createdAt: new Date(),
      };
      propertyInquiryService.findByRenterAndProperty.mockResolvedValue(recentInquiry as any);

      await expect(service.addSilent(mockSilentLead)).rejects.toThrow('Renter already inquired about this property');

      expect(propertyInquiryService.createNewInquiry).not.toHaveBeenCalled();
    });
  });
});

import { EmploymentStatus } from '../../../renter/renter/enums/employment-status.enum';
import { Transform, Type } from 'class-transformer';
import { IsString, IsNumber, IsOptional, IsBoolean, IsEnum, IsArray } from 'class-validator';

class CreditScoreRange {
  @IsNumber()
  creditScoreMin: number;

  @IsNumber()
  creditScoreMax: number;
}

class EmploymentDetail {
  @IsString()
  jobTitle: string;

  @IsString()
  employer: string;

  @IsString()
  startDate: string;

  @IsString()
  endDate: string;
}

class PetDetail {
  @IsString()
  type: string;

  @IsOptional()
  @IsString()
  breed?: string;

  @IsString()
  size: string;

  @IsOptional()
  @IsNumber()
  weightPounds?: number;

  @IsString()
  description: string;
}

export class ZillowNewLead {
  @IsString()
  listingId: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsString()
  email: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  movingDate?: string;

  @IsOptional()
  @IsString()
  numBedroomsSought?: string;

  @IsOptional()
  @IsString()
  numBathroomsSought?: string;

  @IsOptional()
  @IsString()
  introduction?: string;

  @IsOptional()
  @IsString()
  hasPets?: string;

  @IsOptional()
  @IsString()
  message?: string;

  @IsOptional()
  @IsString()
  listingStreet?: string;

  @IsOptional()
  @IsString()
  listingUnit?: string;

  @IsOptional()
  @IsString()
  listingCity?: string;

  @IsOptional()
  @IsString()
  listingPostalCode?: string;

  @IsOptional()
  @IsString()
  listingState?: string;

  @IsOptional()
  @IsString()
  listingContactEmail?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  neighborhoods?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  propertyTypesDesired?: string[];

  @IsOptional()
  @IsString()
  leaseLengthMonths?: string;

  @IsOptional()
  @IsBoolean()
  smoker?: boolean;

  @IsOptional()
  @IsString()
  parkingTypeDesired?: string;

  @IsOptional()
  @IsString()
  incomeYearly?: string;

  @IsOptional()
  @Type(() => CreditScoreRange)
  @Transform(({ value }) => (typeof value === 'string' ? JSON.parse(value) : value), { toClassOnly: true })
  creditScoreRangeJson?: CreditScoreRange;

  @IsOptional()
  @IsString()
  movingFromCity?: string;

  @IsOptional()
  @IsString()
  movingFromState?: string;

  @IsOptional()
  @IsString()
  moveInTimeframe?: string;

  @IsOptional()
  @IsString()
  reasonForMoving?: string;

  @IsOptional()
  @IsEnum(EmploymentStatus)
  employmentStatus?: EmploymentStatus;

  @IsOptional()
  @IsString()
  jobTitle?: string;

  @IsOptional()
  @IsString()
  employer?: string;

  @IsOptional()
  @IsString()
  employmentStartDate?: string;

  @IsOptional()
  @Type(() => EmploymentDetail)
  @Transform(({ value }) => (typeof value === 'string' ? JSON.parse(value) : value), { toClassOnly: true })
  employmentDetailsJson?: EmploymentDetail[];

  @IsOptional()
  @Type(() => PetDetail)
  @Transform(({ value }) => (typeof value === 'string' ? JSON.parse(value) : value), { toClassOnly: true })
  petDetailsJson?: PetDetail[];

  @IsOptional()
  @IsEnum(['question', 'tourRequest', 'applicationRequest'])
  leadType?: 'question' | 'tourRequest' | 'applicationRequest';
}

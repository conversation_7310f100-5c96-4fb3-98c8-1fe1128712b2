import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString, IsNumber, IsBoolean } from 'class-validator';
import { EmploymentStatus } from '../../../renter/renter/enums/employment-status.enum';
import { PropertyInquirySource } from '../../property-inquiry/property-inquiry-source.enum';

export class LeadDto {
  @IsString()
  @IsNotEmpty()
  propertyId: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  message?: string;

  @IsOptional()
  @IsEnum(EmploymentStatus)
  employmentStatus?: EmploymentStatus;

  @IsOptional()
  @IsString()
  jobTitle?: string;

  @IsOptional()
  @IsNumber()
  monthlyIncome?: number;

  @IsOptional()
  @IsString()
  creditScore?: string;

  @IsOptional()
  @IsBoolean()
  hasPets?: boolean;

  @IsOptional()
  @IsString()
  petDescription?: string;

  @IsOptional()
  @IsString()
  petType?: string;

  @IsEnum(PropertyInquirySource)
  source: PropertyInquirySource;
}

import { IsEmail, IsEnum, IsNotEmpty, IsO<PERSON>al, IsPhoneNumber, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LeadAction } from './lead-action.enum';

export class ManualLeadDto {
  @ApiProperty({ enum: LeadAction, default: LeadAction.SCHEDULE_SHOWING })
  @IsEnum(LeadAction)
  @IsOptional()
  action: LeadAction = LeadAction.SCHEDULE_SHOWING;
  @ApiProperty({ nullable: false })
  @IsString()
  @IsNotEmpty()
  propertyId: string;

  @ApiProperty({ nullable: false })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ nullable: false })
  @IsString()
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsOptional()
  @IsPhoneNumber('US')
  @ApiPropertyOptional({ example: '8143148427' })
  phone: string;
}

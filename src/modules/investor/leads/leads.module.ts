import { Module, forwardRef } from '@nestjs/common';
import { LeadsController } from './leads.controller';
import { PropertyInquiryModule } from '../property-inquiry/property-inquiry.module';
import { PropertyModule } from '../property/property/property.module';
import { FollowUpModule } from 'src/modules/shared/communication/follow-up/follow-up.module';
import { LeadsService } from './leads.service';
import { Property } from '../property/property/entities/property.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RenterModule } from '../../renter/renter/renter.module';
import { RenterCommunicationModule } from '../../renter/renter-communication/renter-communication.module';
import { ConversationModule } from '../../shared/communication/conversation/conversetion.module';
import { SlackCommunicationModule } from '../../shared/communication/outbound-communication/slack/slack-communication.module';
import { InvestorApplicationModule } from '../application/investor-application.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Property]),
    RenterModule,
    RenterCommunicationModule,
    PropertyModule,
    PropertyInquiryModule,
    FollowUpModule,
    ConversationModule,
    SlackCommunicationModule,
    forwardRef(() => InvestorApplicationModule),
  ],
  controllers: [LeadsController],
  providers: [LeadsService],
  exports: [LeadsService],
})
export class LeadsModule {}

import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';

import { IsPropertyOwnerGuard } from '../../../guards/investor/is-property-owner-guard';
import { ManualLeadDto } from './models/manual-lead.dto';
import { LeadsService } from './leads.service';

@Controller('leads')
@ApiTags('leads')
@ApiBearerAuth()
export class LeadsController {
  constructor(private readonly leadsService: LeadsService) {}

  @Post()
  @ApiResponse({
    status: 201,
    description: 'Add new lead',
  })
  @UseGuards(IsPropertyOwnerGuard)
  async add(@Body() payload: ManualLeadDto): Promise<void> {
    await this.leadsService.add(payload);
  }

  @Post('silent')
  @ApiResponse({
    status: 201,
    description: 'Create user and inquiry without sending messages',
    schema: {
      type: 'object',
      properties: {
        inquiryId: {
          type: 'string',
          description: 'The ID of the created inquiry',
        },
      },
    },
  })
  @UseGuards(IsPropertyOwnerGuard)
  async addSilent(@Body() payload: ManualLeadDto): Promise<{ inquiryId: string }> {
    const inquiryId = await this.leadsService.addSilent(payload);
    return { inquiryId };
  }
}

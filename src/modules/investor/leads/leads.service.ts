import { BadRequestException, Injectable, Inject, forwardRef } from '@nestjs/common';
import { FollowUpService } from '../../shared/communication/follow-up/follow-up.service';
import { PropertyService } from '../property/property/property.service';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import parsingUtils from '../../../utils/parsing.utils';
import { FollowUpTypeEnum } from '../../shared/communication/follow-up/enums/follow-up-type.enum';
import { CommunicationChannel } from '../../shared/communication/conversation/enums/preferred-communication-channel.enum';
import { ManualLeadDto } from './models/manual-lead.dto';
import { LeadAction } from './models/lead-action.enum';
import { InvestorApplicationService } from '../application/investor-application.service';
import { PropertyInquiry } from '../property-inquiry/property-inquiry.entity';
import { PropertyInquirySource } from '../property-inquiry/property-inquiry-source.enum';
import { RenterService } from '../../renter/renter/renter.service';
import { RenterCommunicationService } from '../../renter/renter-communication/renter-communication.service';
import { ConversationService } from '../../shared/communication/conversation/conversation.service';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../shared/communication/outbound-communication/slack/slack-convo-message-builder.class';
import { Conversation } from '../../shared/communication/conversation/entities/conversation.entity';
import { LeadDto } from './models/lead.dto';

@Injectable()
export class LeadsService {
  constructor(
    private readonly renterService: RenterService,
    private readonly renterCommunicationService: RenterCommunicationService,
    private readonly followUpService: FollowUpService,
    private readonly propertyService: PropertyService,
    private readonly conversationService: ConversationService,
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly slackCommunicationService: SlackCommunicationService,
    @Inject(forwardRef(() => InvestorApplicationService))
    private readonly investorApplicationService: InvestorApplicationService,
  ) {}

  async add(lead: ManualLeadDto): Promise<void> {
    const leadPhone = parsingUtils.formatPhoneNumber(lead.phone);
    const property = await this.propertyService.findById(lead.propertyId, false);
    let renter = await this.renterService.findByEmailOrPhoneAndAddRenterRoleIfNeeded(lead.email, leadPhone);

    if (!renter) {
      renter = await this.renterService.create(lead.name, lead.email, leadPhone, CommunicationChannel.EMAIL, {});
    }

    const existingInquiry = await this.propertyInquiryService.findByRenterAndProperty(
      renter.id,
      property.id,
      true,
      true,
    );

    let inquiry: PropertyInquiry;

    if (existingInquiry && !(await this.isInquiryExpired(existingInquiry))) {
      if (lead.action === LeadAction.SEND_APPLICATION) {
        inquiry = existingInquiry;
      } else {
        throw new BadRequestException('Renter already inquired about this property');
      }
    } else {
      inquiry = await this.propertyInquiryService.createNewInquiry(
        property,
        renter,
        null,
        PropertyInquirySource.MANUALLY_ADDED,
      );
    }

    if (lead.action === LeadAction.SEND_APPLICATION) {
      const propertyOwner = await property.owner;
      const ownerId = (await propertyOwner.user).id;

      await this.investorApplicationService.sendApplicationToRenter(inquiry.id, ownerId, true);

      return;
    }

    const conversation = await inquiry.conversation;
    await this.welcomeRenter(inquiry);

    const messages = await conversation.messages;
    const aiMessage = messages.length > 0 ? messages[0].content : '';

    await this.sendManualLeadSlackNotification(conversation, aiMessage);
  }

  async addSilent(lead: ManualLeadDto): Promise<string> {
    const leadPhone = parsingUtils.formatPhoneNumber(lead.phone);
    const property = await this.propertyService.findById(lead.propertyId, false);
    let renter = await this.renterService.findByEmailOrPhoneAndAddRenterRoleIfNeeded(lead.email, leadPhone);

    if (!renter) {
      renter = await this.renterService.create(lead.name, lead.email, leadPhone, CommunicationChannel.EMAIL, {});
    }

    const existingInquiry = await this.propertyInquiryService.findByRenterAndProperty(
      renter.id,
      property.id,
      true,
      true,
    );

    if (existingInquiry && !(await this.isInquiryExpired(existingInquiry))) {
      throw new BadRequestException('Renter already inquired about this property');
    }

    const inquiry = await this.propertyInquiryService.createNewInquiry(
      property,
      renter,
      null,
      PropertyInquirySource.MANUALLY_ADDED,
    );

    return inquiry.id;
  }

  async addLead(lead: LeadDto): Promise<void> {
    const property = await this.propertyService.findById(lead.propertyId, false);

    let renter = await this.renterService.findByEmailOrPhoneAndAddRenterRoleIfNeeded(lead.email, lead.phone);

    if (!renter) {
      renter = await this.renterService.create(lead.name, lead.email, lead.phone, CommunicationChannel.EMAIL, {
        employmentStatus: lead.employmentStatus,
        occupation: lead.jobTitle,
        monthlyIncome: lead.monthlyIncome,
        creditScore: lead.creditScore,
        hasPets: lead.hasPets,
        petDescription: lead.petDescription,
        petType: lead.petType,
      });
    }

    let inquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, property.id, true, true);

    if (!inquiry || (await this.isInquiryExpired(inquiry))) {
      inquiry = await this.propertyInquiryService.createNewInquiry(property, renter, lead.message, lead.source);
    }

    if (lead.message) {
      await this.conversationService.saveRenterMessages(
        await inquiry.conversation,
        renter,
        [lead.message],
        true,
        CommunicationChannel.EMAIL,
      );

      await this.renterCommunicationService.answerToRenter(
        [lead.message],
        inquiry,
        renter.user.preferredCommunicationChannel,
      );
    } else {
      await this.renterCommunicationService.welcomeRenter(
        await inquiry.renter,
        await inquiry.property,
        await inquiry.conversation,
        inquiry.source,
      );

      await this.followUpService.createFirstFollowUpsForRenter(
        await inquiry.conversation,
        inquiry,
        FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      );
    }
  }

  private async welcomeRenter(inquiry: PropertyInquiry): Promise<void> {
    await this.renterCommunicationService.welcomeRenter(
      await inquiry.renter,
      await inquiry.property,
      await inquiry.conversation,
      inquiry.source,
    );
    await this.followUpService.createFirstFollowUpsForRenter(
      await inquiry.conversation,
      inquiry,
      FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
    );
  }

  private async isInquiryExpired(inquiry: PropertyInquiry): Promise<boolean> {
    const property = await inquiry.property;
    const thirtyDaysAgo = new Date(new Date().setDate(new Date().getDate() - 30));
    const lastUpdatedMoreThan30DaysAgo = inquiry.updatedAt < thirtyDaysAgo;
    const createdBeforeLastListed = property.lastListedAt > inquiry.createdAt;

    // Inquiry is expired if it was last updated more than 30 days ago AND property was listed after inquiry creation
    return lastUpdatedMoreThan30DaysAgo && createdBeforeLastListed;
  }

  private async sendManualLeadSlackNotification(conversation: Conversation, aiMessage: string): Promise<void> {
    const slackMessageBuilder = new SlackConvoMessageBuilder()
      .appendTextLine('🤩 This lead was manually added by the property owner')
      .appendEmptyLine();

    if (aiMessage) {
      slackMessageBuilder.appendTalloMessage(aiMessage);
    }

    await this.slackCommunicationService.sendMessageToConvosChannel(slackMessageBuilder.build(), conversation);
  }
}

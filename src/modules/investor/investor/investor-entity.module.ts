import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InvestorService } from './investor.service';
import { Investor } from './investor.entity';
import { InvestorController } from './investor.controller';
import { UserModule } from '../../shared/user/user.module';
import { InvestorOnboarding } from '../onboarding/entities/investor-onboarding.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Investor, InvestorOnboarding]), UserModule],
  controllers: [InvestorController],
  providers: [InvestorService],
  exports: [InvestorService],
})
export class InvestorEntityModule {}

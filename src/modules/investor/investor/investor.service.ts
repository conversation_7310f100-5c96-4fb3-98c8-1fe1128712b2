import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Investor } from './investor.entity';
import { Repository } from 'typeorm';
import { UpdateAddressDto } from './models/update-address.dto';
import { UpdateInvestorUserDto } from './models/update-investor-user.dto';
import PropertyUtils from '../property/utils/property-utils';
import { UserService } from '../../shared/user/user.service';
import { InvestorOnboarding } from '../onboarding/entities/investor-onboarding.entity';

@Injectable()
export class InvestorService {
  constructor(
    @InjectRepository(Investor)
    private readonly investorRepository: Repository<Investor>,
    @Inject(UserService) private readonly userService: UserService,
    @InjectRepository(InvestorOnboarding)
    private readonly onboardingRepository: Repository<InvestorOnboarding>,
  ) {}

  async create(data: Partial<Investor>): Promise<Investor> {
    const investor = this.investorRepository.create();
    Object.assign(investor, data);

    const savedInvestor = await this.investorRepository.save(investor);

    await this.onboardingRepository.save(this.onboardingRepository.create({ investor: savedInvestor }));

    return savedInvestor;
  }

  async updateAddress(userId: string, data: UpdateAddressDto): Promise<Investor> {
    const investor = await this.findByUserId(userId);
    Object.assign(investor, data);
    investor.zip = +PropertyUtils.transformZip(data.zip.toString());

    return this.investorRepository.save(investor);
  }

  async countInvestorsByCompany(companyId: string): Promise<number> {
    return this.investorRepository.count({ where: { company: { id: companyId } } });
  }

  async update(investor: Investor, data: Partial<Investor>): Promise<Investor> {
    Object.assign(investor, data);

    return this.investorRepository.save(investor);
  }

  async findByUserId(userId: string): Promise<Investor> {
    return this.investorRepository.findOneOrFail({ where: { user: { id: userId } } });
  }

  async updateUser(userId: string, dto: UpdateInvestorUserDto): Promise<Investor> {
    await this.userService.updateUser(userId, {
      name: dto.name,
      email: dto.email,
      phoneNumber: dto.phoneNumber,
      preferredCommunicationChannel: dto.preferredCommunicationChannel,
    });

    return this.findByUserId(userId);
  }
}

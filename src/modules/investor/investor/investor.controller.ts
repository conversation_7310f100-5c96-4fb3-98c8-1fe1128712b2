import { Body, Controller, Get, Patch, Put, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { Role } from '../../shared/auth/models/roles-enum';
import { InvestorService } from './investor.service';
import { UpdateAddressDto } from './models/update-address.dto';
import { UpdateInvestorUserDto } from './models/update-investor-user.dto';
import { InvestorDto } from '../application/models/investor.dto';
import { Investor } from './investor.entity';

@ApiTags('investor')
@Controller('investor')
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
export class InvestorController {
  constructor(private readonly investorService: InvestorService) {}

  @Get('me')
  @ApiOkResponse({ description: 'Returns an investor by ID' })
  public async getMyProfile(@Req() req: Request): Promise<InvestorDto> {
    const investor = await this.investorService.findByUserId(req.user.id);

    return Investor.convertToDto(investor);
  }

  @Put(':address')
  @ApiOkResponse({
    description: 'Update investor address',
  })
  async updateAddress(@Req() req: Request, @Body() dto: UpdateAddressDto): Promise<InvestorDto> {
    const investor = await this.investorService.updateAddress(req.user.id, dto);

    return Investor.convertToDto(investor);
  }

  @Patch('user')
  @ApiOkResponse({
    description: 'Update investor user information',
  })
  async update(@Req() req: Request, @Body() dto: UpdateInvestorUserDto): Promise<InvestorDto> {
    const investor = await this.investorService.updateUser(req.user.id, dto);
    return Investor.convertToDto(investor);
  }
}

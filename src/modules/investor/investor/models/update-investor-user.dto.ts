import { IsEmail, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Is<PERSON><PERSON>, ValidateIf } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsUSPhoneNumber } from '../../../../utils/validators/us-phone-number.validator';
import { CommunicationChannel } from '../../../shared/communication/conversation/enums/preferred-communication-channel.enum';

export class UpdateInvestorUserDto {
  @ApiPropertyOptional({ example: '<PERSON> Do<PERSON>' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ example: '<EMAIL>' })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({ example: '18143148427', description: 'Can be set to null to remove phone number' })
  @ValidateIf((o) => o.phoneNumber)
  @IsUSPhoneNumber()
  @IsOptional()
  phoneNumber?: string | null;

  @ApiPropertyOptional({
    enum: CommunicationChannel,
    example: CommunicationChannel.SMS,
    description: 'Preferred communication channel for the user',
  })
  @IsEnum(CommunicationChannel)
  @IsOptional()
  preferredCommunicationChannel?: CommunicationChannel;
}

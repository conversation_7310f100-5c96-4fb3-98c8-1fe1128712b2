import { IsNumber, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateAddressDto {
  @IsString()
  @ApiProperty()
  address: string;

  @IsString()
  city: string;

  @IsString()
  @ApiProperty()
  state: string;

  @IsNumber()
  @ApiProperty()
  zip: number;

  @IsNumber()
  @ApiPropertyOptional()
  latitude?: number;

  @IsNumber()
  @ApiPropertyOptional()
  longitude?: number;
}

import { Expose, instanceTo<PERSON>lain } from 'class-transformer';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Company } from '../../shared/company/entities/company.entity';
import { User } from '../../shared/user/entities/user.entity';
import { InvestorDto } from '../application/models/investor.dto';
import { convertUserToDto } from '../../shared/user/model/user.dto';
import { ScreeningSensitivity } from '../property/property/property-details/renter-requirements/screening-sensitivity.enum';
import { InvestorOnboarding } from '../onboarding/entities/investor-onboarding.entity';

@Entity()
export class Investor {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Company, { lazy: true, nullable: false })
  company: Promise<Company> | Company;

  @OneToOne(() => User, {
    nullable: false,
    lazy: true,
  })
  @Expose()
  @JoinColumn()
  user: Promise<User> | User;

  @Expose()
  @Column({ type: 'varchar', length: 20, nullable: true })
  state: string;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: true })
  city: string;

  @Expose()
  @Index()
  @Column({ type: 'varchar', length: 100, nullable: true })
  address: string;

  @Expose()
  @Column({ type: 'int', nullable: true })
  zip: number;

  @Expose()
  @Column({ type: 'float', nullable: true })
  latitude: number;

  @Expose()
  @Column({ type: 'float', nullable: true })
  longitude: number;

  @Column({ type: 'varchar', length: 120, nullable: true })
  transUnionLandlordId: string;

  @OneToOne(() => InvestorOnboarding, (investorOnboarding) => investorOnboarding.investor, {
    lazy: true,
  })
  investorOnboarding: Promise<InvestorOnboarding> | InvestorOnboarding;

  @Expose()
  @Column({
    type: 'enum',
    enum: ScreeningSensitivity,
    default: ScreeningSensitivity.MODERATE,
  })
  preferredScreeningSensitivity: ScreeningSensitivity;

  static async convertToDto(investor: Investor): Promise<InvestorDto> {
    const investorDto = <InvestorDto>instanceToPlain(investor, { strategy: 'excludeAll' });
    investorDto.user = await convertUserToDto(await investor.user);

    investorDto.onboarding = <InvestorOnboarding>(
      instanceToPlain(await investor.investorOnboarding, { strategy: 'excludeAll' })
    );

    return investorDto;
  }
}

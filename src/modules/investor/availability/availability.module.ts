import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AvailabilitySlot } from './availability-slot.entity';
import { AvailabilitySlotsService } from './availability-slot.service';
import { AvailabilitySlotController } from './availability-slot.controller';
import { UserModule } from '../../shared/user/user.module';
import { InvestorAvailabilityService } from './investor-availability.service';
import { InvestorAvailabilityController } from './investor-availability.controller';
import { InvestorAvailability } from './investor-availability.entity';
import { IsAvailabilitySlotOwnerGuard } from '../../../guards/investor/is-availability-slot-owner.guard';
import { PropertyAvailabilityService } from '../property/availability/property-availability.service';
import { PropertyAvailability } from '../property/availability/property-availability.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AvailabilitySlot, InvestorAvailability, PropertyAvailability]), UserModule],
  providers: [
    AvailabilitySlotsService,
    InvestorAvailabilityService,
    PropertyAvailabilityService,
    IsAvailabilitySlotOwnerGuard,
  ],
  controllers: [AvailabilitySlotController, InvestorAvailabilityController],
  exports: [AvailabilitySlotsService, InvestorAvailabilityService],
})
export class AvailabilityModule {}

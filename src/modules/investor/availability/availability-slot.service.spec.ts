import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DayOfTheWeekEnum } from '../../../shared/enums/day-of-the-week.enum';
import { UserService } from '../../shared/user/user.service';
import { AvailabilitySlot } from './availability-slot.entity';
import { AvailabilitySlotsService } from './availability-slot.service';

import { PropertyAvailabilityService } from '../property/availability/property-availability.service';
import { InvestorAvailabilityService } from './investor-availability.service';

describe('AvailabilitySlotsService', () => {
  let service: AvailabilitySlotsService;
  let mockAvailabilityRepository: Partial<Record<keyof Repository<AvailabilitySlot>, jest.Mock>>;
  let mockUserService: Partial<UserService>;

  let mockPropertyAvailabilityService: Partial<PropertyAvailabilityService>;
  let mockInvestorAvailabilityService: Partial<InvestorAvailabilityService>;
  let availabilitySlots: AvailabilitySlot[];
  const currentDateUtcInCity = new Date('2023-03-03T10:00:00.000Z');

  beforeEach(async () => {
    mockAvailabilityRepository = {
      findBy: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };
    mockUserService = {
      findById: jest.fn(),
    };

    mockPropertyAvailabilityService = {
      findById: jest.fn(),
    };

    mockInvestorAvailabilityService = {
      findById: jest.fn(),
      findByInvestor: jest.fn(),
      findByUser: jest.fn(),
    };

    availabilitySlots = [
      createAvailability(DayOfTheWeekEnum.TUESDAY, '11:00:00+00', '18:00:00+00'), // Same day, later time
      createAvailability(DayOfTheWeekEnum.WEDNESDAY, '09:00:00+00', '16:00:00+00'), // Next day
    ];

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AvailabilitySlotsService,
        {
          provide: getRepositoryToken(AvailabilitySlot),
          useValue: mockAvailabilityRepository,
        },
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: InvestorAvailabilityService,
          useValue: mockInvestorAvailabilityService,
        },
        {
          provide: PropertyAvailabilityService,
          useValue: mockPropertyAvailabilityService,
        },
      ],
    }).compile();

    service = module.get<AvailabilitySlotsService>(AvailabilitySlotsService);
  });

  it('should find an availability slot by id', async () => {
    const id = 'availability-slot-id';
    const expectedAvailability = new AvailabilitySlot();
    mockAvailabilityRepository.findOne.mockResolvedValue(expectedAvailability);

    const result = await service.findById(id);

    expect(result).toBe(expectedAvailability);
    expect(mockAvailabilityRepository.findOne).toHaveBeenCalledWith({
      where: { id },
      relations: [],
    });
  });

  describe('findClosestAvailableSlot', () => {
    it('should find the closest availability slot on the same day', async () => {
      const startDate = new Date('2023-04-04T10:00:00Z'); // Tuesday at 10:00 AM
      mockAvailabilityRepository.findBy.mockResolvedValue(availabilitySlots);

      const closestAvailability = await service.findClosestAvailableSlot(startDate, availabilitySlots);

      expect(closestAvailability).toBeDefined();
      expect(closestAvailability.weekday).toBe(DayOfTheWeekEnum.TUESDAY);
      expect(closestAvailability.startTime).toBe('11:00:00+00');
    });

    it('should find the closest availability slot on the next day', async () => {
      const startDate = new Date('2023-04-04T20:00:00Z'); // Tuesday at 3:00 PM
      mockAvailabilityRepository.findBy.mockResolvedValue(availabilitySlots);

      const closestAvailability = await service.findClosestAvailableSlot(startDate, availabilitySlots);

      expect(closestAvailability).toBeDefined();
      expect(closestAvailability?.weekday).toBe(DayOfTheWeekEnum.WEDNESDAY);
      expect(closestAvailability?.startTime).toBe('09:00:00+00');
    });

    it('should find the closest availability slot in the next week', async () => {
      const startDate = new Date('2023-04-04T16:00:00Z'); // Tuesday at 4:00 PM
      mockAvailabilityRepository.findBy.mockResolvedValue(availabilitySlots);

      const closestAvailability = await service.findClosestAvailableSlot(startDate, availabilitySlots);

      expect(closestAvailability).toBeDefined();
      expect(closestAvailability?.weekday).toBe(DayOfTheWeekEnum.TUESDAY);
      expect(closestAvailability?.startTime).toBe('11:00:00+00');
    });
  });

  describe('findAvailabilitiesInRange', () => {
    it('should find all availability slots within a given date and time range', async () => {
      const startTime = new Date('2023-12-10T09:00:00.000Z');
      const endTime = new Date('2023-12-10T17:00:00.000Z');

      const availabilitiesInRange = [
        createAvailability(DayOfTheWeekEnum.SUNDAY, '10:00:00+00', '13:00:00+00'), // Within range
        createAvailability(DayOfTheWeekEnum.SUNDAY, '18:00:00+00', '20:00:00+00'), // Outside range
        createAvailability(DayOfTheWeekEnum.WEDNESDAY, '18:00:00+00', '19:00:00+00'), // Outside range
      ];

      mockAvailabilityRepository.findBy.mockResolvedValue(availabilitiesInRange);

      const foundAvailabilities = await service.findAvailabilitySlotsInRange(
        startTime,
        endTime,
        availabilitiesInRange,
        currentDateUtcInCity,
      );

      expect(foundAvailabilities).toHaveLength(1);
      expect(foundAvailabilities[0].startTime.toString()).toBe(new Date('2023-12-10T10:00:00.000Z').toString());
    });

    it('should return an empty array if no availability slots match the range', async () => {
      const startTime = new Date('2023-12-11T09:00:00.000Z');
      const endTime = new Date('2023-12-11T17:00:00.000Z');

      mockAvailabilityRepository.findBy.mockResolvedValue(availabilitySlots);

      const foundAvailabilities = await service.findAvailabilitySlotsInRange(
        startTime,
        endTime,
        availabilitySlots,
        currentDateUtcInCity,
      );

      expect(foundAvailabilities).toHaveLength(0);
    });
  });

  it('should find availability slot that partially overlaps with start time', async () => {
    const startTime = new Date('2023-04-04T10:00:00.000Z'); // Tuesday at 10:00 AM UTC
    const endTime = new Date('2023-04-04T12:00:00.000Z'); // Tuesday at 12:00 PM UTC

    mockAvailabilityRepository.findBy.mockResolvedValue(availabilitySlots);

    const foundAvailabilities = await service.findAvailabilitySlotsInRange(
      startTime,
      endTime,
      availabilitySlots,
      currentDateUtcInCity,
    );

    expect(foundAvailabilities).toHaveLength(1);
    expect(foundAvailabilities[0].startTime.toString()).toBe(new Date('2023-04-04T11:00:00.000Z').toString());
    expect(foundAvailabilities[0].endTime.toString()).toBe(new Date('2023-04-04T18:00:00.000Z').toString());
  });

  it('should find availability slot that overlaps with the start of the availability slot on the same day', async () => {
    // Renter's timeframe overlaps with the start of Tuesday's availability
    const startTime = new Date('2023-04-04T10:30:00.000Z'); // Tuesday at 10:30 AM UTC
    const endTime = new Date('2023-04-04T13:00:00.000Z'); // Tuesday at 1:00 PM UTC

    mockAvailabilityRepository.findBy.mockResolvedValue(availabilitySlots);

    const foundAvailabilities = await service.findAvailabilitySlotsInRange(
      startTime,
      endTime,
      availabilitySlots,
      currentDateUtcInCity,
    );

    expect(foundAvailabilities).toHaveLength(1);
    expect(foundAvailabilities[0].startTime.toString()).toBe(new Date('2023-04-04T11:00:00.000Z').toString());
    expect(foundAvailabilities[0].endTime.toString()).toBe(new Date('2023-04-04T18:00:00.000Z').toString());
  });

  it("should find availability slot when renter's timeframe is entirely during the availability slot on the same day", async () => {
    // Renter's timeframe is entirely during Tuesday's availability
    const startTime = new Date('2023-04-04T12:00:00.000Z'); // Tuesday at 12:00 PM UTC
    const endTime = new Date('2023-04-04T14:00:00.000Z'); // Tuesday at 2:00 PM UTC

    mockAvailabilityRepository.findBy.mockResolvedValue(availabilitySlots);

    const foundAvailabilities = await service.findAvailabilitySlotsInRange(
      startTime,
      endTime,
      availabilitySlots,
      currentDateUtcInCity,
    );

    expect(foundAvailabilities).toHaveLength(1);
    expect(foundAvailabilities[0].startTime.toString()).toBe(new Date('2023-04-04T11:00:00.000Z').toString());
    expect(foundAvailabilities[0].endTime.toString()).toBe(new Date('2023-04-04T18:00:00.000Z').toString());
  });

  it("should find availability slot when renter's timeframe overlaps with the end of the availability slot on the same day", async () => {
    // Renter's timeframe overlaps with the end of Tuesday's availability
    const startTime = new Date('2023-04-04T16:00:00.000Z'); // Tuesday at 4:00 PM UTC
    const endTime = new Date('2023-04-04T19:00:00.000Z'); // Tuesday at 7:00 PM UTC

    mockAvailabilityRepository.findBy.mockResolvedValue(availabilitySlots);

    const foundAvailabilities = await service.findAvailabilitySlotsInRange(
      startTime,
      endTime,
      availabilitySlots,
      currentDateUtcInCity,
    );

    expect(foundAvailabilities).toHaveLength(1);
    expect(foundAvailabilities[0].startTime.toString()).toBe(new Date('2023-04-04T11:00:00.000Z').toString());
    expect(foundAvailabilities[0].endTime.toString()).toBe(new Date('2023-04-04T18:00:00.000Z').toString());
  });

  function createAvailability(weekday: DayOfTheWeekEnum, startTime: string, endTime: string): AvailabilitySlot {
    const availabilitySlot = new AvailabilitySlot();
    availabilitySlot.weekday = weekday;
    availabilitySlot.startTime = startTime;
    availabilitySlot.endTime = endTime;
    return availabilitySlot;
  }
});

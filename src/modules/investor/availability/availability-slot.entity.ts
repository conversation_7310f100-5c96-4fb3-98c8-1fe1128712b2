import { Expose, instanceToPlain } from 'class-transformer';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { DayOfTheWeekEnum } from '../../../shared/enums/day-of-the-week.enum';
import { InvestorAvailability } from './investor-availability.entity';
import { AvailabilitySlotDto } from './models/availability-slot.dto';
import { timetzToLocalTime } from '../../../utils/time.utils';
import { PropertyAvailability } from '../property/availability/property-availability.entity';

@Entity()
export class AvailabilitySlot {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(
    () => InvestorAvailability,
    (investorAvailability: InvestorAvailability) => investorAvailability.availabilitySlots,
    {
      lazy: true,
      nullable: true,
    },
  )
  investorAvailability: Promise<InvestorAvailability> | InvestorAvailability;

  @ManyToOne(
    () => PropertyAvailability,
    (propertyAvailability: PropertyAvailability) => propertyAvailability.availabilitySlots,
    {
      lazy: true,
      nullable: true,
    },
  )
  propertyAvailability: Promise<PropertyAvailability> | PropertyAvailability;

  @Expose()
  @Column({
    type: 'enum',
    enum: DayOfTheWeekEnum,
  })
  weekday: DayOfTheWeekEnum;

  @Expose()
  @Column({ type: 'timetz' }) // represents a time with the timezone information
  startTime: string;

  @Expose()
  @Column({ type: 'timetz' }) // represents a time with the timezone information
  endTime: string;

  public static convertToDto(availabilitySlot: AvailabilitySlot, userTimeZone: string): AvailabilitySlotDto {
    if (!availabilitySlot) {
      return null;
    }

    const availabilitySlotDto = <AvailabilitySlotDto>(
      instanceToPlain(availabilitySlot, { excludeExtraneousValues: true })
    );
    availabilitySlotDto.startTime = timetzToLocalTime(availabilitySlot.startTime, userTimeZone);
    availabilitySlotDto.endTime = timetzToLocalTime(availabilitySlot.endTime, userTimeZone);

    return availabilitySlotDto;
  }
}

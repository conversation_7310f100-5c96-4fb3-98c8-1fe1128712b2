import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InvestorAvailability } from './investor-availability.entity';

@Injectable()
export class InvestorAvailabilityService {
  constructor(
    @InjectRepository(InvestorAvailability)
    private readonly investorAvailabilityRepository: Repository<InvestorAvailability>,
  ) {}

  async findById(investorAvailabilityId: string): Promise<InvestorAvailability> {
    return this.investorAvailabilityRepository.findOneOrFail({
      where: { id: investorAvailabilityId },
    });
  }

  async findByInvestor(investorId: string): Promise<InvestorAvailability> {
    return this.investorAvailabilityRepository.findOneOrFail({
      where: {
        investor: {
          id: investorId,
        },
      },
      relations: ['availabilitySlots'],
    });
  }

  async findByUser(userId: string): Promise<InvestorAvailability> {
    return this.investorAvailabilityRepository.findOneOrFail({
      where: {
        investor: {
          user: { id: userId },
        },
      },
      relations: ['availabilitySlots'],
    });
  }

  async create(data: Partial<InvestorAvailability>): Promise<InvestorAvailability> {
    const investorAvailability = this.investorAvailabilityRepository.create();

    Object.assign(investorAvailability, data);

    return this.investorAvailabilityRepository.save(investorAvailability);
  }

  async updateShowingDuration(investorAvailabilityId: string, durationInMinutes: number): Promise<void> {
    await this.investorAvailabilityRepository.update(investorAvailabilityId, {
      showingDurationInMinutes: durationInMinutes,
    });
  }

  async findAll(): Promise<InvestorAvailability[]> {
    return this.investorAvailabilityRepository.find({
      relations: ['investor', 'availabilitySlots'],
    });
  }
}

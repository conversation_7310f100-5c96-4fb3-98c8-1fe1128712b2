import { Expose, instanceToPlain } from 'class-transformer';
import { Colum<PERSON>, <PERSON>tity, Join<PERSON><PERSON>um<PERSON>, OneToMany, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Investor } from '../investor/investor.entity';
import { AvailabilitySlot } from './availability-slot.entity';
import { InvestorAvailabilityDto } from './models/investor-availability.dto';

@Entity()
export class InvestorAvailability {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Expose()
  @Column({ type: 'int', nullable: false, default: 15 })
  showingDurationInMinutes: number;

  @Expose()
  @OneToMany(() => AvailabilitySlot, (availability) => availability.investorAvailability, {
    lazy: true,
  })
  availabilitySlots: Promise<AvailabilitySlot[]> | AvailabilitySlot[];

  @OneToOne(() => Investor, {
    nullable: false,
    lazy: true,
  })
  @JoinColumn()
  investor: Promise<Investor> | Investor;

  public static async convertToDto(
    investorAvailability: InvestorAvailability,
    userTimeZone: string,
  ): Promise<InvestorAvailabilityDto> {
    if (!investorAvailability) {
      return null;
    }

    const investorAvailabilityDto = <InvestorAvailabilityDto>(
      instanceToPlain(investorAvailability, { excludeExtraneousValues: true })
    );

    const availabilitySlots = await investorAvailability.availabilitySlots;

    investorAvailabilityDto.availabilitySlots = await Promise.all(
      availabilitySlots.map(async (availability) => AvailabilitySlot.convertToDto(availability, userTimeZone)),
    );

    return investorAvailabilityDto;
  }
}

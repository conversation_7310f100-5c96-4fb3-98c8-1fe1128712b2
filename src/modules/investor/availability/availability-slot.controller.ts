import { Body, Controller, Delete, Headers, Patch, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiCreatedResponse, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { AppHeaders } from '../../../shared/const/app-headers.const';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { Role } from '../../shared/auth/models/roles-enum';
import { AvailabilitySlot } from './availability-slot.entity';
import { AvailabilitySlotsService } from './availability-slot.service';
import { AvailabilitySlotDto, CreateAvailabilitySlotDto } from './models/availability-slot.dto';
import { Request } from 'express';
import { IsAvailabilitySlotOwnerGuard } from '../../../guards/investor/is-availability-slot-owner.guard';
import { IsPropertyAvailabilityOwnerGuard } from '../../../guards/investor/is-property-availability-owner-guard.service';

@ApiTags('availability-slot')
@Controller('availability-slot')
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
export class AvailabilitySlotController {
  constructor(private readonly availabilityService: AvailabilitySlotsService) {}

  @Post(':availabilityId')
  @ApiCreatedResponse({
    description: 'Create availability slot',
    type: AvailabilitySlotDto,
  })
  @ApiBody({ type: CreateAvailabilitySlotDto })
  @UseGuards(IsPropertyAvailabilityOwnerGuard)
  async createAvailabilitySlot(
    @Req() req: Request,
    @Body() createAvailabilitySlotDto: CreateAvailabilitySlotDto,
    @Headers(AppHeaders.ClienTimeZone) userTimeZone: string,
  ): Promise<AvailabilitySlotDto> {
    const availabilitySlot = await this.availabilityService.create(
      createAvailabilitySlotDto,
      userTimeZone,
      req.params.availabilityId,
    );

    return AvailabilitySlot.convertToDto(availabilitySlot, userTimeZone);
  }

  @Patch()
  @ApiOkResponse({
    description: 'Update availability slot',
    type: AvailabilitySlotDto,
  })
  @UseGuards(IsAvailabilitySlotOwnerGuard)
  async updateAvailabilitySlot(
    @Body() updateAvailabilitySlotDto: AvailabilitySlotDto,
    @Headers(AppHeaders.ClienTimeZone) userTimeZone: string,
  ): Promise<AvailabilitySlotDto> {
    const availabilitySlot = await this.availabilityService.update(updateAvailabilitySlotDto, userTimeZone);
    return AvailabilitySlot.convertToDto(availabilitySlot, userTimeZone);
  }

  @Delete()
  @ApiOkResponse({
    description: 'Delete availability slot by ids',
  })
  @UseGuards(IsAvailabilitySlotOwnerGuard)
  @ApiBody({ type: [String], description: 'Array of availability slot IDs to delete' })
  async deleteAvailabilitySlot(@Body() ids: string[]): Promise<void> {
    await this.availabilityService.deleteAll(ids);
  }
}

import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { DateUtils } from '../../../utils/date.utils';
import { localTimeToTimetz, parseTimetz } from '../../../utils/time.utils';
import { AvailabilitySlot } from './availability-slot.entity';
import { AvailabilitySlotDto, CreateAvailabilitySlotDto } from './models/availability-slot.dto';
import { TimeRange } from '../../renter/renter-communication/response-generation/strategies/scheduling-strategies/shared/interfaces/time-range';
import { PropertyAvailabilityService } from '../property/availability/property-availability.service';

@Injectable()
export class AvailabilitySlotsService {
  constructor(
    @InjectRepository(AvailabilitySlot)
    private readonly availabilitySlotRepository: Repository<AvailabilitySlot>,
    private readonly availabilityService: PropertyAvailabilityService,
  ) {}

  async create(
    availabilitySlotDto: CreateAvailabilitySlotDto,
    userTimeZone: string,
    availabilityId: string,
  ): Promise<AvailabilitySlot> {
    const availability = await this.availabilityService.findById(availabilityId);
    const availabilitySlot = new AvailabilitySlot();

    availabilitySlot.weekday = availabilitySlotDto.weekday;
    availabilitySlot.startTime = localTimeToTimetz(availabilitySlotDto.startTime, userTimeZone);
    availabilitySlot.endTime = localTimeToTimetz(availabilitySlotDto.endTime, userTimeZone);
    availabilitySlot.propertyAvailability = availability;

    return await this.availabilitySlotRepository.save(availabilitySlot);
  }

  async findById(id: string, withRelations = false): Promise<AvailabilitySlot> {
    return this.availabilitySlotRepository.findOne({
      where: { id },
      relations: withRelations ? ['investorAvailability'] : [],
    });
  }

  async update(updateAvailabilitySlotDto: AvailabilitySlotDto, userTimeZone: string): Promise<AvailabilitySlot> {
    const existingAvailabilitySlot = await this.findById(updateAvailabilitySlotDto.id);

    if (!existingAvailabilitySlot) {
      throw new BadRequestException('Availability slot not found');
    }

    const startTime = localTimeToTimetz(updateAvailabilitySlotDto.startTime, userTimeZone);
    const endTime = localTimeToTimetz(updateAvailabilitySlotDto.endTime, userTimeZone);

    await this.availabilitySlotRepository.update(existingAvailabilitySlot.id, {
      startTime,
      endTime,
    });

    return <AvailabilitySlot>{
      id: existingAvailabilitySlot.id,
      weekday: existingAvailabilitySlot.weekday,
      startTime,
      endTime,
    };
  }

  async deleteAll(ids: string[]) {
    await this.availabilitySlotRepository.delete({ id: In(ids) });
  }

  async delete(id: string): Promise<void> {
    await this.availabilitySlotRepository.delete(id);
  }

  async findClosestAvailableSlot(
    startDate: Date,
    availabilitySlots: AvailabilitySlot[],
  ): Promise<AvailabilitySlot | null> {
    let closestAvailability: AvailabilitySlot | null = null;
    let smallestDiff = Infinity;

    for (const availabilitySlot of availabilitySlots) {
      // Extract just the time part from the timetz string
      const [timePart] = availabilitySlot.endTime.split(/[+-]/);
      const [hours, minutes, seconds] = timePart.split(':').map(Number);

      // Calculate the next available date
      const availabilityDayIndex = DateUtils.daysListFull.indexOf(availabilitySlot.weekday);
      const startDayIndex = startDate.getDay();
      let dayDiff = availabilityDayIndex - startDayIndex;
      if (dayDiff < 0) dayDiff += 7;

      const nextAvailableDate = new Date(startDate);
      nextAvailableDate.setDate(startDate.getDate() + dayDiff);
      nextAvailableDate.setUTCHours(hours, minutes, seconds, 0);

      // If the time has already passed for today's availability, skip it
      if (dayDiff === 0 && startDate >= nextAvailableDate) {
        continue;
      }

      const diff = nextAvailableDate.getTime() - startDate.getTime();

      // If this availability is sooner than any previously checked, store it
      if (diff < smallestDiff) {
        smallestDiff = diff;
        closestAvailability = availabilitySlot;
      }
    }

    return closestAvailability;
  }

  async findAvailabilitySlotsInRange(
    startTime: Date,
    endTime: Date,
    availabilitySlots: AvailabilitySlot[],
    currentDateUtc: Date,
    hoursFromNow = 24,
  ): Promise<TimeRange[]> {
    const matchedTimeSlotsInUtcWithoutTz: TimeRange[] = [];

    // Generate list of weekdays in range
    const weekdaysWithRelatedDatesInRange = this.getWeekdaysInRange(startTime, endTime);

    for (const availabilitySlot of availabilitySlots) {
      // Check if the slot's weekday is in the generated list
      if (!weekdaysWithRelatedDatesInRange.map((wd) => wd.weekday).includes(availabilitySlot.weekday)) {
        continue; // Skip days not in range
      }

      const weekdayDate = weekdaysWithRelatedDatesInRange.find((wd) => wd.weekday === availabilitySlot.weekday).date;

      const closestPossibleTime = DateUtils.roundDownToNearestHalfHour(
        new Date(currentDateUtc.getTime() + hoursFromNow * 60 * 60 * 1000),
      );

      let availabilityStart = parseTimetz(availabilitySlot.startTime, weekdayDate);
      const availabilityEnd = parseTimetz(availabilitySlot.endTime, weekdayDate);

      // If the start time is less than 24 hours from now and the end time
      // is less than 24 hours from now, skip this slot
      if (availabilityStart < closestPossibleTime && availabilityEnd < closestPossibleTime) {
        continue;
      }

      // If the start time is less than 24 hours from now
      // but the end time is more than 24 hours from now, adjust the start time
      if (availabilityStart < closestPossibleTime && availabilityEnd > closestPossibleTime) {
        availabilityStart = closestPossibleTime;
      }

      // Check for any overlap between the availability and the specified time range
      if (availabilityEnd > startTime && availabilityStart <= endTime) {
        matchedTimeSlotsInUtcWithoutTz.push({
          startTime: availabilityStart,
          endTime: availabilityEnd,
        });
      }
    }

    return matchedTimeSlotsInUtcWithoutTz;
  }

  // Helper method to generate a list of weekdays in the range
  private getWeekdaysInRange(start: Date, end: Date): { date: Date; weekday: string }[] {
    const currentDate = new Date(start.getTime());
    const endDay = new Date(end.getTime());
    const weekdaysWithRelatedDates: {
      date: Date;
      weekday: string;
    }[] = [];

    while (currentDate <= endDay) {
      const weekday = DateUtils.daysListFull[currentDate.getUTCDay()];
      if (weekdaysWithRelatedDates.every(({ weekday: wd }) => wd !== weekday)) {
        weekdaysWithRelatedDates.push({
          date: new Date(currentDate),
          weekday,
        });
      }

      currentDate.setDate(currentDate.getDate() + 1); // Move to the next day
    }

    return weekdaysWithRelatedDates;
  }
}

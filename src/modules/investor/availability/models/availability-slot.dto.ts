import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { DayOfTheWeekEnum } from '../../../../shared/enums/day-of-the-week.enum';

export class AvailabilitySlotDto {
  @IsNotEmpty()
  @ApiProperty()
  id: string;

  @IsEnum(DayOfTheWeekEnum)
  @IsNotEmpty()
  @ApiProperty({
    enum: [DayOfTheWeekEnum.SUNDAY, DayOfTheWeekEnum.MONDAY],
    enumName: 'DayOfTheWeekEnum',
    example: DayOfTheWeekEnum.MONDAY,
  })
  weekday: DayOfTheWeekEnum;

  @IsNotEmpty()
  @ApiProperty({ example: '11:30' })
  startTime: string;

  @IsNotEmpty()
  @ApiProperty({ example: '18:00' })
  endTime: string;
}

export class CreateAvailabilitySlotDto {
  @IsNotEmpty()
  @ApiProperty()
  propertyAvailabilityId: string;

  @IsEnum(DayOfTheWeekEnum)
  @IsNotEmpty()
  @ApiProperty({
    enum: [DayOfTheWeekEnum.SUNDAY, DayOfTheWeekEnum.MONDAY],
    enumName: 'DayOfTheWeekEnum',
    example: DayOfTheWeekEnum.MONDAY,
  })
  weekday: DayOfTheWeekEnum;

  @IsNotEmpty()
  @ApiProperty({ example: '11:30' })
  startTime: string;

  @IsNotEmpty()
  @ApiProperty({ example: '18:00' })
  endTime: string;
}

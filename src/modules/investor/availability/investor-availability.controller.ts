import { Body, Controller, Get, Headers, Param, <PERSON>, Req, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { AppHeaders } from '../../../shared/const/app-headers.const';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { Role } from '../../shared/auth/models/roles-enum';
import { IsPropertyAvailabilityOwnerGuard } from '../../../guards/investor/is-property-availability-owner-guard.service';
import { InvestorAvailability } from './investor-availability.entity';
import { InvestorAvailabilityService } from './investor-availability.service';
import { InvestorAvailabilityDto } from './models/investor-availability.dto';
import { UpdateShowingDuration } from './models/update-showing-duration.dto';

@ApiTags('investor-availability')
@Controller('investor-availability')
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
export class InvestorAvailabilityController {
  constructor(private readonly investorAvailability: InvestorAvailabilityService) {}

  @Get()
  @ApiOkResponse({
    description: 'Get investor availability',
    type: InvestorAvailabilityDto,
  })
  async getAvailability(
    @Req() req: Request,
    @Headers(AppHeaders.ClienTimeZone) userTimeZone: string,
  ): Promise<InvestorAvailabilityDto> {
    const investorAvailability = await this.investorAvailability.findByUser(req.user.id);

    return InvestorAvailability.convertToDto(investorAvailability, userTimeZone);
  }

  @Patch(':investorAvailabilityId/showing-duration')
  @ApiOkResponse({
    description: 'Update showing duration',
  })
  @UseGuards(IsPropertyAvailabilityOwnerGuard)
  async updateShowingDuration(
    @Param('investorAvailabilityId') investorAvailabilityId: string,
    @Body() dto: UpdateShowingDuration,
  ): Promise<void> {
    await this.investorAvailability.updateShowingDuration(investorAvailabilityId, dto.durationInMinutes);
  }
}

import { ApiProperty } from '@nestjs/swagger';
import { RescheduleRequestDto } from '../../reschedule/model/reschedule-request.dto';
import { ShowingRequestStatus } from '../../showing-request/enums/showing-request-status.enum';
import { ShowingRequestDto } from '../../showing-request/showing-request.dto';
import { PropertyDto } from '../../property/property/model/property.dto';
import { ShowingAgentDto } from '../../showing-agent/dto/showing-agent.dto';

export class ShowingDto {
  @ApiProperty()
  id: string;

  @ApiProperty({ type: () => ShowingRequestDto, isArray: true })
  showingRequests?: ShowingRequestDto[];

  @ApiProperty({ type: () => RescheduleRequestDto, isArray: true })
  rescheduleRequests?: RescheduleRequestDto[];

  @ApiProperty({ type: () => Date })
  startTime: Date;

  @ApiProperty({
    enum: [
      ShowingRequestStatus.PENDING,
      ShowingRequestStatus.ACCEPTED,
      ShowingRequestStatus.DECLINED,
      ShowingRequestStatus.RESCHEDULED,
      ShowingRequestStatus.CANCELLED_BY_RENTER,
      ShowingRequestStatus.CANCELED_BY_INVESTOR,
    ],
    enumName: 'ShowingRequestStatus',
    example: ShowingRequestStatus.ACCEPTED,
  })
  status: ShowingRequestStatus;

  @ApiProperty({ type: () => Date })
  endTime: Date;

  @ApiProperty({ type: () => Date })
  createdAt: Date;

  @ApiProperty()
  property: PropertyDto;

  @ApiProperty({ type: () => ShowingAgentDto })
  showingAgent: ShowingAgentDto;
}

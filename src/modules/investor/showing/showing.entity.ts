import { Expose } from 'class-transformer';
import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
} from 'typeorm';
import { Property } from '../property/property/entities/property.entity';
import { RescheduleRequest } from '../reschedule/reschedule-request.entity';
import { ShowingRequest } from '../showing-request/showing-request.entity';
import { ShowingStatus } from './enums/showing-status.enum';
import { ShowingAgent } from '../showing-agent/entities/showing-agent.entity';
import { TourType } from './enums/tour-type.enum';

@Entity()
export class Showing {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Expose()
  @Column({
    type: 'enum',
    enum: TourType,
    default: TourType.IN_PERSON,
    nullable: false,
  })
  tourType: TourType;

  @ManyToOne(() => Property, {
    lazy: true,
    nullable: false,
  })
  property: Promise<Property> | Property;

  @RelationId((showing: Showing) => showing.property)
  propertyId: string;

  @ManyToOne(() => ShowingAgent, {
    lazy: true,
    nullable: true,
  })
  showingAgent: Promise<ShowingAgent> | ShowingAgent;

  @RelationId((showing: Showing) => showing.showingAgent)
  showingAgentId: string;

  @OneToMany(() => ShowingRequest, (showingRequest) => showingRequest.showing, {
    lazy: true,
  })
  showingRequests: Promise<ShowingRequest[]> | ShowingRequest[];

  @OneToMany(() => RescheduleRequest, (rescheduleRequest) => rescheduleRequest.showing, {
    lazy: true,
  })
  rescheduleRequests: Promise<RescheduleRequest[]> | RescheduleRequest[];

  @Expose()
  @Column({
    type: 'enum',
    enum: ShowingStatus,
    default: ShowingStatus.PENDING,
  })
  status: ShowingStatus;

  @Expose()
  @Column({ type: 'timestamp' })
  startTime: Date;

  @Expose()
  @Column({ type: 'timestamp' })
  endTime: Date;

  @Column({ type: 'boolean', default: false })
  beforeShowingNotificationSent: boolean;

  @Expose()
  @Column({ type: 'boolean', default: false })
  renterConfirmedAttendance: boolean;

  @Column({ type: 'text', nullable: true })
  calendarEventId: string;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp' })
  deletedAt: Date;
}

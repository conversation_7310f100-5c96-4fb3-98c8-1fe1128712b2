import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { CalendarService } from '../../shared/calendar/calendar.service';
import { CalendarEventDto, CalendarEventStatus } from '../../shared/calendar/models/calendar-event.dto';
import { Showing } from './showing.entity';
import { ShowingRequest } from '../showing-request/showing-request.entity';
import { Property } from '../property/property/entities/property.entity';
import { PropertyAvailabilityService } from '../property/availability/property-availability.service';
import { addMinutes } from 'date-fns';
import { ShowingRequestStatus } from '../showing-request/enums/showing-request-status.enum';
import { RescheduleRequest, RescheduleRequestStatus } from '../reschedule/reschedule-request.entity';

interface ShowingCalendarContext {
  showing: Showing;
  property: Property;
  investorUserId: string;
  showingRequests: ShowingRequest[];
  rescheduleRequests: RescheduleRequest[];
}

@Injectable()
export class ShowingCalendarService {
  constructor(
    private readonly calendarService: CalendarService,
    private readonly propertyAvailabilityService: PropertyAvailabilityService,
    @InjectRepository(Showing)
    private readonly showingRepository: Repository<Showing>,
  ) {}

  async updateEvent(showing: Showing): Promise<void> {
    try {
      const context = await this.resolveShowingContext(showing);

      const hasCalendarIntegration = await this.hasCalendarIntegration(context.investorUserId);
      if (!hasCalendarIntegration) {
        console.log(
          `User ${context.investorUserId} does not have calendar integration, skipping calendar event update`,
        );
        return;
      }

      const eventDto = await this.buildCalendarEventDto(
        context.showing,
        context.property,
        context.showingRequests,
        context.rescheduleRequests,
        CalendarEventStatus.CONFIRMED,
      );

      if (!showing.calendarEventId) {
        const calendarEvent = await this.calendarService.createCalendarEvent(context.investorUserId, eventDto);

        // Update showing with calendar event ID
        context.showing.calendarEventId = calendarEvent.id;
        await this.showingRepository.update(context.showing.id, {
          calendarEventId: calendarEvent.id,
        });
      } else {
        await this.calendarService.updateCalendarEvent(
          context.investorUserId,
          context.showing.calendarEventId,
          eventDto,
        );
      }
    } catch (error) {
      this.handleCalendarError(error, 'update calendar event', showing.id);
    }
  }

  async deleteEvent(showing: Showing): Promise<void> {
    if (!showing.calendarEventId) {
      console.error(`Showing ${showing.id} has no calendar event ID, nothing to delete`);
      return;
    }

    try {
      const context = await this.resolveShowingContext(showing);

      const hasCalendarIntegration = await this.hasCalendarIntegration(context.investorUserId);
      if (!hasCalendarIntegration) {
        console.log(
          `User ${context.investorUserId} does not have calendar integration, skipping calendar event deletion`,
        );
        return;
      }

      await this.calendarService.deleteCalendarEvent(context.investorUserId, context.showing.calendarEventId);

      context.showing.calendarEventId = null;
      await this.showingRepository.update(context.showing.id, {
        calendarEventId: null,
      });
    } catch (error) {
      this.handleCalendarError(error, 'delete calendar event', showing.id);
    }
  }

  private async resolveShowingContext(showing: Showing): Promise<ShowingCalendarContext> {
    try {
      const property = await showing.property;
      const investor = await property.owner;
      const investorUser = await investor.user;
      const showingRequests = await showing.showingRequests;
      const rescheduleRequests = await showing.rescheduleRequests;

      if (!investorUser) {
        throw new Error(`Failed to load investor user for showing ${showing.id}`);
      }

      if (!investorUser.id) {
        throw new Error(`Investor user ID is undefined for showing ${showing.id}`);
      }

      return {
        showing,
        property,
        investorUserId: investorUser.id,
        showingRequests,
        rescheduleRequests,
      };
    } catch (error) {
      throw new Error(`Failed to resolve showing context: ${error.message}`);
    }
  }

  private handleCalendarError(error: any, operationName: string, showingId: string): void {
    console.error(`Failed to ${operationName} for showing ${showingId}:`, error);
  }

  private async buildCalendarEventDto(
    showing: Showing,
    property: Property,
    showingRequests: ShowingRequest[],
    rescheduleRequests: RescheduleRequest[],
    status: CalendarEventStatus = CalendarEventStatus.CONFIRMED,
  ): Promise<CalendarEventDto> {
    const location = await property.location;
    const { showingDurationInMinutes } = await this.propertyAvailabilityService.findByProperty(property.id);

    const endTime = addMinutes(showing.startTime, showingDurationInMinutes);

    const addressParts = [
      location.address,
      location.apartmentNumber ? `Apt ${location.apartmentNumber}` : null,
      location.city,
      location.state,
      location.zip,
    ].filter(Boolean);

    const fullAddress = addressParts.join(', ');
    const title = `Property Showing - ${location.address}`;
    const description = await this.buildEventDescription(showingRequests, rescheduleRequests);

    return {
      title,
      startTime: showing.startTime.toISOString(),
      endTime: endTime.toISOString(),
      description,
      location: fullAddress,
      status,
    };
  }

  private async buildEventDescription(
    showingRequests: ShowingRequest[],
    rescheduleRequests: RescheduleRequest[] = [],
  ): Promise<string> {
    const activeRequests = showingRequests.filter(
      (request) => !request.deletedAt && request.status === ShowingRequestStatus.ACCEPTED,
    );

    const activeRescheduleRequests = rescheduleRequests.filter(
      (request) => request.status === RescheduleRequestStatus.CONFIRMED,
    );

    if (activeRequests.length + activeRescheduleRequests.length === 0) {
      return 'Property showing - No attendees confirmed yet';
    }

    const uniqueRenters = new Map<string, any>();

    for (const request of activeRequests) {
      const renter = await request.renter;
      if (!uniqueRenters.has(renter.id)) {
        uniqueRenters.set(renter.id, renter);
      }
    }

    for (const request of activeRescheduleRequests) {
      const renter = await request.renter;
      if (!uniqueRenters.has(renter.id)) {
        uniqueRenters.set(renter.id, renter);
      }
    }

    const renterDetails = await Promise.all(
      Array.from(uniqueRenters.values()).map(async (renter) => {
        const user = renter.user;

        const details = [
          `Name: ${user.name}`,
          renter.creditScore ? `Credit Score: ${renter.creditScore}` : null,
          renter.monthlyIncome ? `Monthly Income: $${renter.monthlyIncome.toLocaleString()}` : null,
        ].filter(Boolean);

        return details.join('\n');
      }),
    );

    const attendeesSection = renterDetails.map((details, index) => `Attendee ${index + 1}:\n${details}`).join('\n\n');

    return `Property Showing\n\n${attendeesSection}`;
  }

  private async hasCalendarIntegration(userId: string): Promise<boolean> {
    try {
      await this.calendarService.getCalendarEvents(userId, new Date(), new Date());
      return true;
    } catch (error) {
      if (error.response?.code === 'CALENDAR_NOT_CONNECTED') {
        console.log(`User ${userId} does not have calendar integration`);
        return false;
      }
      console.warn(`Calendar integration check failed for user ${userId}, assuming integration exists:`, error.message);
      return true;
    }
  }
}

import { Property } from '../../property/property/entities/property.entity';

export async function calculateShowingEndTime(property: Property, startTime: string | Date): Promise<Date> {
  const propertyAvailability = await property.availability;
  const durationInMinutes = propertyAvailability.showingDurationInMinutes || 15; // Default to 15 minutes
  const startTimeAsDate = new Date(startTime);

  return new Date(startTimeAsDate.getTime() + durationInMinutes * 60 * 1000);
}

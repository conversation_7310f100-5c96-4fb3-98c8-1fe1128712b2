import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiModule } from '../../ai/ai.module';
import { OutboundCommunicationModule } from '../../shared/communication/outbound-communication/outbound-communication.module';
import { CompanyModule } from '../../shared/company/company.module';
import { RescheduleModule } from '../reschedule/reschedule.module';
import { ShowingRequestModule } from '../showing-request/showing-request.module';
import { ShowingController } from './showing.controller';
import { Showing } from './showing.entity';
import { ShowingService } from './showing.service';
import { Property } from '../property/property/entities/property.entity';
import { PropertyInquiryModule } from '../property-inquiry/property-inquiry.module';
import { Company } from '../../shared/company/entities/company.entity';
import { FollowUpModule } from '../../shared/communication/follow-up/follow-up.module';
import { ShowingAgentModule } from '../showing-agent/showing-agent.module';
import { ShowingAgent } from '../showing-agent/entities/showing-agent.entity';
import { RenterOutboundCommsModule } from '../../renter/renter-communication/outbound-communication/renter-outbound-comms.module';
import { PropertyModule } from '../property/property/property.module';
import { CalendarModule } from '../../shared/calendar/calendar.module';
import { AvailabilityModule } from '../availability/availability.module';
import { PropertyAvailabilityModule } from '../property/availability/property-availability-module';
import { ShowingCalendarService } from './showing-calendar.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Property, Showing, Company, ShowingAgent]),
    forwardRef(() => ShowingRequestModule),
    forwardRef(() => RescheduleModule),
    forwardRef(() => PropertyModule),
    OutboundCommunicationModule,
    PropertyInquiryModule,
    CompanyModule,
    AiModule,
    FollowUpModule,
    ShowingAgentModule,
    RenterOutboundCommsModule,
    CalendarModule,
    AvailabilityModule,
    PropertyAvailabilityModule,
  ],
  providers: [ShowingService, ShowingCalendarService],
  controllers: [ShowingController],
  exports: [ShowingService, ShowingCalendarService],
})
export class ShowingModule {}

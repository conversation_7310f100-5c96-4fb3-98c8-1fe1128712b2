import { Body, Controller, Get, HttpCode, Param, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';

import { BasicAuth } from '../../shared/auth/decorators/basic-auth.decorator';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { Role } from '../../shared/auth/models/roles-enum';
import { RescheduledShowingDto } from '../reschedule/model/rescheduled-showing.dto';
import { CancelShowingDto } from './model/cancel-showing.dto';
import { RescheduleShowingDto } from './model/reschedule-showing.dto';
import { ShowingDto } from './model/showing.dto';
import { ShowingService } from './showing.service';
import { TimeFilter } from '../property/statistics/time-filter.enum';
import { IsCompanyEmployeeGuard } from '../../../guards/investor/is-company-employee.guard';
import { IsShowingOwnerGuard } from '../../../guards/investor/is-showing-owner.guard';
import { Request } from 'express';
import { IsShowingAgentAndUserBelongToSameCompanyGuard } from '../../../guards/investor/is-showing-agent-and-user-belong-to-same-company.guard';
import { AssigneShowingAgentDto } from './model/assigne-showing-agent.dto';

@ApiTags('showing')
@Controller('showing')
export class ShowingController {
  constructor(private readonly showingService: ShowingService) {}

  @Get(':showingId')
  @ApiOkResponse({
    description: 'Get showing by ID',
    type: ShowingDto,
  })
  @UseGuards(RolesGuard, IsShowingOwnerGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  async getById(@Param('showingId') _showingId: string, @Req() req: Request): Promise<ShowingDto> {
    return await this.showingService.convertToDto(req.showing, true, true, true);
  }

  @Get('company/:companyId')
  @ApiOkResponse({
    description: 'Get all showings by company ID',
    type: ShowingDto,
  })
  @UseGuards(RolesGuard, IsCompanyEmployeeGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  async getAllByCompany(
    @Param('companyId') companyId: string,
    @Query('filter') filter: TimeFilter,
  ): Promise<ShowingDto[]> {
    const showings = await this.showingService.findAllByCompany(companyId, filter);

    return Promise.all(showings.map((showing) => this.showingService.convertToDto(showing, true, true, true)));
  }

  @Post(':showingId/cancel')
  @HttpCode(200)
  @ApiOkResponse({
    description: 'Cancel showing with all showing requests',
  })
  @UseGuards(RolesGuard, IsShowingOwnerGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  async cancelShowing(@Param('showingId') showingId: string, @Body() dto: CancelShowingDto): Promise<void> {
    await this.showingService.cancelShowing(showingId, dto);
  }

  @Post(':showingId/reschedule')
  @HttpCode(200)
  @ApiOkResponse({
    description: 'Reschedule the whole showing',
    type: RescheduledShowingDto,
  })
  @UseGuards(RolesGuard, IsShowingOwnerGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  async rescheduleShowing(
    @Param('showingId') _showingId: string,
    @Body() dto: RescheduleShowingDto,
    @Req() req: Request,
  ): Promise<RescheduledShowingDto> {
    return this.showingService.rescheduleShowing(req.showing, dto);
  }

  @Post(':showingId/assign-showing-agent')
  @HttpCode(200)
  @ApiOkResponse({ description: 'Assigne showing agent' })
  @UseGuards(RolesGuard, IsShowingOwnerGuard, IsShowingAgentAndUserBelongToSameCompanyGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  async updateShowingAgent(
    @Param('showingId') _showingId: string,
    @Body() payload: AssigneShowingAgentDto,
    @Req() req: Request,
  ): Promise<void> {
    return this.showingService.assignShowingAgent(req.showing.id, payload.showingAgentId);
  }

  @Put('mark-completed')
  @ApiOkResponse({ description: 'Mark all completed showings' })
  @BasicAuth()
  @ApiBearerAuth()
  async markShowingsAsCompleted(): Promise<void> {
    return this.showingService.markCompletedShowings();
  }

  // TODO delete once existing showing are completed
  @Put('reminders/send')
  @ApiOkResponse({
    description: 'Sends showing reminders for all the showings that will happen in 1 hour',
  })
  @BasicAuth()
  @ApiBearerAuth()
  async sendShowingReminders(): Promise<void> {
    await this.showingService.sendBeforeShowingReminders();
  }

  @Put('cancel-unconfirmed')
  @ApiOkResponse({
    description: 'Cancel all showings where the renter has not confirmed attendance and the showing is soon',
  })
  @BasicAuth()
  @ApiBasicAuth('basic')
  async cancelUnconfirmedShowings(): Promise<void> {
    await this.showingService.cancelUnconfirmedShowings();
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ShowingService } from './showing.service';
import { Showing } from './showing.entity';
import { ShowingStatus } from './enums/showing-status.enum';
import { TourType } from './enums/tour-type.enum';
import { ShowingRequestService } from '../showing-request/showing-request.service';
import { OutboundCommunicationService } from '../../shared/communication/outbound-communication/outbound-communication.service';
import { RescheduleService } from '../reschedule/reschedule.service';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import { FollowUpService } from '../../shared/communication/follow-up/follow-up.service';
import { RenterOutboundCommsService } from '../../renter/renter-communication/outbound-communication/renter-outbound-comms.service';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { ShowingRequestStatus } from '../showing-request/enums/showing-request-status.enum';
import { notifyRenterUnconfirmedShowingCanceledTemplate } from '../showing-request/prompts/notify-renter-unconfirmed-showing-canceled.template';
import { RescheduleRequestStatus } from '../reschedule/reschedule-request.entity';
import { PropertyService } from '../property/property/property.service';
import { InvestorAvailabilityService } from '../availability/investor-availability.service';
import { ShowingCalendarService } from './showing-calendar.service';

jest.mock('../property/property/property.service', () => ({
  PropertyService: jest.fn().mockImplementation(),
}));

describe('ShowingService', () => {
  let service: ShowingService;

  // Mock repositories and services
  const mockShowingRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneOrFail: jest.fn(),
    update: jest.fn(),
    save: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getOne: jest.fn(),
      getMany: jest.fn().mockResolvedValue([]),
    })),
  };

  const mockShowingRequestService = {
    cancelRequest: jest.fn(),
    sendRenterShowingReminderNotification: jest.fn(),
    rescheduleShowingRequest: jest.fn(),
  };

  const mockCommunicationService = {
    sendRenterCancelledShowingNotification: jest.fn(),
    sendUpcomingShowingReminderToInvestor: jest.fn(),
  };

  const mockRenterOutboundCommsService = {
    craftMessageAndSend: jest.fn(),
  };

  const mockSlackCommunicationService = {
    sendMessageToConvosChannel: jest.fn(),
  };

  const mockRescheduleService = {
    cancelRescheduleRequestAsOwner: jest.fn(),
    rescheduleRescheduleRequest: jest.fn(),
  };

  const mockPropertyInquiryService = {
    findByRenterAndProperty: jest.fn(),
    updateStageAndAddCorrespondingEvent: jest.fn(),
  };

  const mockFollowUpService = {
    deleteShowingReminderFollowUps: jest.fn(),
    createPostShowingFollowUps: jest.fn(),
    findFollowUpByUserAndType: jest.fn(),
  };

  const mockPropertyService = {
    updateLastShowingAgent: jest.fn(),
  };

  const mockInvestorAvailabilityService = {
    findByInvestor: jest.fn(),
  };

  const mockShowingCalendarService = {
    updateEvent: jest.fn(),
    deleteEvent: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ShowingService,
        {
          provide: getRepositoryToken(Showing),
          useValue: mockShowingRepository,
        },
        {
          provide: PropertyService,
          useValue: mockPropertyService,
        },
        {
          provide: ShowingRequestService,
          useValue: mockShowingRequestService,
        },
        {
          provide: OutboundCommunicationService,
          useValue: mockCommunicationService,
        },
        {
          provide: RescheduleService,
          useValue: mockRescheduleService,
        },
        {
          provide: PropertyInquiryService,
          useValue: mockPropertyInquiryService,
        },
        {
          provide: FollowUpService,
          useValue: mockFollowUpService,
        },
        {
          provide: RenterOutboundCommsService,
          useValue: mockRenterOutboundCommsService,
        },
        {
          provide: SlackCommunicationService,
          useValue: mockSlackCommunicationService,
        },
        {
          provide: InvestorAvailabilityService,
          useValue: mockInvestorAvailabilityService,
        },
        {
          provide: ShowingCalendarService,
          useValue: mockShowingCalendarService,
        },
      ],
    }).compile();

    service = module.get<ShowingService>(ShowingService);

    // Clear mocks and set up default behavior for each test
    jest.clearAllMocks();

    // Default mock behaviors - tests can override these if needed
    mockRenterOutboundCommsService.craftMessageAndSend.mockResolvedValue({
      message: 'test',
      conversation: {},
    });

    mockInvestorAvailabilityService.findByInvestor.mockResolvedValue({
      showingDurationInMinutes: 60, // Default duration
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('cancelShowing', () => {
    it('should cancel a showing and all its requests', async () => {
      // Mock data
      const showingId = 'showing-id';
      const cancelShowingDto = { cancelReason: 'Test cancellation reason' };

      const mockShowing = {
        id: showingId,
        status: ShowingStatus.CONFIRMED,
        propertyId: 'property-id',
        showingRequests: Promise.resolve([
          {
            id: 'request-1',
            status: ShowingRequestStatus.ACCEPTED,
            renter: Promise.resolve({
              id: 'renter-id',
              user: { id: 'renter-user-id', name: 'Test Renter' },
            }),
          },
          {
            id: 'request-2',
            status: ShowingRequestStatus.PENDING,
            renter: Promise.resolve({
              id: 'renter-2-id',
              user: { id: 'renter-2-user-id', name: 'Test Renter 2' },
            }),
          },
        ]),
        rescheduleRequests: Promise.resolve([
          {
            id: 'reschedule-1',
            status: RescheduleRequestStatus.PENDING,
          },
        ]),
      };

      // Setup mocks
      jest.spyOn(service, 'findById').mockResolvedValue(mockShowing as unknown as Showing);

      // Call the method
      await service.cancelShowing(showingId, cancelShowingDto);

      // Verify showingRequestService.cancelRequest was called for both requests
      expect(mockShowingRequestService.cancelRequest).toHaveBeenCalledWith(
        'property-id',
        'request-1',
        'Test cancellation reason',
        ShowingRequestStatus.CANCELED_BY_INVESTOR,
      );

      expect(mockShowingRequestService.cancelRequest).toHaveBeenCalledWith(
        'property-id',
        'request-2',
        'Test cancellation reason',
        ShowingRequestStatus.CANCELED_BY_INVESTOR,
      );

      // Verify that update was called to set status to CANCELED
      expect(mockShowingRepository.update).toHaveBeenCalledWith(showingId, { status: ShowingStatus.CANCELED });
    });
  });

  describe('updateStatusBasedOnRequests', () => {
    it('should set status to CONFIRMED when there are accepted requests', async () => {
      // Mock data
      const mockShowing = {
        id: 'showing-id',
        status: ShowingStatus.PENDING,
        showingRequests: Promise.resolve([
          {
            id: 'request-1',
            status: ShowingRequestStatus.ACCEPTED,
          },
          {
            id: 'request-2',
            status: ShowingRequestStatus.PENDING,
          },
        ]),
        rescheduleRequests: Promise.resolve([]),
      };

      // Call the method
      await service.updateStatusBasedOnRequests(mockShowing as unknown as Showing);

      // Verify that update was called to set status to CONFIRMED
      expect(mockShowingRepository.update).toHaveBeenCalledWith('showing-id', { status: ShowingStatus.CONFIRMED });
    });

    it('should set status to PENDING when there are pending requests but no accepted ones', async () => {
      // Mock data
      const mockShowing = {
        id: 'showing-id',
        status: ShowingStatus.CONFIRMED,
        showingRequests: Promise.resolve([
          {
            id: 'request-1',
            status: ShowingRequestStatus.PENDING,
          },
        ]),
        rescheduleRequests: Promise.resolve([]),
      };

      // Call the method
      await service.updateStatusBasedOnRequests(mockShowing as unknown as Showing);

      // Verify that update was called to set status to PENDING
      expect(mockShowingRepository.update).toHaveBeenCalledWith('showing-id', { status: ShowingStatus.PENDING });
    });

    it('should set status to CANCELED when there are canceled requests', async () => {
      // Mock data
      const mockShowing = {
        id: 'showing-id',
        status: ShowingStatus.CONFIRMED,
        showingRequests: Promise.resolve([
          {
            id: 'request-1',
            status: ShowingRequestStatus.CANCELLED_BY_RENTER,
          },
        ]),
        rescheduleRequests: Promise.resolve([]),
      };

      // Call the method
      await service.updateStatusBasedOnRequests(mockShowing as unknown as Showing);

      // Verify that update was called to set status to CANCELED
      expect(mockShowingRepository.update).toHaveBeenCalledWith('showing-id', { status: ShowingStatus.CANCELED });
    });
  });

  describe('cancelUnconfirmedShowings', () => {
    it('should cancel all showing requests and set showing status to CANCELED', async () => {
      // Mock data
      const mockShowing = {
        id: 'showing-id',
        status: ShowingStatus.CONFIRMED,
        renterConfirmedAttendance: false, // No renter has confirmed attendance
        startTime: new Date(),
        endTime: new Date(new Date().getTime() + 30 * 60000), // 30 minutes later
        propertyId: 'property-id',
        showingAgent: null,
        showingAgentId: null,
        rescheduleRequests: Promise.resolve([]),
        beforeShowingNotificationSent: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
        property: Promise.resolve({
          id: 'property-id',
          location: Promise.resolve({
            address: '123 Test St',
            city: 'Test City',
            timeZone: 'America/New_York',
          }),
          owner: Promise.resolve({
            user: Promise.resolve({
              id: 'owner-id',
              preferredCommunicationChannel: 'email',
            }),
          }),
        }),
        showingRequests: Promise.resolve([
          {
            id: 'request-1',
            status: ShowingRequestStatus.ACCEPTED,
            renter: Promise.resolve({
              id: 'renter-id',
              user: {
                id: 'renter-user-id',
                name: 'Test Renter',
              },
            }),
          },
          {
            id: 'request-2',
            status: ShowingRequestStatus.ACCEPTED,
            renter: Promise.resolve({
              id: 'renter-2-id',
              user: {
                id: 'renter-2-user-id',
                name: 'Test Renter 2',
              },
            }),
          },
        ]),
      };

      const mockUpdatedShowing = {
        ...mockShowing,
        showingRequests: Promise.resolve([
          {
            id: 'request-1',
            status: ShowingRequestStatus.CANCELLED_BY_RENTER, // Status after cancellation
            renter: Promise.resolve({
              id: 'renter-id',
              user: {
                id: 'renter-user-id',
                name: 'Test Renter',
              },
            }),
          },
          {
            id: 'request-2',
            status: ShowingRequestStatus.CANCELLED_BY_RENTER, // Status after cancellation
            renter: Promise.resolve({
              id: 'renter-2-id',
              user: {
                id: 'renter-2-user-id',
                name: 'Test Renter 2',
              },
            }),
          },
        ]),
      };

      // Setup mocks
      mockShowingRepository.find.mockResolvedValue([mockShowing]);

      // Mock findById to return the updated showing after cancellation
      jest.spyOn(service, 'findById').mockResolvedValue(mockUpdatedShowing as unknown as Showing);

      // Call the method
      await service.cancelUnconfirmedShowings();

      // Verify repository calls
      expect(mockShowingRepository.find).toHaveBeenCalledWith({
        where: {
          status: ShowingStatus.CONFIRMED,
          renterConfirmedAttendance: false, // Only showings where no renter confirmed attendance
          startTime: expect.any(Object), // LessThan(cutoffTime)
        },
      });

      // Verify showingRequestService.cancelRequest was called for both requests
      expect(mockShowingRequestService.cancelRequest).toHaveBeenCalledWith(
        'property-id',
        'request-1',
        'Renter did not confirm attendance',
        ShowingRequestStatus.CANCELLED_BY_RENTER,
      );

      expect(mockShowingRequestService.cancelRequest).toHaveBeenCalledWith(
        'property-id',
        'request-2',
        'Renter did not confirm attendance',
        ShowingRequestStatus.CANCELLED_BY_RENTER,
      );

      // Verify that update was called to set status to CANCELED
      expect(mockShowingRepository.update).toHaveBeenCalledWith('showing-id', { status: ShowingStatus.CANCELED });

      // Verify that messages were sent to renters
      expect(mockRenterOutboundCommsService.craftMessageAndSend).toHaveBeenCalledWith({
        renter: expect.any(Object),
        propertyId: 'property-id',
        templateVariables: {
          propertyAddress: '123 Test St',
          showingTime: expect.any(String),
          renterName: 'Test',
        },
        template: notifyRenterUnconfirmedShowingCanceledTemplate,
      });

      // Verify that Slack notifications were sent
      expect(mockSlackCommunicationService.sendMessageToConvosChannel).toHaveBeenCalledWith(
        expect.any(String), // The message builder creates a string
        expect.any(Object), // The conversation object
      );
    });

    it('should not process showings where a renter has confirmed attendance', async () => {
      // Setup mocks - return empty array since the query filters out showings with confirmed attendance
      mockShowingRepository.find.mockResolvedValue([]);

      // Call the method
      await service.cancelUnconfirmedShowings();

      // Verify repository calls
      expect(mockShowingRepository.find).toHaveBeenCalledWith({
        where: {
          status: ShowingStatus.CONFIRMED,
          renterConfirmedAttendance: false,
          startTime: expect.any(Object), // LessThan(cutoffTime)
        },
      });

      // Verify that cancelRequest was not called for any showing
      expect(mockShowingRequestService.cancelRequest).not.toHaveBeenCalled();

      // Verify that update was not called to set status to CANCELED
      expect(mockShowingRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('rescheduleShowing', () => {
    const mockShowing = {
      id: 'showing-id',
      propertyId: 'property-id',
      showingAgentId: 'agent-id',
      tourType: TourType.IN_PERSON,
      property: Promise.resolve({
        id: 'property-id',
        owner: Promise.resolve({
          id: 'investor-id',
        }),
        availability: Promise.resolve({
          showingDurationInMinutes: 60, // Default duration
        }),
      }),
      showingRequests: Promise.resolve([
        {
          id: 'request-1',
          status: ShowingRequestStatus.ACCEPTED,
        },
      ]),
      rescheduleRequests: Promise.resolve([
        {
          id: 'reschedule-1',
          status: RescheduleRequestStatus.PENDING,
        },
      ]),
    };

    beforeEach(() => {
      // Set up specific mocks for rescheduleShowing tests
      mockShowingRequestService.rescheduleShowingRequest = jest
        .fn()
        .mockResolvedValue({ newShowingId: 'new-showing-id' });
      mockRescheduleService.rescheduleRescheduleRequest = jest
        .fn()
        .mockResolvedValue({ newShowingId: 'new-showing-id' });

      // Restore the investor availability service mock that might have been cleared
      mockInvestorAvailabilityService.findByInvestor.mockResolvedValue({
        showingDurationInMinutes: 60, // Default duration
      });
    });

    it('should reschedule showing with provided endTime', async () => {
      const rescheduleDto = {
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T11:00:00Z',
        comment: 'Rescheduled by owner',
        showingAgentId: 'new-agent-id',
      };

      const result = await service.rescheduleShowing(mockShowing as any, rescheduleDto);

      expect(mockShowingRequestService.rescheduleShowingRequest).toHaveBeenCalledWith(
        'property-id',
        {
          showingRequestId: 'request-1',
          startTime: new Date('2024-01-15T10:00:00Z'),
          comment: 'Rescheduled by owner',
          showingAgentId: 'new-agent-id',
        },
        false,
      );

      expect(mockRescheduleService.rescheduleRescheduleRequest).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Object),
        new Date('2024-01-15T10:00:00Z'),
        new Date('2024-01-15T11:00:00Z'),
        'Rescheduled by owner',
        'new-agent-id',
        TourType.IN_PERSON, // showing.tourType
      );

      expect(result).toEqual({ newShowingId: 'new-showing-id' });
    });

    it('should reschedule showing and calculate endTime from investor availability when not provided', async () => {
      const rescheduleDto = {
        startTime: '2024-01-15T10:00:00Z',
        comment: 'Rescheduled by owner',
        showingAgentId: 'new-agent-id',
      };

      // Override default duration for this specific test
      mockInvestorAvailabilityService.findByInvestor.mockResolvedValueOnce({
        showingDurationInMinutes: 30,
      });

      const result = await service.rescheduleShowing(mockShowing as any, rescheduleDto);

      // The calculateShowingEndTime utility uses property.availability, not investor availability
      // So we don't expect the investor availability service to be called
      expect(mockInvestorAvailabilityService.findByInvestor).not.toHaveBeenCalled();

      expect(mockShowingRequestService.rescheduleShowingRequest).toHaveBeenCalledWith(
        'property-id',
        {
          showingRequestId: 'request-1',
          startTime: new Date('2024-01-15T10:00:00Z'),
          comment: 'Rescheduled by owner',
          showingAgentId: 'new-agent-id',
        },
        false,
      );

      expect(mockRescheduleService.rescheduleRescheduleRequest).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Object),
        new Date('2024-01-15T10:00:00Z'),
        expect.any(Date), // endTime calculated by calculateShowingEndTime utility
        'Rescheduled by owner',
        'new-agent-id',
        expect.any(String), // tourType from showing
      );

      expect(result).toEqual({ newShowingId: 'new-showing-id' });
    });
  });
});

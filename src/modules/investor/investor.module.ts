import { Module } from '@nestjs/common';
import { AvailabilityModule } from './availability/availability.module';
import { DataFeedModule } from './data-feed/data-feed.module';
import { PropertyModule } from './property/property/property.module';
import { ShowingRequestModule } from './showing-request/showing-request.module';
import { ShowingModule } from './showing/showing.module';
import { RescheduleModule } from './reschedule/reschedule.module';
import { InvestorEntityModule } from './investor/investor-entity.module';
import { LocationDiscoveryModule } from './property/location-discovery/location-discovery.module';
import { InvestorApplicationModule } from './application/investor-application.module';
import { ShowingAgentModule } from './showing-agent/showing-agent.module';
import { PropertyAvailabilityModule } from './property/availability/property-availability-module';
import { PropertyStatisticsModule } from './property/statistics/property-statistics.module';
import { AptlyModule } from './integrations/aptly/aptly.module';
import { PropertyListingModule } from './property-listing/property-listing.module';
import { InvestorOnboardingModule } from './onboarding/investor-onboarding.module';

@Module({
  imports: [
    AvailabilityModule,
    DataFeedModule,
    PropertyModule,
    PropertyListingModule,
    PropertyStatisticsModule,
    ShowingModule,
    ShowingRequestModule,
    RescheduleModule,
    InvestorEntityModule,
    LocationDiscoveryModule,
    InvestorApplicationModule,
    ShowingAgentModule,
    PropertyListingModule,
    PropertyAvailabilityModule,
    AptlyModule,
    InvestorOnboardingModule,
  ],
  exports: [InvestorEntityModule],
})
export class InvestorModule {}

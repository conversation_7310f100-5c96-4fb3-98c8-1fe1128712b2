import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
} from 'typeorm';
import { Company } from '../../../shared/company/entities/company.entity';
import { Expose, instanceToPlain } from 'class-transformer';
import { ShowingAgentDto } from '../dto/showing-agent.dto';

@Entity()
export class ShowingAgent {
  @PrimaryGeneratedColumn('uuid')
  @Expose()
  id: string;

  @Column({ type: 'varchar', length: 100 })
  @Expose()
  firstName: string;

  @Column({ type: 'varchar', length: 100 })
  @Expose()
  lastName: string;

  @Column({ type: 'varchar', length: 20 })
  @Expose()
  phone: string;

  @ManyToOne(() => Company, { nullable: false })
  @JoinColumn()
  company: Company;

  @RelationId((showingAgent: ShowingAgent) => showingAgent.company)
  companyId: string;

  @Column({ type: 'boolean', default: true })
  @Expose()
  isActive: boolean;

  @CreateDateColumn()
  @Expose()
  createdAt: Date;

  @UpdateDateColumn()
  @Expose()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  static convertToDto(inquiry: ShowingAgent): ShowingAgentDto {
    return <ShowingAgentDto>instanceToPlain(inquiry, { excludeExtraneousValues: true });
  }
}

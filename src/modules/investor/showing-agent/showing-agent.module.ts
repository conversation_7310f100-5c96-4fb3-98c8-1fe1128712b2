import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ShowingAgentService } from './showing-agent.service';
import { ShowingAgentController } from './showing-agent.controller';
import { ShowingAgent } from './entities/showing-agent.entity';
import { Company } from '../../shared/company/entities/company.entity';
import { CompanyModule } from '../../shared/company/company.module';

@Module({
  imports: [TypeOrmModule.forFeature([ShowingAgent, Company]), CompanyModule],
  controllers: [ShowingAgentController],
  providers: [ShowingAgentService],
  exports: [ShowingAgentService],
})
export class ShowingAgentModule {}

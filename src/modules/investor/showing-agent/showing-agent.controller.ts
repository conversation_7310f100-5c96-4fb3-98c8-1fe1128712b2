import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ShowingAgentService } from './showing-agent.service';
import { CreateShowingAgentDto } from './dto/create-showing-agent.dto';
import { UpdateShowingAgentDto } from './dto/update-showing-agent.dto';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../shared/auth/models/roles-enum';
import { Request } from 'express';
import { ShowingAgent } from './entities/showing-agent.entity';
import { ShowingAgentDto } from './dto/showing-agent.dto';
import { IsShowingAgentAndUserBelongToSameCompanyGuard } from '../../../guards/investor/is-showing-agent-and-user-belong-to-same-company.guard';

@ApiTags('showing-agents')
@Controller('showing-agents')
@UseGuards(RolesGuard)
@HasRoles(Role.INVESTOR)
@ApiBearerAuth()
export class ShowingAgentController {
  constructor(private readonly showingAgentService: ShowingAgentService) {}

  @Post()
  @ApiOkResponse({
    description: 'Returns created showing agent',
    type: ShowingAgentDto,
  })
  async create(@Body() createShowingAgentDto: CreateShowingAgentDto, @Req() req: Request): Promise<ShowingAgentDto> {
    const showingAgent = await this.showingAgentService.create(createShowingAgentDto, req.user.id);

    return ShowingAgent.convertToDto(showingAgent);
  }

  @Get()
  @ApiOkResponse({
    description: "Returns list of showing agent associated with the user's company",
    type: ShowingAgentDto,
    isArray: true,
  })
  async findAll(@Req() req: Request): Promise<ShowingAgentDto[]> {
    const showingAgents = await this.showingAgentService.findAll(req.user.id);

    return showingAgents.map((showingAgent) => ShowingAgent.convertToDto(showingAgent));
  }

  @Get(':showingAgentId')
  @UseGuards(IsShowingAgentAndUserBelongToSameCompanyGuard)
  @ApiOkResponse({
    description: 'Returns showing agent by ID',
    type: ShowingAgentDto,
  })
  async findOne(@Param('showingAgentId') showingAgentId: string) {
    const showingAgent = await this.showingAgentService.findOne(showingAgentId);

    return ShowingAgent.convertToDto(showingAgent);
  }

  @Patch(':showingAgentId')
  @UseGuards(IsShowingAgentAndUserBelongToSameCompanyGuard)
  @ApiOkResponse({
    description: 'Returns updated showing agent',
    type: ShowingAgentDto,
  })
  update(@Param('showingAgentId') showingAgentId: string, @Body() updateShowingAgentDto: UpdateShowingAgentDto) {
    return this.showingAgentService.update(showingAgentId, updateShowingAgentDto);
  }

  @Delete(':showingAgentId')
  @UseGuards(IsShowingAgentAndUserBelongToSameCompanyGuard)
  delete(@Param('showingAgentId') showingAgentId: string) {
    return this.showingAgentService.delete(showingAgentId);
  }
}

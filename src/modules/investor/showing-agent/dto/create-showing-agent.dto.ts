import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsUSPhoneNumber } from '../../../../utils/validators/us-phone-number.validator';

export class CreateShowingAgentDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty()
  @IsUSPhoneNumber()
  @IsNotEmpty()
  phone: string;
}

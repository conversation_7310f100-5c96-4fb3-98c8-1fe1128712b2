import { IsOptional, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsUSPhoneNumber } from '../../../../utils/validators/us-phone-number.validator';

export class UpdateShowingAgentDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  firstName?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  lastName?: string;

  @ApiPropertyOptional()
  @IsUSPhoneNumber()
  @IsOptional()
  phone?: string;
}

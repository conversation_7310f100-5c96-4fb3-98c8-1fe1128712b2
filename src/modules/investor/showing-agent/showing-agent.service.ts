import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ShowingAgent } from './entities/showing-agent.entity';
import { CreateShowingAgentDto } from './dto/create-showing-agent.dto';
import { UpdateShowingAgentDto } from './dto/update-showing-agent.dto';
import { CompanyService } from '../../shared/company/company.service';
import { Company } from '../../shared/company/entities/company.entity';

@Injectable()
export class ShowingAgentService {
  constructor(
    @InjectRepository(ShowingAgent)
    private readonly showingAgentRepository: Repository<ShowingAgent>,
    private readonly companyService: CompanyService,
  ) {}

  async create(dto: CreateShowingAgentDto, userId: string): Promise<ShowingAgent> {
    const companyId = await this.getCompanyIdByUser(userId);

    const showingAgent = this.showingAgentRepository.create({
      ...dto,
      company: { id: companyId } as Company,
    });

    return await this.showingAgentRepository.save(showingAgent);
  }

  async findAll(userId: string): Promise<ShowingAgent[]> {
    const companyId = await this.getCompanyIdByUser(userId);

    return this.showingAgentRepository.findBy({
      company: {
        id: companyId,
      },
    });
  }

  async findOne(showingAgentId: string): Promise<ShowingAgent> {
    const agent = await this.showingAgentRepository.findOneBy({ id: showingAgentId });

    if (!agent) {
      throw new NotFoundException('Showing agent not found');
    }

    return agent;
  }

  async update(showingAgentId: string, dto: UpdateShowingAgentDto): Promise<ShowingAgent> {
    const agent = await this.findOne(showingAgentId);

    Object.assign(agent, dto);

    return await this.showingAgentRepository.save(agent);
  }

  async delete(showingAgentId: string): Promise<void> {
    const agent = await this.findOne(showingAgentId);

    await this.showingAgentRepository.softDelete(agent);
  }

  private async getCompanyIdByUser(userId: string): Promise<string> {
    const company = await this.companyService.getCompanyByUser(userId);

    if (!company) {
      throw new BadRequestException('User does not belong to any company');
    }

    return company.id;
  }
}

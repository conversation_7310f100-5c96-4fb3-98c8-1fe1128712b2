import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { AptlySyncController } from './aptly-sync.controller';
import { AptlySyncService } from './aptly-sync.service';
import { AptlyFeedService } from './aptly-feed.service';
import { Property } from '../../property/property/entities/property.entity';
import { Company } from '../../../shared/company/entities/company.entity';
import { PropertyImage } from '../../property/property/property-details/images/entities/property-image.entity';
import { FileModule } from '../../../shared/file/file.module';
import { PropertyAvailabilityModule } from '../../property/availability/property-availability-module';
import { SlackCommunicationModule } from '../../../shared/communication/outbound-communication/slack/slack-communication.module';
import { GeocodingModule } from '../../property/location-discovery/geocoding/geocoding.module';
import { CompanyModule } from '../../../shared/company/company.module';
import { PropertyListingModule } from '../../property-listing/property-listing.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Company, Property, PropertyImage]),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    PropertyListingModule,
    FileModule,
    PropertyAvailabilityModule,
    SlackCommunicationModule,
    GeocodingModule,
    CompanyModule,
  ],
  controllers: [AptlySyncController],
  providers: [AptlySyncService, AptlyFeedService],
  exports: [AptlySyncService, AptlyFeedService],
})
export class AptlyModule {}

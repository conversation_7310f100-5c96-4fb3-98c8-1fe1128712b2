export interface PropertySyncResult {
  aptlyId: string;
  propertyName: string;
  action: 'created' | 'skipped' | 'error';
  reason?: string;
  propertyId?: string;
  error?: string;
}

export interface CompanySyncResult {
  companyId: string;
  companyName: string;
  feedUrl: string;
  success: boolean;
  propertiesProcessed: number;
  propertiesCreated: number;
  propertiesSkipped: number;
  propertiesErrored: number;
  properties: PropertySyncResult[];
  error?: string;
}

export interface SyncResultDto {
  totalCompanies: number;
  companiesProcessed: number;
  companiesSuccessful: number;
  companiesFailed: number;
  totalPropertiesProcessed: number;
  totalPropertiesCreated: number;
  totalPropertiesSkipped: number;
  totalPropertiesErrored: number;
  companies: CompanySyncResult[];
  startTime: Date;
  endTime: Date;
  duration: number; // in milliseconds
}

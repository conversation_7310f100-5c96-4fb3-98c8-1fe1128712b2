export interface AptlyAddress {
  formattedAddress: string;
  address: string;
  city: string;
  county: string;
  state: string;
  stateCode: string;
  postalCode: string;
  country: string;
  countryName: string;
  streetNumber: string;
  streetName: string;
  geopoint: [number, number]; // [longitude, latitude]
}

export interface AptlyDeposit {
  amount: number;
  currency: string;
}

export interface AptlyPropertyDto {
  _id: string;
  aptletUuid: string;
  name: string;
  beds: number;
  baths: number;
  marketRent?: string;
  availableDate: string;
  address: AptlyAddress;
  totalArea?: number;
  targetDeposit?: AptlyDeposit;
  animalDeposit?: AptlyDeposit;
  petsAllowed: boolean;
  publishedForRent: boolean;
  companyId: string;
  applicationFee?: string;
  screeningActive?: boolean;
  deposit?: string;
  petDeposit?: string;
  applicationRequirements?: string;
  incomeToRent?: number;
  applicationLink?: string;
  aptlyListingLink?: string;
  pets?: boolean;
  coverPhoto?: string;
  marketingImages?: string[];
  screeningActiveApplications?: number;
  marketingName?: string;
}

export type AptlyFeedResponse = Array<AptlyPropertyDto>;

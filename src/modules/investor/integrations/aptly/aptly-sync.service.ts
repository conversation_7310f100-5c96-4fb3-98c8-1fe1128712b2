import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { AptlyFeedService } from './aptly-feed.service';
import { AptlyPropertyDto } from './models/aptly-property.dto';
import { SyncResultDto, CompanySyncResult, PropertySyncResult } from './models/sync-result.dto';
import { Company } from 'src/modules/shared/company/entities/company.entity';
import { Property } from '../../property/property/entities/property.entity';
import { PropertyImage } from '../../property/property/property-details/images/entities/property-image.entity';
import { FileService } from 'src/modules/shared/file/file.service';
import { SlackCommunicationService } from 'src/modules/shared/communication/outbound-communication/slack/slack-communication.service';
import { PropertyAvailabilityService } from '../../property/availability/property-availability.service';
import { AvailabilitySlotsService } from '../../availability/availability-slot.service';
import { PropertyDto } from '../../property/property/model/property.dto';
import { PropertyLocationDto } from '../../property/property/property-details/location/property-location.dto';
import { PropertySpecificationsDto } from '../../property/property/property-details/specifications/property-specifications.dto';
import { PropertyTypeEnum } from '../../property/property/enums/property-type.enum';
import { LeaseConditionsDto } from '../../property/property/property-details/lease-conditions/lease-conditions.dto';
import { PetPolicyDto } from '../../property/property/property-details/pet-policy/pet-policy.dto';
import { RenterRequirementsDto } from '../../property/property/property-details/renter-requirements/renter-requirements.dto';
import { IncomeEnum } from '../../property/property/property-details/renter-requirements/income.enum';
import { Investor } from '../../investor/investor.entity';
import { PropertyStatus } from '../../property/property/enums/property-status.enum';
import { GeocodingService } from '../../property/location-discovery/geocoding/geocoding.service';
import { DayOfTheWeekEnum } from '../../../../shared/enums/day-of-the-week.enum';
import { PropertyListingService } from '../../property-listing/property-listing.service';

@Injectable()
export class AptlySyncService {
  constructor(
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(PropertyImage)
    private readonly propertyImageRepository: Repository<PropertyImage>,
    private readonly propertyListingService: PropertyListingService,
    private readonly aptlyFeedService: AptlyFeedService,
    private readonly fileService: FileService,
    private readonly propertyAvailabilityService: PropertyAvailabilityService,
    private readonly availabilitySlotsService: AvailabilitySlotsService,
    private readonly slackService: SlackCommunicationService,
    private readonly geocodingService: GeocodingService,
  ) {}

  async syncAllFeeds(): Promise<SyncResultDto> {
    const startTime = new Date();
    console.log('Starting Aptly feed synchronization...');

    const result: SyncResultDto = {
      totalCompanies: 0,
      companiesProcessed: 0,
      companiesSuccessful: 0,
      companiesFailed: 0,
      totalPropertiesProcessed: 0,
      totalPropertiesCreated: 0,
      totalPropertiesSkipped: 0,
      totalPropertiesErrored: 0,
      companies: [],
      startTime,
      endTime: new Date(),
      duration: 0,
    };

    try {
      // Find all companies with Aptly feed URLs
      const companies = await this.getCompaniesWithAptlyFeeds();
      result.totalCompanies = companies.length;

      console.log(`Found ${companies.length} companies with Aptly feeds`);

      // Process each company's feed
      for (const company of companies) {
        const companyResult = await this.syncCompanyFeed(company);
        result.companies.push(companyResult);
        result.companiesProcessed++;

        if (companyResult.success) {
          result.companiesSuccessful++;
        } else {
          result.companiesFailed++;
        }

        result.totalPropertiesProcessed += companyResult.propertiesProcessed;
        result.totalPropertiesCreated += companyResult.propertiesCreated;
        result.totalPropertiesSkipped += companyResult.propertiesSkipped;
        result.totalPropertiesErrored += companyResult.propertiesErrored;

        // Count missing properties in totals
        const missingPropertiesCount = companyResult.properties.filter(
          (p) => p.reason && p.reason.includes('no longer in feed'),
        ).length;
        result.totalPropertiesProcessed += missingPropertiesCount;
      }

      const endTime = new Date();
      result.endTime = endTime;
      result.duration = endTime.getTime() - startTime.getTime();

      console.log(`Aptly sync completed in ${result.duration}ms`);
      console.log(
        `Summary: ${result.totalPropertiesCreated} created, ${result.totalPropertiesSkipped} skipped, ${result.totalPropertiesErrored} errors`,
      );

      return result;
    } catch (error) {
      console.error('Error during Aptly sync:', error);
      const endTime = new Date();
      result.endTime = endTime;
      result.duration = endTime.getTime() - startTime.getTime();
      throw error;
    }
  }

  private async getCompaniesWithAptlyFeeds(): Promise<Company[]> {
    return this.companyRepository
      .createQueryBuilder('company')
      .leftJoinAndSelect('company.settings', 'settings')
      .leftJoinAndSelect('company.investors', 'investors')
      .where('settings.aptlyFeedUrl IS NOT NULL')
      .andWhere("settings.aptlyFeedUrl != ''")
      .getMany();
  }

  private async syncCompanyFeed(company: Company): Promise<CompanySyncResult> {
    const feedUrl = company.settings.aptlyFeedUrl;
    console.log(`Syncing feed for company ${company.name} (${company.id}): ${feedUrl}`);

    const result: CompanySyncResult = {
      companyId: company.id,
      companyName: company.name || 'Unknown',
      feedUrl,
      success: false,
      propertiesProcessed: 0,
      propertiesCreated: 0,
      propertiesSkipped: 0,
      propertiesErrored: 0,
      properties: [],
    };

    try {
      // Fetch the feed
      const properties = await this.aptlyFeedService.fetchFeed(feedUrl);
      result.propertiesProcessed = properties.length;

      // Process each property
      for (const aptlyProperty of properties) {
        const propertyResult = await this.processProperty(aptlyProperty, company);
        result.properties.push(propertyResult);

        switch (propertyResult.action) {
          case 'created':
            result.propertiesCreated++;
            break;
          case 'skipped':
            result.propertiesSkipped++;
            break;
          case 'error':
            result.propertiesErrored++;
            break;
        }
      }

      // Check for properties that are no longer in the feed
      await this.handleMissingProperties(company, properties, result);

      result.success = true;
      console.log(`Successfully synced ${result.propertiesCreated} properties for company ${company.name}`);
    } catch (error) {
      result.error = error.message;
      console.error(`Error syncing feed for company ${company.name}:`, error.message);
    }

    return result;
  }

  private async processProperty(aptlyProperty: AptlyPropertyDto, company: Company): Promise<PropertySyncResult> {
    const result: PropertySyncResult = {
      aptlyId: aptlyProperty._id,
      propertyName: aptlyProperty.name,
      action: 'error',
    };

    try {
      // Validate the property data
      const validation = this.aptlyFeedService.validateProperty(aptlyProperty);
      if (!validation.isValid) {
        result.action = 'error';
        result.error = `Validation failed: ${validation.errors.join(', ')}`;
        return result;
      }

      // Check if property already exists by Aptly ID
      const existingByAptlyId = await this.propertyRepository.findOne({
        where: { aptlyId: aptlyProperty._id },
      });

      if (existingByAptlyId) {
        result.action = 'skipped';
        result.reason = 'Property already exists with this Aptly ID';
        result.propertyId = existingByAptlyId.id;
        return result;
      }

      // Get the company's investors
      const investors = await company.investors;
      if (!investors || investors.length === 0) {
        result.action = 'error';
        result.error = 'No investors found for company';
        return result;
      }

      const owner = investors[0];

      // Create the property using our Aptly-specific method
      const propertyDto = this.mapAptlyPropertyToPropertyDto(aptlyProperty);
      const imageUrls = this.extractImageUrls(aptlyProperty);
      const createdProperty = await this.createAptlyProperty(propertyDto, company, owner, aptlyProperty._id, imageUrls);

      result.action = 'created';
      result.propertyId = createdProperty.id;
      console.log(`Created property ${aptlyProperty.name} with ID ${createdProperty.id}`);
    } catch (error) {
      result.action = 'error';
      result.error = error.message;
      console.error(`Error processing property ${aptlyProperty.name}:`, error.message);
    }

    return result;
  }

  private mapAptlyPropertyToPropertyDto(aptlyProperty: AptlyPropertyDto): PropertyDto {
    const propertyDto = new PropertyDto();

    // Basic property info
    propertyDto.displayName = aptlyProperty.name;
    propertyDto.description = this.generateDescription(aptlyProperty);
    propertyDto.isAvailable = aptlyProperty.publishedForRent;

    // Set lastListedAt to current date for new Aptly properties
    propertyDto.lastListedAt = new Date();

    // Location
    propertyDto.location = this.mapLocation(aptlyProperty);

    // Specifications
    propertyDto.specifications = this.mapSpecifications(aptlyProperty);

    // Lease conditions
    propertyDto.leaseConditions = this.mapLeaseConditions(aptlyProperty);

    // Renter requirements - set defaults for Aptly properties
    propertyDto.renterRequirements = this.mapRenterRequirements();

    // Pet policy
    if (aptlyProperty.petsAllowed !== undefined) {
      propertyDto.petPolicy = this.mapPetPolicy(aptlyProperty);
    }

    return propertyDto;
  }

  private generateDescription(aptlyProperty: AptlyPropertyDto): string {
    const parts = [];

    parts.push(`${aptlyProperty.beds} bedroom, ${aptlyProperty.baths} bathroom property`);

    if (aptlyProperty.totalArea) {
      parts.push(`${aptlyProperty.totalArea} sq ft`);
    }

    if (aptlyProperty.marketRent) {
      const rent = this.aptlyFeedService.parseRentAmount(aptlyProperty.marketRent);
      if (rent) {
        parts.push(`$${rent}/month`);
      }
    }

    if (aptlyProperty.availableDate) {
      parts.push(`Available ${aptlyProperty.availableDate}`);
    }

    if (aptlyProperty.petsAllowed) {
      parts.push('Pet-friendly');
    }

    if (aptlyProperty.applicationRequirements) {
      parts.push('\n\nApplication Requirements:\n' + aptlyProperty.applicationRequirements);
    }

    return parts.join(' • ');
  }

  private mapLocation(aptlyProperty: AptlyPropertyDto): PropertyLocationDto {
    const location = new PropertyLocationDto();

    location.address = aptlyProperty.address.address;
    location.city = aptlyProperty.address.city;
    location.state = aptlyProperty.address.state;
    location.zip = parseInt(aptlyProperty.address.postalCode) || null;

    if (aptlyProperty.address.geopoint && aptlyProperty.address.geopoint.length === 2) {
      location.longitude = aptlyProperty.address.geopoint[0];
      location.latitude = aptlyProperty.address.geopoint[1];
    }

    return location;
  }

  private mapSpecifications(aptlyProperty: AptlyPropertyDto): PropertySpecificationsDto {
    const specs = new PropertySpecificationsDto();

    specs.bedrooms = aptlyProperty.beds;

    // Split baths into full and half bathrooms
    const totalBaths = aptlyProperty.baths;
    specs.fullBathrooms = Math.floor(totalBaths);
    specs.halfBathrooms = totalBaths % 1 > 0 ? 1 : 0;

    if (aptlyProperty.totalArea) {
      specs.squareFeet = aptlyProperty.totalArea;
    }

    // Default to apartment if not specified
    specs.propertyType = PropertyTypeEnum.APARTMENT;

    return specs;
  }

  private mapLeaseConditions(aptlyProperty: AptlyPropertyDto): LeaseConditionsDto {
    const lease = new LeaseConditionsDto();

    // Parse rent amount
    if (aptlyProperty.marketRent) {
      const rent = this.aptlyFeedService.parseRentAmount(aptlyProperty.marketRent);
      if (rent) {
        lease.rent = rent;
      }
    }

    // Parse application fee
    if (aptlyProperty.applicationFee) {
      const fee = this.aptlyFeedService.parseApplicationFee(aptlyProperty.applicationFee);
      if (fee) {
        lease.applicationFee = fee;
      }
    }

    // Parse available date
    if (aptlyProperty.availableDate) {
      try {
        lease.desiredLeasingDate = new Date(aptlyProperty.availableDate);
      } catch (error) {
        console.error(
          `Invalid date format for property ${aptlyProperty.name}: ${aptlyProperty.availableDate} ${error.message}`,
        );
      }
    }

    return lease;
  }

  private mapRenterRequirements(): RenterRequirementsDto {
    const requirements = new RenterRequirementsDto();

    // Set income requirement to 1x monthly rent
    requirements.minimumIncome = IncomeEnum.X1_RENT;

    // Set minimum credit score to 500
    requirements.minimumCreditScore = 500;

    return requirements;
  }

  private mapPetPolicy(aptlyProperty: AptlyPropertyDto): PetPolicyDto {
    const petPolicy = new PetPolicyDto();

    petPolicy.allowsPets = aptlyProperty.petsAllowed;

    if (aptlyProperty.petDeposit) {
      const deposit = this.aptlyFeedService.parseDepositAmount(aptlyProperty.petDeposit);
      if (deposit) {
        petPolicy.petDeposit = deposit;
      }
    }

    return petPolicy;
  }

  private extractImageUrls(aptlyProperty: AptlyPropertyDto): string[] {
    const imageUrls: string[] = [];

    // Get marketing images first (filter out empty/null values)
    const marketingImages =
      aptlyProperty.marketingImages && Array.isArray(aptlyProperty.marketingImages)
        ? aptlyProperty.marketingImages.filter((url) => url && url.trim())
        : [];

    // Add cover photo if available, otherwise use first marketing image
    if (aptlyProperty.coverPhoto) {
      imageUrls.push(aptlyProperty.coverPhoto);
      // Add remaining marketing images (excluding the cover photo if it's also in marketing images)
      marketingImages.forEach((url) => {
        if (url !== aptlyProperty.coverPhoto) {
          imageUrls.push(url);
        }
      });
    } else if (marketingImages.length > 0) {
      // No cover photo, use first marketing image as cover and add the rest
      imageUrls.push(...marketingImages);
    }

    return imageUrls;
  }

  private async createAptlyProperty(
    propertyDto: PropertyDto,
    company: Company,
    investor: Investor,
    aptlyId: string,
    imageUrls: string[] = [],
  ): Promise<Property> {
    // Get timezone if coordinates are available
    if (propertyDto.location?.longitude && propertyDto.location?.latitude) {
      propertyDto.location.timeZone = await this.geocodingService.getTimezone(
        propertyDto.location.latitude,
        propertyDto.location.longitude,
      );
    }

    // Create the property entity
    const property = this.propertyRepository.create();
    property.company = company;
    property.owner = investor;
    property.aptlyId = aptlyId;

    // Set status to READY_FOR_LISTING for synced properties
    property.status = PropertyStatus.READY_FOR_LISTING;
    property.isAvailable = true;

    // Map the property data
    Object.assign(property, propertyDto);

    // Save the property first
    const createdProperty = await this.propertyRepository.save(property);

    // Handle property images from Aptly URLs
    if (imageUrls && imageUrls.length > 0) {
      await this.processAptlyImages(createdProperty, imageUrls);
    }

    // Create property availability with default settings
    const propertyAvailability = await this.propertyAvailabilityService.create({
      showingDurationInMinutes: 30,
      property: createdProperty,
    });

    // Create 24-hour availability slots for all days of the week
    await this.create24HourAvailabilitySlots(propertyAvailability.id, propertyDto.location?.timeZone || 'UTC');

    // Send notification
    const propertyAddress = propertyDto?.location?.address || propertyDto.displayName;
    this.slackService.sendNewUserRegisteredMessage(
      `🏡 New Aptly property "${propertyAddress}" synced for ${(await investor.user).name}`,
    );

    return createdProperty;
  }

  private async processAptlyImages(property: Property, imageUrls: string[]): Promise<void> {
    try {
      for (let i = 0; i < imageUrls.length; i++) {
        const imageUrl = imageUrls[i];
        if (!imageUrl) continue;

        try {
          console.log(`Processing image ${i + 1}/${imageUrls.length}: ${imageUrl}`);

          // Download the image
          const response = await fetch(imageUrl);
          if (!response.ok) {
            console.warn(`Failed to download image from ${imageUrl}: ${response.statusText}`);
            continue;
          }

          const imageBuffer = await response.arrayBuffer();
          const buffer = Buffer.from(imageBuffer);

          // Extract filename from URL or generate one
          const urlParts = imageUrl.split('/');
          let originalFilename = urlParts[urlParts.length - 1] || `aptly-image-${i + 1}.jpg`;

          // Ensure filename has extension
          if (!originalFilename.includes('.')) {
            originalFilename += '.jpg';
          }

          // Detect content type from response headers or default to JPEG
          const contentType = response.headers.get('content-type') || 'image/jpeg';

          // Create a mock file object for the FileService
          const mockFile = {
            buffer,
            originalname: originalFilename,
            mimetype: contentType,
            size: buffer.length,
          } as Express.Multer.File;

          // Upload using FileService
          const uploadedFile = await this.fileService.uploadFile(mockFile, i);

          // Save the file
          await this.fileService.save(uploadedFile);

          // Create property image record
          const propertyImage = this.propertyImageRepository.create({
            propertyId: property.id,
            fileId: uploadedFile.id,
          });
          await this.propertyImageRepository.save(propertyImage);

          // Set first image as cover image
          if (i === 0) {
            await this.propertyRepository.update(property.id, {
              coverImage: uploadedFile.thumbnailUrl || uploadedFile.url,
            });
          }

          console.log(`Successfully processed image ${i + 1}/${imageUrls.length} for property ${property.id}`);
        } catch (imageError) {
          console.error(`Error processing image ${imageUrl}:`, imageError.message);
          // Continue with other images even if one fails
        }
      }
    } catch (error) {
      console.error(`Error processing Aptly images for property ${property.id}:`, error.message);
      // Don't throw - property creation should succeed even if images fail
    }
  }

  private async handleMissingProperties(
    company: Company,
    currentFeedProperties: AptlyPropertyDto[],
    result: CompanySyncResult,
  ): Promise<void> {
    try {
      // Get all current Aptly properties for this company
      const existingAptlyProperties = await this.propertyRepository.find({
        where: {
          company: { id: company.id },
          aptlyId: Not(null),
        },
        relations: ['company'],
      });

      // Extract Aptly IDs from the current feed
      const currentAptlyIds = new Set(currentFeedProperties.map((p) => p._id));

      // Find properties that are no longer in the feed
      const missingProperties = existingAptlyProperties.filter((property) => !currentAptlyIds.has(property.aptlyId));

      console.log(`Found ${missingProperties.length} properties missing from Aptly feed for company ${company.name}`);

      for (const missingProperty of missingProperties) {
        await this.handleMissingProperty(missingProperty, result);
      }
    } catch (error) {
      console.error(`Error handling missing properties for company ${company.name}:`, error.message);
    }
  }

  private async handleMissingProperty(property: Property, result: CompanySyncResult): Promise<void> {
    try {
      const isActive = property.status === PropertyStatus.LISTED && property.isAvailable;

      if (isActive) {
        // Property was active, mark as rented out
        console.log(
          `Marking active property ${property.displayName} (${property.aptlyId}) as rented out - no longer in Aptly feed`,
        );

        await this.propertyListingService.markAsRentedOut(property.id);

        result.properties.push({
          aptlyId: property.aptlyId,
          propertyName: property.displayName,
          action: 'skipped',
          reason: 'Property no longer in feed - marked as rented out',
          propertyId: property.id,
        });

        console.log(`Successfully marked property ${property.displayName} as rented out`);
      } else {
        // Property was not active, just log and ignore
        console.log(
          `Ignoring inactive property ${property.displayName} (${property.aptlyId}) - no longer in Aptly feed`,
        );

        result.properties.push({
          aptlyId: property.aptlyId,
          propertyName: property.displayName,
          action: 'skipped',
          reason: 'Property no longer in feed - was inactive, ignored',
          propertyId: property.id,
        });
      }
    } catch (error) {
      console.error(`Error handling missing property ${property.displayName}:`, error.message);

      result.properties.push({
        aptlyId: property.aptlyId,
        propertyName: property.displayName,
        action: 'error',
        error: `Failed to handle missing property: ${error.message}`,
        propertyId: property.id,
      });
    }
  }

  private async create24HourAvailabilitySlots(propertyAvailabilityId: string, timeZone: string): Promise<void> {
    const allDays = [
      DayOfTheWeekEnum.SUNDAY,
      DayOfTheWeekEnum.MONDAY,
      DayOfTheWeekEnum.TUESDAY,
      DayOfTheWeekEnum.WEDNESDAY,
      DayOfTheWeekEnum.THURSDAY,
      DayOfTheWeekEnum.FRIDAY,
      DayOfTheWeekEnum.SATURDAY,
    ];

    for (const weekday of allDays) {
      await this.availabilitySlotsService.create(
        {
          propertyAvailabilityId,
          weekday,
          startTime: '09:00',
          endTime: '20:00',
        },
        timeZone,
        propertyAvailabilityId,
      );
    }
  }
}

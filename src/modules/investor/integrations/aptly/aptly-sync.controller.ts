import { Controller, Post, Delete, UseGuards, Body, Req, Put } from '@nestjs/common';
import { ApiTags, ApiOkResponse, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { Request } from 'express';
import { AptlySyncService } from './aptly-sync.service';
import { SyncResultDto } from './models/sync-result.dto';
import { CompanyService } from '../../../shared/company/company.service';
import { CompanySettingsService } from '../../../shared/company/company-settings.service';
import { BasicAuthGuard } from '../../../shared/auth/guards/basic-auth.guard';
import { BasicAuth } from '../../../shared/auth/decorators/basic-auth.decorator';
import { HasRoles } from '../../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../../shared/auth/models/roles-enum';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { ConnectAptlyFeedDto } from '../../../shared/company/model/connect-aptly-feed.dto';

@ApiTags('aptly')
@Controller('aptly')
export class AptlySyncController {
  constructor(
    private readonly aptlySyncService: AptlySyncService,
    private readonly companyService: CompanyService,
    private readonly companySettingsService: CompanySettingsService,
  ) {}

  @Put('sync')
  @UseGuards(BasicAuthGuard)
  @BasicAuth()
  @ApiOperation({ summary: 'Trigger manual sync of all Aptly feeds' })
  @ApiOkResponse({
    description: 'Sync operation completed',
    type: Object,
  })
  async triggerSync(): Promise<SyncResultDto> {
    return this.aptlySyncService.syncAllFeeds();
  }

  @Post('connect')
  @HasRoles(Role.INVESTOR)
  @UseGuards(RolesGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Connect Aptly syndication feed' })
  async connectFeed(@Req() req: Request, @Body() body: ConnectAptlyFeedDto): Promise<void> {
    const company = await this.companyService.getCompanyByUser(req.user.id);
    const settings = company.settings;

    await this.companySettingsService.updateSettings(settings.id, {
      aptlyFeedUrl: body.feedUrl,
    });

    this.aptlySyncService.syncAllFeeds().then((result) => {
      console.log('Aptly sync completed:', result);
    });
  }

  @Delete('disconnect')
  @HasRoles(Role.INVESTOR)
  @UseGuards(RolesGuard)
  @ApiBearerAuth()
  @ApiOkResponse({ description: 'Disconnect Aptly syndication feed' })
  async disconnectFeed(@Req() req: Request): Promise<void> {
    const company = await this.companyService.getCompanyByUser(req.user.id);
    const settings = company.settings;

    await this.companySettingsService.updateSettings(settings.id, {
      aptlyFeedUrl: null,
    });
  }
}

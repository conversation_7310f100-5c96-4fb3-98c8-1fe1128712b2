import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { AptlyFeedResponse, AptlyPropertyDto } from './models/aptly-property.dto';

@Injectable()
export class AptlyFeedService {
  constructor(private readonly httpService: HttpService) {}

  async fetchFeed(feedUrl: string): Promise<AptlyPropertyDto[]> {
    try {
      console.log(`Fetching Aptly feed from: ${feedUrl}`);

      const response = await firstValueFrom(
        this.httpService.get<AptlyFeedResponse>(feedUrl, {
          timeout: 30000, // 30 second timeout
          headers: {
            'User-Agent': 'Tallo-Sync/1.0',
            Accept: 'application/json',
          },
        }),
      );

      if (!response.data || !Array.isArray(response.data)) {
        throw new HttpException('Invalid feed response: Expected array of properties', HttpStatus.BAD_REQUEST);
      }

      console.log(`Successfully fetched ${response.data.length} properties from Aptly feed`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching Aptly feed from ${feedUrl}:`, error.message);

      if (error.response) {
        throw new HttpException(
          `Failed to fetch feed: HTTP ${error.response.status} - ${error.response.statusText}`,
          HttpStatus.BAD_GATEWAY,
        );
      }

      if (error.code === 'ECONNABORTED') {
        throw new HttpException('Feed request timed out', HttpStatus.REQUEST_TIMEOUT);
      }

      throw new HttpException(`Failed to fetch feed: ${error.message}`, HttpStatus.BAD_GATEWAY);
    }
  }

  validateProperty(property: AptlyPropertyDto): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields validation
    if (!property._id) {
      errors.push('Missing required field: _id');
    }

    if (!property.name) {
      errors.push('Missing required field: name');
    }

    if (!property.address) {
      errors.push('Missing required field: address');
    } else {
      if (!property.address.formattedAddress) {
        errors.push('Missing required field: address.formattedAddress');
      }
      if (!property.address.city) {
        errors.push('Missing required field: address.city');
      }
      if (!property.address.state) {
        errors.push('Missing required field: address.state');
      }
    }

    if (typeof property.beds !== 'number' || property.beds < 0) {
      errors.push('Invalid beds value: must be a non-negative number');
    }

    if (typeof property.baths !== 'number' || property.baths < 0) {
      errors.push('Invalid baths value: must be a non-negative number');
    }

    if (!property.companyId) {
      errors.push('Missing required field: companyId');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  parseRentAmount(rentString?: string): number | null {
    if (!rentString) return null;

    // Remove currency symbols and spaces, extract number
    const cleanRent = rentString.replace(/[$,\s]/g, '');
    const rentAmount = parseFloat(cleanRent);

    return isNaN(rentAmount) ? null : rentAmount;
  }

  parseDepositAmount(depositString?: string): number | null {
    if (!depositString) return null;

    // Remove currency symbols and spaces, extract number
    const cleanDeposit = depositString.replace(/[$,\s]/g, '');
    const depositAmount = parseFloat(cleanDeposit);

    return isNaN(depositAmount) ? null : depositAmount;
  }

  parseApplicationFee(feeString?: string): number | null {
    if (!feeString) return null;

    // Remove currency symbols and spaces, extract number
    const cleanFee = feeString.replace(/[$,\s]/g, '');
    const feeAmount = parseFloat(cleanFee);

    return isNaN(feeAmount) ? null : feeAmount;
  }
}

import { Body, Controller, Get, Param, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiCreatedResponse, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { IsPropertyOwnerGuard } from '../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { Role } from '../../shared/auth/models/roles-enum';
import { DeclineShowingRequestDto } from './models/decline-showing-request.dto';
import { RescheduleShowingRequestDto } from './models/reschedule-showing-request.dto';
import { ShowingRequestDto, convertShowingRequestToDto } from './showing-request.dto';
import { ShowingRequestService } from './showing-request.service';
import { BasicAuth } from '../../shared/auth/decorators/basic-auth.decorator';
import { TimeFilter } from '../property/statistics/time-filter.enum';
import { IsCompanyEmployeeGuard } from '../../../guards/investor/is-company-employee.guard';
import { RescheduledShowingDto } from '../reschedule/model/rescheduled-showing.dto';
import { AcceptShowingRequestDto } from './dto/accept-showing-request.dto';

@ApiTags('showing-request')
@Controller('showing-request')
@ApiBearerAuth()
@UseGuards(RolesGuard)
export class ShowingRequestController {
  constructor(private readonly showingRequestService: ShowingRequestService) {}

  @HasRoles(Role.INVESTOR, Role.RENTER)
  @Get('property/:propertyId')
  @ApiOkResponse({
    description: 'Get all showing requests by property ID',
    type: ShowingRequestDto,
  })
  @UseGuards(IsPropertyOwnerGuard)
  async getAllByProperty(@Param('propertyId') propertyId: string): Promise<ShowingRequestDto[]> {
    const showingRequests = await this.showingRequestService.findAllByProperty(propertyId, false, true, false);

    return await Promise.all(showingRequests.map((showingRequest) => convertShowingRequestToDto(showingRequest, true)));
  }

  @HasRoles(Role.INVESTOR)
  @Get('company/:companyId')
  @ApiOkResponse({
    description: 'Get all showing requests by company ID',
    type: Array<ShowingRequestDto>,
  })
  @UseGuards(IsCompanyEmployeeGuard)
  async getAllByCompany(
    @Param('companyId') companyId: string,
    @Query('filter') filter: TimeFilter,
  ): Promise<ShowingRequestDto[]> {
    const showingRequests = await this.showingRequestService.findAllByCompany(companyId, filter);

    return await Promise.all(
      showingRequests.map((showingRequest) => convertShowingRequestToDto(showingRequest, true, true)),
    );
  }

  @HasRoles(Role.INVESTOR)
  @Post('accept/property/:propertyId')
  @ApiOkResponse({
    description: 'Accept showing request by property and request ID',
    type: ShowingRequestDto,
  })
  @UseGuards(IsPropertyOwnerGuard)
  async acceptShowingRequest(
    @Req() req: Request,
    @Body() payload: AcceptShowingRequestDto,
  ): Promise<ShowingRequestDto> {
    const showingRequest = await this.showingRequestService.acceptRequest(req.property.id, payload);

    return convertShowingRequestToDto(showingRequest);
  }

  @HasRoles(Role.INVESTOR)
  @Post('decline/property/:propertyId')
  @ApiOkResponse({
    description: 'Decline showing request by property and request ID',
    type: ShowingRequestDto,
  })
  @ApiBody({ type: DeclineShowingRequestDto })
  @UseGuards(IsPropertyOwnerGuard)
  async declineShowingRequest(
    @Body() body: DeclineShowingRequestDto,
    @Param('propertyId') propertyId: string,
  ): Promise<ShowingRequestDto> {
    const showingRequest = await this.showingRequestService.declineRequest(
      propertyId,
      body.showingRequestId,
      body.declineReason,
    );

    return convertShowingRequestToDto(showingRequest);
  }

  @Post('reschedule/property/:propertyId')
  @ApiCreatedResponse({
    description: 'Reschedule showing',
  })
  @UseGuards(IsPropertyOwnerGuard)
  async rescheduleShowingRequest(
    @Body() rescheduleDto: RescheduleShowingRequestDto,
    @Param('propertyId') propertyId: string,
  ): Promise<RescheduledShowingDto> {
    return await this.showingRequestService.rescheduleShowingRequest(propertyId, rescheduleDto);
  }

  @Put('mark-expired')
  @ApiOkResponse({ description: 'Mark all expired showings' })
  @BasicAuth()
  async markShowingRequestsAsExpired(): Promise<void> {
    return this.showingRequestService.markShowingRequestsAsExpired();
  }
}

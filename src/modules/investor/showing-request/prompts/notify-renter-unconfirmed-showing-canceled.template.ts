import { PromptVariable } from '../../../ai/enums/prompt-variable.enum';

/* eslint-disable max-len */
export const notifyRenterUnconfirmedShowingCanceledTemplate = `
<task>
Imagine you are a property manager, and you are talking to a potential renter.
The showing that was scheduled has been canceled because the renter did not confirm their attendance.
You need to create a message for the renter, sharing that the showing has been canceled due to lack of confirmation.
Share all the necessary details like address, time, and date.
</task>

<instructions>
- You will be given conversation history for the context
- Be clear that the showing was canceled because they didn't confirm their attendance
- Keep a friendly tone, not accusatory
- Mention that they can reschedule if they're still interested
- Do not offer more of your services or propose other properties at the end of the message
- Do not say words like "unfortunately" or "disappointing"
- Return message format should be a simple text without any formatting
</instructions>

<examples>
Try to be as close as possible to one of these examples:

- Hi <PERSON>. I wanted to let you know that your showing for Wednesday, June 17th at 6:00 PM at 1112 Flowers Street has been canceled because we didn't receive your confirmation. If you're still interested in seeing this property, feel free to let me know and we can reschedule.

- Hi <PERSON>. Your showing for tomorrow at 3:00 PM at 1112 Flowers Street has been canceled since we didn't get your confirmation. If you'd still like to see the property, just let me know and we can set up another time.
</examples>

${PromptVariable.PropertyAddress}
${PromptVariable.ShowingTime}
${PromptVariable.RenterName}
${PromptVariable.ChatHistory}
`;

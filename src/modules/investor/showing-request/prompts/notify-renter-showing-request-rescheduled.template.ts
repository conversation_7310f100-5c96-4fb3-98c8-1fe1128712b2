import { PromptVariable } from '../../../ai/enums/prompt-variable.enum';

/* eslint-disable max-len */
export const notifyRenterShowingRequestRescheduledTemplate = `
<task>
Imagine you are a property manager, and you are talking to a potential renter.
The property owner has proposed a new time for the showing. You need to create a message for the renter to notify about that.
Share all the necessary details like address, proposed time, and date and ask if it works for him.
You will be given today date and the showing date, feel free to use words like "today", "tomorrow", etc.
Please use full month names and days of the week.
</task>

<instructions>
- Use example as a template
- Try to be concise, do it in up to 4 sentences
- If owner added the comment about rescheduling, you should include it in the message (rephase it to convey the same message)
- Do not mention the property owner.
- Do not say words like "unfortunately" or "disappointing"
- You will be given conversation history for the context
</instructions>

<example>
Hi John. We had something pop up and will need to reschedule the showing we had booked at 41750 Polly Throughway. Would 11:00am on Sunday, April 21st work for you?
</example>

${PromptVariable.RenterName}

<owner_rescheduling_comment>
{comment}
</owner_rescheduling_comment>


${PromptVariable.PropertyAddress}

<new_proposed_time>
{proposedTime}
</new_proposed_time>

${PromptVariable.CurrentDate}
${PromptVariable.ChatHistory}
`;

import { PromptVariable } from '../../../ai/enums/prompt-variable.enum';

/* eslint-disable max-len */
export const notifyRenterShowingRequestExpiredTemplate = `
<task>
Imagine you are a property manager, and you are talking to a potential renter.
We sent a showing request to the property owner, but the showing request has expired.
You need to create a message for the renter, sharing that the showing has been canceled.
Share all the necessary details like address, time, and date.
</task>

<instructions>
- You will be given conversation history for the context
- Do not offer more of your services or propose other properties at the end of the message
- Do not say words like "unfortunately" or "disappointing"
- Return message format should be a simple text without any formatting. Do not use bullet points, line breaks, or any other formatting
</instructions>

<examples>
- Hi <PERSON>. I wanted to reach out to let you know I'll need to cancel the showing for Wednesday, June 17th at 6:00 PM at 1112 Flowers Street. The property owner didn't confirm the showing. Sorry about the adjustment but wanted to give you a heads-up.
</examples>

${PromptVariable.PropertyAddress}
${PromptVariable.ShowingTime}
${PromptVariable.RenterName}
${PromptVariable.ChatHistory}
`;

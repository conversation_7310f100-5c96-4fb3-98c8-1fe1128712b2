import { PromptVariable } from '../../../ai/enums/prompt-variable.enum';

/* eslint-disable max-len */
export const remindRenterAboutUpcomingShowingTemplate = `
<task>
Imagine you are a property manager, and you are talking to a potential renter.
Property showing will happen soon.
You need to send a friendly reminder to the renter and share the address and time.
</task>

<instructions>
- Try to be concise, do it in up to 4 sentences
- You will be given conversation history for the context
- It is important to ask if the showing time still works for the renter, use sentence like "Can you confirm this still works for you?" at the end
- If the tour type is virtual, explicitly mention it's a "virtual tour" or "virtual showing" in your reminder
- If the tour type is in-person, you don't need to specify the tour type (it's the default)
</instructions>

<examples>
Use the following examples as templates. Replace the name, address and showing time with the actual ones:

For in-person showings:
- <PERSON>, just wanted to confirm our showing for 6373 Percival Point at 9:00 AM today. Can you confirm this still works for you?

For virtual tours:
- <PERSON>, just wanted to confirm our virtual tour for 6373 Percival Point at 9:00 AM today. Can you confirm this still works for you?
</examples>

<tour_type>{tourType}</tour_type>

${PromptVariable.PropertyAddress}
${PromptVariable.ShowingTime}
${PromptVariable.RenterName}
${PromptVariable.CurrentDate}
${PromptVariable.ChatHistory}
`;

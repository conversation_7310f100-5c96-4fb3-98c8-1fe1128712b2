import { ShowingRequestStatus } from './enums/showing-request-status.enum';
import { RenterDto, convertToRenterToDto } from '../../renter/renter/renter.dto';
import { instanceToPlain } from 'class-transformer';
import { ShowingRequest } from './showing-request.entity';
import { PropertyDto, convertPropertyToDto } from '../property/property/model/property.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ShowingRequestDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  renter: RenterDto;

  @ApiProperty()
  status: ShowingRequestStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  declineReason?: string;

  @ApiProperty()
  property: PropertyDto;
}

export async function convertShowingRequestToDto(
  showingRequest: ShowingRequest,
  withRenter = false,
  withProperty = false,
): Promise<ShowingRequestDto> {
  const showingRequestDto = <ShowingRequestDto>instanceToPlain(showingRequest, { excludeExtraneousValues: true });

  if (withRenter) {
    showingRequestDto.renter = convertToRenterToDto(await showingRequest.renter);
  } else {
    showingRequestDto.renter = null;
  }

  if (withProperty) {
    showingRequestDto.property = await convertPropertyToDto(await showingRequest.property);
  } else {
    showingRequestDto.property = null;
  }

  return showingRequestDto;
}

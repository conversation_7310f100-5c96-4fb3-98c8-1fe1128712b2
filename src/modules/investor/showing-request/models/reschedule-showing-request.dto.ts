import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class RescheduleShowingRequestDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  showingRequestId: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  startTime: Date;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  comment: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  showingAgentId: string;
}

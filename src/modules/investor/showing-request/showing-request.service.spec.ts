import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { RenterOutboundCommsService } from '../../renter/renter-communication/outbound-communication/renter-outbound-comms.service';
import { ConversationService } from '../../shared/communication/conversation/conversation.service';
import { RentStageEnum } from '../../shared/communication/conversation/enums/rent-stage.enum';
import { FollowUpService } from '../../shared/communication/follow-up/follow-up.service';
import { OutboundCommunicationService } from '../../shared/communication/outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { PropertyAvailabilityService } from '../property/availability/property-availability.service';
import { PropertyInquirySource } from '../property-inquiry/property-inquiry-source.enum';
import { PropertyInquiry } from '../property-inquiry/property-inquiry.entity';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import { RescheduleService } from '../reschedule/reschedule.service';
import { ShowingAgentService } from '../showing-agent/showing-agent.service';
import { ShowingService } from '../showing/showing.service';
import { ShowingRequestStatus } from './enums/showing-request-status.enum';
import { ShowingRequest } from './showing-request.entity';
import { ShowingRequestService } from './showing-request.service';
import { PropertyService } from '../property/property/property.service';
import { ShowingStatus } from '../showing/enums/showing-status.enum';
import { RescheduleRequestStatus } from '../reschedule/reschedule-request.entity';
import { ShowingCalendarService } from '../showing/showing-calendar.service';

jest.mock('../reschedule/reschedule.service', () => ({
  RescheduleService: jest.fn().mockImplementation(() => ({
    update: jest.fn(),
  })),
}));

jest.mock('../property/property/property.service', () => ({
  PropertyService: jest.fn().mockImplementation(),
}));

describe('ShowingRequestService', () => {
  let service: ShowingRequestService;
  let followUpService: FollowUpService;
  let propertyInquiryService: PropertyInquiryService;
  let showingService: ShowingService;
  let rescheduleService: RescheduleService;
  let communicationService: OutboundCommunicationService;
  let conversationService: ConversationService;
  let showingCalendarService: ShowingCalendarService;

  // Mock repositories and services
  const mockShowingRequestRepository = {
    findOneOrFail: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    save: jest.fn(),
  };

  const mockPropertyService = {
    updateLastShowingAgent: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ShowingRequestService,
        {
          provide: getRepositoryToken(ShowingRequest),
          useValue: mockShowingRequestRepository,
        },
        {
          provide: PropertyService,
          useValue: mockPropertyService,
        },
        {
          provide: PropertyInquiryService,
          useValue: {
            findByRenterAndProperty: jest.fn(),
            updateStageAndAddCorrespondingEvent: jest.fn(),
          },
        },
        {
          provide: FollowUpService,
          useValue: {
            deleteInvestorFollowUpsByInquiry: jest.fn(),
            deleteShowingReminderFollowUps: jest.fn(),
            deleteRenterFollowUps: jest.fn(),
            deleteRenterFollowUpsByStatus: jest.fn(),
            createShowingReminderFollowUps: jest.fn(),
          },
        },
        {
          provide: RenterOutboundCommsService,
          useValue: {
            craftMessageAndSend: jest.fn().mockResolvedValue({ message: 'test', conversation: {} }),
            validateDeclineReasonForFairHousingViolations: jest.fn().mockResolvedValue({ safeToIncludeReason: true }),
          },
        },
        {
          provide: SlackCommunicationService,
          useValue: {
            sendMessageToConvosChannel: jest.fn(),
          },
        },
        {
          provide: ShowingService,
          useValue: {
            findById: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: RescheduleService,
          useValue: {
            update: jest.fn(),
          },
        },
        {
          provide: PropertyAvailabilityService,
          useValue: {
            findByProperty: jest.fn().mockResolvedValue({ showingDurationInMinutes: 60 }),
          },
        },
        {
          provide: ShowingAgentService,
          useValue: {},
        },
        {
          provide: ConversationService,
          useValue: {
            findByPropertyUser: jest.fn(),
          },
        },
        {
          provide: OutboundCommunicationService,
          useValue: {
            sendNewShowingRequestNotification: jest.fn(),
            sendShowingRequestRescheduledNotification: jest.fn(),
          },
        },
        {
          provide: ShowingCalendarService,
          useValue: {
            updateEvent: jest.fn(),
            deleteEvent: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ShowingRequestService>(ShowingRequestService);
    followUpService = module.get<FollowUpService>(FollowUpService);
    propertyInquiryService = module.get<PropertyInquiryService>(PropertyInquiryService);
    showingService = module.get<ShowingService>(ShowingService);
    rescheduleService = module.get<RescheduleService>(RescheduleService);
    communicationService = module.get<OutboundCommunicationService>(OutboundCommunicationService);
    conversationService = module.get<ConversationService>(ConversationService);
    showingCalendarService = module.get<ShowingCalendarService>(ShowingCalendarService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('cancelRequest', () => {
    it('should delete showing reminder follow-ups when a showing is canceled by renter', async () => {
      // Mock data
      const propertyId = 'property-id';
      const showingReqId = 'showing-req-id';
      const cancelReason = 'Test cancel reason';
      const canceledBy = ShowingRequestStatus.CANCELLED_BY_RENTER;

      const mockShowingRequest = {
        id: showingReqId,
        status: ShowingRequestStatus.ACCEPTED,
        property: Promise.resolve({ id: propertyId }),
        renter: Promise.resolve({ id: 'renter-id' }),
        propertyId: propertyId,
        renterId: 'renter-id',
        showingId: 'showing-id',
        showing: Promise.resolve({
          id: 'showing-id',
          showingRequests: Promise.resolve([]),
        }),
        declineReason: null,
        cancelReason: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      } as ShowingRequest;

      const mockInquiry = {
        id: 'inquiry-id',
        stage: RentStageEnum.SHOWING_CONFIRMED,
        property: Promise.resolve({
          id: propertyId,
          owner: Promise.resolve({ user: Promise.resolve({ id: 'owner-id' }) }),
        }),
        renter: Promise.resolve({ id: 'renter-id', user: { id: 'renter-user-id' } }),
        conversation: Promise.resolve({ id: 'conversation-id' }),
        showingRequest: Promise.resolve({ id: 'showing-request-id' }),
        events: Promise.resolve([]),
        aiObjectives: Promise.resolve([]),
        source: PropertyInquirySource.ZILLOW,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as PropertyInquiry;

      // Setup mocks
      mockShowingRequestRepository.findOneOrFail.mockResolvedValue(mockShowingRequest);
      mockShowingRequestRepository.findOne.mockResolvedValue(mockShowingRequest);
      mockShowingRequestRepository.save.mockResolvedValue(mockShowingRequest);

      // Mock findById method
      jest.spyOn(service, 'findById').mockResolvedValue(mockShowingRequest);
      jest.spyOn(propertyInquiryService, 'findByRenterAndProperty').mockResolvedValue(mockInquiry);

      // Mock showing service findById to return showing with showingRequests
      const mockShowingWithRequests = {
        id: 'showing-id',
        showingRequests: Promise.resolve([mockShowingRequest]),
      };
      (showingService.findById as jest.Mock).mockResolvedValue(mockShowingWithRequests);

      // Call the method
      await service.cancelRequest(propertyId, showingReqId, cancelReason, canceledBy);

      // Verify that deleteShowingReminderFollowUps was called
      expect(followUpService.deleteShowingReminderFollowUps).toHaveBeenCalledWith(mockInquiry);
    });

    it('should delete showing reminder follow-ups when a showing is canceled by investor', async () => {
      // Mock data
      const propertyId = 'property-id';
      const showingReqId = 'showing-req-id';
      const cancelReason = 'Test cancel reason';
      const canceledBy = ShowingRequestStatus.CANCELED_BY_INVESTOR;

      const mockShowingRequest = {
        id: showingReqId,
        status: ShowingRequestStatus.ACCEPTED,
        property: Promise.resolve({ id: propertyId }),
        renter: Promise.resolve({ id: 'renter-id', user: { id: 'renter-user-id', name: 'Test Renter' } }),
        showing: Promise.resolve({
          id: 'showing-id',
          showingRequests: Promise.resolve([]),
          property: Promise.resolve({
            location: Promise.resolve({
              address: '123 Test St',
              city: 'Test City',
              timeZone: 'America/New_York',
            }),
          }),
        }),
        propertyId: propertyId,
        renterId: 'renter-id',
        showingId: 'showing-id',
        declineReason: null,
        cancelReason: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      } as ShowingRequest;

      const mockInquiry = {
        id: 'inquiry-id',
        stage: RentStageEnum.SHOWING_CONFIRMED,
        property: Promise.resolve({
          id: propertyId,
          owner: Promise.resolve({ user: Promise.resolve({ id: 'owner-id' }) }),
        }),
        renter: Promise.resolve({ id: 'renter-id', user: { id: 'renter-user-id' } }),
        conversation: Promise.resolve({ id: 'conversation-id' }),
        showingRequest: Promise.resolve({ id: 'showing-request-id' }),
        events: Promise.resolve([]),
        aiObjectives: Promise.resolve([]),
        source: PropertyInquirySource.ZILLOW,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as PropertyInquiry;

      // Setup mocks
      mockShowingRequestRepository.findOneOrFail.mockResolvedValue(mockShowingRequest);
      mockShowingRequestRepository.findOne.mockResolvedValue(mockShowingRequest);
      mockShowingRequestRepository.save.mockResolvedValue(mockShowingRequest);

      // Mock findById method
      jest.spyOn(service, 'findById').mockResolvedValue(mockShowingRequest);
      jest.spyOn(propertyInquiryService, 'findByRenterAndProperty').mockResolvedValue(mockInquiry);

      // Mock showing service findById to return showing with showingRequests
      const mockShowingWithRequests = {
        id: 'showing-id',
        showingRequests: Promise.resolve([mockShowingRequest]),
      };
      (showingService.findById as jest.Mock).mockResolvedValue(mockShowingWithRequests);

      // Call the method
      await service.cancelRequest(propertyId, showingReqId, cancelReason, canceledBy);

      // Verify that deleteShowingReminderFollowUps was called
      expect(followUpService.deleteShowingReminderFollowUps).toHaveBeenCalledWith(mockInquiry);
    });
  });

  describe('confirmRescheduleRequest', () => {
    it('should create new calendar event when reschedule is confirmed and no calendar event exists', async () => {
      // Reset mocks
      jest.clearAllMocks();

      // Mock data
      const mockProperty = {
        id: 'property-id',
        owner: Promise.resolve({
          id: 'investor-id',
          user: Promise.resolve({
            id: 'user-id',
            name: 'Test Investor',
          }),
        }),
        location: Promise.resolve({
          address: '123 Test St',
          city: 'Test City',
          timeZone: 'America/New_York',
        }),
      };

      const mockRenter = {
        id: 'renter-id',
        user: {
          id: 'renter-user-id',
          name: 'Test Renter',
        },
      };

      const mockShowing = {
        id: 'showing-id',
        status: ShowingStatus.PENDING, // Initial status is PENDING
        startTime: new Date('2024-01-15T10:00:00Z'),
        calendarEventId: null, // No calendar event ID
      };

      const mockShowingRequest = {
        id: 'showing-request-id',
        status: ShowingRequestStatus.PENDING,
      };

      const mockRescheduleRequest = {
        id: 'reschedule-request-id',
        showing: Promise.resolve(mockShowing),
      };

      const mockInquiry = {
        id: 'inquiry-id',
        property: Promise.resolve(mockProperty),
      };

      const mockConversation = {
        id: 'conversation-id',
      };

      const showingDate = new Date('2024-01-15T10:00:00Z');

      // Setup mocks
      jest.spyOn(service, 'update').mockResolvedValue(undefined);
      (showingService.update as jest.Mock).mockResolvedValue(undefined);
      (rescheduleService.update as jest.Mock).mockResolvedValue(undefined);
      (communicationService.sendShowingRequestRescheduledNotification as jest.Mock).mockResolvedValue(undefined);
      (conversationService.findByPropertyUser as jest.Mock).mockResolvedValue(mockConversation);
      (followUpService.deleteRenterFollowUpsByStatus as jest.Mock).mockResolvedValue(undefined);
      (followUpService.createShowingReminderFollowUps as jest.Mock).mockResolvedValue(undefined);
      (followUpService.deleteInvestorFollowUpsByInquiry as jest.Mock).mockResolvedValue(undefined);

      // Execute the method
      await service.confirmRescheduleRequest(
        mockProperty as any,
        mockRenter as any,
        mockShowingRequest as any,
        showingDate,
        mockRescheduleRequest as any,
        mockInquiry as any,
      );

      // Verify that calendar event update was triggered
      expect(showingCalendarService.updateEvent).toHaveBeenCalledWith(mockShowing);

      // Verify that showing status was updated to CONFIRMED
      expect(showingService.update).toHaveBeenCalledWith('showing-id', {
        status: ShowingStatus.CONFIRMED,
      });

      // Verify that reschedule request was marked as confirmed
      expect(rescheduleService.update).toHaveBeenCalledWith('reschedule-request-id', {
        status: RescheduleRequestStatus.CONFIRMED,
      });
    });

    it('should update calendar event description when showing already has calendar event', async () => {
      // Reset mocks
      jest.clearAllMocks();

      // Mock data with showing already CONFIRMED and has calendar event
      const mockProperty = {
        id: 'property-id',
        owner: Promise.resolve({
          id: 'investor-id',
          user: Promise.resolve({
            id: 'user-id',
            name: 'Test Investor',
          }),
        }),
        location: Promise.resolve({
          address: '123 Test St',
          city: 'Test City',
          timeZone: 'America/New_York',
        }),
      };

      const mockRenter = {
        id: 'renter-id',
        user: {
          id: 'renter-user-id',
          name: 'Test Renter',
        },
      };

      const mockShowing = {
        id: 'showing-id',
        status: ShowingStatus.CONFIRMED, // Already confirmed
        startTime: new Date('2024-01-15T10:00:00Z'),
        calendarEventId: 'existing-calendar-event-id', // Has existing calendar event
      };

      const mockShowingRequest = {
        id: 'showing-request-id',
        status: ShowingRequestStatus.PENDING,
      };

      const mockRescheduleRequest = {
        id: 'reschedule-request-id',
        showing: Promise.resolve(mockShowing),
      };

      const mockInquiry = {
        id: 'inquiry-id',
        property: Promise.resolve(mockProperty),
      };

      const mockConversation = {
        id: 'conversation-id',
      };

      const showingDate = new Date('2024-01-15T10:00:00Z');

      // Setup mocks
      jest.spyOn(service, 'update').mockResolvedValue(undefined);
      (showingService.update as jest.Mock).mockResolvedValue(undefined);
      (rescheduleService.update as jest.Mock).mockResolvedValue(undefined);
      (communicationService.sendShowingRequestRescheduledNotification as jest.Mock).mockResolvedValue(undefined);
      (conversationService.findByPropertyUser as jest.Mock).mockResolvedValue(mockConversation);
      (followUpService.deleteRenterFollowUpsByStatus as jest.Mock).mockResolvedValue(undefined);
      (followUpService.createShowingReminderFollowUps as jest.Mock).mockResolvedValue(undefined);
      (followUpService.deleteInvestorFollowUpsByInquiry as jest.Mock).mockResolvedValue(undefined);

      // Execute the method
      await service.confirmRescheduleRequest(
        mockProperty as any,
        mockRenter as any,
        mockShowingRequest as any,
        showingDate,
        mockRescheduleRequest as any,
        mockInquiry as any,
      );

      // Verify that calendar event update was triggered
      expect(showingCalendarService.updateEvent).toHaveBeenCalledWith(mockShowing);

      // Verify that showing status update was not called since it was already CONFIRMED
      expect(showingService.update).not.toHaveBeenCalledWith('showing-id', {
        status: ShowingStatus.CONFIRMED,
      });
    });
  });
});

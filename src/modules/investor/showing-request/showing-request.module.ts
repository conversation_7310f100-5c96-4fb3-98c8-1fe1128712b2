import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RenterOutboundCommsModule } from '../../renter/renter-communication/outbound-communication/renter-outbound-comms.module';
import { FollowUpModule } from '../../shared/communication/follow-up/follow-up.module';
import { OutboundCommunicationModule } from '../../shared/communication/outbound-communication/outbound-communication.module';
import { CompanyModule } from '../../shared/company/company.module';
import { AvailabilityModule } from '../availability/availability.module';
import { PropertyAvailabilityModule } from '../property/availability/property-availability-module';
import { PropertyInquiryModule } from '../property-inquiry/property-inquiry.module';
import { Property } from '../property/property/entities/property.entity';
import { RescheduleModule } from '../reschedule/reschedule.module';
import { ShowingModule } from '../showing/showing.module';
import { ShowingRequestController } from './showing-request.controller';
import { ShowingRequest } from './showing-request.entity';
import { ShowingRequestService } from './showing-request.service';
import { Company } from '../../shared/company/entities/company.entity';
import { ShowingAgentModule } from '../showing-agent/showing-agent.module';
import { ConversationModule } from '../../shared/communication/conversation/conversetion.module';
import { PropertyModule } from '../property/property/property.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ShowingRequest, Property, Company]),
    forwardRef(() => ShowingModule),
    forwardRef(() => ShowingAgentModule),
    forwardRef(() => FollowUpModule),
    forwardRef(() => PropertyModule),
    OutboundCommunicationModule,
    CompanyModule,
    RenterOutboundCommsModule,
    forwardRef(() => RescheduleModule),
    AvailabilityModule,
    PropertyAvailabilityModule,
    PropertyInquiryModule,
    ConversationModule,
  ],
  providers: [ShowingRequestService],
  controllers: [ShowingRequestController],
  exports: [ShowingRequestService],
})
export class ShowingRequestModule {}

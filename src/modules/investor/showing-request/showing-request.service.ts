import { addMinutes } from 'date-fns';
import { <PERSON><PERSON>han, Repository } from 'typeorm';

import { ForbiddenException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { DateUtils } from '../../../utils/date.utils';
import { FilterUtils } from '../../../utils/filter.utils';
import { TimezoneUtils } from '../../../utils/timezone.utils';
import { RenterOutboundCommsService } from '../../renter/renter-communication/outbound-communication/renter-outbound-comms.service';
import { Renter } from '../../renter/renter/renter.entity';
import { ConversationService } from '../../shared/communication/conversation/conversation.service';
import { RentStageEnum } from '../../shared/communication/conversation/enums/rent-stage.enum';
import { FollowUpTypeEnum } from '../../shared/communication/follow-up/enums/follow-up-type.enum';
import { FollowUpService } from '../../shared/communication/follow-up/follow-up.service';
import { OutboundCommunicationService } from '../../shared/communication/outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../shared/communication/outbound-communication/slack/slack-convo-message-builder.class';
import { PropertyAvailabilityService } from '../property/availability/property-availability.service';
import { PropertyInquiryEventTypeEnum } from '../property-inquiry/events/property-inquiry-event-type.enum';
import { PropertyInquiry } from '../property-inquiry/property-inquiry.entity';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import { Property } from '../property/property/entities/property.entity';
import { TimeFilter } from '../property/statistics/time-filter.enum';
import { RescheduledShowingDto } from '../reschedule/model/rescheduled-showing.dto';
import { RescheduleRequest, RescheduleRequestStatus } from '../reschedule/reschedule-request.entity';
import { RescheduleService } from '../reschedule/reschedule.service';
import { ShowingAgent } from '../showing-agent/entities/showing-agent.entity';
import { ShowingAgentService } from '../showing-agent/showing-agent.service';
import { ShowingStatus } from '../showing/enums/showing-status.enum';
import { Showing } from '../showing/showing.entity';
import { ShowingService } from '../showing/showing.service';
import { AcceptShowingRequestDto } from './dto/accept-showing-request.dto';
import { ShowingRequestStatus } from './enums/showing-request-status.enum';
import { RescheduleShowingRequestDto } from './models/reschedule-showing-request.dto';
import { notifyRenterShowingRequestCanceledTemplate } from './prompts/notify-renter-showing-request-canceled.template';
import { notifyRenterShowingRequestConfirmed } from './prompts/notify-renter-showing-request-confirmed.template';
import { notifyRenterShowingRequestDeclinedTemplate } from './prompts/notify-renter-showing-request-declined.template';
import { notifyRenterShowingRequestExpiredTemplate } from './prompts/notify-renter-showing-request-expired.template';
import { notifyRenterShowingRequestRescheduledTemplate } from './prompts/notify-renter-showing-request-rescheduled.template';
import { remindRenterAboutUpcomingShowingTemplate } from './prompts/remind-renter-about-upcoming-showing.template';
import { ShowingRequest } from './showing-request.entity';
import { LanguageModelsEnum } from '../../ai/enums/language-models.enum';
import { PropertyService } from '../property/property/property.service';
import { ShowingCalendarService } from '../showing/showing-calendar.service';
import { TourType } from '../showing/enums/tour-type.enum';
import { calculateShowingEndTime } from '../showing/utils/calculate-showing-end-time.util';

@Injectable()
export class ShowingRequestService {
  constructor(
    @InjectRepository(ShowingRequest)
    private readonly showingReqRepository: Repository<ShowingRequest>,
    @Inject(forwardRef(() => ShowingService))
    private readonly showingService: ShowingService,
    @Inject(forwardRef(() => PropertyService))
    private readonly propertyService: PropertyService,
    private readonly showingAgentService: ShowingAgentService,
    private readonly rescheduleService: RescheduleService,
    private readonly communicationService: OutboundCommunicationService,
    private readonly renterOutboundCommsService: RenterOutboundCommsService,
    private readonly propertyAvailabilityService: PropertyAvailabilityService,
    private readonly slackCommunicationService: SlackCommunicationService,
    private readonly followUpService: FollowUpService,
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly conversationService: ConversationService,
    private readonly showingCalendarService: ShowingCalendarService,
  ) {}

  async sendNewRequestToPropertyOwner(
    property: Property,
    renter: Renter,
    showingRequest: ShowingRequest,
    startTimeUtc: Date,
    inquiry: PropertyInquiry,
    tourType: TourType,
  ): Promise<ShowingRequest> {
    const propertyLocation = await property.location;
    const investor = await property.owner;
    const { showingDurationInMinutes } = await this.propertyAvailabilityService.findByProperty(property.id);
    const endTimeUtc = addMinutes(startTimeUtc, showingDurationInMinutes);
    const isInPersonTour = tourType === TourType.IN_PERSON;

    const showings = await this.showingService.findShowingsOverlappingWithTimeRange(
      property.id,
      startTimeUtc,
      endTimeUtc,
    );

    let showing: Showing;

    if (isInPersonTour && showings.length > 0) {
      showing = showings[0];
    } else {
      showing = await this.showingService.create({
        property,
        tourType,
        startTime: startTimeUtc,
        endTime: endTimeUtc,
        status: ShowingStatus.PENDING,
      });
    }

    showingRequest.showing = showing;
    showingRequest.status = ShowingRequestStatus.PENDING;

    await this.showingReqRepository.save(showingRequest);

    const investorUser = await investor.user;

    await this.communicationService.sendNewShowingRequestNotification(
      property,
      propertyLocation,
      investorUser,
      TimezoneUtils.convertDateToStringInCityTz(startTimeUtc, propertyLocation.city, propertyLocation.timeZone),
      renter,
      investorUser.preferredCommunicationChannel,
      showing.tourType,
    );

    // 6 hours before the showing time
    const followUpTime = addMinutes(startTimeUtc, -360);

    await this.followUpService.createInvestorFollowUps(
      investorUser,
      FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
      {
        followUpsSchedule: [followUpTime],
      },
      inquiry,
    );

    return showingRequest;
  }

  async confirmRescheduleRequest(
    property: Property,
    renter: Renter,
    showingRequest: ShowingRequest,
    showingDate: Date,
    rescheduleRequest: RescheduleRequest,
    inquiry: PropertyInquiry,
  ): Promise<void> {
    const showing = await rescheduleRequest.showing;
    showingRequest.status = ShowingRequestStatus.ACCEPTED;

    await this.update(showingRequest.id, {
      showing,
      status: showingRequest.status,
    });

    if (showing.status === ShowingStatus.PENDING) {
      showing.status = ShowingStatus.CONFIRMED;
      await this.showingService.update(showing.id, {
        status: showing.status,
      });
    }

    rescheduleRequest.status = RescheduleRequestStatus.CONFIRMED;
    await this.rescheduleService.update(rescheduleRequest.id, {
      status: rescheduleRequest.status,
    });

    const investor = await property.owner;
    const investorUser = await investor.user;

    const propertyLocation = await property.location;

    await this.communicationService.sendShowingRequestRescheduledNotification(
      showing.id,
      propertyLocation,
      investorUser,
      TimezoneUtils.convertDateToStringInCityTz(showingDate, propertyLocation.city, propertyLocation.timeZone),
      renter.user.name,
    );

    // Get the conversation to create follow-ups
    const conversation = await this.conversationService.findByPropertyUser(property.id, renter.user.id);

    if (conversation) {
      // Delete existing renter follow-ups
      await this.followUpService.deleteRenterFollowUpsByStatus(conversation.id);

      // Create showing reminder follow-ups
      await this.followUpService.createShowingReminderFollowUps(conversation, inquiry, showing.startTime);
    }

    await this.followUpService.deleteInvestorFollowUpsByInquiry(
      inquiry,
      investorUser.id,
      FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
    );

    this.showingCalendarService.updateEvent(showing);
  }

  async declinedRescheduleRequest(
    property: Property,
    renter: Renter,
    showingRequest: ShowingRequest,
    startTimeUtc: Date,
    rescheduleRequest: RescheduleRequest,
    inquiry: PropertyInquiry,
    tourType: TourType,
  ): Promise<void> {
    await this.rescheduleService.update(rescheduleRequest.id, {
      status: RescheduleRequestStatus.DECLINED,
    });

    const oldShowing = await rescheduleRequest.showing;
    await this.showingService.updateStatusBasedOnRequests(oldShowing);

    await this.sendNewRequestToPropertyOwner(property, renter, showingRequest, startTimeUtc, inquiry, tourType);
  }

  async create(showingRequestData: Partial<ShowingRequest> = {}): Promise<ShowingRequest> {
    const showingRequest: ShowingRequest = this.showingReqRepository.create();
    Object.assign(showingRequest, showingRequestData);

    return this.showingReqRepository.save(showingRequest);
  }

  async findById(id: string, withRelations = false): Promise<ShowingRequest> {
    return this.showingReqRepository.findOne({
      where: { id },
      relations: withRelations ? ['showing'] : [],
    });
  }

  async findByRenterAndProperty(renterId: string, propertyId: string, withRenter?: boolean): Promise<ShowingRequest> {
    const relations = [];

    if (withRenter) {
      relations.push('renter');
    }

    return this.showingReqRepository.findOneOrFail({
      where: {
        renter: { id: renterId },
        property: { id: propertyId },
      },
      relations,
    });
  }

  async findAllByProperty(
    propertyId: string,
    withRenter = false,
    withProperty = false,
    withShowing = false,
    status?: ShowingRequestStatus,
  ): Promise<ShowingRequest[]> {
    const relations = [];

    if (withRenter) {
      relations.push('renter');
    }

    if (withProperty) {
      relations.push('property');
    }

    if (withShowing) {
      relations.push('showing');
    }

    return this.showingReqRepository.find({
      where: {
        property: { id: propertyId },
        ...(status ? { status } : {}),
      },
      relations,
    });
  }

  async findAllByCompany(companyId: string, filter = TimeFilter.ALL_TIME): Promise<ShowingRequest[]> {
    const startDate = FilterUtils.getStartDateByFilter(filter);

    return this.showingReqRepository
      .createQueryBuilder('showingRequest')
      .leftJoinAndSelect('showingRequest.property', 'property')
      .leftJoinAndSelect('showingRequest.renter', 'renter')
      .leftJoinAndSelect('renter.user', 'user')
      .leftJoin('property.company', 'company')
      .where('company.id = :companyId', { companyId })
      .andWhere('showingRequest.createdAt >= :startDate', { startDate })
      .getMany();
  }

  async acceptRequest(propertyId: string, acceptShowingRequestDto: AcceptShowingRequestDto): Promise<ShowingRequest> {
    const showingRequest = await this.findById(acceptShowingRequestDto.showingRequestId, false);

    if (showingRequest.propertyId !== propertyId) {
      throw new ForbiddenException('This showing request is not for this property');
    }

    const showing = await showingRequest.showing;
    const showingAgentId = acceptShowingRequestDto.showingAgentId ?? showing.showingAgentId;

    await this.showingService.update(showing.id, {
      status: ShowingStatus.CONFIRMED,
      showingAgent: {
        id: showingAgentId,
      } as ShowingAgent,
    });

    await this.propertyService.updateLastShowingAgent(showing.propertyId, showingAgentId);

    const { address, city, timeZone } = await (await showing.property).location;

    showingRequest.status = ShowingRequestStatus.ACCEPTED;

    const renterId = showingRequest.renterId;
    const inquiry = await this.propertyInquiryService.findByRenterAndProperty(renterId, propertyId);
    await this.propertyInquiryService.updateStageAndAddCorrespondingEvent(inquiry.id, RentStageEnum.SHOWING_CONFIRMED);

    const renter = await showingRequest.renter;

    const currentDateInCityUtc = TimezoneUtils.getCurrentCityDate(city, timeZone);
    const currentDate = DateUtils.toAiReadableFormat(currentDateInCityUtc);
    const tomorrowDateInCityInAiReadableFormat = DateUtils.getNextDayFormatted(currentDateInCityUtc);
    const showingAgent = showingAgentId ? await this.showingAgentService.findOne(showingAgentId) : null;

    this.renterOutboundCommsService
      .craftMessageAndSend({
        renter,
        propertyId,
        templateVariables: {
          propertyAddress: address,
          currentDate,
          showingTime: TimezoneUtils.convertDateToStringInCityTz(showing.startTime, city, timeZone),
          tomorrowDate: tomorrowDateInCityInAiReadableFormat,
          showingAgent: {
            name: showingAgent?.firstName,
            phone: showingAgent?.phone,
          },
          tourType: showing.tourType,
        },
        template: notifyRenterShowingRequestConfirmed,
      })
      .then(async ({ message, conversation }) => {
        this.slackCommunicationService.sendMessageToConvosChannel(
          new SlackConvoMessageBuilder()
            .appendTextLine('🎉 Showing request accepted')
            .appendEmptyLine()
            .appendTalloMessage(message)
            .build(),
          conversation,
        );

        await this.followUpService.deleteRenterFollowUpsByStatus(conversation.id);
        await this.followUpService.createShowingReminderFollowUps(conversation, inquiry, showing.startTime);
      });

    const property = await inquiry.property;
    const investor = await property.owner;

    this.followUpService.deleteInvestorFollowUpsByInquiry(
      inquiry,
      (await investor.user).id,
      FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
    );

    this.showingCalendarService.updateEvent(showing);

    return this.showingReqRepository.save(showingRequest);
  }

  async cancelRequest(
    propertyId: string,
    showingReqId: string,
    cancelReason: string,
    canceledBy: ShowingRequestStatus.CANCELED_BY_INVESTOR | ShowingRequestStatus.CANCELLED_BY_RENTER,
  ): Promise<ShowingRequest> {
    const showingRequest = await this.findById(showingReqId);

    if ((await showingRequest.property).id !== propertyId) {
      throw new Error('This showing request is not for this property');
    }

    let safeToIncludeReason = false;

    if (cancelReason && canceledBy === ShowingRequestStatus.CANCELED_BY_INVESTOR) {
      const output = await this.renterOutboundCommsService.validateDeclineReasonForFairHousingViolations(cancelReason);
      safeToIncludeReason = output.safeToIncludeReason;
    }

    showingRequest.status = canceledBy;
    showingRequest.cancelReason = cancelReason || null;

    await this.showingReqRepository.update(showingReqId, {
      cancelReason: showingRequest.cancelReason,
      status: showingRequest.status,
    });

    const renter = await showingRequest.renter;
    const inquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, propertyId);

    if (canceledBy === ShowingRequestStatus.CANCELED_BY_INVESTOR) {
      await this.propertyInquiryService.updateStageAndAddCorrespondingEvent(
        inquiry.id,
        RentStageEnum.SHOWING_CANCELLED_BY_OWNER,
      );

      const showing: Showing = await showingRequest.showing;
      const { address, city, timeZone } = await (await showing.property).location;

      this.renterOutboundCommsService
        .craftMessageAndSend({
          renter,
          propertyId,
          templateVariables: {
            propertyAddress: address,
            showingTime: TimezoneUtils.convertDateToStringInCityTz(showing.startTime, city, timeZone),
            cancelReason: cancelReason && safeToIncludeReason ? cancelReason : 'Not specified',
            renterName: renter.user.name.split(' ')[0],
          },
          template: notifyRenterShowingRequestCanceledTemplate,
          model: LanguageModelsEnum.CLAUDE_4_SONNET,
        })
        .then(({ message, conversation }) => {
          this.slackCommunicationService.sendMessageToConvosChannel(
            new SlackConvoMessageBuilder()
              .appendTextLine('❌ Showing request was canceled by the owner')
              .appendEmptyLine()
              .appendOwnerMessageAsBullet(cancelReason || 'Not specified')
              .appendTalloMessageAsBullet(message)
              .build(),
            conversation,
          );
          this.followUpService.deleteRenterFollowUpsByStatus(conversation.id);
        });
    } else {
      await this.propertyInquiryService.updateStageAndAddCorrespondingEvent(
        inquiry.id,
        RentStageEnum.SHOWING_CANCELLED_BY_RENTER,
      );
    }

    await this.followUpService.deleteShowingReminderFollowUps(inquiry);

    return showingRequest;
  }

  async declineRequest(
    propertyId: string,
    showingReqId: string,
    declineReason?: string,
    updateShowingStatus = true,
  ): Promise<ShowingRequest> {
    const showingRequest = await this.findById(showingReqId);

    if ((await showingRequest.property).id !== propertyId) {
      throw new Error('This showing request is not for this property');
    }

    let safeToIncludeReason = false;

    if (declineReason) {
      const output = await this.renterOutboundCommsService.validateDeclineReasonForFairHousingViolations(declineReason);
      safeToIncludeReason = output.safeToIncludeReason;
    }

    showingRequest.status = ShowingRequestStatus.DECLINED;
    showingRequest.declineReason = declineReason || null;

    const renter = await showingRequest.renter;

    await this.showingReqRepository.update(showingReqId, {
      declineReason: showingRequest.declineReason,
      status: showingRequest.status,
    });

    const inquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, propertyId);
    await this.propertyInquiryService.updateStageAndAddCorrespondingEvent(inquiry.id, RentStageEnum.SHOWING_DECLINED);

    const showing: Showing = await showingRequest.showing;

    if (updateShowingStatus) {
      await this.showingService.updateStatusBasedOnRequests(showing);
    }

    const { city, timeZone, address } = await (await showing.property).location;

    this.renterOutboundCommsService
      .craftMessageAndSend({
        renter,
        propertyId,
        templateVariables: {
          propertyAddress: address,
          showingTime: TimezoneUtils.convertDateToStringInCityTz(showing.startTime, city, timeZone),
          renterName: renter.user.name.split(' ')[0],
          declineReason: declineReason && safeToIncludeReason ? declineReason : 'Not specified',
        },
        model: LanguageModelsEnum.CLAUDE_4_SONNET,
        template: notifyRenterShowingRequestDeclinedTemplate,
      })
      .then(({ message, conversation }) => {
        this.slackCommunicationService.sendMessageToConvosChannel(
          new SlackConvoMessageBuilder()
            .appendTextLine('⛔️ Showing request was declined by the owner')
            .appendEmptyLine()
            .appendOwnerMessageAsBullet(declineReason || 'Not specified')
            .appendTalloMessageAsBullet(message)
            .build(),
          conversation,
        );
        this.followUpService.deleteRenterFollowUpsByStatus(conversation.id);
      });

    const property = await inquiry.property;
    const investor = await property.owner;

    this.followUpService.deleteInvestorFollowUpsByInquiry(
      inquiry,
      (await investor.user).id,
      FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
    );

    return showingRequest;
  }

  async rescheduleShowingRequest(
    propertyId: string,
    rescheduleDto: RescheduleShowingRequestDto,
    updateShowingStatus = true,
  ): Promise<RescheduledShowingDto> {
    try {
      const startTimeDate = new Date(rescheduleDto.startTime);

      console.log(
        '[Owner reschedules data] [FE request]',
        JSON.stringify({
          proposedStartTime: startTimeDate.toISOString() || 'nothing',
          proposedStartTimeGet: startTimeDate.getTime() || 'nothing',
          comment: rescheduleDto?.comment || 'no comment',
        }),
      );
    } catch (error) {
      console.log('Error logging reschedule data', error);
    }

    const showingRequest = await this.findById(rescheduleDto.showingRequestId);

    if (showingRequest.propertyId !== propertyId) {
      throw new ForbiddenException('This showing request is not for this property');
    }

    const renter = await showingRequest.renter;
    const inquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, propertyId);
    await this.propertyInquiryService.updateStageAndAddCorrespondingEvent(
      inquiry.id,
      RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER,
    );

    await this.showingReqRepository.update(rescheduleDto.showingRequestId, {
      status: ShowingRequestStatus.RESCHEDULED,
    });

    const oldShowing = await showingRequest.showing;

    if (updateShowingStatus) {
      await this.showingService.updateStatusBasedOnRequests(oldShowing);
    }

    const showingAgentId = rescheduleDto.showingAgentId ?? oldShowing.showingAgentId;
    const property = await showingRequest.property;
    const newShowing = await this.rescheduleService.createRescheduleRequest(
      property,
      await showingRequest.renter,
      rescheduleDto.startTime,
      await calculateShowingEndTime(property, rescheduleDto.startTime),
      rescheduleDto.comment,
      showingAgentId,
      oldShowing.tourType, // for owner initiated reschedule, we keep the same tour type selected by the renter
    );

    await this.propertyService.updateLastShowingAgent(showingRequest.propertyId, showingAgentId);

    const { city, timeZone, address } = await property.location;

    this.renterOutboundCommsService
      .craftMessageAndSend({
        renter,
        propertyId,
        templateVariables: {
          comment: rescheduleDto.comment,
          propertyAddress: address,
          currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone),
          proposedTime: TimezoneUtils.convertDateToStringInCityTz(rescheduleDto.startTime, city, timeZone),
          renterName: renter.user.name.split(' ')[0],
        },
        template: notifyRenterShowingRequestRescheduledTemplate,
      })
      .then(({ message, conversation }) => {
        this.slackCommunicationService.sendMessageToConvosChannel(
          new SlackConvoMessageBuilder()
            .appendTextLine('🕟 Owner reschedules showing request')
            .appendEmptyLine()
            .appendOwnerMessageAsBullet(rescheduleDto.comment || 'Not specified')
            .appendTalloMessageAsBullet(message)
            .build(),
          conversation,
        );
      });

    const investor = await property.owner;
    this.followUpService.deleteInvestorFollowUpsByInquiry(
      inquiry,
      (await investor.user).id,
      FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
    );

    return newShowing;
  }

  async markShowingRequestsAsExpired(): Promise<void> {
    // TODO add logic to mark reschedule requests too
    // find all showing requests that expire in 2 hours
    // current UTC date + 2 hours
    const deadline = new Date(new Date().getTime() + 120 * 60 * 1000);
    const showingRequests = await this.showingReqRepository.find({
      where: {
        status: ShowingRequestStatus.PENDING,
        showing: {
          startTime: LessThan(deadline),
        },
      },
      relations: ['showing', 'property', 'renter'],
    });

    showingRequests.map(async (showingRequest: ShowingRequest) => {
      const property = await showingRequest.property;
      const renter = await showingRequest.renter;
      const inquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, property.id, true, true);
      const showing = await showingRequest.showing;

      await this.propertyInquiryService.updateStageAndAddCorrespondingEvent(
        inquiry.id,
        RentStageEnum.SHOWING_REQUEST_IGNORED_BY_OWNER,
      );

      await this.update(showingRequest.id, {
        status: ShowingRequestStatus.EXPIRED,
      });

      await this.showingService.updateStatusBasedOnRequests(await showingRequest.showing);

      const { address, city, timeZone } = await property.location;

      this.renterOutboundCommsService
        .craftMessageAndSend({
          renter,
          propertyId: property.id,
          templateVariables: {
            propertyAddress: address,
            showingTime: TimezoneUtils.convertDateToStringInCityTz(showing.startTime, city, timeZone),
            renterName: renter.user.name.split(' ')[0],
          },
          template: notifyRenterShowingRequestExpiredTemplate,
        })
        .then(({ message, conversation }) => {
          this.slackCommunicationService.sendMessageToConvosChannel(
            new SlackConvoMessageBuilder()
              .appendTextLine('❌ Showing request was ignored by the owner')
              .appendEmptyLine()
              .appendTalloMessage(message)
              .build(),
            conversation,
          );
        });

      const owner = await property.owner;
      const ownerUser = await owner.user;

      this.communicationService.sendPendingShowingRequestExpiredNotification(ownerUser, property, renter);
    });
  }

  async sendRenterShowingReminderNotification(showingRequest: ShowingRequest): Promise<void> {
    const property = await showingRequest.property;
    const { address, city, timeZone } = await property.location;
    const renter = await showingRequest.renter;
    const inquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, property.id, true, true);
    const events = (await inquiry.events).map((event) => event.type);

    if (events.includes(PropertyInquiryEventTypeEnum.SHOWING_REMINDER_SENT)) {
      return;
    } else {
      this.propertyInquiryService.addEvent(inquiry.id, PropertyInquiryEventTypeEnum.SHOWING_REMINDER_SENT).then();
    }

    const showing = await showingRequest.showing;

    this.renterOutboundCommsService
      .craftMessageAndSend({
        renter,
        propertyId: property.id,
        templateVariables: {
          renterName: renter.user.name,
          propertyAddress: address,
          showingTime: TimezoneUtils.convertDateToStringInCityTz(showing.startTime, city, timeZone),
          currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone),
          tourType: showing.tourType,
        },
        template: remindRenterAboutUpcomingShowingTemplate,
        numberOfHistoryMessages: 1,
      })
      .then(({ message, conversation }) => {
        this.slackCommunicationService.sendMessageToConvosChannel(
          new SlackConvoMessageBuilder()
            .appendTextLine('🔔 Showing reminders sent')
            .appendEmptyLine()
            .appendAuthoredBulletItem('Owner reminder', 'Sent from a Brevo template')
            .appendAuthoredBulletItem('Renter reminder', message)
            .build(),
          conversation,
        );
      });
  }

  async update(id: string, showingRequestData: Partial<ShowingRequest>): Promise<void> {
    await this.showingReqRepository.update(id, showingRequestData);
  }

  async save(showingRequest: ShowingRequest[]): Promise<ShowingRequest[]> {
    return this.showingReqRepository.save(showingRequest);
  }

  async delete(id: string): Promise<void> {
    await this.showingReqRepository.softDelete(id);
  }
}

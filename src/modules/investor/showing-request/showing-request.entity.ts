import { Expose } from 'class-transformer';
import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
} from 'typeorm';

import { Renter } from '../../renter/renter/renter.entity';
import { Property } from '../property/property/entities/property.entity';
import { Showing } from '../showing/showing.entity';
import { ShowingRequestStatus } from './enums/showing-request-status.enum';

@Entity()
export class ShowingRequest {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Expose()
  @ManyToOne(() => Renter, (renter) => renter.showingRequests, {
    lazy: true,
    cascade: false,
  })
  renter: Promise<Renter> | Renter;

  @RelationId((showingRequest: ShowingRequest) => showingRequest.renter)
  renterId: string;

  @ManyToOne(() => Property, (property) => property.showingRequests, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @RelationId((showingRequest: ShowingRequest) => showingRequest.property)
  propertyId: string;

  @ManyToOne(() => Showing, (showing) => showing.showingRequests, {
    nullable: true,
    lazy: true,
  })
  showing: Promise<Showing> | Showing;

  @RelationId((showingRequest: ShowingRequest) => showingRequest.showing)
  showingId: string;

  @Expose()
  @Column({
    type: 'enum',
    enum: ShowingRequestStatus,
    default: ShowingRequestStatus.PENDING,
  })
  status: ShowingRequestStatus;

  @DeleteDateColumn()
  deletedAt?: Date;

  @Expose()
  @Column({ type: 'text', nullable: true })
  declineReason: string;

  @Expose()
  @Column({ type: 'text', nullable: true })
  cancelReason: string;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PropertyAvailability } from './property-availability.entity';

@Injectable()
export class PropertyAvailabilityService {
  constructor(
    @InjectRepository(PropertyAvailability)
    private readonly propertyAvailabilityRepository: Repository<PropertyAvailability>,
  ) {}

  async findById(propertyAvailabilityId: string): Promise<PropertyAvailability> {
    return this.propertyAvailabilityRepository.findOneOrFail({
      where: { id: propertyAvailabilityId },
    });
  }

  async findByProperty(propertyId: string): Promise<PropertyAvailability> {
    return this.propertyAvailabilityRepository.findOneOrFail({
      where: {
        property: {
          id: propertyId,
        },
      },
      relations: ['availabilitySlots'],
    });
  }

  async create(data: Partial<PropertyAvailability>): Promise<PropertyAvailability> {
    const propertyAvailability = this.propertyAvailabilityRepository.create();

    Object.assign(propertyAvailability, data);

    return this.propertyAvailabilityRepository.save(propertyAvailability);
  }

  async updateShowingDuration(propertyAvailabilityId: string, durationInMinutes: number): Promise<void> {
    await this.propertyAvailabilityRepository.update(propertyAvailabilityId, {
      showingDurationInMinutes: durationInMinutes,
    });
  }

  async findByPropertyId(propertyId: string): Promise<PropertyAvailability | null> {
    return this.propertyAvailabilityRepository.findOne({
      where: {
        property: {
          id: propertyId,
        },
      },
    });
  }
}

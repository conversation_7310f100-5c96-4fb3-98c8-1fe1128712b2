import { Expose, instanceToPlain } from 'class-transformer';
import { Column, Entity, OneToMany, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { PropertyAvailabilityDto } from './models/property-availability.dto';
import { Property } from '../property/entities/property.entity';
import { AvailabilitySlot } from '../../availability/availability-slot.entity';

@Entity('property_availability')
export class PropertyAvailability {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Expose()
  @Column({ type: 'int', nullable: false, default: 15 })
  showingDurationInMinutes: number;

  @Expose()
  @OneToMany(() => AvailabilitySlot, (availability) => availability.propertyAvailability, {
    lazy: true,
  })
  availabilitySlots: Promise<AvailabilitySlot[]> | AvailabilitySlot[];

  @OneToOne(() => Property, (property) => property.availability, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  public static async convertToDto(
    propertyAvailability: PropertyAvailability,
    userTimeZone: string,
  ): Promise<PropertyAvailabilityDto> {
    if (!propertyAvailability) {
      return null;
    }

    const propertyAvailabilityDto = <PropertyAvailabilityDto>(
      instanceToPlain(propertyAvailability, { excludeExtraneousValues: true })
    );

    const availabilitySlots = await propertyAvailability.availabilitySlots;

    propertyAvailabilityDto.availabilitySlots = await Promise.all(
      availabilitySlots.map(async (availability) => AvailabilitySlot.convertToDto(availability, userTimeZone)),
    );

    return propertyAvailabilityDto;
  }
}

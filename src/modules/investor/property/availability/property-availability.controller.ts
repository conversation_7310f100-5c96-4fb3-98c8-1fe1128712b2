import { Body, Controller, Get, Headers, Param, <PERSON>, Req, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { PropertyAvailability } from './property-availability.entity';
import { PropertyAvailabilityService } from './property-availability.service';
import { PropertyAvailabilityDto } from './models/property-availability.dto';
import { UpdateShowingDuration } from './models/update-showing-duration.dto';
import { HasRoles } from '../../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../../shared/auth/models/roles-enum';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { AppHeaders } from '../../../../shared/const/app-headers.const';
import { IsPropertyOwnerGuard } from '../../../../guards/investor/is-property-owner-guard';
import { IsPropertyAvailabilityOwnerGuard } from '../../../../guards/investor/is-property-availability-owner-guard.service';

@ApiTags('property-availability')
@Controller('property-availability')
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
export class PropertyAvailabilityController {
  constructor(private readonly propertyAvailabilityId: PropertyAvailabilityService) {}

  @Get(':propertyId')
  @ApiOkResponse({
    description: 'Get investor availability',
    type: PropertyAvailabilityDto,
  })
  @UseGuards(IsPropertyOwnerGuard)
  async getAvailability(
    @Req() req: Request,
    @Headers(AppHeaders.ClienTimeZone) userTimeZone: string,
  ): Promise<PropertyAvailabilityDto> {
    const investorAvailability = await this.propertyAvailabilityId.findByProperty(req.params.propertyId);

    return PropertyAvailability.convertToDto(investorAvailability, userTimeZone);
  }

  @Patch(':propertyAvailabilityId/showing-duration')
  @ApiOkResponse({
    description: 'Update showing duration',
  })
  @UseGuards(IsPropertyAvailabilityOwnerGuard)
  async updateShowingDuration(
    @Param('propertyAvailabilityId') propertyAvailabilityId: string,
    @Body() dto: UpdateShowingDuration,
  ): Promise<void> {
    await this.propertyAvailabilityId.updateShowingDuration(propertyAvailabilityId, dto.durationInMinutes);
  }
}

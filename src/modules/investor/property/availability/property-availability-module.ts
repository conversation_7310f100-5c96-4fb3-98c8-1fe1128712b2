import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PropertyAvailabilityService } from './property-availability.service';
import { PropertyAvailabilityController } from './property-availability.controller';
import { PropertyAvailability } from './property-availability.entity';
import { AvailabilitySlot } from '../../availability/availability-slot.entity';
import { AvailabilitySlotsService } from '../../availability/availability-slot.service';
import { AvailabilitySlotController } from '../../availability/availability-slot.controller';
import { IsPropertyOwnerGuard } from '../../../../guards/investor/is-property-owner-guard';
import { Property } from '../property/entities/property.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AvailabilitySlot, PropertyAvailability, Property])],
  providers: [AvailabilitySlotsService, PropertyAvailabilityService, IsPropertyOwnerGuard],
  controllers: [AvailabilitySlotController, PropertyAvailabilityController],
  exports: [AvailabilitySlotsService, PropertyAvailabilityService],
})
export class PropertyAvailabilityModule {}

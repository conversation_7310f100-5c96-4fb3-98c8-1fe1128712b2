import { HttpException, Injectable } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class GoogleGeocodingService {
  private readonly apiKey: string;

  constructor() {
    this.apiKey = process.env.GOOGLE_MAPS_KEY;
  }

  async getCoordinates(address: string): Promise<{ lat: number; lng: number } | null> {
    try {
      const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
        params: {
          address: address,
          key: this.apiKey,
        },
      });

      if (response.data.results && response.data.results.length > 0) {
        const { lat, lng } = response.data.results[0].geometry.location;
        return { lat, lng };
      } else {
        return null;
      }
    } catch (error) {
      throw new HttpException(
        `Error fetching coordinates: ${error.response?.data?.error_message || error.message}`,
        500,
      );
    }
  }

  async getTimezone(latitude: number, longitude: number): Promise<string> {
    try {
      const timestamp = Math.floor(Date.now() / 1000);
      const response = await axios.get('https://maps.googleapis.com/maps/api/timezone/json', {
        params: {
          location: `${latitude},${longitude}`,
          timestamp: timestamp,
          key: this.apiKey,
        },
      });

      if (response.data.status === 'OK') {
        return response.data.timeZoneId;
      } else {
        throw new HttpException(`Error fetching timezone: ${response.data.errorMessage}`, 500);
      }
    } catch (error) {
      throw new HttpException(`Error fetching timezone: ${error.response?.data?.errorMessage || error.message}`, 500);
    }
  }
}

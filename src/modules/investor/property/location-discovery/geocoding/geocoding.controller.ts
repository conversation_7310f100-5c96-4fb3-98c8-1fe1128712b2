import { Body, Controller, Put } from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON>erAuth, ApiTags } from '@nestjs/swagger';
import { GeocodingService } from './geocoding.service';
import { HasRoles } from '../../../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../../../shared/auth/models/roles-enum';

@ApiTags('geocoding')
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@Controller('geocoding')
export class GeocodingController {
  constructor(private readonly geocodingService: GeocodingService) {}

  @Put()
  getCoordinates(@Body('address') address: string) {
    return this.geocodingService.getCoordinates(address);
  }
}

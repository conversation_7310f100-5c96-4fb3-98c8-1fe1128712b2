import { Injectable } from '@nestjs/common';
import { GoogleGeocodingService } from './google-geocoding/google-geocoding.service';

@Injectable()
export class GeocodingService {
  constructor(private readonly googleGeocodingService: GoogleGeocodingService) {}

  async getCoordinates(address: string): Promise<any> {
    return await this.googleGeocodingService.getCoordinates(address);
  }

  async getTimezone(latitude: number, longitude: number): Promise<string> {
    return await this.googleGeocodingService.getTimezone(latitude, longitude);
  }
}

import { Module } from '@nestjs/common';
import { GeocodingService } from './geocoding.service';
import { GeocodingController } from './geocoding.controller';
import { GoogleGeocodingService } from './google-geocoding/google-geocoding.service';
import { PlacesLookupService } from '../places-lookup/places-lookup.service';

@Module({
  providers: [GeocodingService, GoogleGeocodingService, PlacesLookupService],
  controllers: [GeocodingController],
  exports: [GeocodingService],
})
export class GeocodingModule {}

import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { PropertyLocationNearbyPlace } from '../../property/property-details/location/nearby-place/property-location-nearby-place.entity';

@Injectable()
export class PlacesLookupService {
  private readonly apiKey: string;

  constructor() {
    this.apiKey = process.env.GOOGLE_MAPS_KEY;
  }

  async getNearbyPlaces(
    latitude: number,
    longitude: number,
    radius: number,
    includedTypes: string[],
    maxResultCount = 20,
  ): Promise<PropertyLocationNearbyPlace[]> {
    try {
      const fields = ['name', 'location', 'rating', 'primaryType', 'displayName', 'primaryTypeDisplayName'];
      const response = await axios.post(
        'https://places.googleapis.com/v1/places:searchNearby',
        {
          includedTypes,
          maxResultCount,
          locationRestriction: {
            circle: {
              center: {
                latitude,
                longitude,
              },
              radius,
            },
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': this.apiKey,
            'X-Goog-FieldMask': this.constructFieldMask(fields),
          },
        },
      );

      if (response.data.places && response.data.places.length > 0) {
        return response.data.places.map((place: any) => ({
          name: place.displayName?.text,
          latitude: place.location.latitude,
          longitude: place.location.longitude,
          primaryType: place.primaryTypeDisplayName?.text,
          rating: place.rating,
        }));
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error fetching nearby places:', error);
    }
  }

  constructFieldMask(fields: string[]): string {
    const fieldPaths = fields.map((field) => `places.${field}`);
    return fieldPaths.join(',');
  }
}

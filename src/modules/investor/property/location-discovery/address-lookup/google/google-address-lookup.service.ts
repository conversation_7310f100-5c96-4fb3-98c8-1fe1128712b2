import { HttpException, Injectable } from '@nestjs/common';
import axios from 'axios';
import { Address } from '../models/address';

@Injectable()
export class GoogleAddressLookupService {
  private readonly apiKey: string;

  constructor() {
    this.apiKey = process.env.GOOGLE_MAPS_KEY;
  }

  async getAddressDetails(address: string): Promise<Address> {
    try {
      const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
        params: {
          address: address,
          key: this.apiKey,
        },
      });

      if (response.data.status === 'OK' && response.data.results.length > 0) {
        const result = response.data.results[0];
        const addressComponents = result.address_components;

        const getAddressComponent = (type: string) => {
          const component = addressComponents.find((comp) => comp.types.includes(type));
          return component ? component.long_name : null;
        };

        const city = getAddressComponent('locality') || getAddressComponent('administrative_area_level_2');
        const state = getAddressComponent('administrative_area_level_1');
        const zip = +getAddressComponent('postal_code');

        return {
          address,
          city,
          state,
          zip,
          latitude: result.geometry.location.lat,
          longitude: result.geometry.location.lng,
        };
      } else {
        throw new HttpException(`No property details found for address: ${address}`, 404);
      }
    } catch (error) {
      throw new HttpException(
        `Error fetching property details: ${error.response?.data?.error_message || error.message}`,
        500,
      );
    }
  }
}

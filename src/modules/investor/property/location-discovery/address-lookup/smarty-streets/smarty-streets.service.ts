import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { core, usStreet } from 'smartystreets-javascript-sdk';
import ClientBuilder = core.ClientBuilder;
import StaticCredentials = core.StaticCredentials;
import Client = core.Client;

@Injectable()
export class SmartyStreetsService {
  private validationClient: any;
  private autocompleteClient: Client<any, any>;

  constructor(private readonly config: ConfigService) {
    const credentials = new StaticCredentials(
      this.config.get('SMARTY_API_AUTH_ID'),
      this.config.get('SMARTY_API_AUTH_TOKEN'),
    );
    this.validationClient = new ClientBuilder(credentials).buildUsStreetApiClient();
    this.autocompleteClient = new ClientBuilder(credentials).buildUsAutocompleteProClient();
  }

  async validateAddress(address: string): Promise<any> {
    const lookup = new usStreet.Lookup();
    lookup.street = address;
    try {
      await this.validationClient.send(lookup);
    } catch (e) {
      console.error('Error when validating address', e);
    }
    return lookup.result;
  }

  async autocompleteAddress(address: string): Promise<any> {
    console.log(address);
    // const lookup = new usAutocomplete.Lookup({ address });
    // lookup. = address;
    // try {
    //   await this.validationClient.send(lookup);
    // } catch (e) {
    //   console.log(e);
    // }
    return 'done';
    // return lookup.result;
  }
}

import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class Address {
  @IsNotEmpty()
  @ApiProperty()
  @IsString()
  address: string;

  @ApiProperty()
  @IsString()
  city?: string;

  @ApiProperty()
  @IsString()
  state?: string;

  @ApiProperty()
  @IsNumber()
  zip?: number;

  @ApiProperty()
  @IsNumber()
  latitude?: number;

  @ApiProperty()
  @IsNumber()
  longitude?: number;
}

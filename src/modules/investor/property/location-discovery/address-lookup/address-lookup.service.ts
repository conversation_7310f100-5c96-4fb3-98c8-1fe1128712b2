import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SmartyStreetsService } from './smarty-streets/smarty-streets.service';
import { GoogleAddressLookupService } from './google/google-address-lookup.service';
import { Address } from './models/address';
import { RentCastService } from './rent-cast/rent-cast.service';
import { PropertyDto } from '../../property/model/property.dto';

@Injectable()
export class AddressLookupService {
  constructor(
    private readonly smartyStreetsService: SmartyStreetsService,
    private readonly googleAddressLookupService: GoogleAddressLookupService,
    private readonly rentCaseService: RentCastService,
    private readonly configService: ConfigService,
  ) {}

  async validateAddress(address: string): Promise<any> {
    return await this.smartyStreetsService.validateAddress(address);
  }

  async getAddressDetails(address: string): Promise<Address> {
    return await this.googleAddressLookupService.getAddressDetails(address);
  }

  async autocompleteAddress(address: string): Promise<any> {
    return await this.smartyStreetsService.autocompleteAddress(address);
  }

  async getPropertyDetails(address: Address): Promise<PropertyDto> {
    // Only perform RentCast property lookup in production environment
    if (this.configService.get('IS_PRODUCTION')) {
      return await this.rentCaseService.getPropertyData(address);
    }

    // Return an empty PropertyDto in non-production environments
    return new PropertyDto();
  }
}

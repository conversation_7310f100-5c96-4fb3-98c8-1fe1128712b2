import { Module } from '@nestjs/common';
import { AddressLookupService } from './address-lookup.service';
import { SmartyStreetsService } from './smarty-streets/smarty-streets.service';
import { AddressLookupController } from './address-lookup.controller';
import { GoogleAddressLookupService } from './google/google-address-lookup.service';
import { RentCastService } from './rent-cast/rent-cast.service';

@Module({
  providers: [AddressLookupService, SmartyStreetsService, GoogleAddressLookupService, RentCastService],
  controllers: [AddressLookupController],
  exports: [AddressLookupService],
})
export class AddressLookupModule {}

import { Body, Controller, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { AddressLookupService } from './address-lookup.service';
import { Address } from './models/address';
import { HasRoles } from '../../../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../../../shared/auth/models/roles-enum';
import { RolesGuard } from '../../../../shared/auth/guards/roles.guard';
import { PropertyDto } from '../../property/model/property.dto';

@ApiTags('address-lookup')
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@ApiBearerAuth()
@Controller('address-lookup')
export class AddressLookupController {
  constructor(private readonly addressLookupService: AddressLookupService) {}

  @Get()
  autocompleteAddress(@Query('address') address: string) {
    return this.addressLookupService.autocompleteAddress(address);
  }

  @Post()
  validateAddress(@Body('address') address: string) {
    return this.addressLookupService.validateAddress(address);
  }

  @Get(':address')
  @ApiParam({
    name: 'address',
    required: true,
  })
  @ApiOkResponse({
    description: 'Returns address details by address',
  })
  getAddressDetails(@Param('address') address: string): Promise<Address> {
    return this.addressLookupService.getAddressDetails(address);
  }

  @Put('property/details')
  @ApiOkResponse({
    description: 'Property details returned',
    type: PropertyDto,
  })
  @ApiBody({
    type: Address,
  })
  async getPropertyDetails(@Body() address: Address): Promise<PropertyDto> {
    return await this.addressLookupService.getPropertyDetails(address);
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AddressLookupService } from './address-lookup.service';
import { SmartyStreetsService } from './smarty-streets/smarty-streets.service';
import { GoogleAddressLookupService } from './google/google-address-lookup.service';
import { RentCastService } from './rent-cast/rent-cast.service';
import { Address } from './models/address';
import { PropertyDto } from '../../property/model/property.dto';

describe('AddressLookupService', () => {
  let service: AddressLookupService;
  let configService: ConfigService;
  let rentCastService: RentCastService;

  const mockAddress: Address = {
    address: '123 Main St',
    city: 'New York',
    state: 'NY',
    zip: 10001,
    latitude: 40.7128,
    longitude: -74.006,
  };

  const mockPropertyDto = new PropertyDto();
  mockPropertyDto.displayName = 'Test Property';

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AddressLookupService,
        {
          provide: SmartyStreetsService,
          useValue: {
            validateAddress: jest.fn().mockResolvedValue({}),
            autocompleteAddress: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: GoogleAddressLookupService,
          useValue: {
            getAddressDetails: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: RentCastService,
          useValue: {
            getPropertyData: jest.fn().mockResolvedValue(mockPropertyDto),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AddressLookupService>(AddressLookupService);
    configService = module.get<ConfigService>(ConfigService);
    rentCastService = module.get<RentCastService>(RentCastService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPropertyDetails', () => {
    it('should call RentCast service when in production environment', async () => {
      // Mock production environment
      jest.spyOn(configService, 'get').mockReturnValue(true);

      const result = await service.getPropertyDetails(mockAddress);

      expect(configService.get).toHaveBeenCalledWith('IS_PRODUCTION');
      expect(rentCastService.getPropertyData).toHaveBeenCalledWith(mockAddress);
      expect(result).toEqual(mockPropertyDto);
    });

    it('should not call RentCast service when not in production environment', async () => {
      // Mock non-production environment
      jest.spyOn(configService, 'get').mockReturnValue(false);

      const result = await service.getPropertyDetails(mockAddress);

      expect(configService.get).toHaveBeenCalledWith('IS_PRODUCTION');
      expect(rentCastService.getPropertyData).not.toHaveBeenCalled();
      expect(result).toBeInstanceOf(PropertyDto);
      expect(result).not.toEqual(mockPropertyDto); // Should be a new empty PropertyDto
    });
  });

  describe('validateAddress', () => {
    it('should call SmartyStreets service', async () => {
      const address = '123 Main St';
      await service.validateAddress(address);

      const smartyStreetsService = jest.spyOn(service['smartyStreetsService'], 'validateAddress');
      expect(smartyStreetsService).toHaveBeenCalledWith(address);
    });
  });

  describe('getAddressDetails', () => {
    it('should call Google address lookup service', async () => {
      const address = '123 Main St';
      await service.getAddressDetails(address);

      const googleAddressLookupService = jest.spyOn(service['googleAddressLookupService'], 'getAddressDetails');
      expect(googleAddressLookupService).toHaveBeenCalledWith(address);
    });
  });

  describe('autocompleteAddress', () => {
    it('should call SmartyStreets autocomplete service', async () => {
      const address = '123 Main';
      await service.autocompleteAddress(address);

      const smartyStreetsService = jest.spyOn(service['smartyStreetsService'], 'autocompleteAddress');
      expect(smartyStreetsService).toHaveBeenCalledWith(address);
    });
  });
});

interface Features {
  architectureType?: string;
  cooling?: boolean;
  coolingType?: string;
  exteriorType?: string;
  fireplace?: boolean;
  fireplaceType?: string;
  floorCount?: number;
  foundationType?: string;
  garage?: boolean;
  garageSpaces?: number;
  garageType?: string;
  heating?: boolean;
  heatingType?: string;
  pool?: boolean;
  poolType?: string;
  roofType?: string;
  roomCount?: number;
  unitCount?: number;
  viewType?: string;
}

interface HOA {
  fee?: number;
}

export enum RentCastPropertyTypeEnum {
  SINGLE_FAMILY = 'Single Family',
  CONDO = 'Condo',
  TOWNHOUSE = 'Townhouse',
  MANUFACTURED = 'Manufactured',
  MULTI_FAMILY = 'Multi-Family',
  APARTMENT = 'Apartment',
  LAND = 'Land',
}

export interface PropertyData {
  id?: string;
  formattedAddress?: string;
  addressLine1?: string;
  addressLine2?: string | null;
  city?: string;
  state?: string;
  zipCode?: string;
  county?: string;
  latitude?: number;
  longitude?: number;
  propertyType?: RentCastPropertyTypeEnum;
  bedrooms?: number;
  bathrooms?: number;
  squareFootage?: number;
  lotSize?: number;
  yearBuilt?: number;
  assessorID?: string;
  legalDescription?: string;
  subdivision?: string;
  zoning?: string;
  lastSaleDate?: string;
  lastSalePrice?: number;
  hoa?: HOA;
  features?: Features;
  ownerOccupied?: boolean;
}

// Property Valuation Interfaces
export interface PropertyValuation {
  latitude?: number;
  longitude?: number;
  price?: number;
  priceRangeLow?: number;
  priceRangeHigh?: number;
  rent?: number;
  rentRangeLow?: number;
  rentRangeHigh?: number;
  comparables?: PropertyComparable[];
}

export interface PropertyComparable {
  id?: string;
  formattedAddress?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  county?: string;
  latitude?: number;
  longitude?: number;
  propertyType?: RentCastPropertyTypeEnum;
  bedrooms?: number;
  bathrooms?: number;
  squareFootage?: number;
  lotSize?: number;
  yearBuilt?: number;
  price?: number;
  listingType?: string;
  listedDate?: string;
  removedDate?: string;
  lastSeenDate?: string;
  daysOnMarket?: number;
  distance?: number;
  daysOld?: number;
  correlation?: number;
}

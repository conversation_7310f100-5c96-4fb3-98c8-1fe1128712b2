import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { Address } from '../models/address';
import { PropertyDto } from '../../../property/model/property.dto';
import { PropertySpecificationsDto } from '../../../property/property-details/specifications/property-specifications.dto';
import { PropertyData, RentCastPropertyTypeEnum, PropertyValuation } from './property-details-response.interface';
import { PropertyTypeEnum } from '../../../property/enums/property-type.enum';
import { ParkingType } from '../../../property/property-details/parking/parking.type';
import { AmenitiesDto } from '../../../property/property-details/amenities/amenities.dto';
import { ParkingDto } from '../../../property/property-details/parking/parking.dto';
import { ArchitectureStyle } from '../../../property/enums/architecture-style.enum';
import { ExteriorType } from '../../../property/enums/exterior-type.enum';
import { RoofType } from '../../../property/enums/roof-type.enum';
import { ViewType } from '../../../property/enums/view-type.enum';
import { HeatingSystemEnum } from '../../../property/enums/heat-system.enum';
import { CoolingSystem } from '../../../property/enums/cooling-system.enum';

@Injectable()
export class RentCastService {
  private readonly apiKey: string;

  constructor() {
    this.apiKey = process.env.RENTCAST_API_KEY;
  }

  async getPropertyData(address: Address): Promise<PropertyDto> {
    const formattedAddress = `${address.address}, ${address.city}, ${address.state} ${address.zip}`;
    const url = `https://api.rentcast.io/v1/properties?address=${encodeURIComponent(formattedAddress)}`;

    try {
      const response = await axios.get(url, {
        headers: {
          Accept: 'application/json',
          'X-Api-Key': this.apiKey,
        },
      });

      if (response.data) {
        // TODO add more field mappings
        const property = response.data[0] as PropertyData;
        const specifications = new PropertySpecificationsDto();

        if (property.bathrooms) {
          const bathrooms = this.mapBathrooms(property.bathrooms);
          specifications.fullBathrooms = bathrooms.fullBathrooms;
          specifications.halfBathrooms = bathrooms.halfBathrooms;
        }

        specifications.propertyType = this.mapPropertyType(property.propertyType);
        specifications.squareFeet = property.squareFootage;
        specifications.bedrooms = property.bedrooms ? Math.floor(property.bedrooms) : null;
        specifications.yearBuilt = property.yearBuilt;

        const propertyDto = new PropertyDto();
        propertyDto.specifications = specifications;

        if (property.features) {
          const features = property.features;
          const amenities = new AmenitiesDto();
          const parking = new ParkingDto();

          // Map basic amenities
          amenities.hasFireplace = features.fireplace;
          amenities.hasHeating = features.heating;
          amenities.hasAirConditioning = features.cooling;
          amenities.hasPool = features.pool;

          // Map parking information
          if (features.garage) {
            parking.hasParking = true;
            parking.parkingAvailableSpaces = features.garageSpaces || 1;

            // Map garage types to our parking types
            switch (features.garageType) {
              case 'Garage':
              case 'Attached':
                parking.parkingType = ParkingType.GARAGE_ATTACHED;
                break;
              case 'Detached':
                parking.parkingType = ParkingType.GARAGE_DETACHED;
                break;
              case 'Carport':
                parking.parkingType = ParkingType.CARPORT;
                break;
              default:
                parking.parkingType = ParkingType.GARAGE_ATTACHED;
            }
            propertyDto.parking = parking;
          }

          propertyDto.amenities = amenities;
        }

        // Map additional property specifications
        if (property.lotSize) {
          specifications.lotSize = property.lotSize;
        }

        // Map HOA information if available
        if (property.hoa?.fee) {
          specifications.hoaFee = property.hoa.fee;
        }

        // Map room counts and floor information
        if (property.features?.roomCount) {
          specifications.totalRooms = property.features.roomCount;
        }

        if (property.features?.floorCount) {
          specifications.numberOfFloors = property.features.floorCount;
        }

        // Map property feature enums to our system
        if (property.features?.architectureType) {
          specifications.architectureStyle = this.mapArchitectureStyle(property.features.architectureType);
        }

        if (property.features?.exteriorType) {
          specifications.exteriorType = this.mapExteriorType(property.features.exteriorType);
        }

        if (property.features?.roofType) {
          specifications.roofType = this.mapRoofType(property.features.roofType);
        }

        if (property.features?.viewType) {
          specifications.viewType = this.mapViewType(property.features.viewType);
        }

        if (property.features?.heatingType) {
          specifications.heatingSystem = this.mapHeatingSystem(property.features.heatingType);
        }

        if (property.features?.coolingType) {
          specifications.coolingSystem = this.mapCoolingSystem(property.features.coolingType);
        }

        return propertyDto;
      } else {
        return null;
      }
    } catch (error) {
      if (error.response.status === 404 || error.response.status === 400) {
        console.log(`No data found for address: ${formattedAddress}`);
      } else {
        console.error('Error fetching property data:', error.response.data);
      }
    }
  }

  async getRentEstimate(
    address: string,
    latitude?: number,
    longitude?: number,
    propertyType?: string,
    bedrooms?: number,
    bathrooms?: number,
    squareFootage?: number,
    daysOld?: number,
  ): Promise<PropertyValuation> {
    const url = 'https://api.rentcast.io/v1/avm/rent/long-term';

    // Build query parameters
    const params = new URLSearchParams();
    if (address) params.append('address', address);
    if (latitude) params.append('latitude', latitude.toString());
    if (longitude) params.append('longitude', longitude.toString());
    if (propertyType) params.append('propertyType', propertyType);
    if (bedrooms) params.append('bedrooms', bedrooms.toString());
    if (bathrooms) params.append('bathrooms', bathrooms.toString());
    if (squareFootage) params.append('squareFootage', squareFootage.toString());
    if (daysOld) params.append('daysOld', daysOld.toString());

    try {
      const response = await axios.get(`${url}?${params.toString()}`, {
        headers: {
          Accept: 'application/json',
          'X-Api-Key': this.apiKey,
        },
      });

      if (response.data) {
        return response.data as PropertyValuation;
      } else {
        return null;
      }
    } catch (error) {
      if (error.response?.status === 404) {
        console.log(`No rent estimate data found for address: ${address}`);
      } else {
        console.error('Error fetching rent estimate:', error.response?.data || error.message);
      }
      return null;
    }
  }

  async getValueEstimate(
    address: string,
    latitude?: number,
    longitude?: number,
    propertyType?: string,
    bedrooms?: number,
    bathrooms?: number,
    squareFootage?: number,
    daysOld?: number,
  ): Promise<PropertyValuation> {
    const url = 'https://api.rentcast.io/v1/avm/value';

    // Build query parameters
    const params = new URLSearchParams();
    if (address) params.append('address', address);
    if (latitude) params.append('latitude', latitude.toString());
    if (longitude) params.append('longitude', longitude.toString());
    if (propertyType) params.append('propertyType', propertyType);
    if (bedrooms) params.append('bedrooms', bedrooms.toString());
    if (bathrooms) params.append('bathrooms', bathrooms.toString());
    if (squareFootage) params.append('squareFootage', squareFootage.toString());
    if (daysOld) params.append('daysOld', daysOld.toString());

    try {
      const response = await axios.get(`${url}?${params.toString()}`, {
        headers: {
          Accept: 'application/json',
          'X-Api-Key': this.apiKey,
        },
      });

      if (response.data) {
        return response.data as PropertyValuation;
      } else {
        return null;
      }
    } catch (error) {
      if (error.response?.status === 404) {
        console.log(`No value estimate data found for address: ${address}`);
      } else {
        console.error('Error fetching value estimate:', error.response?.data || error.message);
      }
      return null;
    }
  }

  mapBathrooms(bathrooms: number): {
    fullBathrooms: number;
    halfBathrooms: number;
  } {
    // RentCast API returns bathrooms as a float
    // if the value is 1.5, it means 1 full bathroom and 1 half bathroom
    const fullBathrooms = Math.floor(bathrooms);
    const halfBathrooms = bathrooms % 1 === 0.5 ? 1 : 0;

    return {
      fullBathrooms,
      halfBathrooms,
    };
  }

  mapPropertyType(responseType: RentCastPropertyTypeEnum): PropertyTypeEnum {
    switch (responseType) {
      case RentCastPropertyTypeEnum.SINGLE_FAMILY:
        return PropertyTypeEnum.HOUSE;
      case RentCastPropertyTypeEnum.CONDO:
        return PropertyTypeEnum.CONDO;
      case RentCastPropertyTypeEnum.TOWNHOUSE:
        return PropertyTypeEnum.TOWNHOUSE;
      case RentCastPropertyTypeEnum.MULTI_FAMILY:
        return PropertyTypeEnum.APARTMENT;
      case RentCastPropertyTypeEnum.APARTMENT:
        return PropertyTypeEnum.APARTMENT;
      default:
        return null;
    }
  }

  // Mapping methods for RentCast feature values to our system enums
  mapArchitectureStyle(rentCastValue: string): ArchitectureStyle {
    const normalizedValue = rentCastValue?.toLowerCase();

    switch (normalizedValue) {
      case 'bungalow':
        return ArchitectureStyle.BUNGALOW;
      case 'cape cod':
        return ArchitectureStyle.CAPE_COD;
      case 'colonial':
        return ArchitectureStyle.COLONIAL;
      case 'contemporary':
        return ArchitectureStyle.CONTEMPORARY;
      case 'craftsman':
        return ArchitectureStyle.CRAFTSMAN;
      case 'french':
        return ArchitectureStyle.FRENCH;
      case 'georgian':
        return ArchitectureStyle.GEORGIAN;
      case 'loft':
        return ArchitectureStyle.LOFT;
      case 'modern':
        return ArchitectureStyle.MODERN;
      case 'queen anne victorian':
        return ArchitectureStyle.QUEEN_ANNE_VICTORIAN;
      case 'ranch':
      case 'rambler':
      case 'ranch/rambler':
        return ArchitectureStyle.RANCH_RAMBLER;
      case 'santa fe':
      case 'pueblo':
      case 'santa fe/pueblo style':
        return ArchitectureStyle.SANTA_FE_PUEBLO_STYLE;
      case 'spanish':
        return ArchitectureStyle.SPANISH;
      case 'split level':
      case 'split-level':
        return ArchitectureStyle.SPLIT_LEVEL;
      case 'tudor':
        return ArchitectureStyle.TUDOR;
      default:
        return ArchitectureStyle.OTHER;
    }
  }

  mapExteriorType(rentCastValue: string): ExteriorType {
    const normalizedValue = rentCastValue?.toLowerCase();

    switch (normalizedValue) {
      case 'brick':
        return ExteriorType.BRICK;
      case 'cement':
      case 'concrete':
      case 'cement concrete':
        return ExteriorType.CEMENT_CONCRETE;
      case 'composition':
        return ExteriorType.COMPOSITION;
      case 'metal':
        return ExteriorType.METAL;
      case 'shingle':
        return ExteriorType.SHINGLE;
      case 'stone':
        return ExteriorType.STONE;
      case 'stucco':
        return ExteriorType.STUCCO;
      case 'vinyl':
        return ExteriorType.VINYL;
      case 'wood':
        return ExteriorType.WOOD;
      case 'wood products':
        return ExteriorType.WOOD_PRODUCTS;
      default:
        return ExteriorType.OTHER;
    }
  }

  mapRoofType(rentCastValue: string): RoofType {
    const normalizedValue = rentCastValue?.toLowerCase();

    switch (normalizedValue) {
      case 'asphalt':
        return RoofType.ASPHALT;
      case 'built up':
      case 'built-up':
        return RoofType.BUILT_UP;
      case 'composition':
        return RoofType.COMPOSITION;
      case 'metal':
        return RoofType.METAL;
      case 'shake':
      case 'shingle':
      case 'shake shingle':
        return RoofType.SHAKE_SHINGLE;
      case 'slate':
        return RoofType.SLATE;
      case 'tile':
        return RoofType.TILE;
      default:
        return RoofType.OTHER;
    }
  }

  mapViewType(rentCastValue: string): ViewType {
    const normalizedValue = rentCastValue?.toLowerCase();

    switch (normalizedValue) {
      case 'water':
      case 'waterfront':
      case 'lake':
      case 'river':
      case 'beach':
      case 'canal':
        return ViewType.WATER;
      case 'mountain':
        return ViewType.MOUNTAIN;
      case 'city':
        return ViewType.CITY;
      case 'territorial':
        return ViewType.TERRITORIAL;
      case 'park':
        return ViewType.PARK;
      default:
        return ViewType.NONE;
    }
  }

  mapHeatingSystem(rentCastValue: string): HeatingSystemEnum {
    const normalizedValue = rentCastValue?.toLowerCase();

    switch (normalizedValue) {
      case 'baseboard':
        return HeatingSystemEnum.BASEBOARD;
      case 'forced air':
        return HeatingSystemEnum.FORCED_AIR;
      case 'heat pump':
        return HeatingSystemEnum.HEAT_PUMP;
      case 'radiant':
        return HeatingSystemEnum.RADIANT;
      case 'stove':
        return HeatingSystemEnum.STOVE;
      case 'wall':
        return HeatingSystemEnum.WALL;
      default:
        return HeatingSystemEnum.OTHER;
    }
  }

  mapCoolingSystem(rentCastValue: string): CoolingSystem {
    const normalizedValue = rentCastValue?.toLowerCase();

    switch (normalizedValue) {
      case 'central':
        return CoolingSystem.CENTRAL;
      case 'evaporative':
        return CoolingSystem.EVAPORATIVE;
      case 'geothermal':
        return CoolingSystem.GEOTHERMAL;
      case 'wall':
        return CoolingSystem.WALL;
      case 'solar':
        return CoolingSystem.SOLAR;
      default:
        return CoolingSystem.OTHER;
    }
  }
}

import { <PERSON>, Get, Query, Req, UseGuards, Res } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { Request } from 'express';
import { IsPropertyOwnerGuard } from '../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { Role } from '../../../shared/auth/models/roles-enum';
import { PropertyStatisticsDto } from './property-statistics.dto';
import { PropertyStatisticsService } from './property-statistics.service';
import { TimeFilter } from './time-filter.enum';

@ApiTags('property-statistics')
@Controller('property-statistics')
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@ApiBearerAuth()
export class PropertyStatisticsController {
  constructor(private readonly propertyStatisticsService: PropertyStatisticsService) {}

  @Get(':propertyId')
  @ApiOkResponse({
    description: 'Get property statistics by property id',
  })
  @UseGuards(IsPropertyOwnerGuard)
  async getPropertyStatistics(
    @Req() req: Request,
    @Query('filter') filter: TimeFilter,
  ): Promise<PropertyStatisticsDto> {
    return await this.propertyStatisticsService.getPropertyStats(req.property.id, filter);
  }

  @Get(':propertyId/download')
  @ApiOkResponse({
    description: 'Download property statistics as Excel file',
  })
  @UseGuards(IsPropertyOwnerGuard)
  async downloadPropertyStatistics(
    @Req() req: Request,
    @Res() res: Response,
    @Query('filter') filter: TimeFilter,
  ): Promise<void> {
    try {
      console.log(`Generating Excel file for property ${req.property.id} with filter: ${filter || 'all'}`);

      const excelBuffer = await this.propertyStatisticsService.generateExcelBuffer(
        req.property.id,
        filter || TimeFilter.ALL_TIME,
      );

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `${req.property.displayName}-${timestamp}.xlsx`;

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', excelBuffer.length);

      res.send(excelBuffer);
    } catch (error) {
      console.error('Error generating Excel file:', error);
      res.status(500).json({
        error: 'Failed to generate Excel file',
        message: error.message,
      });
    }
  }
}

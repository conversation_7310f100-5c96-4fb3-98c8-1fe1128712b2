import { Module } from '@nestjs/common';
import { PropertyStatisticsService } from './property-statistics.service';
import { PropertyStatisticsController } from './property-statistics.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Showing } from '../../showing/showing.entity';
import { Message } from '../../../shared/communication/conversation/message/message.entity';
import { PropertyInquiry } from '../../property-inquiry/property-inquiry.entity';
import { Property } from '../property/entities/property.entity';
import { ApplicationBundle } from '../../../shared/application/application-bundle/application-bundle.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Property, Showing, Message, PropertyInquiry, ApplicationBundle])],
  controllers: [PropertyStatisticsController],
  providers: [PropertyStatisticsService],
  exports: [PropertyStatisticsService],
})
export class PropertyStatisticsModule {}

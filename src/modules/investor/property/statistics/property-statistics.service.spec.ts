import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PropertyStatisticsService } from './property-statistics.service';
import { Showing } from '../../showing/showing.entity';
import { PropertyInquiry } from '../../property-inquiry/property-inquiry.entity';
import { Message } from '../../../shared/communication/conversation/message/message.entity';
import { Property } from '../property/entities/property.entity';
import { ApplicationBundle } from '../../../shared/application/application-bundle/application-bundle.entity';
import { TimeFilter } from './time-filter.enum';
import { ApplicationStatus } from '../../../shared/application/application/enums/application-status.enum';

describe('PropertyStatisticsService', () => {
  let service: PropertyStatisticsService;
  let showingRepository: jest.Mocked<Repository<Showing>>;
  let propertyInquiryRepository: jest.Mocked<Repository<PropertyInquiry>>;
  let messageRepository: jest.Mocked<Repository<Message>>;
  let applicationBundleRepository: jest.Mocked<Repository<ApplicationBundle>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PropertyStatisticsService,
        {
          provide: getRepositoryToken(Showing),
          useValue: {
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(PropertyInquiry),
          useValue: {
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Message),
          useValue: {
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Property),
          useValue: {},
        },
        {
          provide: getRepositoryToken(ApplicationBundle),
          useValue: {
            createQueryBuilder: jest.fn(),
            find: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PropertyStatisticsService>(PropertyStatisticsService);
    showingRepository = module.get(getRepositoryToken(Showing));
    propertyInquiryRepository = module.get(getRepositoryToken(PropertyInquiry));
    messageRepository = module.get(getRepositoryToken(Message));
    applicationBundleRepository = module.get(getRepositoryToken(ApplicationBundle));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPropertyStats', () => {
    it('should return property statistics with all required fields', async () => {
      const propertyId = 'test-property-id';
      const filter = TimeFilter.LAST_30_DAYS;

      // Mock query builders
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(5),
      };

      showingRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
      propertyInquiryRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
      messageRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
      applicationBundleRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      // Mock application bundles with completed applications
      const mockApplicationBundle = {
        applicationInvites: Promise.resolve([
          {
            application: Promise.resolve({
              status: ApplicationStatus.COMPLETED,
            }),
          },
        ]),
      };

      applicationBundleRepository.find.mockResolvedValue([mockApplicationBundle] as any);

      const result = await service.getPropertyStats(propertyId, filter);

      expect(result).toEqual({
        leads: 5,
        qualifiedLeads: 5,
        showingsTotal: 5,
        showingsBooked: 5,
        messagesSent: 5,
        followUpsSent: 5,
        applicationsSent: 5,
        applicationsCompleted: 1,
      });
    });
  });
});

import { Injectable } from '@nestjs/common';
import { PropertyStatisticsDto } from './property-statistics.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { MoreThan, Repository } from 'typeorm';
import { Showing } from '../../showing/showing.entity';
import { Message } from '../../../shared/communication/conversation/message/message.entity';
import { MessageType } from '../../../shared/communication/conversation/message/message-type.enum';
import { ShowingStatus } from '../../showing/enums/showing-status.enum';
import { PropertyInquiry } from '../../property-inquiry/property-inquiry.entity';
import { TimeFilter } from './time-filter.enum';
import { FilterUtils } from '../../../../utils/filter.utils';
import { Property } from '../property/entities/property.entity';
import { Renter } from '../../../renter/renter/renter.entity';
import { User } from '../../../shared/user/entities/user.entity';
import { ApplicationBundle } from '../../../shared/application/application-bundle/application-bundle.entity';
import { ApplicationStatus } from '../../../shared/application/application/enums/application-status.enum';
import { RentStageEnum } from '../../../shared/communication/conversation/enums/rent-stage.enum';
import * as ExcelJS from 'exceljs';
import { stringify } from 'csv-stringify/sync';

@Injectable()
export class PropertyStatisticsService {
  constructor(
    @InjectRepository(Showing)
    private readonly showingRepository: Repository<Showing>,
    @InjectRepository(PropertyInquiry)
    private readonly propertyInquiryRepository: Repository<PropertyInquiry>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(ApplicationBundle)
    private readonly applicationBundleRepository: Repository<ApplicationBundle>,
  ) {}

  async getPropertyStats(propertyId: string, filter: TimeFilter): Promise<PropertyStatisticsDto> {
    const startDate = FilterUtils.getStartDateByFilter(filter);

    // 1) Total Showings
    const showingBuilder = this.showingRepository
      .createQueryBuilder('showing')
      .where('showing.propertyId = :propertyId', { propertyId })
      .andWhere('showing.createdAt >= :startDate', { startDate });

    const allShowings = await showingBuilder.getCount();

    // 2) Booked Showings (excluding canceled/rescheduled)
    const bookedShowingBuilder = this.showingRepository
      .createQueryBuilder('showing')
      .where('showing.propertyId = :propertyId', { propertyId })
      .andWhere('showing.createdAt >= :startDate', { startDate })
      .andWhere('showing.status NOT IN (:...excludedStatuses)', {
        excludedStatuses: [ShowingStatus.CANCELED, ShowingStatus.RESCHEDULED],
      });

    const bookedShowings = await bookedShowingBuilder.getCount();

    // 3) Total Leads
    const inquiryBuilder = this.propertyInquiryRepository
      .createQueryBuilder('propertyInquiry')
      .where('propertyInquiry.propertyId = :propertyId', { propertyId })
      .andWhere('propertyInquiry.createdAt >= :startDate', { startDate });

    const totalLeads = await inquiryBuilder.getCount();

    // 4) Qualified Leads (those passing initial contact stage)
    const qualifiedLeadsBuilder = this.propertyInquiryRepository
      .createQueryBuilder('propertyInquiry')
      .where('propertyInquiry.propertyId = :propertyId', { propertyId })
      .andWhere('propertyInquiry.createdAt >= :startDate', { startDate })
      .andWhere('(propertyInquiry.passedInitialScreening = true OR propertyInquiry.stage != :initialStage)', {
        initialStage: RentStageEnum.INITIAL_CONTACT,
      });

    const qualifiedLeads = await qualifiedLeadsBuilder.getCount();

    // 5) All System Messages
    const messageBuilder = this.messageRepository
      .createQueryBuilder('message')
      .innerJoin('message.conversations', 'conversation')
      .where('conversation.propertyId = :propertyId', { propertyId })
      .andWhere('message.createdAt >= :startDate', { startDate })
      .andWhere('message.isSystem = true');

    const allPropertyMessages = await messageBuilder.getCount();

    // 6) Follow-ups
    const followUpBuilder = this.messageRepository
      .createQueryBuilder('message')
      .innerJoin('message.conversations', 'conversation')
      .where('conversation.propertyId = :propertyId', { propertyId })
      .andWhere('message.createdAt >= :startDate', { startDate })
      .andWhere('message.isSystem = true')
      .andWhere('message.type = :type', { type: MessageType.FOLLOW_UP });

    const totalFollowUps = await followUpBuilder.getCount();

    // 7) Applications Sent (total application bundles)
    const applicationsSentBuilder = this.applicationBundleRepository
      .createQueryBuilder('applicationBundle')
      .where('applicationBundle.propertyId = :propertyId', { propertyId })
      .andWhere('applicationBundle.createdAt >= :startDate', { startDate });

    const applicationsSent = await applicationsSentBuilder.getCount();

    // 8) Applications Completed (bundles where all applications are completed)
    const applicationBundles = await this.applicationBundleRepository.find({
      where: {
        property: { id: propertyId },
        createdAt: MoreThan(startDate),
      },
      relations: ['applicationInvites', 'applicationInvites.application'],
    });

    let applicationsCompleted = 0;
    for (const bundle of applicationBundles) {
      const invites = await bundle.applicationInvites;
      const applications = await Promise.all(invites.map(async (invite) => await invite.application));

      const validApplications = applications.filter((app) => app !== null);

      if (validApplications.length > 0 && validApplications.length === invites.length) {
        const areAllCompleted = validApplications.every((app) => app.status === ApplicationStatus.COMPLETED);

        if (areAllCompleted) {
          applicationsCompleted++;
        }
      }
    }

    return {
      leads: totalLeads,
      qualifiedLeads: qualifiedLeads,
      showingsTotal: allShowings,
      showingsBooked: bookedShowings,
      messagesSent: allPropertyMessages,
      followUpsSent: totalFollowUps,
      applicationsSent: applicationsSent,
      applicationsCompleted: applicationsCompleted,
    };
  }

  async getLeadsForExport(propertyId: string, filter: TimeFilter) {
    const startDate = FilterUtils.getStartDateByFilter(filter);

    // Query all leads/property inquiries with related data using relations array
    const leads = await this.propertyInquiryRepository.find({
      where: {
        property: { id: propertyId },
        createdAt: MoreThan(startDate),
      },
      relations: ['property', 'property.location', 'renter', 'renter.user'],
      order: {
        createdAt: 'DESC',
      },
    });

    console.log(`Found ${leads.length} leads for property ${propertyId}`);
    if (leads.length > 0) {
      const sampleProperty = leads[0].property as Property;
      const sampleRenter = leads[0].renter as Renter;
      const sampleUser = sampleRenter?.user as User;
      console.log('Sample lead data:', {
        id: leads[0].id,
        source: leads[0].source,
        stage: leads[0].stage,
        hasProperty: !!leads[0].property,
        hasRenter: !!leads[0].renter,
        propertyId: sampleProperty?.id,
        propertyName: sampleProperty?.displayName,
        renterUserId: sampleUser?.id,
        renterEmail: sampleUser?.email,
      });
    }

    return leads;
  }

  async generateExcelBuffer(propertyId: string, filter: TimeFilter): Promise<Buffer> {
    const leads = await this.getLeadsForExport(propertyId, filter);

    // Create Excel workbook using exceljs
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Property Leads');

    // Define headers
    const headers = [
      'Date',
      'Listing Source',
      'Property Name',
      'Address',
      'City',
      'State',
      'Zip',
      'Communication Type',
      'Email',
      'Phone',
      'Name',
      'Move-in Date',
      'Credit Score',
      'Income',
      'Status',
    ];

    // Add headers to worksheet
    worksheet.addRow(headers);

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // Add data rows
    const rows = leads.map((lead) => this.convertLeadToRow(lead));
    const resolvedRows = await Promise.all(rows);

    resolvedRows.forEach((row) => {
      worksheet.addRow(row);
    });

    // Auto-fit columns
    worksheet.columns.forEach((column) => {
      column.width = 15;
    });

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  private async convertLeadsToCSV(leads: PropertyInquiry[]): Promise<string> {
    const headers = [
      'Date',
      'Listing Source',
      'Property Name',
      'Address',
      'City',
      'State',
      'Zip',
      'Communication Type',
      'Email',
      'Phone',
      'Name',
      'Move-in Date',
      'Credit Score',
      'Income',
      'Status',
    ];

    const rows = leads.map((lead) => this.convertLeadToRow(lead));
    const resolvedRows = await Promise.all(rows);

    // Prepare data for csv-stringify
    const csvData = [headers, ...resolvedRows];

    // Generate CSV using csv-stringify
    const csvString = stringify(csvData, {
      delimiter: ',',
      quoted: true,
      quoted_empty: true,
      escape: '"',
      header: false, // We're providing headers manually
    });

    return csvString;
  }

  private async convertLeadsToExcelXML(leads: PropertyInquiry[]): Promise<string> {
    const headers = [
      'Date',
      'Listing Source',
      'Property Name',
      'Address',
      'City',
      'State',
      'Zip',
      'Communication Type',
      'Email',
      'Phone',
      'Name',
      'Move-in Date',
      'Credit Score',
      'Income',
      'Status',
    ];

    const rows = leads.map((lead) => this.convertLeadToRow(lead));
    const resolvedRows = await Promise.all(rows);

    // Generate Excel XML format
    let xml = `<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <Worksheet ss:Name="Property Leads">
  <Table>`;

    // Add header row
    xml += '\n   <Row>';
    headers.forEach((header) => {
      xml += `\n    <Cell><Data ss:Type="String">${this.escapeXml(header)}</Data></Cell>`;
    });
    xml += '\n   </Row>';

    // Add data rows
    resolvedRows.forEach((row) => {
      xml += '\n   <Row>';
      row.forEach((cell) => {
        const cellValue = this.escapeXml(cell);
        // Detect if it's a number for proper Excel formatting
        const isNumber = !isNaN(Number(cell)) && cell !== '' && cell !== 'N/A';
        const dataType = isNumber ? 'Number' : 'String';
        xml += `\n    <Cell><Data ss:Type="${dataType}">${cellValue}</Data></Cell>`;
      });
      xml += '\n   </Row>';
    });

    xml += `
  </Table>
 </Worksheet>
</Workbook>`;

    return xml;
  }

  private async convertLeadToRow(propertyInquiry: PropertyInquiry): Promise<string[]> {
    // Since we used relations array, the relationships should be loaded directly
    const property = await propertyInquiry.property;
    const renter = await propertyInquiry.renter;
    const user = renter.user;
    // Cast location to handle TypeScript Promise/non-Promise union type
    const location = await property.location;

    console.log('Converting propertyInquiry to row:', {
      leadId: propertyInquiry.id,
      source: propertyInquiry.source,
      stage: propertyInquiry.stage,
      propertyName: property?.displayName,
      renterEmail: user?.email,
      hasLocation: !!location,
      locationAddress: location?.address,
    });

    return [
      this.formatDate(propertyInquiry.createdAt),
      propertyInquiry.source || 'Unknown',
      property?.displayName || 'N/A',
      location?.address || 'N/A',
      location?.city || 'N/A',
      location?.state || 'N/A',
      location?.zip?.toString() || 'N/A',
      user?.preferredCommunicationChannel || 'N/A',
      user?.email || 'N/A',
      user?.phoneNumber || 'N/A',
      user?.name || 'N/A',
      this.formatDate(renter?.desiredMoveInDate),
      renter?.creditScore?.toString() || 'N/A',
      renter?.monthlyIncome ? `$${renter.monthlyIncome}` : 'N/A',
      propertyInquiry.stage || 'N/A',
    ].map((value) => this.escapeCsvValue(value));
  }

  private formatDate(date: Date | null | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toISOString().split('T')[0]; // YYYY-MM-DD format
  }

  private escapeCsvValue(value: string): string {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  }

  private escapeXml(value: string): string {
    return value
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
}

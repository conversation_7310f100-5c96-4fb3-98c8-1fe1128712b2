import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
} from 'typeorm';
import { Conversation } from '../../../../shared/communication/conversation/entities/conversation.entity';
import { Company } from '../../../../shared/company/entities/company.entity';
import { Investor } from '../../../investor/investor.entity';
import { IntelligentEscalation } from '../../../property-question/entities/property-question.entity';
import { ShowingRequest } from '../../../showing-request/showing-request.entity';
import { Showing } from '../../../showing/showing.entity';
import { PropertyStatus } from '../enums/property-status.enum';
import { PropertyWithTypesDto } from '../model/property-with-types.dto';
import { Accessibility } from '../property-details/accessibility/accessibility.entity';
import { Amenities } from '../property-details/amenities/amenities.entity';
import { IncludedUtilities } from '../property-details/included-utilities/included-utilities.entity';
import { LeaseConditions } from '../property-details/lease-conditions/lease-conditions.entity';
import { PropertyLocation } from '../property-details/location/property-location.entity';
import { Parking } from '../property-details/parking/parking.entity';
import { PetPolicy } from '../property-details/pet-policy/pet-policy.entity';
import { RenterRequirements } from '../property-details/renter-requirements/renter-requirements.entity';
import { PropertySpecifications } from '../property-details/specifications/property-specifications.entity';
import { Expose } from 'class-transformer';
import { PropertyInquiry } from '../../../property-inquiry/property-inquiry.entity';
import { ShowingAgent } from '../../../showing-agent/entities/showing-agent.entity';
import { PropertyImage } from '../property-details/images/entities/property-image.entity';
import { PropertyAvailability } from '../../availability/property-availability.entity';
import { PropertyManagementType } from '../enums/property-management-type.enum';

@Entity()
export class Property {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Investor, { nullable: false, lazy: true })
  @JoinColumn()
  owner: Promise<Investor> | Investor;

  @ManyToOne(() => Company, { nullable: false, lazy: true })
  @JoinColumn()
  company: Promise<Company> | Company;

  @OneToMany(() => Conversation, (conversation) => conversation.property, {
    lazy: true,
    eager: false,
  })
  conversations: Promise<Conversation[]> | Conversation[];

  @OneToMany(() => PropertyInquiry, (propertyInquiry) => propertyInquiry.property, {
    lazy: true,
    eager: false,
  })
  propertyInquiries: Promise<PropertyInquiry[]> | PropertyInquiry[];

  @OneToMany(() => ShowingRequest, (showingRequest) => showingRequest.property, {
    lazy: true,
    eager: false,
  })
  showingRequests: Promise<ShowingRequest[]> | ShowingRequest[];

  @OneToMany(() => IntelligentEscalation, (propertyQuestion) => propertyQuestion.property, {
    lazy: true,
    eager: false,
  })
  questions: Promise<IntelligentEscalation[]> | IntelligentEscalation[];

  @OneToMany(() => Showing, (showing) => showing.property)
  showings: Promise<Showing[]> | Showing[];

  @Expose()
  @OneToOne(() => PetPolicy, (petPolicy) => petPolicy.property, {
    nullable: true,
    cascade: true,
    lazy: true,
    eager: false,
  })
  @JoinColumn()
  petPolicy: Promise<PetPolicy> | PetPolicy;

  @Expose()
  @OneToOne(() => IncludedUtilities, (includedUtilities) => includedUtilities.property, {
    nullable: true,
    cascade: true,
    lazy: true,
    eager: false,
  })
  @JoinColumn()
  includedUtilities: Promise<IncludedUtilities> | IncludedUtilities;

  @Expose()
  @OneToOne(() => Accessibility, (accessibility) => accessibility.property, {
    nullable: true,
    cascade: true,
    lazy: true,
    eager: false,
  })
  @JoinColumn()
  @Expose({ groups: ['property'] })
  accessibility: Promise<Accessibility> | Accessibility;

  @Expose()
  @OneToOne(() => Parking, (parking) => parking.property, {
    nullable: true,
    cascade: true,
    lazy: true,
    eager: false,
  })
  @JoinColumn()
  parking: Promise<Parking> | Parking;

  @Expose()
  @OneToOne(() => Amenities, (amenities) => amenities.property, {
    nullable: true,
    cascade: true,
    lazy: true,
  })
  @JoinColumn()
  amenities: Promise<Amenities> | Amenities;

  @Expose()
  @OneToOne(() => RenterRequirements, (renterRequirements) => renterRequirements.property, {
    nullable: true,
    cascade: true,
    lazy: true,
  })
  @JoinColumn()
  renterRequirements: Promise<RenterRequirements> | RenterRequirements;

  @Expose()
  @OneToOne(() => LeaseConditions, (leaseConditions) => leaseConditions.property, {
    nullable: true,
    cascade: true,
    lazy: true,
  })
  @JoinColumn()
  leaseConditions: Promise<LeaseConditions> | LeaseConditions;

  @Expose()
  @OneToOne(() => PropertySpecifications, {
    nullable: true,
    cascade: true,
    lazy: true,
  })
  @JoinColumn()
  specifications: Promise<PropertySpecifications> | PropertySpecifications;

  @Expose()
  @OneToOne(() => PropertyLocation, {
    nullable: true,
    cascade: true,
    lazy: true,
  })
  @JoinColumn()
  location: Promise<PropertyLocation> | PropertyLocation;

  @Expose()
  @OneToOne(() => PropertyAvailability, {
    nullable: true,
    cascade: true,
    lazy: true,
  })
  @JoinColumn()
  availability: Promise<PropertyAvailability> | PropertyAvailability;

  @Expose()
  @Column({ type: 'varchar', length: 201, nullable: false })
  displayName: string;

  @Expose()
  @Column({ type: 'varchar', length: 4097, nullable: true })
  coverImage: string;

  @Expose()
  @Column({ type: 'boolean', nullable: true, default: true })
  isAvailable? = true;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  allowsSmoking: boolean;

  @Expose()
  @Column({ type: 'int', nullable: true })
  maximumOccupancy: number;

  @Expose()
  @OneToMany(() => PropertyImage, (propertyImage) => propertyImage.property, { lazy: true })
  propertyImages: Promise<PropertyImage[]> | PropertyImage[];

  @Expose()
  @Column({ type: 'varchar', length: 4097, nullable: true })
  description: string;

  @Expose()
  @Column({ type: 'text', nullable: true })
  highlights: string;

  @Index()
  @Expose()
  @Column({
    type: 'varchar',
    length: 32,
    nullable: false,
    default: PropertyStatus.DRAFT,
  })
  status: PropertyStatus = PropertyStatus.DRAFT;

  @Expose()
  @Column({ type: 'timestamp', nullable: true })
  lastListedAt: Date;

  @Expose()
  @Column({ type: 'boolean', nullable: true, default: true })
  autoRefresh?: boolean = true;

  @Column({ type: 'varchar', length: 128, nullable: true })
  transUnionPropertyId: string;

  @Index()
  @Column({ type: 'varchar', length: 128, nullable: true })
  aptlyId: string;

  @DeleteDateColumn()
  deletedAt?: Date;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  @ManyToOne(() => ShowingAgent, { nullable: true, lazy: true })
  @JoinColumn()
  lastShowingAgent: Promise<ShowingAgent> | ShowingAgent;

  @Expose()
  @RelationId((property: Property) => property.lastShowingAgent)
  lastShowingAgentId: string;

  @Expose()
  @Column({ type: 'boolean', nullable: false, default: true })
  allowsInPersonTours: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true, default: null })
  allowsVirtualTours: boolean | null;

  @Expose()
  @Column({
    type: 'enum',
    enum: PropertyManagementType,
    nullable: true,
    default: null,
  })
  propertyManagementType: PropertyManagementType | null;

  @Expose()
  @Column({ type: 'varchar', length: 255, nullable: true })
  propertyManagementCompanyName: string | null;

  static getDtoWithTypes(property: Property): PropertyWithTypesDto {
    return {
      id: {
        value: property.id,
        type: 'string',
      },
      petPolicy: null,
      includedUtilities: null,
      accessibility: null,
      parking: null,
      renterAcceptanceCriteria: null,
      amenities: null,
      location: null,
      specifications: null,
      leaseConditions: null,
      displayName: {
        value: property.displayName,
        type: 'string',
      },
      allowsSmoking: {
        value: property.allowsSmoking,
        type: 'boolean',
      },
      maximumOccupancy: {
        value: property.maximumOccupancy,
        type: 'number',
      },
      description: {
        value: property.description,
        type: 'string',
      },
      allowsInPersonTours: {
        value: property.allowsInPersonTours,
        type: 'boolean',
      },
      allowsVirtualTours: {
        value: property.allowsVirtualTours,
        type: 'boolean',
      },
      propertyManagementType: {
        value: property.propertyManagementType,
        type: 'enum',
        enum: {
          type: 'PropertyManagementType',
          values: Object.values(PropertyManagementType),
        },
      },
      propertyManagementCompanyName: {
        value: property.propertyManagementCompanyName,
        type: 'string',
        aiInstructions: 'Set to null if not operated by a property management company',
      },
    };
  }
}

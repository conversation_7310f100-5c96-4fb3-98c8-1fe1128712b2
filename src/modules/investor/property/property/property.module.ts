import { forwardRef, Module } from '@nestjs/common';
import { PropertyController } from './property.controller';
import { PropertyService } from './property.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Property } from './entities/property.entity';
import { DataFeedModule } from '../../data-feed/data-feed.module';
import { AiModule } from '../../../ai/ai.module';
import { ConversationModule } from '../../../shared/communication/conversation/conversetion.module';
import { CompanyModule } from '../../../shared/company/company.module';
import { Accessibility } from './property-details/accessibility/accessibility.entity';
import { Amenities } from './property-details/amenities/amenities.entity';
import { IncludedUtilities } from './property-details/included-utilities/included-utilities.entity';
import { Parking } from './property-details/parking/parking.entity';
import { PetPolicy } from './property-details/pet-policy/pet-policy.entity';
import { RenterRequirements } from './property-details/renter-requirements/renter-requirements.entity';
import { AccessibilityService } from './property-details/accessibility/accessibility.service';
import { AmenitiesService } from './property-details/amenities/amenities.service';
import { IncludedUtilitiesService } from './property-details/included-utilities/included-utilities.service';
import { ParkingService } from './property-details/parking/parking.service';
import { RenterRequirementsService } from './property-details/renter-requirements/renter-requirements.service';
import { PetPolicyService } from './property-details/pet-policy/pet-policy.service';
import { PropertyLocation } from './property-details/location/property-location.entity';
import { PropertySpecifications } from './property-details/specifications/property-specifications.entity';
import { LeaseConditions } from './property-details/lease-conditions/lease-conditions.entity';
import { PropertySpecificationsService } from './property-details/specifications/property-specifications.service';
import { PropertyLocationService } from './property-details/location/property-location.service';
import { LeaseConditionsService } from './property-details/lease-conditions/lease-conditions.service';
import { PetPolicyController } from './property-details/pet-policy/pet-policy.controller';
import { PropertySpecificationsController } from './property-details/specifications/property-specifications.controller';
import { LeaseConditionsController } from './property-details/lease-conditions/lease-conditions.controller';
import { ParkingController } from './property-details/parking/parking.controller';
import { RenterRequirementsController } from './property-details/renter-requirements/renter-requirements.controller';
import { AccessibilityController } from './property-details/accessibility/accessibility.controller';
import { AmenitiesController } from './property-details/amenities/amenities.controller';
import { IncludedUtilitiesController } from './property-details/included-utilities/included-utilities.controller';
import { PropertyLocationController } from './property-details/location/property-location.controller';
import { FollowUpModule } from '../../../shared/communication/follow-up/follow-up.module';
import { ShowingRequestModule } from '../../showing-request/showing-request.module';
import { PropertyImageController } from './property-details/images/property-image.controller';
import { PropertyImagesModule } from './property-details/images/property-images.module';
import { DescriptionController } from './property-details/description/description.controller';
import { PlacesLookupModule } from '../location-discovery/places-lookup/places-lookup.module';
import { PropertyLocationNearbyPlace } from './property-details/location/nearby-place/property-location-nearby-place.entity';
import { GeocodingModule } from '../location-discovery/geocoding/geocoding.module';
import { ShowingModule } from '../../showing/showing.module';
import { InvestorEntityModule } from '../../investor/investor-entity.module';
import { AddressLookupModule } from '../location-discovery/address-lookup/address-lookup.module';
import { PropertyImage } from './property-details/images/entities/property-image.entity';
import { TransUnionModule } from '../../../shared/background-check/trans-union/trans-union.module';
import { FileModule } from '../../../shared/file/file.module';
import { ImageEnhancerModule } from '../../../shared/file/image-enhancer/image-enhancer.module';
import { Company } from '../../../shared/company/entities/company.entity';
import { PropertyInquiryModule } from '../../property-inquiry/property-inquiry.module';
import { PropertyAvailability } from '../availability/property-availability.entity';
import { PropertyAvailabilityService } from '../availability/property-availability.service';
import { PropertyListingModule } from '../../property-listing/property-listing.module';
import { DescriptionModule } from './property-details/description/description.module';
import { AvailabilitySlot } from '../../availability/availability-slot.entity';
import { AvailabilitySlotsService } from '../../availability/availability-slot.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Company,
      Property,
      PropertyImage,
      PropertyAvailability,
      AvailabilitySlot,
      Accessibility,
      Amenities,
      IncludedUtilities,
      Parking,
      PetPolicy,
      RenterRequirements,
      PropertyLocation,
      PropertyLocationNearbyPlace,
      PropertySpecifications,
      LeaseConditions,
    ]),
    AiModule,
    ImageEnhancerModule,
    forwardRef(() => DataFeedModule),
    ConversationModule,
    CompanyModule,
    PlacesLookupModule,
    GeocodingModule,
    forwardRef(() => ShowingModule),
    FollowUpModule,
    forwardRef(() => ShowingRequestModule),
    InvestorEntityModule,
    AddressLookupModule,
    FileModule,
    TransUnionModule,
    PropertyInquiryModule,
    PropertyImagesModule,
    forwardRef(() => PropertyListingModule),
    forwardRef(() => DescriptionModule),
  ],
  controllers: [
    PropertyController,
    PetPolicyController,
    PropertySpecificationsController,
    LeaseConditionsController,
    RenterRequirementsController,
    ParkingController,
    AccessibilityController,
    AmenitiesController,
    IncludedUtilitiesController,
    PropertyLocationController,
    PropertyImageController,
    DescriptionController,
  ],
  providers: [
    PropertyService,
    PropertyAvailabilityService,
    AvailabilitySlotsService,
    PetPolicyService,
    AccessibilityService,
    AmenitiesService,
    IncludedUtilitiesService,
    ParkingService,
    RenterRequirementsService,
    PetPolicyService,
    PropertySpecificationsService,
    PropertyLocationService,
    LeaseConditionsService,
  ],
  exports: [PropertyService],
})
export class PropertyModule {}

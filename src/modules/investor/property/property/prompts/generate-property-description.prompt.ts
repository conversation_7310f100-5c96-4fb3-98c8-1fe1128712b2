import { PromptTemplate } from '@langchain/core/prompts';

const template = `Image you are a home owner.
Please write an ad for a property description based on the information I provide.
Your goal is to rent out that property.
Please don't sound too salesy or too technical.
Don't come up with the details that are not in the description.
Street: {street}
Size: {size}
Floors: {floors}
Bedrooms: {bedrooms}
Bathrooms: {bathrooms}
{format_instructions}
`;

export function generatePropertyDescriptionPrompt(formatInstructions: string): PromptTemplate {
  return new PromptTemplate({
    template: template,
    inputVariables: ['street', 'size', 'floors', 'bedrooms', 'bathrooms'],
    partialVariables: { format_instructions: formatInstructions },
  });
}

import { PropertyWithPendingEventsDto } from './property-with-pending-events.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';

export class PortfolioDto {
  @ApiPropertyOptional()
  properties: PropertyWithPendingEventsDto[];

  @IsNumber()
  @ApiPropertyOptional()
  page: number;

  @IsNumber()
  @ApiPropertyOptional()
  limit: number;

  @IsNumber()
  @ApiPropertyOptional()
  totalPages: number;
}

import { instance<PERSON><PERSON><PERSON><PERSON> } from 'class-transformer';
import { ShowingRequestDto } from '../../../showing-request/showing-request.dto';
import { Property } from '../entities/property.entity';
import { AccessibilityDto } from '../property-details/accessibility/accessibility.dto';
import { Accessibility } from '../property-details/accessibility/accessibility.entity';
import { AmenitiesDto } from '../property-details/amenities/amenities.dto';
import { Amenities } from '../property-details/amenities/amenities.entity';
import { IncludedUtilitiesDto } from '../property-details/included-utilities/included-utilities.dto';
import { IncludedUtilities } from '../property-details/included-utilities/included-utilities.entity';
import { LeaseConditionsDto } from '../property-details/lease-conditions/lease-conditions.dto';
import { LeaseConditions } from '../property-details/lease-conditions/lease-conditions.entity';
import { PropertyLocationDto } from '../property-details/location/property-location.dto';
import { PropertyLocation } from '../property-details/location/property-location.entity';
import { ParkingDto } from '../property-details/parking/parking.dto';
import { Parking } from '../property-details/parking/parking.entity';
import { PetPolicy } from '../property-details/pet-policy/pet-policy.entity';
import { RenterRequirementsDto } from '../property-details/renter-requirements/renter-requirements.dto';
import { RenterRequirements } from '../property-details/renter-requirements/renter-requirements.entity';
import { PropertySpecificationsDto } from '../property-details/specifications/property-specifications.dto';
import { PropertySpecifications } from '../property-details/specifications/property-specifications.entity';
import { PetPolicyDto } from '../property-details/pet-policy/pet-policy.dto';
import { IsArray, IsBoolean, IsEnum, IsNumber, IsObject, IsOptional, IsString, Min } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PropertyBaseDto } from './property-base.dto';
import { File } from '../../../../shared/file/entities/file.entity';
import { PropertyManagementType } from '../enums/property-management-type.enum';

export class PropertyDto extends PropertyBaseDto {
  @IsObject()
  @IsOptional()
  @ApiPropertyOptional()
  petPolicy?: PetPolicyDto;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional()
  includedUtilities?: IncludedUtilitiesDto;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional()
  accessibility?: AccessibilityDto;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional()
  parking?: ParkingDto;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional()
  renterRequirements?: RenterRequirementsDto;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional()
  amenities?: AmenitiesDto;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional()
  leaseConditions?: LeaseConditionsDto;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional()
  location?: PropertyLocationDto;

  @IsObject()
  @IsOptional()
  @ApiPropertyOptional()
  specifications?: PropertySpecificationsDto;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional()
  images?: File[];

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional()
  isAvailable?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional()
  allowsSmoking?: boolean;

  @IsNumber()
  @IsOptional()
  @Min(1)
  @ApiPropertyOptional()
  maximumOccupancy?: number;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional()
  allowsInPersonTours?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional()
  allowsVirtualTours?: boolean;

  @IsEnum(PropertyManagementType)
  @IsOptional()
  @ApiPropertyOptional()
  propertyManagementType?: PropertyManagementType;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  propertyManagementCompanyName?: string;

  @IsString()
  @IsOptional()
  highlights?: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional()
  autoRefresh?: boolean;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional()
  showingRequests?: ShowingRequestDto[];
}

export async function convertPropertyToDto(property: Property, withRelations = false): Promise<PropertyDto> {
  const propertyDto = <PropertyDto>instanceToPlain(property, { excludeExtraneousValues: true });

  if (withRelations) {
    propertyDto.accessibility = Accessibility.convertToDto(
      Object.assign(new Accessibility(), await property.accessibility),
    );
    propertyDto.parking = Parking.convertToDto(Object.assign(new Parking(), await property.parking));
    propertyDto.petPolicy = PetPolicy.convertToDto(Object.assign(new PetPolicy(), await property.petPolicy));
    propertyDto.includedUtilities = IncludedUtilities.convertToDto(
      Object.assign(new IncludedUtilities(), await property.includedUtilities),
    );
    propertyDto.renterRequirements = RenterRequirements.convertToDto(
      Object.assign(new RenterRequirements(), await property.renterRequirements),
    );
    propertyDto.amenities = Amenities.convertToDto(Object.assign(new Amenities(), await property.amenities));
    propertyDto.leaseConditions = LeaseConditions.convertToDto(
      Object.assign(new LeaseConditions(), await property.leaseConditions),
    );
    propertyDto.location = PropertyLocation.convertToDto(
      Object.assign(new PropertyLocation(), await property.location),
    );
    propertyDto.specifications = PropertySpecifications.convertToDto(
      Object.assign(new PropertySpecifications(), await property.specifications),
    );

    const propertyImages = await property.propertyImages;
    propertyDto.images = propertyImages.filter((pi) => pi?.file).map((pi) => pi?.file);
  }

  return propertyDto;
}

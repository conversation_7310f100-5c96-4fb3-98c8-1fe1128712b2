import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PropertyStatus } from '../enums/property-status.enum';

export class PropertyBaseDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  id: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  displayName: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  coverImage?: string;

  @IsEnum(PropertyStatus)
  @IsOptional()
  @ApiPropertyOptional({ enum: PropertyStatus, enumName: 'PropertyStatus' })
  status?: PropertyStatus;

  @IsOptional()
  @ApiPropertyOptional()
  lastListedAt?: Date;
}

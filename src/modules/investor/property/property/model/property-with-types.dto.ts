import { FieldValueWithType } from '../../../../ai/models/field-value-with-type';
import { Property } from '../entities/property.entity';
import { PetPolicy } from '../property-details/pet-policy/pet-policy.entity';
import { PetPolicyWithTypesDto } from '../property-details/pet-policy/pet-policy-with-types.dto';
import { IncludedUtilitiesWithTypesDto } from '../property-details/included-utilities/included-utilities-with-types.dto';
import { AccessibilityWithTypesDto } from '../property-details/accessibility/accessibility-with-types.dto';
import { RenterRequirementsWithTypesDto } from '../property-details/renter-requirements/renter-requirements-with-types.dto';
import { AmenitiesWithTypesDto } from '../property-details/amenities/amenities-with-types.dto';
import { ParkingWithTypesDto } from '../property-details/parking/parking-with-types.dto';
import { Accessibility } from '../property-details/accessibility/accessibility.entity';
import { Parking } from '../property-details/parking/parking.entity';
import { IncludedUtilities } from '../property-details/included-utilities/included-utilities.entity';
import { Amenities } from '../property-details/amenities/amenities.entity';
import { RenterRequirements } from '../property-details/renter-requirements/renter-requirements.entity';
import { PropertySpecificationsWithTypesDto } from '../property-details/specifications/property-specifications-with-types.dto';
import { LeaseConditionsWithTypesDto } from '../property-details/lease-conditions/lease-conditions-with-types.dto';
import { PropertyLocationWithTypesDto } from '../property-details/location/property-location-with-types.dto';
import { PropertyLocation } from '../property-details/location/property-location.entity';
import { PropertySpecifications } from '../property-details/specifications/property-specifications.entity';
import { LeaseConditions } from '../property-details/lease-conditions/lease-conditions.entity';

/*
  This dto is needed for the AI to understand the types of the properties that are being passed in.
  It is used when we need to parse the property object to JSON and send it to the AI.
  The AI needs to know the types of the properties in order to parse the object correctly
 */
export class PropertyWithTypesDto {
  id: FieldValueWithType;
  petPolicy: PetPolicyWithTypesDto;
  includedUtilities: IncludedUtilitiesWithTypesDto;
  accessibility: AccessibilityWithTypesDto;
  parking: ParkingWithTypesDto;
  renterAcceptanceCriteria: RenterRequirementsWithTypesDto;
  amenities: AmenitiesWithTypesDto;
  displayName: FieldValueWithType;
  location: PropertyLocationWithTypesDto;
  specifications: PropertySpecificationsWithTypesDto;
  leaseConditions: LeaseConditionsWithTypesDto;
  allowsSmoking: FieldValueWithType;
  maximumOccupancy: FieldValueWithType;
  description: FieldValueWithType;
  allowsInPersonTours: FieldValueWithType;
  allowsVirtualTours: FieldValueWithType;
  propertyManagementType: FieldValueWithType;
  propertyManagementCompanyName: FieldValueWithType;
}

// null is used for the default value of the property
export async function convertPropertyToDtoWithTypes(
  property: Property,
  withRelations: boolean,
): Promise<PropertyWithTypesDto> {
  const propertyWithTypesDto = Property.getDtoWithTypes(property);

  if (withRelations) {
    let petPolicy = await property.petPolicy;

    if (!petPolicy) {
      petPolicy = new PetPolicy();
    }
    propertyWithTypesDto.petPolicy = PetPolicy.getDtoWithTypes(petPolicy);

    let includedUtilities = await property.includedUtilities;

    if (!includedUtilities) {
      includedUtilities = new IncludedUtilities();
    }
    propertyWithTypesDto.includedUtilities = IncludedUtilities.getDtoWithTypes(includedUtilities);

    let accessibility = await property.accessibility;

    if (!accessibility) {
      accessibility = new Accessibility();
    }

    propertyWithTypesDto.accessibility = Accessibility.convertToDtoWithTypes(accessibility);

    let parking = await property.parking;

    if (!parking) {
      parking = new Parking();
    }

    propertyWithTypesDto.parking = Parking.getDtoWithTypes(parking);

    let renterAcceptanceCriteria = await property.renterRequirements;

    if (!renterAcceptanceCriteria) {
      renterAcceptanceCriteria = new RenterRequirements();
    }

    propertyWithTypesDto.renterAcceptanceCriteria = RenterRequirements.getDtoWithTypes(renterAcceptanceCriteria);

    let amenities = await property.amenities;

    if (!amenities) {
      amenities = new Amenities();
    }
    propertyWithTypesDto.amenities = Amenities.getDtoWithTypes(amenities);

    let location = await property.location;

    if (!location) {
      location = new PropertyLocation();
    }

    propertyWithTypesDto.location = PropertyLocation.getDtoWithTypes(location);

    let specifications = await property.specifications;

    if (!specifications) {
      specifications = new PropertySpecifications();

      propertyWithTypesDto.specifications = PropertySpecifications.getDtoWithTypes(specifications);
    }

    let leaseConditions = await property.leaseConditions;

    if (!leaseConditions) {
      leaseConditions = new LeaseConditions();
    }

    propertyWithTypesDto.leaseConditions = LeaseConditions.getDtoWithTypes(leaseConditions);
  }

  return propertyWithTypesDto;
}

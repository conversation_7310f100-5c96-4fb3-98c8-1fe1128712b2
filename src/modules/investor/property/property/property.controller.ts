import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { IsPropertyOwnerGuard } from '../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { Role } from '../../../shared/auth/models/roles-enum';

import { convertPropertyToDto, PropertyDto } from './model/property.dto';
import { PropertyService } from './property.service';
import { ShowingReqsAndQuestionsCountDto } from './model/showing-reqs-and-questions-count.dto';
import { PortfolioDto } from './model/portfolio.dto';

import { IsCompanyEmployeeGuard } from '../../../../guards/investor/is-company-employee.guard';

@ApiTags('property')
@Controller('property')
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@ApiBearerAuth()
export class PropertyController {
  constructor(private readonly propertyService: PropertyService) {}

  @Get('srs-and-questions-count/:companyId')
  @ApiOkResponse({
    description: 'Get count of pending showing requests and questions',
  })
  @UseGuards(IsCompanyEmployeeGuard)
  async getShowingRequestsAndQuestionsCount(
    @Param('companyId') companyId: string,
  ): Promise<ShowingReqsAndQuestionsCountDto> {
    return this.propertyService.getPendingShowingRequestsAndQuestionCountsByCompany(companyId);
  }

  @Get('portfolio')
  @ApiOkResponse({
    description: 'Get portfolio',
  })
  async getPortfolio(
    @Req() req: Request,
    @Query('page') page: string,
    @Query('limit') limit: string,
  ): Promise<PortfolioDto> {
    const pageNumber = parseInt(page, 10) || 1;
    const limitNumber = parseInt(limit, 10) || 10;
    return await this.propertyService.getPortfolio(req.user.id, pageNumber, limitNumber);
  }

  @Get(':propertyId')
  @ApiParam({
    name: 'propertyId',
    required: true,
  })
  @ApiOkResponse({
    description: 'Get a property by id',
  })
  @UseGuards(IsPropertyOwnerGuard)
  async getById(@Param('propertyId') propertyId: string): Promise<PropertyDto> {
    const property = await this.propertyService.findById(propertyId, true, false, true);
    return convertPropertyToDto(property, true);
  }

  @Delete(':propertyId')
  @ApiParam({
    name: 'propertyId',
    required: true,
  })
  @ApiOkResponse({
    description: 'Delete a property by id',
  })
  @UseGuards(IsPropertyOwnerGuard)
  async delete(@Param('propertyId') propertyId: string): Promise<void> {
    return await this.propertyService.delete(propertyId);
  }

  @Post()
  @ApiOkResponse({
    description: 'Create a new property',
  })
  async createProperty(@Body() propertyData: PropertyDto, @Req() req: Request): Promise<PropertyDto> {
    const property = await this.propertyService.createProperty(propertyData, req.user.id);

    return convertPropertyToDto(property, true);
  }

  @Put(':propertyId')
  @ApiOkResponse({
    description: 'Update a property by id',
  })
  @UseGuards(IsPropertyOwnerGuard)
  async updateProperty(@Body() propertyData: PropertyDto, @Req() req: Request): Promise<PropertyDto> {
    const updatedProperty = await this.propertyService.update(req.property.id, propertyData);

    return convertPropertyToDto(updatedProperty, true);
  }
}

import { <PERSON>, Param, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { DescriptionService } from './description.service';

@ApiTags('description')
@Controller('property/:propertyId/description')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class DescriptionController {
  constructor(private readonly amenitiesService: DescriptionService) {}

  @Put('generate')
  @ApiOkResponse({
    description: 'Generate description',
  })
  async update(@Param('propertyId') propertyId: string): Promise<string> {
    return await this.amenitiesService.generate(propertyId);
  }
}

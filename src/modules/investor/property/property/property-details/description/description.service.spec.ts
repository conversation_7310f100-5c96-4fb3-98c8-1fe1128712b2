import { Test, TestingModule } from '@nestjs/testing';
import { DescriptionService } from './description.service';
import { AiService } from '../../../../../ai/ai.service';
import { PropertyService } from '../../property.service';
import { File } from '../../../../../shared/file/entities/file.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { PropertyImage } from '../images/entities/property-image.entity';
import { Property } from '../../entities/property.entity';

describe('DescriptionService', () => {
  let service: DescriptionService;
  let aiService: jest.Mocked<AiService>;
  let propertyImageRepository: any;
  let propertyRepository: any;

  beforeEach(async () => {
    const mockAiService = {
      getResponse: jest.fn(),
    };

    const mockPropertyService = {
      findById: jest.fn(),
    };

    const mockPropertyImageRepository = {
      find: jest.fn().mockResolvedValue([]),
      findOne: jest.fn(),
      save: jest.fn(),
      create: jest.fn(),
    };

    const mockPropertyRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
      create: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DescriptionService,
        {
          provide: AiService,
          useValue: mockAiService,
        },
        {
          provide: PropertyService,
          useValue: mockPropertyService,
        },
        {
          provide: getRepositoryToken(PropertyImage),
          useValue: mockPropertyImageRepository,
        },
        {
          provide: getRepositoryToken(Property),
          useValue: mockPropertyRepository,
        },
      ],
    }).compile();

    service = module.get<DescriptionService>(DescriptionService);
    aiService = module.get(AiService);
    propertyImageRepository = module.get(getRepositoryToken(PropertyImage));
    propertyRepository = module.get(getRepositoryToken(Property));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('extractAppealingImageTags', () => {
    it('should categorize image tags correctly', () => {
      const mockImages = [
        {
          id: '1',
          tags: ['living room', 'modern', 'bright', 'hardwood floors'],
        } as unknown as File,
        {
          id: '2',
          tags: ['kitchen', 'luxury', 'granite counters', 'stainless appliances'],
        } as unknown as File,
        {
          id: '3',
          tags: ['bedroom', 'spacious', 'large windows'],
        } as unknown as File,
        {
          id: '4',
          tags: null, // No tags
        } as unknown as File,
      ];

      // Access the private method for testing
      const result = (service as any).extractAppealingImageTags(mockImages);

      expect(result.roomTypes).toEqual(['living room', 'kitchen', 'bedroom']);
      expect(result.appealingAttributes).toEqual(['modern', 'bright', 'luxury', 'spacious']);
      expect(result.luxuryFeatures).toEqual([
        'hardwood floors',
        'granite counters',
        'stainless appliances',
        'large windows',
      ]);
      expect(result.allTags).toContain('living room');
      expect(result.allTags).toContain('modern');
      expect(result.allTags).toContain('granite counters');
    });

    it('should handle duplicate tags across images', () => {
      const mockImages = [
        {
          id: '1',
          tags: ['modern', 'bright'],
        } as unknown as File,
        {
          id: '2',
          tags: ['modern', 'luxury'], // 'modern' appears again
        } as unknown as File,
      ];

      const result = (service as any).extractAppealingImageTags(mockImages);

      expect(result.appealingAttributes).toEqual(['modern', 'bright', 'luxury']);
      expect(result.allTags).toEqual(['modern', 'bright', 'luxury']);
    });

    it('should handle images with no tags', () => {
      const mockImages = [{ id: '1', tags: null } as unknown as File, { id: '2', tags: [] } as unknown as File];

      const result = (service as any).extractAppealingImageTags(mockImages);

      expect(result.roomTypes).toEqual([]);
      expect(result.appealingAttributes).toEqual([]);
      expect(result.luxuryFeatures).toEqual([]);
      expect(result.allTags).toEqual([]);
    });
  });

  describe('generate', () => {
    it('should include image tags in AI request', async () => {
      const mockProperty = {
        id: 'property-1',
        highlights: ['Pet Friendly', 'Recently Renovated'],
        images: Promise.resolve([
          {
            id: '1',
            tags: ['living room', 'modern', 'bright', 'hardwood floors'],
          } as unknown as File,
          {
            id: '2',
            tags: ['kitchen', 'luxury', 'granite counters'],
          } as unknown as File,
        ]),
        petPolicy: Promise.resolve({}),
        includedUtilities: Promise.resolve({}),
        accessibility: Promise.resolve({}),
        parking: Promise.resolve({}),
        amenities: Promise.resolve({}),
        location: Promise.resolve({
          nearbyPlaces: [],
        }),
        specifications: Promise.resolve({}),
      };

      const mockPropertyImages = [
        {
          propertyId: 'property-1',
          fileId: '1',
          tags: ['living room', 'modern', 'bright', 'hardwood floors'],
        },
        {
          propertyId: 'property-1',
          fileId: '2',
          tags: ['kitchen', 'luxury', 'granite counters'],
        },
      ];

      propertyRepository.findOne.mockResolvedValue(mockProperty as any);
      propertyImageRepository.find.mockResolvedValue(mockPropertyImages);
      aiService.getResponse.mockResolvedValue('Generated description with appealing features');

      await service.generate('property-1');

      expect(aiService.getResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          imageTags: expect.stringMatching(
            /roomTypes.*appealingAttributes.*luxuryFeatures|luxuryFeatures.*appealingAttributes.*roomTypes|appealingAttributes.*roomTypes.*luxuryFeatures/,
          ),
        }),
        expect.any(String),
        null,
        expect.any(String),
      );
    });
  });
});

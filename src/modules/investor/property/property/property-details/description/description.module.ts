import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { PropertyImage } from '../images/entities/property-image.entity';
import { AiModule } from '../../../../../ai/ai.module';
import { DescriptionService } from './description.service';
import { DescriptionController } from './description.controller';
import { Property } from '../../entities/property.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PropertyImage, Property]), AiModule],
  controllers: [DescriptionController],
  providers: [DescriptionService],
  exports: [DescriptionService],
})
export class DescriptionModule {}

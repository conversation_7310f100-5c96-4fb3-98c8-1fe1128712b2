export const rephraseDescriptionTemplate = `
<task>
  You are helping a property manager rephrase an existing property description to make it appear fresh and updated.
  Your goal is to rephrase the content without changing any specific facts, numbers, or key details.
  The rephrased description should follow the tone of original description and maintain all the original information.
</task>

<instructions>
- No explanation needed. Only return the rephrased description.
- Keep ALL specific details exactly the same (number of bedrooms, bathrooms, square footage, amenities, location details, etc.)
- Do NOT add any new information that wasn't in the original description
- Do NOT remove any important information from the original description
- Change the sentence structure, word order, and phrasing to make it sound different
- Use synonyms and alternative expressions where appropriate
- Maintain the same tone and professionalism as the original
- Keep the same length approximately (don't make it significantly longer or shorter)
- Make sure you don't violate any real estate fair housing laws in any way
- The goal is to make the listing appear "updated" to platforms while keeping all facts identical
- Don't add words like 'updated' at the beginning of the ad. It will go to leasing platforms directly without any changes.
</instructions>

<example_transformations>
Original: "Spacious 2 bedroom, 1 bathroom apartment in downtown. Features modern kitchen with stainless steel appliances."
Rephrased: "Modern 2 bedroom, 1 bathroom unit located in the heart of downtown. Includes contemporary kitchen equipped with stainless steel appliances."

Original: "Beautiful home with hardwood floors throughout and a large backyard perfect for entertaining."
Rephrased: "Stunning residence featuring hardwood flooring and an expansive backyard ideal for hosting gatherings."
</example_transformations>

<current_description>
{description}
</current_description>
`;

export const generateDescriptionTemplate = `
<task>
  Image you are renting out your property. 
  You want to make sure that your property is well described so that potential renters can easily find it.
  You want to generate a description for your property based on these highlights.
  
  The text should be ready to be published on a property listing website.
</task>

<instructions>
- No explanation needed. Only return the generated description.
- You will be given a property information, use it to generate a description. 
You don't have to use all the information, only use the information that you think is relevant 
and will make the property more appealing.
- You will be given a list of highlights. Use these highlights to generate the description, but don't overuse them.
- You will be given AI-analyzed image tags that describe the property's visual features. Use these to highlight
the most appealing aspects of the property. Focus on luxury features, modern amenities, and attractive attributes
that would appeal to potential renters. Prioritize mentioning the most appealing visual features identified by AI analysis.
Don't try to include too much of it. If there is something outstanding, that attracts potential renters,
like a pool, fireplace, etc, put it in the very first sentence.
- You will be given format example, try to stick close to it, but feel free to modify. If
some information is missing, don't make it up, simply don't mention it. If there is more information,
feel free to mention it if it makes sense.
- Don't sound cheesy, make it sound professional and appealing.
- Make sure you don't violate any real estate fair housing laws in any way
- You will be given a list of places nearby, like schools or parks. Try to use it in the description.
For example, if place is pet friendly mention that there is a park nearby to walk it. If children are allowed
and there are schools with a good rating nearby, mention it too.
- Adjust ad tone according to the property segment. For example, if it's a luxury property, use more luxurious
language, if it's a budget property, use more budget-friendly language.
- If it's a room emphasize it in the first sentence. Be very specific about it since people miss it a lot.
</instructions>

<example>
(Spacious/Newly renovated/Beautiful) (#) bedroom, (#) bathroom (apartment/home/<USER>
One or two sentences about the property and surrounding area.

Highlights:
Written in bullets.  
Include anything that is unique about the home or that could be a big selling point.  
Include if it is pet friendly, if it has been remodeled, 
if it has a convenient location to anything, if there is parking and if so, 
what kind, what the deal is with the laundry (in unit, in building, hookups, or where the nearest laundromat is. 
Write which utilities are included in the rent.
Any other information from the property information that you think is relevant.

Area:
Description of the area if it's available from the data provided.

Closing statement (tell when it's available, prompt go get in touch to schedule a showing).

</example>

<highlights>
{highlights}
</highlights>

<image_tags>
{imageTags}
</image_tags>

<property>
{property}
</property>
`;

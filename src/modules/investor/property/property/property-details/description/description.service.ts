import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AiService } from '../../../../../ai/ai.service';
import { PropertySpecifications } from '../specifications/property-specifications.entity';
import { PropertyLocation } from '../location/property-location.entity';
import { PetPolicy } from '../pet-policy/pet-policy.entity';
import { IncludedUtilities } from '../included-utilities/included-utilities.entity';
import { Accessibility } from '../accessibility/accessibility.entity';
import { Parking } from '../parking/parking.entity';
import { Amenities } from '../amenities/amenities.entity';
import { PropertyLocationNearbyPlace } from '../location/nearby-place/property-location-nearby-place.entity';
import { generateDescriptionTemplate } from './prompts/generate-description.template';
import { rephraseDescriptionTemplate } from './prompts/rephrase-description.template';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { PropertyImage } from '../images/entities/property-image.entity';
import { Property } from '../../entities/property.entity';

@Injectable()
export class DescriptionService {
  constructor(
    @InjectRepository(PropertyImage)
    private readonly propertyImageRepository: Repository<PropertyImage>,
    private readonly aiService: AiService,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async generate(propertyId: string): Promise<string> {
    const property = await this.propertyRepository.findOne({
      where: { id: propertyId },
      relations: [
        'petPolicy',
        'includedUtilities',
        'accessibility',
        'parking',
        'amenities',
        'location',
        'specifications',
        'location.nearbyPlaces',
      ],
    });

    const petPolicy = PetPolicy.convertToDto(await property.petPolicy, true);
    const includedUtilities = IncludedUtilities.convertToDto(await property.includedUtilities, true);
    const accessibility = Accessibility.convertToDto(await property.accessibility, true);
    const parking = Parking.convertToDto(await property.parking, true);
    const amenities = Amenities.convertToDto(await property.amenities, true);
    const location = PropertyLocation.convertToDto(await property.location, true);
    const specifications = PropertySpecifications.convertToDto(await property.specifications, true);
    const nearbyPlaces = await (await property.location).nearbyPlaces;
    const nearbyPlacesDto = [];

    nearbyPlaces.forEach((nearbyPlace) => {
      nearbyPlacesDto.push(PropertyLocationNearbyPlace.convertToDto(nearbyPlace, true));
    });

    // Extract image tags to highlight appealing features
    const propertyImages = await this.propertyImageRepository.find({
      where: { propertyId: propertyId },
    });
    const imageTags = this.extractAppealingImageTags(propertyImages);

    return await this.aiService.getResponse(
      {
        highlights: JSON.stringify(property.highlights),
        imageTags: JSON.stringify(imageTags),
        property: JSON.stringify({
          specifications: JSON.stringify(specifications),
          location: JSON.stringify(location),
          petPolicy: JSON.stringify(petPolicy),
          includedUtilities: JSON.stringify(includedUtilities),
          accessibility: JSON.stringify(accessibility),
          parking: JSON.stringify(parking),
          amenities: JSON.stringify(amenities),
          interestingPlacesNearby: JSON.stringify(nearbyPlacesDto),
        }),
      },
      generateDescriptionTemplate,
      null,
      LanguageModelsEnum.GPT_5_MINI,
    );
  }

  async rephraseDescription(description: string): Promise<string> {
    return await this.aiService.getResponse(
      {
        description: description,
      },
      rephraseDescriptionTemplate,
      null,
      LanguageModelsEnum.GPT_4_CREATIVE,
    );
  }

  private extractAppealingImageTags(propertyImages: PropertyImage[]): {
    roomTypes: string[];
    appealingAttributes: string[];
    luxuryFeatures: string[];
    allTags: string[];
  } {
    const allTags: string[] = [];
    const roomTypes: string[] = [];
    const appealingAttributes: string[] = [];
    const luxuryFeatures: string[] = [];

    // Define categories for better organization
    const roomTypeKeywords = [
      'living room',
      'kitchen',
      'bedroom',
      'dining room',
      'bathroom',
      'office',
      'balcony',
      'patio',
      'exterior',
      'laundry room',
    ];

    const appealingAttributeKeywords = [
      'luxury',
      'modern',
      'bright',
      'spacious',
      'updated',
      'cozy',
      'contemporary',
      'elegant',
      'stunning',
      'beautiful',
    ];

    const luxuryFeatureKeywords = [
      'granite counters',
      'stainless appliances',
      'hardwood floors',
      'large windows',
      'high ceilings',
      'fireplace',
      'marble',
      'quartz',
      'crown molding',
      'exposed brick',
    ];

    // Extract and categorize tags from all property images
    propertyImages.forEach((propertyImage) => {
      if (propertyImage.tags && propertyImage.tags.length > 0) {
        propertyImage.tags.forEach((tag) => {
          const normalizedTag = tag.toLowerCase().trim();

          if (!allTags.includes(normalizedTag)) {
            allTags.push(normalizedTag);

            if (roomTypeKeywords.includes(normalizedTag)) {
              roomTypes.push(normalizedTag);
            } else if (appealingAttributeKeywords.includes(normalizedTag)) {
              appealingAttributes.push(normalizedTag);
            } else if (luxuryFeatureKeywords.includes(normalizedTag)) {
              luxuryFeatures.push(normalizedTag);
            }
          }
        });
      }
    });

    return {
      roomTypes: [...new Set(roomTypes)], // Remove duplicates
      appealingAttributes: [...new Set(appealingAttributes)],
      luxuryFeatures: [...new Set(luxuryFeatures)],
      allTags: [...new Set(allTags)],
    };
  }
}

import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { PropertyLocationDto } from './property-location.dto';
import { PropertyLocationWithTypesDto } from './property-location-with-types.dto';
import { Property } from '../../entities/property.entity';
import { PropertyLocationNearbyPlace } from './nearby-place/property-location-nearby-place.entity';

@Entity()
export class PropertyLocation {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Property, (property) => property.location, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @OneToMany(() => PropertyLocationNearbyPlace, (nearbyPlace) => nearbyPlace.propertyLocation, {
    cascade: true,
    lazy: true,
  })
  nearbyPlaces: Promise<PropertyLocationNearbyPlace[]> | PropertyLocationNearbyPlace[];

  @Expose()
  @Column({ type: 'varchar', length: 20, nullable: false })
  state: string;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: false })
  city: string;

  @Expose()
  @Index()
  @Column({ type: 'varchar', length: 100, nullable: false })
  address: string;

  @Expose()
  @Column({ type: 'varchar', length: 10, nullable: true })
  apartmentNumber: string;

  @Expose()
  @Column({ type: 'boolean', default: false })
  isAddressHidden: boolean;

  @Expose()
  @Column({ type: 'int', nullable: true })
  zip: number;

  @Expose()
  @Column({ type: 'float', nullable: true })
  latitude: number;

  @Expose()
  @Column({ type: 'float', nullable: true })
  longitude: number;

  @Expose()
  @Column({ type: 'varchar', length: 100, nullable: true })
  timeZone: string;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  public static convertToDto(location: PropertyLocation, excludeNullValues = false): PropertyLocationDto {
    if (!location || !location.id) {
      return null;
    }

    const result = <PropertyLocationDto>instanceToPlain(location, {
      excludeExtraneousValues: true,
    });

    if (excludeNullValues) {
      Object.keys(result).forEach((key) => result[key] === null && delete result[key]);
    }

    return result;
  }

  public static getDtoWithTypes(location: PropertyLocation): PropertyLocationWithTypesDto {
    return {
      address: {
        value: location.address,
        type: 'string',
      },
      city: {
        value: location.city,
        type: 'string',
      },
      state: {
        value: location.state,
        type: 'string',
      },
      zip: {
        value: location.zip,
        type: 'string',
      },
      latitude: {
        value: location.latitude,
        type: 'number',
      },
      longitude: {
        value: location.longitude,
        type: 'number',
      },
    };
  }
}

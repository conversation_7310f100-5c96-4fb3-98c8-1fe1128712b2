import { Repository } from 'typeorm';

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import ParsingUtils from '../../../../../../utils/parsing.utils';
import { GeocodingService } from '../../../location-discovery/geocoding/geocoding.service';
import { PlacesLookupService } from '../../../location-discovery/places-lookup/places-lookup.service';
import { Property } from '../../entities/property.entity';
import { PropertyLocationNearbyPlace } from './nearby-place/property-location-nearby-place.entity';
import { PropertyLocationDto } from './property-location.dto';
import { PropertyLocation } from './property-location.entity';
import { TransUnionService } from '../../../../../shared/background-check/trans-union/trans-union.service';

@Injectable()
export class PropertyLocationService {
  constructor(
    @InjectRepository(PropertyLocation)
    private readonly propertyLocationRepository: Repository<PropertyLocation>,
    @InjectRepository(PropertyLocationNearbyPlace)
    private readonly placesRepository: Repository<PropertyLocationNearbyPlace>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    private readonly placesLookupService: PlacesLookupService,
    private readonly geocodingService: GeocodingService,
    private readonly transUnionService: TransUnionService,
  ) {}

  async create(propertyId: string, payload: PropertyLocationDto): Promise<PropertyLocation> {
    const result = await this.propertyLocationRepository
      .createQueryBuilder()
      .insert()
      .values({ ...payload })
      .returning('*')
      .execute();

    const propertyLocation = Object.assign(new PropertyLocation(), { ...result.raw[0] });

    await this.propertyRepository.update(propertyId, {
      location: { id: propertyLocation.id },
    });

    return propertyLocation;
  }

  async update(
    propertyLocationId: string,
    propertyId: string,
    payload: PropertyLocationDto,
  ): Promise<PropertyLocation> {
    const property = await this.propertyRepository.findOneOrFail({
      where: { id: propertyId },
    });
    const location = await this.propertyLocationRepository.findOneOrFail({
      where: { id: propertyLocationId },
    });

    const updatedLocation = Object.assign(new PropertyLocation(), { ...location, ...payload });
    updatedLocation.timeZone = await this.geocodingService.getTimezone(
      updatedLocation.latitude,
      updatedLocation.longitude,
    );

    await this.propertyLocationRepository.update(propertyLocationId, updatedLocation);

    if (
      location.address !== updatedLocation.address ||
      location.city !== updatedLocation.city ||
      location.state !== updatedLocation.state ||
      location.zip !== updatedLocation.zip
    ) {
      await this.removeNearbyPlaces(location);
      await this.addNearbyPlaces(updatedLocation);
      await this.propertyRepository.update(propertyId, {
        displayName: payload.address,
      });

      this.transUnionService
        .updateProperty(property)
        .catch((error) => console.error('Failed to update property in TransUnion', error));
    }

    return updatedLocation;
  }

  getUpdatedLocation(location: PropertyLocation, data: any): PropertyLocation {
    if (!location) {
      location = new PropertyLocation();
    }

    return this.mapFieldValuesToEntity(location, data);
  }

  async addNearbyPlaces(location: PropertyLocation): Promise<void> {
    if (!location.latitude || !location.longitude) {
      return;
    }

    const nearbyPlaces = await this.placesLookupService.getNearbyPlaces(location.latitude, location.longitude, 500, [
      'school',
      'park',
      'hospital',
      'university',
      'shopping_mall',
    ]);

    await this.placesRepository.delete({
      propertyLocation: location,
    });

    if (nearbyPlaces) {
      nearbyPlaces.forEach((place) => {
        place.propertyLocation = location;
      });

      await this.placesRepository.insert(nearbyPlaces);
    }
  }

  async removeNearbyPlaces(location: PropertyLocation): Promise<void> {
    await this.placesRepository.delete({
      propertyLocation: location,
    });
  }

  mapFieldValuesToEntity(location: PropertyLocation, data: any): PropertyLocation {
    const locationCopy = { ...location };

    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;
      try {
        switch (item.fieldName.split('.').reverse()[0]) {
          case 'city':
            locationCopy.city = item.value;
            break;
          case 'state':
            locationCopy.state = item.value;
            break;
          case 'zip':
            locationCopy.zip = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'latitude':
            locationCopy.latitude = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'longitude':
            locationCopy.longitude = ParsingUtils.parseStringToNumber(item.value);
            break;
          default:
            break;
        }
      } catch (error) {
        throw new Error(error);
      }
    }

    return locationCopy;
  }
}

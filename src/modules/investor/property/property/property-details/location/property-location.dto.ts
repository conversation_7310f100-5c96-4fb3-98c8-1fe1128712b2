import { IsBoolean, <PERSON>N<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class PropertyLocationDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  address: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  apartmentNumber: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional()
  isAddressHidden: boolean;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  city: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  state: string;

  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional()
  zip: number;

  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional()
  latitude: number;

  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional()
  longitude: number;

  @IsString()
  @IsOptional()
  timeZone: string;
}

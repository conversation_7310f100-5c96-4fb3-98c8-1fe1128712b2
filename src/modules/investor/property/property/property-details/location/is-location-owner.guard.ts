import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PropertyLocation } from './property-location.entity';

@Injectable()
export class IsLocationOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(PropertyLocation)
    private readonly propertyLocationRepository: Repository<PropertyLocation>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const locationId = request.params.propertyLocationId || request.body.propertyLocationId;
    const propertyId = request.params.propertyId || request.body.propertyId;

    if (!locationId) {
      throw new NotFoundException('No location id provided');
    }

    if (!propertyId) {
      throw new NotFoundException('No property id provided');
    }

    const userOwnsAccessibility = await this.propertyLocationRepository
      .createQueryBuilder('location')
      .innerJoin('location.property', 'property')
      .where('location.id = :locationId', { locationId })
      .andWhere('property.id = :propertyId', { propertyId })
      .getCount()
      .then((count) => count > 0);

    if (!userOwnsAccessibility) {
      throw new ForbiddenException('User does not own this property location');
    }

    return true;
  }
}

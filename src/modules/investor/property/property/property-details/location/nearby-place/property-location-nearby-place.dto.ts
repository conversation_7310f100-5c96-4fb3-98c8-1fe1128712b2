import { IsNumber, IsOptional, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class PropertyLocationNearbyPlaceDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  name: string;

  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional()
  rating: number;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  primaryType: string;

  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional()
  latitude: number;

  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional()
  longitude: number;
}

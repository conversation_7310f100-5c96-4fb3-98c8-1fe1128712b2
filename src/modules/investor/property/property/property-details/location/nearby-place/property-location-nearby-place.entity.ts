import { <PERSON>tity, Column, PrimaryGeneratedColumn, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { PropertyLocation } from '../property-location.entity';
import { PropertyLocationNearbyPlaceDto } from './property-location-nearby-place.dto';

@Entity()
export class PropertyLocationNearbyPlace {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => PropertyLocation, (propertyLocation) => propertyLocation.nearbyPlaces, {
    lazy: true,
    eager: false,
    nullable: false,
  })
  propertyLocation: Promise<PropertyLocation> | PropertyLocation;

  @Expose()
  @Column({ type: 'varchar', length: 100, nullable: true })
  name: string;

  @Expose()
  @Column({ nullable: true, type: 'float' })
  rating: number;

  @Expose()
  @Column({ nullable: true })
  primaryType: string;

  @Expose()
  @Column({ type: 'float', nullable: true })
  latitude: number;

  @Expose()
  @Column({ type: 'float', nullable: true })
  longitude: number;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  public static convertToDto(
    place: PropertyLocationNearbyPlace,
    excludeNullValues = false,
  ): PropertyLocationNearbyPlaceDto {
    if (!place || !place.id) {
      return null;
    }

    const result = <PropertyLocationNearbyPlaceDto>instanceToPlain(place, {
      excludeExtraneousValues: true,
    });

    if (excludeNullValues) {
      Object.keys(result).forEach((key) => result[key] === null && delete result[key]);
    }

    return result;
  }
}

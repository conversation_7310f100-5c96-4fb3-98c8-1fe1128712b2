import { Body, Controller, Param, Patch, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { IsLocationOwnerGuard } from './is-location-owner.guard';
import { PropertyLocationService } from './property-location.service';
import { PropertyLocationDto } from './property-location.dto';
import { PropertyLocation } from './property-location.entity';

@ApiTags('property-location')
@Controller('property/:propertyId/property-location')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class PropertyLocationController {
  constructor(private readonly propertyLocationService: PropertyLocationService) {}

  @Patch(':propertyLocationId')
  @ApiOkResponse({
    description: 'Update property location',
  })
  @ApiParam({
    name: 'propertyLocationId',
    required: true,
  })
  @UseGuards(IsLocationOwnerGuard)
  async update(
    @Param('propertyId') propertyId: string,
    @Param('propertyLocationId') propertyLocationId: string,
    @Body() body: PropertyLocationDto,
  ): Promise<PropertyLocationDto> {
    const propertyLocation = await this.propertyLocationService.update(propertyLocationId, propertyId, body);

    return PropertyLocation.convertToDto(propertyLocation);
  }
}

import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IncludedUtilities } from './included-utilities.entity';

@Injectable()
export class IsIncludedUtilitiesOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(IncludedUtilities)
    private readonly leaseConditionsRepository: Repository<IncludedUtilities>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const includedUtilitiesId = request.params.includedUtilitiesId || request.body.includedUtilitiesId;
    const propertyId = request.params.propertyId || request.body.propertyId;

    if (!includedUtilitiesId) {
      throw new NotFoundException('No included utilities id provided');
    }

    if (!propertyId) {
      throw new NotFoundException('No property id provided');
    }

    const userOwnsUtilities = await this.leaseConditionsRepository
      .createQueryBuilder('includedUtilities')
      .innerJoin('includedUtilities.property', 'property')
      .where('includedUtilities.id = :includedUtilitiesId', { includedUtilitiesId })
      .andWhere('property.id = :propertyId', { propertyId })
      .getCount()
      .then((count) => count > 0);

    if (!userOwnsUtilities) {
      throw new ForbiddenException('User does not own this utilities');
    }

    return true;
  }
}

import { Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { IncludedUtilitiesDto } from './included-utilities.dto';
import { IncludedUtilitiesWithTypesDto } from './included-utilities-with-types.dto';
import { Property } from '../../entities/property.entity';

@Entity()
export class IncludedUtilities {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Property, (property) => property.includedUtilities, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  isUtilitiesIncludedInRent: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  water: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  sewage: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  garbage: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  electricity: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  gas: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  internet: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  cable: boolean;

  @Column({ type: 'boolean', default: false })
  public isDeleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;

  public static convertToDto(utilities: IncludedUtilities, excludeNullValues = false): IncludedUtilitiesDto {
    if (!utilities || !utilities.id) {
      return null;
    }

    const result = <IncludedUtilitiesDto>instanceToPlain(utilities, {
      excludeExtraneousValues: true,
    });

    if (excludeNullValues) {
      Object.keys(result).forEach((key) => result[key] === null && delete result[key]);
    }

    return result;
  }

  public static getDtoWithTypes(utilities: IncludedUtilities): IncludedUtilitiesWithTypesDto {
    return {
      isUtilitiesIncludedInRent: {
        value: utilities.isUtilitiesIncludedInRent,
        type: 'boolean',
      },
      water: {
        value: utilities.water,
        type: 'boolean',
      },
      sewage: {
        value: utilities.sewage,
        type: 'boolean',
      },
      garbage: {
        value: utilities.garbage,
        type: 'boolean',
      },
      electricity: {
        value: utilities.electricity,
        type: 'boolean',
      },
      gas: {
        value: utilities.gas,
        type: 'boolean',
      },
      internet: {
        value: utilities.internet,
        type: 'boolean',
      },
      cable: {
        value: utilities.cable,
        type: 'boolean',
      },
    };
  }
}

import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import ParsingUtils from '../../../../../../utils/parsing.utils';
import { IncludedUtilities } from './included-utilities.entity';
import { Property } from '../../entities/property.entity';
import { IncludedUtilitiesDto } from './included-utilities.dto';

@Injectable()
export class IncludedUtilitiesService {
  constructor(
    @InjectRepository(IncludedUtilities)
    private readonly includedUtilitiesRepository: Repository<IncludedUtilities>,

    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async create(propertyId: string, payload: IncludedUtilitiesDto): Promise<IncludedUtilities> {
    const result = await this.includedUtilitiesRepository
      .createQueryBuilder()
      .insert()
      .values({ ...this.processPayload(payload) })
      .returning('*')
      .execute();

    const includedUtilities = Object.assign(new IncludedUtilities(), { ...result.raw[0] });

    await this.propertyRepository.update(propertyId, {
      includedUtilities: { id: includedUtilities.id },
    });

    return includedUtilities;
  }

  async update(includedUtilitiesId: string, payload: IncludedUtilitiesDto): Promise<IncludedUtilities> {
    const result = await this.includedUtilitiesRepository
      .createQueryBuilder()
      .update({ ...this.processPayload(payload) })
      .where({ id: includedUtilitiesId })
      .returning('*')
      .execute();

    return Object.assign(new IncludedUtilities(), { ...result.raw[0] });
  }

  private processPayload(payload: IncludedUtilitiesDto): IncludedUtilitiesDto {
    const processedPayload = { ...payload };

    if (processedPayload.hasOwnProperty('isUtilitiesIncludedInRent') && !processedPayload.isUtilitiesIncludedInRent) {
      processedPayload.water = false;
      processedPayload.sewage = false;
      processedPayload.garbage = false;
      processedPayload.electricity = false;
      processedPayload.gas = false;
      processedPayload.internet = false;
      processedPayload.cable = false;
    }

    return processedPayload;
  }

  async getUpdatedIncludedUtilities(includedUtilities: IncludedUtilities, data: any): Promise<IncludedUtilities> {
    if (!includedUtilities) {
      includedUtilities = new IncludedUtilities();
    }

    return this.mapFieldValuesToEntity(includedUtilities, data);
  }

  private mapFieldValuesToEntity(includedUtilities: IncludedUtilities, data: any): IncludedUtilities {
    const includedUtilitiesCopy = { ...includedUtilities };

    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;

      try {
        switch (item.fieldName.split('.').reverse()[0]) {
          case 'isUtilitiesIncluded':
            includedUtilitiesCopy.isUtilitiesIncludedInRent = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'electricity':
            includedUtilitiesCopy.electricity = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'gas':
            includedUtilitiesCopy.gas = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'water':
            includedUtilitiesCopy.water = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'sewage':
            includedUtilitiesCopy.sewage = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'garbage':
            includedUtilitiesCopy.garbage = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'internet':
            includedUtilitiesCopy.internet = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'cable':
            includedUtilitiesCopy.cable = ParsingUtils.parseStringToBoolean(item.value);
            break;
          default:
            break;
        }
      } catch (e) {
        throw new HttpException('Invalid field value: ' + item.value, 400);
      }
    }
    return includedUtilitiesCopy;
  }
}

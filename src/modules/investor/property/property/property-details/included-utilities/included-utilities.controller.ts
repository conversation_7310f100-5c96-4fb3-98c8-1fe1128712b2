import { Body, Controller, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { IsIncludedUtilitiesOwnerGuard } from './is-included-utilities-owner.guard';
import { IncludedUtilitiesService } from './included-utilities.service';
import { IncludedUtilitiesDto } from './included-utilities.dto';
import { IncludedUtilities } from './included-utilities.entity';

@ApiTags('included-utilities')
@Controller('property/:propertyId/included-utilities')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class IncludedUtilitiesController {
  constructor(private readonly includedUtilitiesService: IncludedUtilitiesService) {}

  @Post()
  @ApiOkResponse({
    description: 'Create included utilities',
  })
  async create(
    @Param('propertyId') propertyId: string,
    @Body() body: IncludedUtilitiesDto,
  ): Promise<IncludedUtilitiesDto> {
    const includedUtilities = await this.includedUtilitiesService.create(propertyId, body);

    return IncludedUtilities.convertToDto(includedUtilities);
  }

  @Patch(':includedUtilitiesId')
  @ApiOkResponse({
    description: 'Update included utilities',
  })
  @ApiParam({
    name: 'includedUtilitiesId',
    required: true,
  })
  @UseGuards(IsIncludedUtilitiesOwnerGuard)
  async update(
    @Param('propertyId') _propertyId: string,
    @Param('includedUtilitiesId') includedUtilitiesId: string,
    @Body() body: IncludedUtilitiesDto,
  ): Promise<IncludedUtilitiesDto> {
    const includedUtilities = await this.includedUtilitiesService.update(includedUtilitiesId, body);

    return IncludedUtilities.convertToDto(includedUtilities);
  }
}

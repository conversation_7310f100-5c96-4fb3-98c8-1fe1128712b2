import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class IncludedUtilitiesDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  isUtilitiesIncludedInRent: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  water: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  sewage: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  garbage: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  electricity: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  gas: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  internet: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  cable: boolean;
}

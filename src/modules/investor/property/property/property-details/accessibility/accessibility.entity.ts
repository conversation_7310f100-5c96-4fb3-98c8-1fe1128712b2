import { Column, CreateDate<PERSON>olumn, <PERSON>tity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { AccessibilityDto } from './accessibility.dto';
import { AccessibilityWithTypesDto } from './accessibility-with-types.dto';
import { Property } from '../../entities/property.entity';

@Entity()
export class Accessibility {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Property, (property) => property.accessibility, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  accessibleBathroom: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  elevator: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  ramp: boolean;

  @Expose()
  @Column({ type: 'text', nullable: true })
  accessibilityNotes: string;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  disabilityAccess: boolean;

  @Column({ type: 'boolean', default: false })
  public isDeleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;

  public static convertToDto(accessibility: Accessibility, excludeNullValues = false): AccessibilityDto {
    if (!accessibility || !accessibility.id) {
      return null;
    }

    const result = <AccessibilityDto>instanceToPlain(accessibility, { excludeExtraneousValues: true });

    if (excludeNullValues) {
      Object.keys(result).forEach((key) => result[key] === null && delete result[key]);
    }

    return result;
  }

  public static convertToDtoWithTypes(accessibility: Accessibility): AccessibilityWithTypesDto {
    return {
      accessibleBathroom: {
        value: accessibility.accessibleBathroom,
        type: 'boolean',
      },
      elevator: {
        value: accessibility.elevator,
        type: 'boolean',
      },
      ramp: {
        value: accessibility.ramp,
        type: 'boolean',
      },
      accessibilityNotes: {
        value: accessibility.accessibilityNotes,
        type: 'string',
      },
      disabilityAccess: {
        value: accessibility.disabilityAccess,
        type: 'boolean',
      },
    };
  }
}

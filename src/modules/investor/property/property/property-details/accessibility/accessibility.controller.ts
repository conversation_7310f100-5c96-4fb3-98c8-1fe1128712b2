import { Body, Controller, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { AccessibilityService } from './accessibility.service';
import { AccessibilityDto } from './accessibility.dto';
import { Accessibility } from './accessibility.entity';
import { IsAccessibilityOwnerGuard } from './is-accessibility-owner.guard';

@ApiTags('property-accessibility')
@Controller('property/:propertyId/accessibility')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class AccessibilityController {
  constructor(private readonly accessibilityService: AccessibilityService) {}

  @Post()
  @ApiOkResponse({
    description: 'Create property accessibility',
  })
  async create(@Param('propertyId') propertyId: string, @Body() body: AccessibilityDto): Promise<AccessibilityDto> {
    const accessibility = await this.accessibilityService.create(propertyId, body);

    return Accessibility.convertToDto(accessibility);
  }

  @Patch(':accessibilityId')
  @ApiOkResponse({
    description: 'Update accessibility',
  })
  @ApiParam({
    name: 'accessibilityId',
    required: true,
  })
  @UseGuards(IsAccessibilityOwnerGuard)
  async update(
    @Param('propertyId') _propertyId: string,
    @Param('accessibilityId') accessibilityId: string,
    @Body() body: AccessibilityDto,
  ): Promise<AccessibilityDto> {
    const accessibility = await this.accessibilityService.update(accessibilityId, body);

    return Accessibility.convertToDto(accessibility);
  }
}

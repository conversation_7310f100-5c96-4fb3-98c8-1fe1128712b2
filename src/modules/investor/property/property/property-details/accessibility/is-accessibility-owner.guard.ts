import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Accessibility } from './accessibility.entity';

@Injectable()
export class IsAccessibilityOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(Accessibility)
    private readonly leaseConditionsRepository: Repository<Accessibility>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const accessibilityId = request.params.accessibilityId || request.body.accessibilityId;
    const propertyId = request.params.propertyId || request.body.propertyId;

    if (!accessibilityId) {
      throw new NotFoundException('No accessibility id provided');
    }

    if (!propertyId) {
      throw new NotFoundException('No property id provided');
    }

    const userOwnsAccessibility = await this.leaseConditionsRepository
      .createQueryBuilder('accessibility')
      .innerJoin('accessibility.property', 'property')
      .where('accessibility.id = :accessibilityId', { accessibilityId })
      .andWhere('property.id = :propertyId', { propertyId })
      .getCount()
      .then((count) => count > 0);

    if (!userOwnsAccessibility) {
      throw new ForbiddenException('User does not own this accessibility');
    }

    return true;
  }
}

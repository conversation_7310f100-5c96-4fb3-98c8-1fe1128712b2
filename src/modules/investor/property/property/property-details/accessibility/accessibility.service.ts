import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Accessibility } from './accessibility.entity';
import { FieldValue } from '../../../../../ai/models/field-value';
import ParsingUtils from '../../../../../../utils/parsing.utils';
import { Property } from '../../entities/property.entity';
import { AccessibilityDto } from './accessibility.dto';

@Injectable()
export class AccessibilityService {
  constructor(
    @InjectRepository(Accessibility)
    private readonly accessibilityRepository: Repository<Accessibility>,

    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async create(propertyId: string, payload: AccessibilityDto): Promise<Accessibility> {
    const result = await this.accessibilityRepository
      .createQueryBuilder()
      .insert()
      .values({ ...payload })
      .returning('*')
      .execute();

    const accessibility = Object.assign(new Accessibility(), { ...result.raw[0] });

    await this.propertyRepository.update(propertyId, {
      accessibility: { id: accessibility.id },
    });

    return accessibility;
  }

  async update(accessibilityId: string, payload: AccessibilityDto): Promise<Accessibility> {
    const result = await this.accessibilityRepository
      .createQueryBuilder()
      .update({ ...payload })
      .where({ id: accessibilityId })
      .returning('*')
      .execute();

    return Object.assign(new Accessibility(), { ...result.raw[0] });
  }

  async getUpdatedAccessibility(accessibility: Accessibility, data: any): Promise<Accessibility> {
    if (!accessibility) {
      accessibility = new Accessibility();
    }

    return this.mapFieldValuesToEntity(accessibility, data);
  }

  private mapFieldValuesToEntity(accessibility: Accessibility, data: FieldValue[]): Accessibility {
    const accessibilityCopy = { ...accessibility };

    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;

      try {
        switch (item.fieldName.split('.').reverse()[0]) {
          case 'accessibleBathroom':
            accessibilityCopy.accessibleBathroom = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'elevator':
            accessibilityCopy.elevator = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'ramp':
            accessibilityCopy.ramp = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'accessibilityNotes':
            accessibilityCopy.accessibilityNotes = item.value;
            break;
          case 'disabilityAccess':
            accessibilityCopy.disabilityAccess = ParsingUtils.parseStringToBoolean(item.value);
            break;
          default:
            break;
        }
      } catch (error) {
        throw new HttpException('Invalid field name', 400);
      }
    }

    return accessibilityCopy;
  }
}

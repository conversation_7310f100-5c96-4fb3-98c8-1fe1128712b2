import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AccessibilityDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  accessibleBathroom: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  elevator: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  ramp: boolean;

  @IsString()
  @IsOptional()
  @ApiProperty()
  accessibilityNotes: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  disabilityAccess: boolean;
}

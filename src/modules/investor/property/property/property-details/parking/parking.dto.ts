import { IsBoolean, IsEnum, Is<PERSON><PERSON>ber, IsOptional, IsString, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ParkingType } from './parking.type';

export class ParkingDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasParking: boolean;

  @IsEnum(ParkingType)
  @IsOptional()
  @ApiPropertyOptional({ enum: ParkingType, enumName: 'ParkingType' })
  parkingType: ParkingType;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  parkingAvailableSpaces: number;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  parkingSpacesAreTandem: boolean;

  @IsString()
  @IsOptional()
  @ApiProperty()
  parkingOtherDescription: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  parkingMonthlyChargePerSpace: number;
}

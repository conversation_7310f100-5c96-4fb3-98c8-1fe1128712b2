import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Parking } from './parking.entity';

@Injectable()
export class IsParkingOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(Parking)
    private readonly parkingRepository: Repository<Parking>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const parkingId = request.params.parkingId || request.body.parkingId;
    const propertyId = request.params.propertyId || request.body.propertyId;

    if (!parkingId) {
      throw new NotFoundException('No parking id provided');
    }

    if (!propertyId) {
      throw new NotFoundException('No property id provided');
    }

    const userOwnsParking = await this.parkingRepository
      .createQueryBuilder('parking')
      .innerJoin('parking.property', 'property')
      .where('parking.id = :parkingId', { parkingId })
      .andWhere('property.id = :propertyId', { propertyId })
      .getCount()
      .then((count) => count > 0);

    if (!userOwnsParking) {
      throw new ForbiddenException('User does not own this parking');
    }

    return true;
  }
}

import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Parking } from './parking.entity';
import { ParkingType } from './parking.type';
import ParsingUtils from '../../../../../../utils/parsing.utils';
import { Property } from '../../entities/property.entity';
import { ParkingDto } from './parking.dto';

@Injectable()
export class ParkingService {
  constructor(
    @InjectRepository(Parking)
    private readonly parkingRepository: Repository<Parking>,

    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async create(propertyId: string, payload: ParkingDto): Promise<Parking> {
    const result = await this.parkingRepository
      .createQueryBuilder()
      .insert()
      .values({ ...this.processPayload(payload) })
      .returning('*')
      .execute();

    const parking = Object.assign(new Parking(), { ...result.raw[0] });

    await this.propertyRepository.update(propertyId, {
      parking: { id: parking.id },
    });

    return parking;
  }

  async update(parkingId: string, payload: ParkingDto): Promise<Parking> {
    const result = await this.parkingRepository
      .createQueryBuilder()
      .update({ ...this.processPayload(payload) })
      .where({ id: parkingId })
      .returning('*')
      .execute();

    return Object.assign(new Parking(), { ...result.raw[0] });
  }

  private processPayload(payload: ParkingDto): ParkingDto {
    const processedPayload = { ...payload };

    if (processedPayload.hasOwnProperty('hasParking') && !processedPayload.hasParking) {
      processedPayload.parkingType = null;
      processedPayload.parkingAvailableSpaces = null;
      processedPayload.parkingSpacesAreTandem = null;
      processedPayload.parkingOtherDescription = null;
      processedPayload.parkingMonthlyChargePerSpace = null;
    }

    return processedPayload;
  }

  async getUpdatedParking(parking: Parking, data: any): Promise<Parking> {
    if (!parking) {
      parking = new Parking();
    }

    return this.mapFieldValuesToEntity(parking, data);
  }

  private mapFieldValuesToEntity(parking: Parking, data: any): Parking {
    const parkingCopy = { ...parking };

    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;

      try {
        switch (item.fieldName.split('.').reverse()[0]) {
          case 'hasParking':
            parkingCopy.hasParking = Boolean(item.value);
            break;
          case 'parkingType':
            parkingCopy.parkingType = item.value as ParkingType;
            break;
          case 'parkingAvailableSpaces':
            parkingCopy.parkingAvailableSpaces = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'parkingSpacesAreTandem':
            parkingCopy.parkingSpacesAreTandem = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'parkingOtherDescription':
            parkingCopy.parkingOtherDescription = item.value;
            break;
          case 'parkingMonthlyChargePerSpace':
            parkingCopy.parkingMonthlyChargePerSpace = ParsingUtils.parseStringToNumber(item.value);
            break;
          default:
            break;
        }
      } catch (error) {
        throw new HttpException('Invalid field name', 400);
      }
    }

    return parkingCopy;
  }
}

import { Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { ParkingType } from './parking.type';
import { ParkingDto } from './parking.dto';
import { ParkingWithTypesDto } from './parking-with-types.dto';
import { Property } from '../../entities/property.entity';

@Entity()
export class Parking {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Property, (property) => property.parking, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasParking: boolean;

  @Expose()
  @Column({
    type: 'enum',
    enum: ParkingType,
    nullable: true,
  })
  parkingType: ParkingType;

  @Expose()
  @Column({ type: 'int', nullable: true })
  parkingAvailableSpaces: number;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  parkingSpacesAreTandem: boolean;

  @Expose()
  @Column({ type: 'text', nullable: true })
  parkingOtherDescription: string;

  @Expose()
  @Column({ type: 'float', nullable: true })
  parkingMonthlyChargePerSpace: number;

  @Column({ type: 'boolean', default: false })
  public isDeleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;

  public static convertToDto(parking: Parking, excludeNullValues = false): ParkingDto {
    if (!parking || !parking.id) {
      return null;
    }

    const result = <ParkingDto>instanceToPlain(parking, { excludeExtraneousValues: true });

    if (excludeNullValues) {
      Object.keys(result).forEach((key) => result[key] === null && delete result[key]);
    }

    return result;
  }

  public static getDtoWithTypes(parking: Parking): ParkingWithTypesDto {
    return {
      hasParking: {
        value: parking.hasParking,
        type: 'boolean',
      },
      parkingType: {
        value: parking.parkingType,
        type: 'enum',
        enum: {
          type: 'ParkingTypeEnum',
          values: Object.values(ParkingType),
        },
      },
      parkingAvailableSpaces: {
        value: parking.parkingAvailableSpaces,
        type: 'number',
      },
      parkingSpacesAreTandem: {
        value: parking.parkingSpacesAreTandem,
        type: 'boolean',
      },
      parkingOtherDescription: {
        value: parking.parkingOtherDescription,
        type: 'string',
      },
      parkingMonthlyChargePerSpace: {
        value: parking.parkingMonthlyChargePerSpace,
        type: 'number',
      },
    };
  }
}

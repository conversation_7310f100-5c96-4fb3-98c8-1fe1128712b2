import { Body, Controller, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { ParkingService } from './parking.service';
import { ParkingDto } from './parking.dto';
import { Parking } from './parking.entity';
import { IsParkingOwnerGuard } from './is-parking-owner.guard';

@ApiTags('parking')
@Controller('property/:propertyId/parking')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class ParkingController {
  constructor(private readonly parkingService: ParkingService) {}

  @Post()
  @ApiOkResponse({
    description: 'Create parking',
  })
  async create(@Param('propertyId') propertyId: string, @Body() body: ParkingDto): Promise<ParkingDto> {
    const petPolicy = await this.parkingService.create(propertyId, body);

    return Parking.convertToDto(petPolicy);
  }

  @Patch(':parkingId')
  @ApiOkResponse({
    description: 'Update parking',
  })
  @ApiParam({
    name: 'parkingId',
    required: true,
  })
  @UseGuards(IsParkingOwnerGuard)
  async update(
    @Param('propertyId') _propertyId: string,
    @Param('parkingId') parkingId: string,
    @Body() body: ParkingDto,
  ): Promise<ParkingDto> {
    const parking = await this.parkingService.update(parkingId, body);

    return Parking.convertToDto(parking);
  }
}

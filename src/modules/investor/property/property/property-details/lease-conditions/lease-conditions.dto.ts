import { LeaseTermEnum } from '../../enums/lease-term.enum';
import { IsArray, IsBoolean, IsDateString, IsEnum, IsNumber, IsOptional, Min } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IncomeEnum } from '../renter-requirements/income.enum';

export class LeaseConditionsDto {
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ type: [String], enum: LeaseTermEnum, enumName: 'LeaseTermEnum' })
  possibleLeaseTerms: LeaseTermEnum[];

  @IsDateString()
  @IsOptional()
  @ApiPropertyOptional()
  desiredLeasingDate: Date;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional()
  isSubleaseAllowed: boolean;

  @IsNumber()
  @IsOptional()
  @Min(0)
  @ApiPropertyOptional()
  rent: number;

  @IsNumber()
  @IsOptional()
  @Min(0)
  @ApiPropertyOptional()
  applicationFee: number;

  @IsEnum(IncomeEnum)
  @IsOptional()
  @ApiPropertyOptional({ enum: IncomeEnum, enumName: 'IncomeEnum' })
  lastMonthRent: IncomeEnum;

  @IsEnum(IncomeEnum)
  @IsOptional()
  @ApiPropertyOptional({ enum: IncomeEnum, enumName: 'IncomeEnum' })
  securityDeposit: IncomeEnum;
}

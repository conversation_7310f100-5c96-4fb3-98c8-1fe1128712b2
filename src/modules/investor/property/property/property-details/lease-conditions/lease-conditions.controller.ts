import { Body, Controller, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { LeaseConditionsService } from './lease-conditions.service';
import { LeaseConditionsDto } from './lease-conditions.dto';
import { LeaseConditions } from './lease-conditions.entity';
import { IsLeaseConditionsOwnerGuard } from './is-lease-conditions-owner.guard';

@ApiTags('lease-conditions')
@Controller('property/:propertyId/lease-conditions')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class LeaseConditionsController {
  constructor(private readonly leaseConditionsService: LeaseConditionsService) {}

  @Post()
  @ApiOkResponse({
    description: 'Create lease conditions',
  })
  async create(@Param('propertyId') propertyId: string, @Body() body: LeaseConditionsDto): Promise<LeaseConditionsDto> {
    const leaseConditions = await this.leaseConditionsService.create(propertyId, body);

    return LeaseConditions.convertToDto(leaseConditions);
  }

  @Patch(':leaseConditionsId')
  @ApiOkResponse({
    description: 'Update lease conditions',
  })
  @ApiParam({
    name: 'leaseConditionsId',
    required: true,
  })
  @UseGuards(IsLeaseConditionsOwnerGuard)
  async update(
    @Param('propertyId') _propertyId: string,
    @Param('leaseConditionsId') petPolicyId: string,
    @Body() body: LeaseConditionsDto,
  ): Promise<LeaseConditionsDto> {
    const renterRequirements = await this.leaseConditionsService.update(petPolicyId, body);

    return LeaseConditions.convertToDto(renterRequirements);
  }
}

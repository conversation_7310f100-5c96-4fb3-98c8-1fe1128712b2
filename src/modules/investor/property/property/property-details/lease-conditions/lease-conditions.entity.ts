import { Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { LeaseConditionsDto } from './lease-conditions.dto';
import { LeaseConditionsWithTypesDto } from './lease-conditions-with-types.dto';
import { LeaseTermEnum } from '../../enums/lease-term.enum';
import { Property } from '../../entities/property.entity';
import { IncomeEnum } from '../renter-requirements/income.enum';

@Entity()
export class LeaseConditions {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Property, (property) => property.leaseConditions, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @Expose()
  @Column({ type: 'text', nullable: true, array: true })
  possibleLeaseTerms: LeaseTermEnum[];

  @Expose()
  @Column({ type: 'timestamp', nullable: true })
  desiredLeasingDate: Date;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  isSubleaseAllowed: boolean;

  @Expose()
  @Column({ type: 'float', default: 0, nullable: true })
  rent: number;

  @Expose()
  @Column({ type: 'float', default: 0, nullable: true })
  applicationFee: number;

  @Expose()
  @Column({ type: 'enum', enum: IncomeEnum, nullable: true })
  lastMonthRent: IncomeEnum;

  @Expose()
  @Column({ type: 'enum', enum: IncomeEnum, nullable: true })
  securityDeposit: IncomeEnum;

  @Column({ type: 'boolean', default: false })
  public isDeleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;

  public static convertToDto(leaseConditions: LeaseConditions, excludeNullValues = false): LeaseConditionsDto {
    if (!leaseConditions || !leaseConditions.id) {
      return null;
    }

    const result = <LeaseConditionsDto>instanceToPlain(leaseConditions, {
      excludeExtraneousValues: true,
    });

    if (excludeNullValues) {
      Object.keys(result).forEach((key) => result[key] === null && delete result[key]);
    }

    return result;
  }

  public static getDtoWithTypes(propertyPrice: LeaseConditions): LeaseConditionsWithTypesDto {
    return {
      possibleLeaseTerms: {
        value: propertyPrice.possibleLeaseTerms,
        type: 'enum array',
        fieldDescription: 'List of possible terms property could potentially be rented for',
        enum: {
          type: 'LeaseTermEnum',
          values: Object.values(LeaseTermEnum),
        },
      },
      desiredLeasingDate: {
        value: propertyPrice.desiredLeasingDate,
        type: 'date',
      },
      isSubleaseAllowed: {
        value: propertyPrice.isSubleaseAllowed,
        type: 'boolean',
      },
      rent: {
        value: propertyPrice.rent,
        type: 'number',
      },
      lastMonthRent: {
        value: propertyPrice.lastMonthRent,
        type: 'enum',
        enum: {
          type: 'IncomeEnum',
          values: Object.values(IncomeEnum),
        },
      },
      securityDeposit: {
        value: propertyPrice.securityDeposit,
        type: 'enum',
        enum: {
          type: 'IncomeEnum',
          values: Object.values(IncomeEnum),
        },
      },
    };
  }
}

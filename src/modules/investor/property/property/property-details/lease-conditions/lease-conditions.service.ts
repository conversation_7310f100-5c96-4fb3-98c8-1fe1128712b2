import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LeaseConditions } from './lease-conditions.entity';
import ParsingUtils from '../../../../../../utils/parsing.utils';
import { LeaseTermEnum } from '../../enums/lease-term.enum';
import { Property } from '../../entities/property.entity';
import { LeaseConditionsDto } from './lease-conditions.dto';
import { IncomeEnum } from '../renter-requirements/income.enum';
import { TalloProductsEnum } from '../../../../../shared/checkout/tallo-products.enum';
import { talloProductPricesMap } from '../../../../../shared/checkout/tallo-product-prices.map';

@Injectable()
export class LeaseConditionsService {
  constructor(
    @InjectRepository(LeaseConditions)
    private readonly leaseConditionsRepository: Repository<LeaseConditions>,

    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async create(propertyId: string, payload: LeaseConditionsDto): Promise<LeaseConditions> {
    const result = await this.leaseConditionsRepository
      .createQueryBuilder()
      .insert()
      .values({
        ...payload,
        applicationFee: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_FULL_PACKAGE),
      })
      .returning('*')
      .execute();

    const leaseConditions = <LeaseConditions>Object.assign(new LeaseConditions(), { ...result.raw[0] });

    await this.propertyRepository.update(propertyId, {
      leaseConditions: { id: leaseConditions.id },
    });

    return leaseConditions;
  }

  async update(leaseConditionsId: string, payload: LeaseConditionsDto): Promise<LeaseConditions> {
    const result = await this.leaseConditionsRepository
      .createQueryBuilder()
      .update({ ...payload })
      .where({ id: leaseConditionsId })
      .returning('*')
      .execute();

    return Object.assign(new LeaseConditions(), { ...result.raw[0] });
  }

  async getUpdatedLeaseConditions(leaseConditions: LeaseConditions, data: any): Promise<LeaseConditions> {
    if (!leaseConditions) {
      leaseConditions = new LeaseConditions();
    }

    return this.mapFieldValuesToEntity(leaseConditions, data);
  }

  private mapFieldValuesToEntity(leaseConditions: LeaseConditions, data: any): LeaseConditions {
    const leaseConditionsCopy = { ...leaseConditions };

    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;

      try {
        switch (item.fieldName.split('.').reverse()[0]) {
          case 'isSubleaseAllowed':
            leaseConditionsCopy.isSubleaseAllowed = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'possibleLeaseTerms':
            leaseConditionsCopy.possibleLeaseTerms = item.value as LeaseTermEnum[];
            break;
          case 'rent':
            leaseConditionsCopy.rent = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'lastMonthRent':
            leaseConditionsCopy.lastMonthRent = item.value as IncomeEnum;
            break;
          case 'securityDeposit':
            leaseConditionsCopy.securityDeposit = item.value as IncomeEnum;
            break;
          default:
            break;
        }
      } catch (error) {
        throw new HttpException('Invalid field value: ' + item.value, 400, {
          cause: error,
        });
      }

      return leaseConditionsCopy;
    }
  }
}

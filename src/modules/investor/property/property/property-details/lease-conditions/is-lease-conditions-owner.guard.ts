import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LeaseConditions } from './lease-conditions.entity';

@Injectable()
export class IsLeaseConditionsOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(LeaseConditions)
    private readonly leaseConditionsRepository: Repository<LeaseConditions>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const leaseConditionsId = request.params.leaseConditionsId || request.body.leaseConditionsId;
    const propertyId = request.params.propertyId || request.body.propertyId;

    if (!leaseConditionsId) {
      throw new NotFoundException('No lease conditions id provided');
    }

    if (!propertyId) {
      throw new NotFoundException('No property id provided');
    }

    const userOwnsLeaseConditions = await this.leaseConditionsRepository
      .createQueryBuilder('leaseConditions')
      .innerJoin('leaseConditions.property', 'property')
      .where('leaseConditions.id = :leaseConditionsId', { leaseConditionsId })
      .andWhere('property.id = :propertyId', { propertyId })
      .getCount()
      .then((count) => count > 0);

    if (!userOwnsLeaseConditions) {
      throw new ForbiddenException('User does not own this lease conditions');
    }

    return true;
  }
}

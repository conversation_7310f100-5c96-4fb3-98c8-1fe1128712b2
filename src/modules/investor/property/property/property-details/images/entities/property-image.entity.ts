import { <PERSON><PERSON>ty, PrimaryColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Expose } from 'class-transformer';
import { Property } from '../../../entities/property.entity';
import { File } from '../../../../../../shared/file/entities/file.entity';

@Entity({ name: 'property_images_file' })
export class PropertyImage {
  @PrimaryColumn('uuid')
  propertyId: string;

  @PrimaryColumn('uuid')
  fileId: string;

  @Expose()
  @Column({ type: 'text', array: true, nullable: true })
  tags?: string[];

  @Expose()
  @Column({ type: 'int', nullable: true })
  qualityLighting?: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  qualityComposition?: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  qualityClarity?: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  qualityOverall?: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  stagingCleanliness?: number;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  stagingFurnished?: boolean;

  @Expose()
  @Column({ type: 'int', nullable: true })
  stagingDecluttered?: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  uniqueness?: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  marketAppeal?: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  analysisConfidence?: number;

  @Expose()
  @Column({ type: 'varchar', length: 10, nullable: true })
  analysisVersion?: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  @ManyToOne(() => Property, (property) => property.propertyImages)
  @JoinColumn({ name: 'propertyId' })
  property: Property;

  @ManyToOne(() => File)
  @JoinColumn({ name: 'fileId' })
  file: File;
}

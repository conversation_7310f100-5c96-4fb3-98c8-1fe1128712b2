import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PropertyTagMapperService } from './property-tag-mapper.service';
import { Property } from '../../entities/property.entity';
import { Amenities } from '../amenities/amenities.entity';
import { Parking } from '../parking/parking.entity';
import { PropertySpecifications } from '../specifications/property-specifications.entity';
import { ImageAnalysis } from '../../../../../shared/file/image-analysis/interfaces/image-analysis.intefrace';

describe('PropertyTagMapperService', () => {
  let service: PropertyTagMapperService;
  let propertyRepository: jest.Mocked<Repository<Property>>;
  let amenitiesRepository: jest.Mocked<Repository<Amenities>>;
  let parkingRepository: jest.Mocked<Repository<Parking>>;
  let specificationsRepository: jest.Mocked<Repository<PropertySpecifications>>;

  beforeEach(async () => {
    const mockPropertyRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const mockAmenitiesRepository = {
      create: jest.fn(),
      save: jest.fn(),
    };

    const mockParkingRepository = {
      create: jest.fn(),
      save: jest.fn(),
    };

    const mockSpecificationsRepository = {
      create: jest.fn(),
      save: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PropertyTagMapperService,
        {
          provide: getRepositoryToken(Property),
          useValue: mockPropertyRepository,
        },
        {
          provide: getRepositoryToken(Amenities),
          useValue: mockAmenitiesRepository,
        },
        {
          provide: getRepositoryToken(Parking),
          useValue: mockParkingRepository,
        },
        {
          provide: getRepositoryToken(PropertySpecifications),
          useValue: mockSpecificationsRepository,
        },
      ],
    }).compile();

    service = module.get<PropertyTagMapperService>(PropertyTagMapperService);
    propertyRepository = module.get(getRepositoryToken(Property));
    amenitiesRepository = module.get(getRepositoryToken(Amenities));
    parkingRepository = module.get(getRepositoryToken(Parking));
    specificationsRepository = module.get(getRepositoryToken(PropertySpecifications));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('updatePropertyFromImageTags', () => {
    it('should update amenities from structured property features', async () => {
      const propertyId = 'test-property-id';
      const mockAmenities = {
        id: 'amenities-id',
        hasFireplace: null,
        hasPool: null,
        hasAirConditioning: null,
      };
      const mockProperty = {
        id: propertyId,
        amenities: Promise.resolve(mockAmenities),
        parking: Promise.resolve({
          id: 'parking-id',
          hasParking: null,
          parkingType: null,
        }),
        specifications: Promise.resolve({
          id: 'specs-id',
          coolingSystem: null,
          heatingSystem: null,
          floorCovering: null,
        }),
      };

      const analysis: ImageAnalysis = {
        tags: ['living room', 'modern'],
        propertyFeatures: [
          { tag: 'hasFireplace', confidence: 85, value: true },
          { tag: 'hasPool', confidence: 90, value: true },
          { tag: 'hasAirConditioning', confidence: 80, value: true },
        ],
      };

      propertyRepository.findOne.mockResolvedValue(mockProperty as any);
      amenitiesRepository.save.mockResolvedValue({} as any);

      const result = await service.updatePropertyFromImageTags(propertyId, analysis);

      expect(result.amenitiesUpdated).toBe(true);
      expect(result.updatedFields).toContain('hasFireplace');
      expect(result.updatedFields).toContain('hasPool');
      expect(result.updatedFields).toContain('hasAirConditioning');
      expect(amenitiesRepository.save).toHaveBeenCalled();
    });

    it('should update parking from structured property features', async () => {
      const propertyId = 'test-property-id';
      const mockParking = {
        id: 'parking-id',
        hasParking: null,
        parkingType: null,
      };
      const mockProperty = {
        id: propertyId,
        amenities: Promise.resolve({
          id: 'amenities-id',
          hasFireplace: null,
        }),
        parking: Promise.resolve(mockParking),
        specifications: Promise.resolve({
          id: 'specs-id',
          coolingSystem: null,
        }),
      };

      const analysis: ImageAnalysis = {
        tags: ['garage', 'exterior'],
        propertyFeatures: [{ tag: 'garage_attached', confidence: 85, value: true }],
      };

      propertyRepository.findOne.mockResolvedValue(mockProperty as any);
      parkingRepository.save.mockResolvedValue({} as any);

      const result = await service.updatePropertyFromImageTags(propertyId, analysis);

      expect(result.parkingUpdated).toBe(true);
      expect(result.updatedFields).toContain('parkingType');
      expect(parkingRepository.save).toHaveBeenCalled();
    });

    it('should update specifications from structured property features', async () => {
      const propertyId = 'test-property-id';
      const mockSpecifications = {
        id: 'specs-id',
        coolingSystem: null,
        heatingSystem: null,
        floorCovering: null,
      };
      const mockProperty = {
        id: propertyId,
        amenities: Promise.resolve({
          id: 'amenities-id',
          hasAirConditioning: null,
        }),
        parking: Promise.resolve({
          id: 'parking-id',
          hasParking: null,
        }),
        specifications: Promise.resolve(mockSpecifications),
      };

      const analysis: ImageAnalysis = {
        tags: ['living room', 'hardwood floors'],
        propertyFeatures: [
          { tag: 'hasAirConditioning', confidence: 85, value: true },
          { tag: 'hardwood_floors', confidence: 90, value: true },
        ],
      };

      propertyRepository.findOne.mockResolvedValue(mockProperty as any);
      specificationsRepository.save.mockResolvedValue({} as any);

      const result = await service.updatePropertyFromImageTags(propertyId, analysis);

      expect(result.specificationsUpdated).toBe(true);
      expect(result.updatedFields).toContain('coolingSystem');
      expect(result.updatedFields).toContain('floorCovering');
      expect(specificationsRepository.save).toHaveBeenCalled();
    });

    it('should fallback to unstructured tags when propertyFeatures is not available', async () => {
      const propertyId = 'test-property-id';
      const mockAmenities = {
        id: 'amenities-id',
        hasFireplace: null,
        hasPool: null,
      };
      const mockProperty = {
        id: propertyId,
        amenities: Promise.resolve(mockAmenities),
        parking: Promise.resolve({
          id: 'parking-id',
          hasParking: null,
          parkingType: null,
        }),
        specifications: Promise.resolve({
          id: 'specs-id',
          coolingSystem: null,
        }),
      };

      const analysis: ImageAnalysis = {
        tags: ['fireplace', 'pool', 'living room'],
      };

      propertyRepository.findOne.mockResolvedValue(mockProperty as any);
      amenitiesRepository.save.mockResolvedValue({} as any);

      const result = await service.updatePropertyFromImageTags(propertyId, analysis);

      expect(result.amenitiesUpdated).toBe(true);
      expect(result.updatedFields).toContain('hasFireplace');
      expect(result.updatedFields).toContain('hasPool');
      expect(amenitiesRepository.save).toHaveBeenCalled();
    });

    it('should detect conflicts when existing values differ from detected values', async () => {
      const propertyId = 'test-property-id';
      const mockAmenities = {
        id: 'amenities-id',
        hasFireplace: false, // Existing value conflicts with detected
        hasPool: null,
      };
      const mockProperty = {
        id: propertyId,
        amenities: Promise.resolve(mockAmenities),
        parking: Promise.resolve({
          id: 'parking-id',
          hasParking: null,
          parkingType: null,
        }),
        specifications: Promise.resolve({
          id: 'specs-id',
          coolingSystem: null,
        }),
      };

      const analysis: ImageAnalysis = {
        tags: ['fireplace', 'pool'],
      };

      propertyRepository.findOne.mockResolvedValue(mockProperty as any);
      amenitiesRepository.save.mockResolvedValue({} as any);

      const result = await service.updatePropertyFromImageTags(propertyId, analysis);

      expect(result.conflicts).toContain('hasFireplace: existing=false, detected=true');
      expect(result.updatedFields).toContain('hasPool');
      expect(result.updatedFields).not.toContain('hasFireplace');
    });

    it('should create new entities when they do not exist and relevant features are detected', async () => {
      const propertyId = 'test-property-id';
      const mockProperty = {
        id: propertyId,
        amenities: Promise.resolve(null),
        parking: Promise.resolve(null),
        specifications: Promise.resolve(null),
      };

      const analysis: ImageAnalysis = {
        tags: ['fireplace', 'garage', 'hardwood floors'], // Features that trigger all three entity types
        propertyFeatures: [
          { tag: 'hasFireplace', confidence: 85, value: true },
          { tag: 'hasParking', confidence: 90, value: true },
          { tag: 'hasAirConditioning', confidence: 80, value: true },
        ],
      };

      const newAmenities = { id: 'new-amenities-id', hasFireplace: null };
      const newParking = { id: 'new-parking-id', hasParking: null };
      const newSpecifications = { id: 'new-specs-id', coolingSystem: null };

      propertyRepository.findOne.mockResolvedValue(mockProperty as any);
      amenitiesRepository.create.mockReturnValue(newAmenities as any);
      amenitiesRepository.save.mockResolvedValue(newAmenities as any);
      parkingRepository.create.mockReturnValue(newParking as any);
      parkingRepository.save.mockResolvedValue(newParking as any);
      specificationsRepository.create.mockReturnValue(newSpecifications as any);
      specificationsRepository.save.mockResolvedValue(newSpecifications as any);

      const result = await service.updatePropertyFromImageTags(propertyId, analysis);

      expect(result.amenitiesUpdated).toBe(true);
      expect(result.parkingUpdated).toBe(true);
      expect(result.specificationsUpdated).toBe(true);
      expect(amenitiesRepository.create).toHaveBeenCalled();
      expect(parkingRepository.create).toHaveBeenCalled();
      expect(specificationsRepository.create).toHaveBeenCalled();
      expect(propertyRepository.save).toHaveBeenCalledTimes(3); // Once for each new entity
    });

    it('should not create entities when no relevant features are detected', async () => {
      const propertyId = 'test-property-id';
      const mockProperty = {
        id: propertyId,
        amenities: Promise.resolve(null),
        parking: Promise.resolve(null),
        specifications: Promise.resolve(null),
      };

      const analysis: ImageAnalysis = {
        tags: ['living room', 'modern', 'bright'], // No amenity/parking/specification features
      };

      propertyRepository.findOne.mockResolvedValue(mockProperty as any);

      const result = await service.updatePropertyFromImageTags(propertyId, analysis);

      expect(result.amenitiesUpdated).toBe(false);
      expect(result.parkingUpdated).toBe(false);
      expect(result.specificationsUpdated).toBe(false);
      expect(result.updatedFields).toHaveLength(0);
      expect(result.conflicts).toHaveLength(0);

      // Should not create any entities when no relevant features are detected
      expect(amenitiesRepository.create).not.toHaveBeenCalled();
      expect(parkingRepository.create).not.toHaveBeenCalled();
      expect(specificationsRepository.create).not.toHaveBeenCalled();
      expect(propertyRepository.save).not.toHaveBeenCalled();
    });

    it('should handle empty analysis gracefully', async () => {
      const propertyId = 'test-property-id';
      const mockProperty = {
        id: propertyId,
        amenities: Promise.resolve(null),
        parking: Promise.resolve(null),
        specifications: Promise.resolve(null),
      };
      const analysis: ImageAnalysis = {
        tags: [],
      };

      propertyRepository.findOne.mockResolvedValue(mockProperty as any);

      const result = await service.updatePropertyFromImageTags(propertyId, analysis);

      expect(result.amenitiesUpdated).toBe(false);
      expect(result.parkingUpdated).toBe(false);
      expect(result.specificationsUpdated).toBe(false);
      expect(result.updatedFields).toHaveLength(0);
      expect(result.conflicts).toHaveLength(0);
      // Should not create any entities when no relevant features are detected
      expect(amenitiesRepository.create).not.toHaveBeenCalled();
      expect(parkingRepository.create).not.toHaveBeenCalled();
      expect(specificationsRepository.create).not.toHaveBeenCalled();
    });

    it('should throw error when property is not found', async () => {
      const propertyId = 'non-existent-property';
      const analysis: ImageAnalysis = {
        tags: ['fireplace'],
      };

      propertyRepository.findOne.mockResolvedValue(null);

      await expect(service.updatePropertyFromImageTags(propertyId, analysis)).rejects.toThrow(
        'Property with ID non-existent-property not found',
      );
    });
  });
});

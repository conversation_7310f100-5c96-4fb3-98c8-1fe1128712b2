import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Property } from '../../entities/property.entity';
import { Amenities } from '../amenities/amenities.entity';
import { Parking } from '../parking/parking.entity';
import { PropertySpecifications } from '../specifications/property-specifications.entity';
import { ParkingType } from '../parking/parking.type';
import { CoolingSystem } from '../../enums/cooling-system.enum';
import { HeatingSystemEnum } from '../../enums/heat-system.enum';
import { FloorCovering } from '../../enums/floor-covering.enum';
import { ImageAnalysis } from '../../../../../shared/file/image-analysis/interfaces/image-analysis.intefrace';

export interface PropertyUpdateResult {
  amenitiesUpdated: boolean;
  parkingUpdated: boolean;
  specificationsUpdated: boolean;
  updatedFields: string[];
  conflicts: string[];
}

@Injectable()
export class PropertyTagMapperService {
  constructor(
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(Amenities)
    private readonly amenitiesRepository: Repository<Amenities>,
    @InjectRepository(Parking)
    private readonly parkingRepository: Repository<Parking>,
    @InjectRepository(PropertySpecifications)
    private readonly specificationsRepository: Repository<PropertySpecifications>,
  ) {}

  async updatePropertyFromImageTags(propertyId: string, analysis: ImageAnalysis): Promise<PropertyUpdateResult> {
    const result: PropertyUpdateResult = {
      amenitiesUpdated: false,
      parkingUpdated: false,
      specificationsUpdated: false,
      updatedFields: [],
      conflicts: [],
    };

    if (!analysis.tags && !analysis.propertyFeatures) {
      return result;
    }

    try {
      // Load property with all related entities
      const property = await this.propertyRepository.findOne({
        where: { id: propertyId },
        relations: ['amenities', 'parking', 'specifications'],
      });

      if (!property) {
        throw new Error(`Property with ID ${propertyId} not found`);
      }

      // Extract mappable features from both structured and unstructured tags
      const mappedFeatures = this.extractMappableFeatures(analysis);

      // Update amenities
      const amenitiesResult = await this.updateAmenities(property, mappedFeatures);
      result.amenitiesUpdated = amenitiesResult.updated;
      result.updatedFields.push(...amenitiesResult.fields);
      result.conflicts.push(...amenitiesResult.conflicts);

      // Update parking
      const parkingResult = await this.updateParking(property, mappedFeatures);
      result.parkingUpdated = parkingResult.updated;
      result.updatedFields.push(...parkingResult.fields);
      result.conflicts.push(...parkingResult.conflicts);

      // Update specifications
      const specificationsResult = await this.updateSpecifications(property, mappedFeatures);
      result.specificationsUpdated = specificationsResult.updated;
      result.updatedFields.push(...specificationsResult.fields);
      result.conflicts.push(...specificationsResult.conflicts);

      return result;
    } catch (error) {
      console.error('Error updating property from image tags:', error);
      throw error;
    }
  }

  private extractMappableFeatures(analysis: ImageAnalysis): Map<string, boolean> {
    const features = new Map<string, boolean>();

    // Process structured property features (preferred)
    if (analysis.propertyFeatures && analysis.propertyFeatures.length > 0) {
      analysis.propertyFeatures.forEach((feature) => {
        features.set(feature.tag, feature.value);
      });
    }

    // Fallback: Process unstructured tags with mapping
    if (analysis.tags && analysis.tags.length > 0) {
      const tagMappings = this.getTagMappings();
      analysis.tags.forEach((tag) => {
        const normalizedTag = tag.toLowerCase().trim();
        if (tagMappings.has(normalizedTag)) {
          const mappedField = tagMappings.get(normalizedTag);
          if (mappedField && !features.has(mappedField)) {
            features.set(mappedField, true);
          }
        }
      });
    }

    return features;
  }

  private getTagMappings(): Map<string, string> {
    const mappings = new Map<string, string>();

    // Amenities mappings
    mappings.set('fireplace', 'hasFireplace');
    mappings.set('pool', 'hasPool');
    mappings.set('swimming pool', 'hasPool');
    mappings.set('hot tub', 'hasHotTubSpa');
    mappings.set('hot tub spa', 'hasHotTubSpa');
    mappings.set('spa', 'hasHotTubSpa');
    mappings.set('balcony', 'hasBalcony');
    mappings.set('patio', 'hasPatio');
    mappings.set('deck', 'hasDeck');
    mappings.set('basement', 'hasBasement');
    mappings.set('dishwasher', 'hasDishwasher');
    mappings.set('washer', 'hasWasher');
    mappings.set('dryer', 'hasDryer');
    mappings.set('washer dryer', 'hasWasherDryer');
    mappings.set('microwave', 'hasMicrowave');
    mappings.set('refrigerator', 'hasRefrigerator');
    mappings.set('garbage disposal', 'hasGarbageDisposal');
    mappings.set('air conditioning', 'hasAirConditioning');
    mappings.set('ac', 'hasAirConditioning');
    mappings.set('central air', 'hasAirConditioning');
    mappings.set('heating', 'hasHeating');
    mappings.set('fitness center', 'hasFitnessCenter');
    mappings.set('gym', 'hasFitnessCenter');

    // Parking mappings
    mappings.set('garage', 'hasParking');
    mappings.set('garage attached', 'garage_attached');
    mappings.set('garage detached', 'garage_detached');
    mappings.set('carport', 'carport');
    mappings.set('parking', 'hasParking');
    mappings.set('parking space', 'hasParking');
    mappings.set('parking spaces', 'hasParking');

    // Floor covering mappings
    mappings.set('hardwood floors', 'hardwood_floors');
    mappings.set('hardwood', 'hardwood_floors');
    mappings.set('carpet', 'carpet_flooring');
    mappings.set('tile', 'tile_flooring');
    mappings.set('laminate', 'laminate_flooring');
    mappings.set('concrete', 'concrete_flooring');

    return mappings;
  }

  private async updateAmenities(
    property: Property,
    features: Map<string, boolean>,
  ): Promise<{ updated: boolean; fields: string[]; conflicts: string[] }> {
    const result = { updated: false, fields: [], conflicts: [] };

    const amenityMappings = {
      hasFireplace: 'hasFireplace',
      hasPool: 'hasPool',
      hasHotTubSpa: 'hasHotTubSpa',
      hasBalcony: 'hasBalcony',
      hasPatio: 'hasPatio',
      hasDeck: 'hasDeck',
      hasBasement: 'hasBasement',
      hasDishwasher: 'hasDishwasher',
      hasWasher: 'hasWasher',
      hasDryer: 'hasDryer',
      hasWasherDryer: 'hasWasherDryer',
      hasMicrowave: 'hasMicrowave',
      hasRefrigerator: 'hasRefrigerator',
      hasGarbageDisposal: 'hasGarbageDisposal',
      hasAirConditioning: 'hasAirConditioning',
      hasHeating: 'hasHeating',
      hasFitnessCenter: 'hasFitnessCenter',
    };

    // Check if we have any amenity-related features before proceeding
    const hasAmenityFeatures = Array.from(features.keys()).some((key) => amenityMappings[key]);
    if (!hasAmenityFeatures) {
      return result;
    }

    let amenities = await property.amenities;
    if (!amenities) {
      amenities = this.amenitiesRepository.create({});
      await this.amenitiesRepository.save(amenities);
      property.amenities = amenities;
      await this.propertyRepository.save(property);
    }

    let hasChanges = false;
    for (const [featureKey, value] of features) {
      const amenityField = amenityMappings[featureKey];
      if (amenityField && amenityField in amenities) {
        const currentValue = amenities[amenityField];
        if (currentValue === null || currentValue === undefined) {
          amenities[amenityField] = value;
          result.fields.push(amenityField);
          hasChanges = true;
        } else if (currentValue !== value) {
          result.conflicts.push(`${amenityField}: existing=${currentValue}, detected=${value}`);
        }
      }
    }

    if (hasChanges) {
      await this.amenitiesRepository.save(amenities);
      result.updated = true;
    }

    return result;
  }

  private async updateParking(
    property: Property,
    features: Map<string, boolean>,
  ): Promise<{ updated: boolean; fields: string[]; conflicts: string[] }> {
    const result = { updated: false, fields: [], conflicts: [] };

    // Check if we have any parking-related features before proceeding
    const hasParkingFeatures =
      features.has('hasParking') ||
      features.has('garage_attached') ||
      features.has('garage_detached') ||
      features.has('carport');

    if (!hasParkingFeatures) {
      return result;
    }

    let parking = await property.parking;
    if (!parking) {
      parking = this.parkingRepository.create({});
      await this.parkingRepository.save(parking);
      property.parking = parking;
      await this.propertyRepository.save(property);
    }

    let hasChanges = false;

    // Check for parking presence
    if (features.has('hasParking') && parking.hasParking === null) {
      parking.hasParking = features.get('hasParking');
      result.fields.push('hasParking');
      hasChanges = true;
    }

    // Determine parking type
    if (parking.parkingType === null) {
      if (features.has('garage_attached')) {
        parking.parkingType = ParkingType.GARAGE_ATTACHED;
        parking.hasParking = true;
        result.fields.push('parkingType');
        hasChanges = true;
      } else if (features.has('garage_detached')) {
        parking.parkingType = ParkingType.GARAGE_DETACHED;
        parking.hasParking = true;
        result.fields.push('parkingType');
        hasChanges = true;
      } else if (features.has('carport')) {
        parking.parkingType = ParkingType.CARPORT;
        parking.hasParking = true;
        result.fields.push('parkingType');
        hasChanges = true;
      }
    }

    if (hasChanges) {
      await this.parkingRepository.save(parking);
      result.updated = true;
    }

    return result;
  }

  private async updateSpecifications(
    property: Property,
    features: Map<string, boolean>,
  ): Promise<{ updated: boolean; fields: string[]; conflicts: string[] }> {
    const result = { updated: false, fields: [], conflicts: [] };

    // Check if we have any specification-related features before proceeding
    const hasSpecificationFeatures =
      features.has('hasAirConditioning') ||
      features.has('hasHeating') ||
      features.has('hardwood_floors') ||
      features.has('carpet_flooring') ||
      features.has('tile_flooring') ||
      features.has('laminate_flooring') ||
      features.has('concrete_flooring');

    if (!hasSpecificationFeatures) {
      return result;
    }

    let specifications = await property.specifications;
    if (!specifications) {
      specifications = this.specificationsRepository.create({});
      await this.specificationsRepository.save(specifications);
      property.specifications = specifications;
      await this.propertyRepository.save(property);
    }

    let hasChanges = false;

    // Update cooling system
    if (specifications.coolingSystem === null && features.has('hasAirConditioning')) {
      specifications.coolingSystem = CoolingSystem.CENTRAL;
      result.fields.push('coolingSystem');
      hasChanges = true;
    }

    // Update heating system
    if (specifications.heatingSystem === null && features.has('hasHeating')) {
      specifications.heatingSystem = HeatingSystemEnum.FORCED_AIR;
      result.fields.push('heatingSystem');
      hasChanges = true;
    }

    // Update floor covering
    if (specifications.floorCovering === null) {
      if (features.has('hardwood_floors')) {
        specifications.floorCovering = FloorCovering.HARDWOOD;
        result.fields.push('floorCovering');
        hasChanges = true;
      } else if (features.has('carpet_flooring')) {
        specifications.floorCovering = FloorCovering.CARPET;
        result.fields.push('floorCovering');
        hasChanges = true;
      } else if (features.has('tile_flooring')) {
        specifications.floorCovering = FloorCovering.TILE;
        result.fields.push('floorCovering');
        hasChanges = true;
      } else if (features.has('laminate_flooring')) {
        specifications.floorCovering = FloorCovering.LAMINATE;
        result.fields.push('floorCovering');
        hasChanges = true;
      } else if (features.has('concrete_flooring')) {
        specifications.floorCovering = FloorCovering.CONCRETE;
        result.fields.push('floorCovering');
        hasChanges = true;
      }
    }

    if (hasChanges) {
      await this.specificationsRepository.save(specifications);
      result.updated = true;
    }

    return result;
  }
}

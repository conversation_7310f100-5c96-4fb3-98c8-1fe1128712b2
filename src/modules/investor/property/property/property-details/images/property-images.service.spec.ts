import { Test, TestingModule } from '@nestjs/testing';
import { PropertyImageService } from './property-image.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Property } from '../../entities/property.entity';
import { FileService } from '../../../../../shared/file/file.service';
import { File } from '../../../../../shared/file/entities/file.entity';
import { ImageScoringService } from './image-scoring.service';
import { PropertyImage } from './entities/property-image.entity';
import { ImageAnalysisService } from '../../../../../shared/file/image-analysis/image-analysis.service';
import { PropertyTagMapperService } from './property-tag-mapper.service';

describe('PropertyImagesService', () => {
  let service: PropertyImageService;
  let propertyRepository: jest.Mocked<Repository<Property>>;
  let fileService: jest.Mocked<FileService>;
  let propertyTagMapperService: jest.Mocked<PropertyTagMapperService>;
  let imageAnalysisService: jest.Mocked<ImageAnalysisService>;
  let module: TestingModule;

  beforeEach(async () => {
    const mockPropertyRepository = {
      findOneOrFail: jest.fn(),
      update: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        relation: jest.fn().mockReturnThis(),
        of: jest.fn().mockReturnThis(),
        add: jest.fn(),
      }),
    };

    const mockPropertyImageRepository = {
      create: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      findOne: jest.fn(),
    };

    const mockFileService = {
      uploadFile: jest.fn(),
      save: jest.fn(),
      updateFileOrder: jest.fn(),
      deleteFile: jest.fn(),
    };

    const mockImageAnalysisService = {
      analyzeImage: jest.fn(),
    };

    const mockPropertyTagMapperService = {
      updatePropertyFromImageTags: jest.fn(),
    };

    module = await Test.createTestingModule({
      providers: [
        PropertyImageService,
        ImageScoringService,
        {
          provide: getRepositoryToken(Property),
          useValue: mockPropertyRepository,
        },
        {
          provide: getRepositoryToken(PropertyImage),
          useValue: mockPropertyImageRepository,
        },
        {
          provide: FileService,
          useValue: mockFileService,
        },
        {
          provide: ImageAnalysisService,
          useValue: mockImageAnalysisService,
        },
        {
          provide: PropertyTagMapperService,
          useValue: mockPropertyTagMapperService,
        },
      ],
    }).compile();

    service = module.get<PropertyImageService>(PropertyImageService);
    propertyRepository = module.get(getRepositoryToken(Property));
    fileService = module.get(FileService);
    propertyTagMapperService = module.get(PropertyTagMapperService);
    imageAnalysisService = module.get(ImageAnalysisService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadImage', () => {
    it('should upload image and return immediately without waiting for analysis', async () => {
      const mockFile = {
        buffer: Buffer.from('fake-image-data'),
        mimetype: 'image/jpeg',
      } as Express.Multer.File;

      const mockProperty = {
        id: 'property-1',
        propertyImages: Promise.resolve([]),
      };

      const mockUploadedFile = {
        id: 'file-1',
        url: 'https://example.com/image.jpg',
        thumbnailUrl: 'https://example.com/thumb.jpg',
      };

      const mockPropertyImageRepository = module.get(getRepositoryToken(PropertyImage));

      propertyRepository.findOneOrFail.mockResolvedValue(mockProperty as any);
      fileService.uploadFile.mockResolvedValue(mockUploadedFile as any);
      fileService.save.mockResolvedValue(undefined);
      mockPropertyImageRepository.create.mockReturnValue({ propertyId: 'property-1', fileId: 'file-1' });
      mockPropertyImageRepository.save.mockResolvedValue(undefined);

      const result = await service.uploadImage(mockFile, 'property-1');

      expect(fileService.uploadFile).toHaveBeenCalledWith(mockFile, 0);
      expect(fileService.save).toHaveBeenCalledWith(mockUploadedFile);
      expect(mockPropertyImageRepository.create).toHaveBeenCalledWith({
        propertyId: 'property-1',
        fileId: 'file-1',
      });
      expect(mockPropertyImageRepository.save).toHaveBeenCalled();
      expect(result).toEqual(mockUploadedFile);

      // The method should return immediately, but background analysis may have started
      // We just need to verify the upload completed successfully
    });

    it('should set cover image when uploading first image', async () => {
      const mockFile = {
        buffer: Buffer.from('fake-image-data'),
        mimetype: 'image/jpeg',
      } as Express.Multer.File;

      const mockProperty = {
        id: 'property-1',
        propertyImages: Promise.resolve([]), // Empty array means this is the first image
      };

      const mockUploadedFile = {
        id: 'file-1',
        url: 'https://example.com/image.jpg',
        thumbnailUrl: 'https://example.com/thumb.jpg',
      };

      const mockPropertyImageRepository = module.get(getRepositoryToken(PropertyImage));

      propertyRepository.findOneOrFail.mockResolvedValue(mockProperty as any);
      fileService.uploadFile.mockResolvedValue(mockUploadedFile as any);
      fileService.save.mockResolvedValue(undefined);
      mockPropertyImageRepository.create.mockReturnValue({ propertyId: 'property-1', fileId: 'file-1' });
      mockPropertyImageRepository.save.mockResolvedValue(undefined);

      const result = await service.uploadImage(mockFile, 'property-1');

      expect(propertyRepository.update).toHaveBeenCalledWith('property-1', {
        coverImage: 'https://example.com/thumb.jpg',
      });
      expect(result).toEqual(mockUploadedFile);
    });
  });

  describe('background image analysis', () => {
    it('should upload image immediately and start analysis in background', async () => {
      const mockFile = {
        buffer: Buffer.from('fake-image-data'),
        mimetype: 'image/jpeg',
      } as Express.Multer.File;

      const mockProperty = {
        id: 'property-1',
        propertyImages: Promise.resolve([]),
      };

      const mockUploadedFile = {
        id: 'file-1',
        url: 'https://example.com/image.jpg',
        thumbnailUrl: 'https://example.com/thumb.jpg',
      };

      const mockPropertyImageRepository = module.get(getRepositoryToken(PropertyImage));

      propertyRepository.findOneOrFail.mockResolvedValue(mockProperty as any);
      fileService.uploadFile.mockResolvedValue(mockUploadedFile as any);
      fileService.save.mockResolvedValue(undefined);
      mockPropertyImageRepository.create.mockReturnValue({ propertyId: 'property-1', fileId: 'file-1' });
      mockPropertyImageRepository.save.mockResolvedValue(undefined);

      // Mock analysis to succeed immediately for this test
      const mockAnalysis = {
        tags: ['living room', 'modern'],
        qualityLighting: 85,
        analysisVersion: '2.1',
      };
      imageAnalysisService.analyzeImage.mockResolvedValue(mockAnalysis);
      mockPropertyImageRepository.update.mockResolvedValue(undefined);
      propertyTagMapperService.updatePropertyFromImageTags.mockResolvedValue({
        amenitiesUpdated: true,
        parkingUpdated: false,
        specificationsUpdated: false,
        updatedFields: [],
        conflicts: [],
      });

      // Upload the image - this should return immediately
      const startTime = Date.now();
      const result = await service.uploadImage(mockFile, 'property-1');
      const endTime = Date.now();

      // Verify upload completed quickly (less than 100ms)
      expect(endTime - startTime).toBeLessThan(100);
      expect(result).toEqual(mockUploadedFile);

      // Verify the core upload operations completed
      expect(fileService.uploadFile).toHaveBeenCalledWith(mockFile, 0);
      expect(fileService.save).toHaveBeenCalledWith(mockUploadedFile);
      expect(mockPropertyImageRepository.create).toHaveBeenCalledWith({
        propertyId: 'property-1',
        fileId: 'file-1',
      });
      expect(mockPropertyImageRepository.save).toHaveBeenCalled();
    });
  });

  describe('sortImagesByTags', () => {
    it('should not change order when no images have tags', async () => {
      const mockPropertyImages = [
        { tags: null, file: { id: '1', tags: null, order: 0 } },
        { tags: [], file: { id: '2', tags: [], order: 1 } },
        { tags: null, file: { id: '3', tags: null, order: 2 } },
      ];

      const mockProperty = {
        id: 'property-1',
        propertyImages: Promise.resolve(mockPropertyImages),
      } as unknown as Property;

      propertyRepository.findOneOrFail.mockResolvedValue(mockProperty);

      const result = await service.sortImagesByTags('property-1');

      expect(result).toEqual([
        { id: '1', tags: null, order: 0 },
        { id: '2', tags: [], order: 1 },
        { id: '3', tags: null, order: 2 },
      ]);
      expect(fileService.updateFileOrder).not.toHaveBeenCalled();
    });

    it('should sort images by appeal score with living room first', async () => {
      const mockPropertyImages = [
        {
          tags: ['garage', 'basic'],
          file: { id: '1', tags: ['garage', 'basic'], order: 0, url: 'garage.jpg' },
        },
        {
          tags: ['living room', 'modern', 'bright'],
          file: {
            id: '2',
            tags: ['living room', 'modern', 'bright'],
            order: 1,
            url: 'living.jpg',
            thumbnailUrl: 'living-thumb.jpg',
          },
        },
        {
          tags: ['bedroom', 'cozy'],
          file: { id: '3', tags: ['bedroom', 'cozy'], order: 2, url: 'bedroom.jpg' },
        },
        {
          tags: null,
          file: { id: '4', tags: null, order: 3, url: 'no-tags.jpg' },
        },
      ];

      const mockProperty = {
        id: 'property-1',
        propertyImages: Promise.resolve(mockPropertyImages),
        specifications: Promise.resolve(null),
        leaseConditions: Promise.resolve(null),
      } as unknown as Property;

      const expectedSortedImages = [
        {
          id: '2',
          tags: ['living room', 'modern', 'bright'],
          order: 0,
          url: 'living.jpg',
          thumbnailUrl: 'living-thumb.jpg',
        },
        { id: '3', tags: ['bedroom', 'cozy'], order: 1, url: 'bedroom.jpg' },
        { id: '1', tags: ['garage', 'basic'], order: 2, url: 'garage.jpg' },
        { id: '4', tags: null, order: 3, url: 'no-tags.jpg' },
      ];

      propertyRepository.findOneOrFail.mockResolvedValue(mockProperty);
      fileService.updateFileOrder.mockResolvedValue(expectedSortedImages as unknown as File[]);

      const result = await service.sortImagesByTags('property-1');

      expect(fileService.updateFileOrder).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ id: '2', order: 0 }), // Living room first
          expect.objectContaining({ id: '3', order: 1 }), // Bedroom second
          expect.objectContaining({ id: '1', order: 2 }), // Garage third
          expect.objectContaining({ id: '4', order: 3 }), // No tags last
        ]),
      );

      expect(propertyRepository.update).toHaveBeenCalledWith('property-1', {
        coverImage: 'living-thumb.jpg', // Living room image as cover
      });

      expect(result).toEqual(expectedSortedImages);
    });

    it('should return empty array when property has no images', async () => {
      const mockProperty = {
        id: 'property-1',
        propertyImages: Promise.resolve([]),
      } as Property;

      propertyRepository.findOneOrFail.mockResolvedValue(mockProperty);

      const result = await service.sortImagesByTags('property-1');

      expect(result).toEqual([]);
      expect(fileService.updateFileOrder).not.toHaveBeenCalled();
      expect(propertyRepository.update).not.toHaveBeenCalled();
    });

    it('should filter out property images with null file relationships', async () => {
      const mockPropertyImages = [
        { tags: null, file: { id: '1', tags: null, order: 0 } },
        { tags: [], file: null }, // This should be filtered out
        { tags: null, file: { id: '3', tags: null, order: 2 } },
      ];

      const mockProperty = {
        id: 'property-1',
        propertyImages: Promise.resolve(mockPropertyImages),
      } as unknown as Property;

      propertyRepository.findOneOrFail.mockResolvedValue(mockProperty);

      const result = await service.sortImagesByTags('property-1');

      expect(result).toEqual([
        { id: '1', tags: null, order: 0 },
        { id: '3', tags: null, order: 2 },
      ]);
      expect(fileService.updateFileOrder).not.toHaveBeenCalled();
    });
  });
});

import { Test, TestingModule } from '@nestjs/testing';
import { ImageScoringService } from './image-scoring.service';
import { PropertyImage } from './entities/property-image.entity';
import { PropertyTypeEnum } from '../../enums/property-type.enum';

describe('ImageScoringService', () => {
  let service: ImageScoringService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ImageScoringService],
    }).compile();

    service = module.get<ImageScoringService>(ImageScoringService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return -1 for images without tags', () => {
    const image = new PropertyImage();
    image.tags = null;

    const score = service.calculatePropertyImageAppealScore(image);
    expect(score).toBe(-1);
  });

  it('should calculate higher score for living room vs garage', () => {
    const livingRoomImage = new PropertyImage();
    livingRoomImage.tags = ['living room', 'modern', 'bright'];
    livingRoomImage.qualityOverall = 85;
    livingRoomImage.stagingCleanliness = 90;
    livingRoomImage.uniqueness = 70;
    livingRoomImage.marketAppeal = 85;
    livingRoomImage.analysisConfidence = 90;

    const garageImage = new PropertyImage();
    garageImage.tags = ['garage', 'basic'];
    garageImage.qualityOverall = 70;
    garageImage.stagingCleanliness = 60;
    garageImage.uniqueness = 40;
    garageImage.marketAppeal = 50;
    garageImage.analysisConfidence = 85;

    const livingRoomScore = service.calculatePropertyImageAppealScore(livingRoomImage);
    const garageScore = service.calculatePropertyImageAppealScore(garageImage);

    expect(livingRoomScore).toBeGreaterThan(garageScore);
    // Garage should be heavily penalized due to 'never-cover' tier
    expect(garageScore).toBeLessThan(30); // Should be very low due to hierarchy
  });

  it('should heavily penalize bathroom images for cover photo', () => {
    const bathroomImage = new PropertyImage();
    bathroomImage.tags = ['bathroom', 'luxury', 'modern'];
    bathroomImage.qualityOverall = 95; // High quality
    bathroomImage.stagingCleanliness = 95;
    bathroomImage.uniqueness = 90;
    bathroomImage.marketAppeal = 90;
    bathroomImage.analysisConfidence = 95;

    const livingRoomImage = new PropertyImage();
    livingRoomImage.tags = ['living room', 'basic'];
    livingRoomImage.qualityOverall = 60; // Lower quality
    livingRoomImage.stagingCleanliness = 60;
    livingRoomImage.uniqueness = 40;
    livingRoomImage.marketAppeal = 50;
    livingRoomImage.analysisConfidence = 80;

    const bathroomScore = service.calculatePropertyImageAppealScore(bathroomImage);
    const livingRoomScore = service.calculatePropertyImageAppealScore(livingRoomImage);

    // Even with high quality, bathroom should score much lower than living room
    expect(livingRoomScore).toBeGreaterThan(bathroomScore);
    expect(bathroomScore).toBeLessThan(50); // Should be heavily penalized
  });

  it('should adjust scoring based on property type', () => {
    const exteriorImage = new PropertyImage();
    exteriorImage.tags = ['exterior', 'modern'];
    exteriorImage.qualityOverall = 80;
    exteriorImage.stagingCleanliness = 75;
    exteriorImage.uniqueness = 60;
    exteriorImage.marketAppeal = 70;
    exteriorImage.analysisConfidence = 85;

    // Exterior should score higher for houses
    const houseScore = service.calculatePropertyImageAppealScore(exteriorImage, PropertyTypeEnum.HOUSE);

    const apartmentScore = service.calculatePropertyImageAppealScore(exteriorImage, PropertyTypeEnum.APARTMENT);

    expect(houseScore).toBeGreaterThan(apartmentScore);
  });

  it('should apply confidence penalty for low confidence analysis', () => {
    const highConfidenceImage = new PropertyImage();
    highConfidenceImage.tags = ['living room', 'modern'];
    highConfidenceImage.qualityOverall = 80;
    highConfidenceImage.stagingCleanliness = 80;
    highConfidenceImage.uniqueness = 70;
    highConfidenceImage.marketAppeal = 75;
    highConfidenceImage.analysisConfidence = 95;

    const lowConfidenceImage = new PropertyImage();
    lowConfidenceImage.tags = ['living room', 'modern'];
    lowConfidenceImage.qualityOverall = 80;
    lowConfidenceImage.stagingCleanliness = 80;
    lowConfidenceImage.uniqueness = 70;
    lowConfidenceImage.marketAppeal = 75;
    lowConfidenceImage.analysisConfidence = 50;

    const highScore = service.calculatePropertyImageAppealScore(highConfidenceImage);
    const lowScore = service.calculatePropertyImageAppealScore(lowConfidenceImage);

    expect(highScore).toBeGreaterThan(lowScore);
  });

  it('should handle negative attribute tags', () => {
    const positiveImage = new PropertyImage();
    positiveImage.tags = ['living room', 'bright', 'modern'];
    positiveImage.qualityOverall = 80;
    positiveImage.stagingCleanliness = 80;
    positiveImage.uniqueness = 70;
    positiveImage.marketAppeal = 75;
    positiveImage.analysisConfidence = 90;

    const negativeImage = new PropertyImage();
    negativeImage.tags = ['living room', 'dark', 'dated'];
    negativeImage.qualityOverall = 80;
    negativeImage.stagingCleanliness = 80;
    negativeImage.uniqueness = 70;
    negativeImage.marketAppeal = 75;
    negativeImage.analysisConfidence = 90;

    const positiveScore = service.calculatePropertyImageAppealScore(positiveImage);
    const negativeScore = service.calculatePropertyImageAppealScore(negativeImage);

    expect(positiveScore).toBeGreaterThan(negativeScore);
  });

  it('should determine market segment correctly', () => {
    // Test rent-based determination
    expect(service.determineMarketSegment(3500)).toBe('luxury');
    expect(service.determineMarketSegment(1800)).toBe('mid');
    expect(service.determineMarketSegment(1000)).toBe('budget');

    // Test tag-based determination
    const luxuryTags = ['luxury', 'granite counters', 'stainless appliances'];
    expect(service.determineMarketSegment(undefined, luxuryTags)).toBe('luxury');

    const basicTags = ['basic', 'carpet'];
    expect(service.determineMarketSegment(undefined, basicTags)).toBe('mid');
  });

  it('should calculate quality score from components', () => {
    const image = new PropertyImage();
    image.tags = ['living room'];
    image.qualityLighting = 90;
    image.qualityComposition = 85;
    image.qualityClarity = 80;
    image.qualityOverall = 88;
    image.stagingCleanliness = 80;
    image.uniqueness = 70;
    image.marketAppeal = 75;
    image.analysisConfidence = 90;

    const score = service.calculatePropertyImageAppealScore(image);
    expect(score).toBeGreaterThan(0);
    expect(typeof score).toBe('number');
  });

  it('should handle furnished staging bonus', () => {
    const furnishedImage = new PropertyImage();
    furnishedImage.tags = ['living room'];
    furnishedImage.qualityOverall = 80;
    furnishedImage.stagingCleanliness = 80;
    furnishedImage.stagingFurnished = true;
    furnishedImage.stagingDecluttered = 80;
    furnishedImage.uniqueness = 70;
    furnishedImage.marketAppeal = 75;
    furnishedImage.analysisConfidence = 90;

    const unfurnishedImage = new PropertyImage();
    unfurnishedImage.tags = ['living room'];
    unfurnishedImage.qualityOverall = 80;
    unfurnishedImage.stagingCleanliness = 80;
    unfurnishedImage.stagingFurnished = false;
    unfurnishedImage.stagingDecluttered = 80;
    unfurnishedImage.uniqueness = 70;
    unfurnishedImage.marketAppeal = 75;
    unfurnishedImage.analysisConfidence = 90;

    const furnishedScore = service.calculatePropertyImageAppealScore(furnishedImage);
    const unfurnishedScore = service.calculatePropertyImageAppealScore(unfurnishedImage);

    expect(furnishedScore).toBeGreaterThan(unfurnishedScore);
  });
});

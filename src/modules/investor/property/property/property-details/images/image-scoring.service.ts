import { Injectable } from '@nestjs/common';
import { PropertyTypeEnum } from '../../enums/property-type.enum';
import { PropertyImage } from './entities/property-image.entity';

export interface ScoringWeights {
  roomTypeWeight: number;
  qualityWeight: number;
  stagingWeight: number;
  uniquenessWeight: number;
  marketAppealWeight: number;
}

export interface RoomHierarchy {
  tier: 'primary-cover' | 'secondary-cover' | 'tertiary-cover' | 'never-cover';
  baseScore: number;
  multiplier: number;
}

@Injectable()
export class ImageScoringService {
  calculatePropertyImageAppealScore(
    propertyImage: PropertyImage,
    propertyType?: PropertyTypeEnum,
    marketSegment: 'budget' | 'mid' | 'luxury' = 'mid',
  ): number {
    // If no tags, return -1 (images without analysis go to end)
    if (!propertyImage.tags || propertyImage.tags.length === 0) {
      return -1;
    }

    // First check room type hierarchy for cover image suitability
    const roomHierarchy = this.getRoomHierarchy(propertyImage.tags, propertyType);

    // If this is a room type that should never be a cover image, heavily penalize it
    if (roomHierarchy.tier === 'never-cover') {
      return Math.max(1, roomHierarchy.baseScore * 0.3); // Max 30% of base score
    }

    const weights = this.getWeights(propertyType, marketSegment);

    // Calculate component scores
    const roomTypeScore = this.calculateRoomTypeScore(propertyImage.tags, propertyType);
    const qualityScore = this.calculatePropertyImageQualityScore(propertyImage);
    const stagingScore = this.calculatePropertyImageStagingScore(propertyImage);
    const uniquenessScore = propertyImage.uniqueness || 50;
    const marketAppealScore = propertyImage.marketAppeal || 50;

    // Calculate weighted total score
    let totalScore =
      roomTypeScore * weights.roomTypeWeight +
      qualityScore * weights.qualityWeight +
      stagingScore * weights.stagingWeight +
      uniquenessScore * weights.uniquenessWeight +
      marketAppealScore * weights.marketAppealWeight;

    // Apply room hierarchy multiplier to ensure proper ordering
    totalScore *= roomHierarchy.multiplier;

    // Apply confidence penalty if analysis confidence is low
    const confidencePenalty = this.getConfidencePenalty(propertyImage.analysisConfidence);

    return Math.round(totalScore * confidencePenalty);
  }

  private getWeights(propertyType?: PropertyTypeEnum, marketSegment: string = 'mid'): ScoringWeights {
    const weights: ScoringWeights = {
      roomTypeWeight: 0.5,
      qualityWeight: 0.2,
      stagingWeight: 0.15,
      uniquenessWeight: 0.08,
      marketAppealWeight: 0.07,
    };

    if (
      propertyType === PropertyTypeEnum.HOUSE ||
      propertyType === PropertyTypeEnum.TOWNHOUSE ||
      propertyType === PropertyTypeEnum.DUPLEX
    ) {
      weights.uniquenessWeight = 0.12;
      weights.marketAppealWeight = 0.11;
      weights.qualityWeight = 0.17;
      weights.stagingWeight = 0.1;
    } else if (propertyType === PropertyTypeEnum.APARTMENT || propertyType === PropertyTypeEnum.CONDO) {
      weights.qualityWeight = 0.25;
      weights.stagingWeight = 0.18;
      weights.uniquenessWeight = 0.04;
      weights.marketAppealWeight = 0.13;
    }

    if (marketSegment === 'luxury') {
      weights.qualityWeight = 0.25;
      weights.uniquenessWeight = 0.15;
      weights.stagingWeight = 0.08;
      weights.marketAppealWeight = 0.02;
    } else if (marketSegment === 'budget') {
      weights.roomTypeWeight = 0.6;
      weights.qualityWeight = 0.15;
      weights.stagingWeight = 0.12;
      weights.uniquenessWeight = 0.08;
      weights.marketAppealWeight = 0.05;
    }

    return weights;
  }

  private getRoomHierarchy(tags: string[], propertyType?: PropertyTypeEnum): RoomHierarchy {
    const roomHierarchies: { [key: string]: RoomHierarchy } = {
      'living room': { tier: 'primary-cover', baseScore: 100, multiplier: 1.0 },
      kitchen: { tier: 'primary-cover', baseScore: 95, multiplier: 1.0 },

      'dining room': { tier: 'secondary-cover', baseScore: 85, multiplier: 0.95 },
      bedroom: { tier: 'secondary-cover', baseScore: 90, multiplier: 0.9 },
      office: { tier: 'secondary-cover', baseScore: 75, multiplier: 0.85 },

      balcony: { tier: 'tertiary-cover', baseScore: 70, multiplier: 0.8 },
      patio: { tier: 'tertiary-cover', baseScore: 70, multiplier: 0.8 },
      exterior: { tier: 'tertiary-cover', baseScore: 65, multiplier: 0.75 },

      bathroom: { tier: 'never-cover', baseScore: 80, multiplier: 0.3 },
      'laundry room': { tier: 'never-cover', baseScore: 60, multiplier: 0.3 },
      garage: { tier: 'never-cover', baseScore: 50, multiplier: 0.3 },
      basement: { tier: 'never-cover', baseScore: 45, multiplier: 0.3 },
      attic: { tier: 'never-cover', baseScore: 40, multiplier: 0.3 },
      hallway: { tier: 'never-cover', baseScore: 35, multiplier: 0.3 },
      closet: { tier: 'never-cover', baseScore: 30, multiplier: 0.3 },
    };

    if (
      propertyType === PropertyTypeEnum.HOUSE ||
      propertyType === PropertyTypeEnum.TOWNHOUSE ||
      propertyType === PropertyTypeEnum.DUPLEX
    ) {
      // For houses, exterior becomes secondary cover
      roomHierarchies['exterior'] = { tier: 'secondary-cover', baseScore: 75, multiplier: 0.9 };
      roomHierarchies['garage'] = { tier: 'tertiary-cover', baseScore: 60, multiplier: 0.7 };
    } else if (propertyType === PropertyTypeEnum.APARTMENT) {
      // For apartments, balcony becomes secondary cover
      roomHierarchies['balcony'] = { tier: 'secondary-cover', baseScore: 80, multiplier: 0.9 };
      roomHierarchies['patio'] = { tier: 'secondary-cover', baseScore: 75, multiplier: 0.85 };
    }

    // Find the highest tier room in the tags
    let bestHierarchy: RoomHierarchy = { tier: 'tertiary-cover', baseScore: 50, multiplier: 0.5 }; // Default for unknown rooms

    tags.forEach((tag) => {
      const normalizedTag = tag.toLowerCase().trim();
      const hierarchy = roomHierarchies[normalizedTag];

      if (hierarchy) {
        if (
          this.isHigherTier(hierarchy.tier, bestHierarchy.tier) ||
          (hierarchy.tier === bestHierarchy.tier && hierarchy.baseScore > bestHierarchy.baseScore)
        ) {
          bestHierarchy = hierarchy;
        }
      }
    });

    return bestHierarchy;
  }

  private isHigherTier(tier1: string, tier2: string): boolean {
    const tierOrder = ['primary-cover', 'secondary-cover', 'tertiary-cover', 'never-cover'];
    return tierOrder.indexOf(tier1) < tierOrder.indexOf(tier2);
  }

  private calculateRoomTypeScore(tags: string[], propertyType?: PropertyTypeEnum): number {
    const roomTypeScores: { [key: string]: number } = {
      'living room': 100,
      kitchen: 95,
      bedroom: 90,
      'dining room': 85,
      bathroom: 80,
      office: 75,
      balcony: 70,
      patio: 70,
      exterior: 65,
      'laundry room': 60,
      garage: 50,
      basement: 45,
      attic: 40,
      hallway: 35,
      closet: 30,
    };

    if (
      propertyType === PropertyTypeEnum.HOUSE ||
      propertyType === PropertyTypeEnum.TOWNHOUSE ||
      propertyType === PropertyTypeEnum.DUPLEX
    ) {
      roomTypeScores['exterior'] = 75;
      roomTypeScores['garage'] = 60;
      roomTypeScores['basement'] = 55;
    } else if (propertyType === PropertyTypeEnum.APARTMENT) {
      roomTypeScores['balcony'] = 80;
      roomTypeScores['patio'] = 75;
      roomTypeScores['garage'] = 40;
    }

    let maxScore = 0;
    tags.forEach((tag) => {
      const normalizedTag = tag.toLowerCase().trim();
      if (roomTypeScores[normalizedTag]) {
        maxScore = Math.max(maxScore, roomTypeScores[normalizedTag]);
      }
    });

    const attributeBonus = this.calculateAttributeBonus(tags);
    const featureBonus = this.calculateFeatureBonus(tags);

    return Math.min(100, maxScore + attributeBonus + featureBonus);
  }

  private calculateAttributeBonus(tags: string[]): number {
    const attributeScores: { [key: string]: number } = {
      luxury: 25,
      modern: 20,
      bright: 18,
      spacious: 15,
      updated: 12,
      cozy: 10,
      traditional: 8,
      basic: 3,
      dark: -8,
      cramped: -10,
      dated: -12,
      worn: -15,
    };

    let bonus = 0;
    tags.forEach((tag) => {
      const normalizedTag = tag.toLowerCase().trim();
      if (attributeScores[normalizedTag]) {
        bonus += attributeScores[normalizedTag];
      }
    });

    return Math.max(-20, Math.min(30, bonus)); // Cap bonus between -20 and +30
  }

  private calculateFeatureBonus(tags: string[]): number {
    const featureScores: { [key: string]: number } = {
      'granite counters': 15,
      'stainless appliances': 12,
      'hardwood floors': 12,
      'large windows': 10,
      'high ceilings': 10,
      fireplace: 8,
      'crown molding': 6,
      tile: 5,
      carpet: 2,
      'exposed beams': 8,
      'bay windows': 7,
      'french doors': 6,
    };

    let bonus = 0;
    tags.forEach((tag) => {
      const normalizedTag = tag.toLowerCase().trim();
      if (featureScores[normalizedTag]) {
        bonus += featureScores[normalizedTag];
      }
    });

    return Math.min(25, bonus); // Cap feature bonus at 25
  }

  private calculatePropertyImageQualityScore(propertyImage: PropertyImage): number {
    if (!propertyImage.qualityOverall) {
      return 50; // Default score if no quality data
    }

    // Weight the quality components
    const lighting = propertyImage.qualityLighting || 50;
    const composition = propertyImage.qualityComposition || 50;
    const clarity = propertyImage.qualityClarity || 50;
    const overall = propertyImage.qualityOverall;

    // Weighted average with emphasis on overall quality
    return Math.round(lighting * 0.3 + composition * 0.3 + clarity * 0.2 + overall * 0.2);
  }

  private calculatePropertyImageStagingScore(propertyImage: PropertyImage): number {
    if (!propertyImage.stagingCleanliness && !propertyImage.stagingDecluttered) {
      return 50; // Default score if no staging data
    }

    const cleanliness = propertyImage.stagingCleanliness || 50;
    const decluttered = propertyImage.stagingDecluttered || 50;
    const furnishedBonus = propertyImage.stagingFurnished ? 10 : 0;

    return Math.min(100, Math.round(cleanliness * 0.5 + decluttered * 0.5 + furnishedBonus));
  }

  private getConfidencePenalty(confidence?: number): number {
    if (!confidence) return 0.9; // 10% penalty for no confidence data

    if (confidence >= 90) return 1.0; // No penalty
    if (confidence >= 80) return 0.98; // 2% penalty
    if (confidence >= 70) return 0.95; // 5% penalty
    if (confidence >= 60) return 0.9; // 10% penalty
    return 0.8; // 20% penalty for low confidence
  }

  /**
   * Determine market segment based on rent or property features
   */
  determineMarketSegment(rent?: number, tags?: string[]): 'budget' | 'mid' | 'luxury' {
    // Use rent as primary indicator
    if (rent) {
      if (rent >= 3000) return 'luxury';
      if (rent <= 1200) return 'budget';
      return 'mid';
    }

    if (tags) {
      const luxuryTags = ['luxury', 'granite counters', 'stainless appliances', 'hardwood floors', 'high ceilings'];
      const luxuryCount = tags.filter((tag) => luxuryTags.some((luxTag) => tag.toLowerCase().includes(luxTag))).length;

      if (luxuryCount >= 2) return 'luxury';
    }

    return 'mid';
  }
}

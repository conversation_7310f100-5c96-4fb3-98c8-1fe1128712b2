import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Property } from '../../entities/property.entity';
import { FileModule } from '../../../../../shared/file/file.module';
import { PropertyImageService } from './property-image.service';
import { ImageScoringService } from './image-scoring.service';
import { PropertyImage } from './entities/property-image.entity';
import { ImageAnalysisModule } from '../../../../../shared/file/image-analysis/image-analysis.module';
import { PropertyTagMapperService } from './property-tag-mapper.service';
import { Amenities } from '../amenities/amenities.entity';
import { Parking } from '../parking/parking.entity';
import { PropertySpecifications } from '../specifications/property-specifications.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Property, PropertyImage, Amenities, Parking, PropertySpecifications]),
    FileModule,
    ImageAnalysisModule,
  ],
  providers: [PropertyImageService, ImageScoringService, PropertyTagMapperService],
  exports: [PropertyImageService, ImageScoringService, PropertyTagMapperService],
})
export class PropertyImagesModule {}

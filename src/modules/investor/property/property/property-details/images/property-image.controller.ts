import {
  Body,
  Controller,
  Delete,
  HttpStatus,
  NotFoundException,
  Param,
  ParseFilePipeBuilder,
  Post,
  Put,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { PropertyImageService } from './property-image.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileDto } from '../../../../../shared/file/models/file.dto';
import { File } from '../../../../../shared/file/entities/file.entity';

@ApiTags('property-image')
@Controller('property-image')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class PropertyImageController {
  constructor(private readonly imagesService: PropertyImageService) {}

  @Post(':propertyId')
  @ApiResponse({ status: 200, description: 'Uploaded image', type: File })
  @UseInterceptors(FileInterceptor('image'))
  async uploadImage(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /image\/*/,
        })
        .addMaxSizeValidator({
          maxSize: 25 * 1024 * 1024,
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        }),
    )
    file: Express.Multer.File,
    @Param('propertyId') propertyId: string,
  ): Promise<FileDto> {
    const image = await this.imagesService.uploadImage(file, propertyId);

    return File.convertToDto(image);
  }

  @Put('order/:propertyId')
  @ApiResponse({ status: 200, description: 'Updated images order' })
  @UseGuards(IsPropertyOwnerGuard)
  async updateImagesOrder(@Param('propertyId') propertyId: string, @Body() images: FileDto[]): Promise<FileDto[]> {
    const updatedImages = await this.imagesService.updateImageOrder(propertyId, images);

    return Promise.all(updatedImages.map((image) => File.convertToDto(image)));
  }

  @Put('sort/:propertyId')
  @ApiResponse({ status: 200, description: 'Images sorted by AI tags for maximum appeal' })
  @UseGuards(IsPropertyOwnerGuard)
  async sortImagesByTags(@Param('propertyId') propertyId: string): Promise<FileDto[]> {
    const sortedImages = await this.imagesService.sortImagesByTags(propertyId);

    return Promise.all(sortedImages.map((image) => File.convertToDto(image)));
  }

  @Delete(':propertyId/:imageId')
  @ApiResponse({ status: 200, description: 'Image deleted' })
  @UseGuards(IsPropertyOwnerGuard)
  async deleteImage(@Param('imageId') imageId: string): Promise<void> {
    try {
      await this.imagesService.deleteImage(imageId);
    } catch (error) {
      throw new NotFoundException(`Failed to delete image, message: ${error.message}`);
    }
  }
}

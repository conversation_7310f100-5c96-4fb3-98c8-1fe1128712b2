import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Property } from '../../entities/property.entity';
import { FileService } from '../../../../../shared/file/file.service';
import { File } from '../../../../../shared/file/entities/file.entity';
import { FileDto } from '../../../../../shared/file/models/file.dto';
import { ImageScoringService } from './image-scoring.service';
import { PropertyImage } from './entities/property-image.entity';
import { ImageAnalysisService } from '../../../../../shared/file/image-analysis/image-analysis.service';
import { PropertyTagMapperService } from './property-tag-mapper.service';
import { ImageAnalysis } from '../../../../../shared/file/image-analysis/interfaces/image-analysis.intefrace';

@Injectable()
export class PropertyImageService {
  constructor(
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(PropertyImage)
    private readonly propertyImageRepository: Repository<PropertyImage>,
    private readonly fileService: FileService,
    private readonly imageScoringService: ImageScoringService,
    private readonly imageAnalysisService: ImageAnalysisService,
    private readonly propertyTagMapperService: PropertyTagMapperService,
  ) {}

  async uploadImage(file: Express.Multer.File, propertyId: string): Promise<File> {
    const property = await this.propertyRepository.findOneOrFail({
      where: { id: propertyId },
      relations: ['propertyImages'],
    });

    const propertyImages = await property.propertyImages;
    const imageOrder = propertyImages.length;

    const image = await this.fileService.uploadFile(file, imageOrder);

    if (imageOrder === 0) {
      await this.propertyRepository.update(propertyId, {
        coverImage: image.thumbnailUrl ? image.thumbnailUrl : image.url,
      });
    }
    await this.fileService.save(image);

    const propertyImage = this.propertyImageRepository.create({
      propertyId,
      fileId: image.id,
    });
    await this.propertyImageRepository.save(propertyImage);

    this.analyzeImageInBackground(file.buffer, file.mimetype, propertyId, image.id);

    return image;
  }

  private async analyzeImageInBackground(
    imageBuffer: Buffer,
    mimeType: string,
    propertyId: string,
    fileId: string,
  ): Promise<void> {
    try {
      const analysis = await this.analyzeImageWithRetry(imageBuffer, mimeType);

      if (analysis && analysis.tags) {
        await this.saveImageAnalysis(propertyId, fileId, analysis);
      }
    } catch (error) {
      console.error(`Failed to analyze image ${fileId} after all retries:`, error);
    }
  }

  private async analyzeImageWithRetry(
    imageBuffer: Buffer,
    mimeType: string,
    maxRetries: number = 3,
    baseDelay: number = 1000,
  ): Promise<ImageAnalysis | null> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Analyzing image - attempt ${attempt}/${maxRetries}`);
        return await this.imageAnalysisService.analyzeImage(imageBuffer, mimeType);
      } catch (error) {
        lastError = error;
        console.warn(`Image analysis attempt ${attempt} failed:`, error.message);

        if (attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          console.log(`Retrying in ${delay}ms...`);
          await this.sleep(delay);
        }
      }
    }

    throw lastError;
  }

  private async saveImageAnalysis(propertyId: string, fileId: string, analysis: ImageAnalysis): Promise<void> {
    const analysisData = {
      tags: analysis.tags,
      qualityLighting: analysis.qualityLighting,
      qualityComposition: analysis.qualityComposition,
      qualityClarity: analysis.qualityClarity,
      qualityOverall: analysis.qualityOverall,
      stagingCleanliness: analysis.stagingCleanliness,
      stagingFurnished: analysis.stagingFurnished,
      stagingDecluttered: analysis.stagingDecluttered,
      uniqueness: analysis.uniqueness,
      marketAppeal: analysis.marketAppeal,
      analysisConfidence: analysis.analysisConfidence,
      analysisVersion: analysis.analysisVersion,
    };

    await this.propertyImageRepository.update({ propertyId, fileId }, analysisData);

    try {
      await this.propertyTagMapperService.updatePropertyFromImageTags(propertyId, analysis);
    } catch (error) {
      console.error('Error updating property from image tags:', error);
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async deleteImage(imageId: string): Promise<void> {
    await this.propertyImageRepository.delete({ fileId: imageId });
    await this.fileService.deleteFile(imageId);
  }

  async updateImageOrder(propertyId: string, images: FileDto[]): Promise<File[]> {
    const updatedImages = await this.fileService.updateFileOrder(images);

    // set display image
    await this.propertyRepository.update(propertyId, {
      coverImage: updatedImages[0]?.thumbnailUrl,
    });

    return updatedImages;
  }

  async sortImagesByTags(propertyId: string): Promise<File[]> {
    const property = await this.propertyRepository.findOneOrFail({
      where: { id: propertyId },
      relations: ['specifications', 'leaseConditions', 'propertyImages', 'propertyImages.file'],
    });

    const propertyImages = await property.propertyImages;

    if (propertyImages.length === 0) {
      return [];
    }

    const imagesWithTags = propertyImages.filter(
      (propertyImage) => propertyImage.tags && propertyImage.tags.length > 0,
    );

    if (imagesWithTags.length === 0) {
      return propertyImages.filter((pi) => pi.file !== null).map((pi) => pi.file);
    }

    // Get property context for enhanced scoring
    const specifications = await property.specifications;
    const leaseConditions = await property.leaseConditions;

    const sortedPropertyImages = this.sortPropertyImagesByScore(
      propertyImages,
      specifications?.propertyType,
      leaseConditions?.rent,
    );

    const sortedFiles = sortedPropertyImages
      .filter((pi) => pi.file !== null) // Filter out property images with null file relationships
      .map((pi) => pi.file);
    const updatedImages = await this.fileService.updateFileOrder(
      sortedFiles.map((image, index) => ({ ...image, order: index })),
    );

    if (updatedImages.length > 0) {
      await this.propertyRepository.update(propertyId, {
        coverImage: updatedImages[0].thumbnailUrl || updatedImages[0].url,
      });
    }

    return updatedImages;
  }

  private sortPropertyImagesByScore(
    propertyImages: PropertyImage[],
    propertyType?: any,
    rent?: number,
  ): PropertyImage[] {
    const marketSegment = this.imageScoringService.determineMarketSegment(rent);

    return propertyImages.sort((a, b) => {
      const scoreA = this.imageScoringService.calculatePropertyImageAppealScore(a, propertyType, marketSegment);
      const scoreB = this.imageScoringService.calculatePropertyImageAppealScore(b, propertyType, marketSegment);

      return scoreB - scoreA;
    });
  }
}

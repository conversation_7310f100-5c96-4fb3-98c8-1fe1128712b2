import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RenterRequirements } from './renter-requirements.entity';
import { FieldValue } from '../../../../../ai/models/field-value';
import ParsingUtils from '../../../../../../utils/parsing.utils';
import { Property } from '../../entities/property.entity';
import { RenterRequirementsDto } from './renter-requirements.dto';

@Injectable()
export class RenterRequirementsService {
  constructor(
    @InjectRepository(RenterRequirements)
    private readonly renterRequirementsRepository: Repository<RenterRequirements>,

    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async getUpdatedRenterRequirements(renterRequirements: RenterRequirements, data: any): Promise<RenterRequirements> {
    if (!renterRequirements) {
      renterRequirements = new RenterRequirements();
    }

    return this.mapFieldValuesToEntity(renterRequirements, data);
  }

  async create(propertyId: string, payload: RenterRequirementsDto): Promise<RenterRequirements> {
    const result = await this.renterRequirementsRepository
      .createQueryBuilder()
      .insert()
      .values({ ...payload })
      .returning('*')
      .execute();

    const renterRequirements = Object.assign(new RenterRequirements(), { ...result.raw[0] });

    await this.propertyRepository.update(propertyId, {
      renterRequirements: { id: renterRequirements.id },
    });

    return renterRequirements;
  }

  async update(requirementsId: string, payload: RenterRequirementsDto): Promise<RenterRequirements> {
    const result = await this.renterRequirementsRepository
      .createQueryBuilder()
      .update({ ...payload })
      .where({ id: requirementsId })
      .returning('*')
      .execute();

    return Object.assign(new RenterRequirements(), { ...result.raw[0] });
  }

  private mapFieldValuesToEntity(renterRequirements: RenterRequirements, data: FieldValue[]): RenterRequirements {
    const renterRequirementsCopy = { ...renterRequirements };

    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;

      try {
        switch (item.fieldName.split('.').reverse()[0]) {
          case 'minimumCreditScore':
            renterRequirementsCopy.minimumCreditScore = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'criminalBackground':
            renterRequirementsCopy.acceptsCosigners = ParsingUtils.parseStringToBoolean(item.value);
            break;
          default:
            break;
        }
      } catch (error) {
        throw new HttpException('Invalid field name', 400, {
          cause: error,
        });
      }
    }

    return renterRequirementsCopy;
  }
}

import { Body, Controller, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { RenterRequirementsService } from './renter-requirements.service';
import { RenterRequirementsDto } from './renter-requirements.dto';
import { RenterRequirements } from './renter-requirements.entity';
import { IsRenterRequirementsOwnerGuard } from './is-renter-requirements-owner.guard';

@ApiTags('renter-requirements')
@Controller('property/:propertyId/renter-requirements')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class RenterRequirementsController {
  constructor(private readonly renterRequirementsService: RenterRequirementsService) {}

  @Post()
  @ApiOkResponse({
    description: 'Create renter requirements',
  })
  async create(
    @Param('propertyId') propertyId: string,
    @Body() body: RenterRequirementsDto,
  ): Promise<RenterRequirementsDto> {
    const renterRequirements = await this.renterRequirementsService.create(propertyId, body);

    return RenterRequirements.convertToDto(renterRequirements);
  }

  @Patch(':renterRequirementsId')
  @ApiOkResponse({
    description: 'Update rent requirements',
  })
  @ApiParam({
    name: 'renterRequirementsId',
    required: true,
  })
  @UseGuards(IsRenterRequirementsOwnerGuard)
  async update(
    @Param('propertyId') _propertyId: string,
    @Param('renterRequirementsId') renterRequirementsId: string,
    @Body() body: RenterRequirementsDto,
  ): Promise<RenterRequirementsDto> {
    const renterRequirements = await this.renterRequirementsService.update(renterRequirementsId, body);

    return RenterRequirements.convertToDto(renterRequirements);
  }
}

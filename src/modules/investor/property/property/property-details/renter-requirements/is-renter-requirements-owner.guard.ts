import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RenterRequirements } from './renter-requirements.entity';

@Injectable()
export class IsRenterRequirementsOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(RenterRequirements)
    private readonly renterRequirementsRepository: Repository<RenterRequirements>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const renterRequirementsId = request.params.renterRequirementsId || request.body.renterRequirementsId;
    const propertyId = request.params.propertyId || request.body.propertyId;

    if (!renterRequirementsId) {
      throw new NotFoundException('No renter requirements id provided');
    }

    if (!propertyId) {
      throw new NotFoundException('No property id provided');
    }

    const userOwnsRenterRequirements = await this.renterRequirementsRepository
      .createQueryBuilder('renterRequirements')
      .innerJoin('renterRequirements.property', 'property')
      .where('renterRequirements.id = :renterRequirementsId', { renterRequirementsId })
      .andWhere('property.id = :propertyId', { propertyId })
      .getCount()
      .then((count) => count > 0);

    if (!userOwnsRenterRequirements) {
      throw new ForbiddenException('User does not own this renter requirements');
    }

    return true;
  }
}

export enum ScreeningSensitivity {
  /**
   * For clients looking for nearly ideal renters. Renters should be able to pass all requirements.
   * For example, if a 650 credit score is required, then the renter should have at least a 650 credit score.
   */
  STRICT = 'Strict',

  /**
   * For clients looking for good renters with more flexible requirements.
   * For example, if a 650 credit score is required, then the renter should have at least a 630 credit score.
   */
  MODERATE = 'Moderate',

  /**
   * Classic Tallo mode. We ask all the questions, but the owner makes the final decision.
   */
  OWNER_REVIEW = 'Owner Review',
}

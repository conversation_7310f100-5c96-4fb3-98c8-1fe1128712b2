import { IsBoolean, IsEnum, IsN<PERSON>ber, IsOptional, IsString, Min } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IncomeEnum } from './income.enum';
import { ScreeningSensitivity } from './screening-sensitivity.enum';

export class RenterRequirementsDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  minimumCreditScore: number;

  @IsEnum(IncomeEnum)
  @IsOptional()
  @ApiPropertyOptional({ enum: IncomeEnum, enumName: 'IncomeEnum' })
  minimumIncome: IncomeEnum;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional()
  acceptsCosigners: boolean;

  @IsEnum(ScreeningSensitivity)
  @IsOptional()
  @ApiPropertyOptional({ enum: ScreeningSensitivity, enumName: 'ScreeningSensitivity' })
  screeningSensitivity: ScreeningSensitivity;
}

import { Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { RenterRequirementsDto } from './renter-requirements.dto';
import { RenterRequirementsWithTypesDto } from './renter-requirements-with-types.dto';
import { Property } from '../../entities/property.entity';
import { IncomeEnum } from './income.enum';
import { ScreeningSensitivity } from './screening-sensitivity.enum';

@Entity()
export class RenterRequirements {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Property, (property) => property.renterRequirements, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @Expose()
  @Column({ type: 'int', nullable: true })
  minimumCreditScore: number;

  @Expose()
  @Column({ type: 'enum', enum: IncomeEnum, nullable: true })
  minimumIncome: IncomeEnum;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  acceptsCosigners: boolean;

  @Expose()
  @Column({
    type: 'enum',
    enum: ScreeningSensitivity,
    default: ScreeningSensitivity.MODERATE,
  })
  screeningSensitivity: ScreeningSensitivity;

  @Column({ type: 'boolean', default: false })
  public isDeleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;

  public static convertToDto(requirements: RenterRequirements): RenterRequirementsDto {
    if (!requirements || !requirements.id) {
      return null;
    }

    return <RenterRequirementsDto>instanceToPlain(requirements, {
      excludeExtraneousValues: true,
    });
  }

  public static getDtoWithTypes(requirements: RenterRequirements): RenterRequirementsWithTypesDto {
    return {
      minimumCreditScore: {
        value: requirements.minimumCreditScore,
        type: 'int',
      },
      minimumIncome: {
        value: requirements.minimumIncome,
        type: 'int',
      },
      acceptsCosigners: {
        value: requirements.acceptsCosigners,
        type: 'boolean',
      },
    };
  }
}

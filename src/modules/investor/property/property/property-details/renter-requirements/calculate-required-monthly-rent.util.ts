import { IncomeEnum } from './income.enum';

export function calculateRequiredMonthlyRent(monthlyRent: number, requiredIncome: IncomeEnum): number {
  if (!monthlyRent) {
    return 0;
  }

  switch (requiredIncome) {
    case IncomeEnum.X0_5_RENT:
      return monthlyRent * 0.5;
    case IncomeEnum.X1_RENT:
      return monthlyRent * 1;
    case IncomeEnum.X2_RENT:
      return monthlyRent * 2;
    case IncomeEnum.X2_5_RENT:
      return monthlyRent * 2.5;
    case IncomeEnum.X3_RENT:
      return monthlyRent * 3;
    case IncomeEnum.X3_5_RENT:
      return monthlyRent * 3.5;
    default:
      return 0;
  }
}

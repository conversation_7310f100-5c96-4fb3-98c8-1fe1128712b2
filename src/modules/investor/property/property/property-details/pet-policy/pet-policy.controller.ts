import { Body, Controller, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { PetPolicyService } from './pet-policy.service';
import { PetPolicyDto } from './pet-policy.dto';
import { PetPolicy } from './pet-policy.entity';
import { IsPetPolicyOwnerGuard } from './is-pet-policy-owner-guard';

@ApiTags('pet-policy')
@Controller('property/:propertyId/pet-policy')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class PetPolicyController {
  constructor(private readonly petPolicyService: PetPolicyService) {}

  @Post()
  @ApiOkResponse({
    description: 'Create pet policy',
  })
  async create(@Param('propertyId') propertyId: string, @Body() body: PetPolicyDto): Promise<PetPolicyDto> {
    const petPolicy = await this.petPolicyService.create(propertyId, body);

    return PetPolicy.convertToDto(petPolicy);
  }

  @Patch(':petPolicyId')
  @ApiOkResponse({
    description: 'Update pet policy',
  })
  @ApiParam({
    name: 'petPolicyId',
    required: true,
  })
  @UseGuards(IsPetPolicyOwnerGuard)
  async update(
    @Param('propertyId') _propertyId: string,
    @Param('petPolicyId') petPolicyId: string,
    @Body() body: PetPolicyDto,
  ): Promise<PetPolicyDto> {
    const petPolicy = await this.petPolicyService.update(petPolicyId, body);

    return PetPolicy.convertToDto(petPolicy);
  }
}

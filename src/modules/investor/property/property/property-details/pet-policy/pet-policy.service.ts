import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PetPolicy } from './pet-policy.entity';
import { FieldValue } from '../../../../../ai/models/field-value';
import { PetPolicyEnum } from './pet-policy.enum';
import ParsingUtils from '../../../../../../utils/parsing.utils';
import { Property } from '../../entities/property.entity';
import { PetPolicyDto } from './pet-policy.dto';

@Injectable()
export class PetPolicyService {
  constructor(
    @InjectRepository(PetPolicy)
    private readonly petPolicyRepository: Repository<PetPolicy>,

    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async getUpdatedPetPolicy(petPolicy: PetPolicy, data: FieldValue[]): Promise<PetPolicy> {
    if (!petPolicy) {
      petPolicy = new PetPolicy();
    }

    return this.mapFieldValuesToEntity(petPolicy, data);
  }

  async create(propertyId: string, payload: PetPolicyDto): Promise<PetPolicy> {
    const result = await this.petPolicyRepository
      .createQueryBuilder()
      .insert()
      .values({ ...this.processPayload(payload) })
      .returning('*')
      .execute();

    const petPolicy = Object.assign(new PetPolicy(), { ...result.raw[0] });

    await this.propertyRepository.update(propertyId, {
      petPolicy: { id: petPolicy.id },
    });

    return petPolicy;
  }

  async update(petPolicyId: string, payload: PetPolicyDto): Promise<PetPolicy> {
    const result = await this.petPolicyRepository
      .createQueryBuilder()
      .update({ ...this.processPayload(payload) })
      .where({ id: petPolicyId })
      .returning('*')
      .execute();

    return Object.assign(new PetPolicy(), { ...result.raw[0] });
  }

  private processPayload(payload: PetPolicyDto): PetPolicyDto {
    const processedPayload = { ...payload };

    if (processedPayload.hasOwnProperty('allowsPets') && !processedPayload.allowsPets) {
      processedPayload.catsPolicy = PetPolicyEnum.NOT_ALLOWED;
      processedPayload.largeDogsPolicy = PetPolicyEnum.NOT_ALLOWED;
      processedPayload.smallDogsPolicy = PetPolicyEnum.NOT_ALLOWED;
      processedPayload.otherPetsPolicy = PetPolicyEnum.NOT_ALLOWED;
      processedPayload.petRent = null;
      processedPayload.petDeposit = null;
      processedPayload.maxPets = 0;
    }

    return processedPayload;
  }

  private mapFieldValuesToEntity(petPolicy: PetPolicy, data: FieldValue[]): PetPolicy {
    const petPolicyCopy = { ...petPolicy };

    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;

      try {
        switch (item.fieldName.split('.').reverse()[0]) {
          case 'allowsPets':
            petPolicyCopy.allowsPets = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'smallDogsPolicy':
            petPolicyCopy.smallDogsPolicy = item.value as PetPolicyEnum;
            break;
          case 'largeDogsPolicy':
            petPolicyCopy.largeDogsPolicy = item.value as PetPolicyEnum;
            break;
          case 'catsPolicy':
            petPolicyCopy.catsPolicy = item.value as PetPolicyEnum;
            break;
          case 'otherPetsPolicy':
            petPolicyCopy.otherPetsPolicy = item.value as PetPolicyEnum;
            break;
          default:
            break;
        }
      } catch (e) {
        throw new HttpException('Invalid field value: ' + item.value, 400);
      }
    }
    return petPolicyCopy;
  }
}

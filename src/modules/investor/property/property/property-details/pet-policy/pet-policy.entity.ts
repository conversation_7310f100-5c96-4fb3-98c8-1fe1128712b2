import { Expose, instanceToPlain } from 'class-transformer';
import { Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

import { Property } from '../../entities/property.entity';
import { PetPolicyWithTypesDto } from './pet-policy-with-types.dto';
import { PetPolicyEnum } from './pet-policy.enum';
import { PetPolicyDto } from './pet-policy.dto';

@Entity()
export class PetPolicy {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToOne(() => Property, (property) => property.petPolicy, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @Expose()
  @Column({ type: 'boolean', default: false })
  allowsPets: boolean;

  @Expose()
  @Column({ type: 'enum', enum: PetPolicyEnum, nullable: true })
  smallDogsPolicy: PetPolicyEnum;

  @Expose()
  @Column({ type: 'enum', enum: PetPolicyEnum, nullable: true })
  largeDogsPolicy: PetPolicyEnum;

  @Expose()
  @Column({ type: 'enum', enum: PetPolicyEnum, nullable: true })
  catsPolicy: PetPolicyEnum;

  @Expose()
  @Column({ type: 'enum', enum: PetPolicyEnum, nullable: true })
  otherPetsPolicy: PetPolicyEnum;

  @Expose()
  @Column({ type: 'int', nullable: true })
  public petRent: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  public petDeposit: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  public maxPets: number;

  @Column({ type: 'boolean', default: false })
  public isDeleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;

  public static convertToDto(petPolicy: PetPolicy, excludeNullValues = false): PetPolicyDto {
    if (!petPolicy || !petPolicy.id) {
      return null;
    }

    const result = <PetPolicyDto>instanceToPlain(petPolicy, {
      excludeExtraneousValues: true,
    });

    if (excludeNullValues) {
      Object.keys(result).forEach((key) => result[key] === null && delete result[key]);
    }

    return result;
  }

  public static getDtoWithTypes(petPolicy: PetPolicy): PetPolicyWithTypesDto {
    const result: PetPolicyWithTypesDto = {
      allowsPets: {
        value: petPolicy.allowsPets,
        type: 'boolean',
      },
    };

    if (!petPolicy.allowsPets) {
      return result;
    }

    return {
      ...result,
      smallDogsPolicy: {
        value: petPolicy.smallDogsPolicy,
        type: 'enum',
        enum: {
          type: 'PetPolicyEnum',
          values: Object.values(PetPolicyEnum),
        },
      },
      largeDogsPolicy: {
        value: petPolicy.largeDogsPolicy,
        type: 'enum',
        enum: {
          type: 'PetPolicyEnum',
          values: Object.values(PetPolicyEnum),
        },
      },
      catsPolicy: {
        value: petPolicy.catsPolicy,
        type: 'enum',
        enum: {
          type: 'PetPolicyEnum',
          values: Object.values(PetPolicyEnum),
        },
      },
      otherPetsPolicy: {
        value: petPolicy.otherPetsPolicy,
        type: 'enum',
        enum: {
          type: 'PetPolicyEnum',
          values: Object.values(PetPolicyEnum),
        },
      },
      petRent: {
        value: petPolicy.petRent,
        type: 'number',
      },
      petDeposit: {
        value: petPolicy.petDeposit,
        type: 'number',
      },
      maxPets: {
        value: petPolicy.maxPets,
        type: 'number',
      },
    };
  }
}

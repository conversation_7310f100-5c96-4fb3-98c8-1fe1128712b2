import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PetPolicy } from './pet-policy.entity';
import { Repository } from 'typeorm';

@Injectable()
export class IsPetPolicyOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(PetPolicy)
    private readonly petPolicyRepository: Repository<PetPolicy>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const petPolicyId = request.params.petPolicyId || request.body.petPolicyId;
    const propertyId = request.params.propertyId || request.body.propertyId;

    if (!petPolicyId) {
      throw new NotFoundException('No pet policy id provided');
    }

    if (!propertyId) {
      throw new NotFoundException('No property id provided');
    }

    const userOwnsPetPolicy = await this.petPolicyRepository
      .createQueryBuilder('petPolicy')
      .innerJoin('petPolicy.property', 'property')
      .where('petPolicy.id = :petPolicyId', { petPolicyId })
      .andWhere('property.id = :propertyId', { propertyId })
      .getCount()
      .then((count) => count > 0);

    if (!userOwnsPetPolicy) {
      throw new ForbiddenException('User does not own this pet policy');
    }

    return true;
  }
}

import { IsBoolean, IsEnum, IsN<PERSON>ber, IsOptional, IsString, Min } from 'class-validator';
import { PetPolicyEnum } from './pet-policy.enum';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PetPolicyDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  allowsPets: boolean;

  @IsEnum(PetPolicyEnum)
  @IsOptional()
  @ApiPropertyOptional({ enum: PetPolicyEnum, enumName: 'PetPolicyEnum' })
  smallDogsPolicy: PetPolicyEnum;

  @IsEnum(PetPolicyEnum)
  @IsOptional()
  @ApiPropertyOptional({ enum: PetPolicyEnum, enumName: 'PetPolicyEnum' })
  largeDogsPolicy: PetPolicyEnum;

  @IsEnum(PetPolicyEnum)
  @IsOptional()
  @ApiPropertyOptional({ enum: PetPolicyEnum, enumName: 'PetPolicyEnum' })
  catsPolicy: PetPolicyEnum;

  @IsEnum(PetPolicyEnum)
  @IsOptional()
  @ApiPropertyOptional({ enum: PetPolicyEnum, enumName: 'PetPolicyEnum' })
  otherPetsPolicy: PetPolicyEnum;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  petRent: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  petDeposit: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  maxPets: number;
}

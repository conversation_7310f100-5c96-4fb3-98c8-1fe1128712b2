import { PropertyTypeEnum } from '../../enums/property-type.enum';
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { HeatSourceEnum } from '../../enums/heat-source.enum';
import { HeatingSystemEnum } from '../../enums/heat-system.enum';
import { CoolingSystem } from '../../enums/cooling-system.enum';
import { RoofType } from '../../enums/roof-type.enum';
import { ViewType } from '../../enums/view-type.enum';
import { ExteriorType } from '../../enums/exterior-type.enum';
import { FloorCovering } from '../../enums/floor-covering.enum';
import { ArchitectureStyle } from '../../enums/architecture-style.enum';

export class PropertySpecificationsDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsEnum(PropertyTypeEnum)
  @IsOptional()
  @ApiPropertyOptional({ enum: PropertyTypeEnum, enumName: 'PropertyTypeEnum' })
  propertyType: PropertyTypeEnum;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  isFurnished: boolean;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  squareFeet: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  lotSize: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  fullBathrooms: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  halfBathrooms: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  bedrooms: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  yearBuilt: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  numberOfFloors: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  apartmentFloor: number;

  @IsString()
  @IsOptional()
  @ApiProperty()
  floorPlanLink: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  breakfastNook: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  diningRoom: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  familyRoom: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  laundryRoom: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  library: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  masterBath: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  mudRoom: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  office: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  pantry: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  recreationRoom: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  workshop: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  solariumAtrium: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  sunRoom: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  walkInCloset: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  totalRooms: number;

  @IsEnum(HeatSourceEnum)
  @IsOptional()
  @ApiPropertyOptional({ enum: HeatSourceEnum, enumName: 'HeatSourceEnum' })
  heatSource: HeatSourceEnum;

  @IsEnum(HeatingSystemEnum)
  @IsOptional()
  @ApiPropertyOptional({ enum: HeatingSystemEnum, enumName: 'HeatingSystemEnum' })
  heatingSystem: HeatingSystemEnum;

  @IsEnum(CoolingSystem)
  @IsOptional()
  @ApiPropertyOptional({ enum: CoolingSystem, enumName: 'CoolingSystem' })
  coolingSystem: CoolingSystem;

  @IsEnum(RoofType)
  @IsOptional()
  @ApiPropertyOptional({ enum: RoofType, enumName: 'RoofType' })
  roofType: RoofType;

  @IsEnum(ViewType)
  @IsOptional()
  @ApiPropertyOptional({ enum: ViewType, enumName: 'ViewType' })
  viewType: ViewType;

  @IsEnum(ExteriorType)
  @IsOptional()
  @ApiPropertyOptional({ enum: ExteriorType, enumName: 'ExteriorType' })
  exteriorType: ExteriorType;

  @IsEnum(FloorCovering)
  @IsOptional()
  @ApiPropertyOptional({ enum: FloorCovering, enumName: 'FloorCovering' })
  floorCovering: FloorCovering;

  @IsEnum(ArchitectureStyle)
  @IsOptional()
  @ApiPropertyOptional({ enum: ArchitectureStyle, enumName: 'ArchitectureStyle' })
  architectureStyle: ArchitectureStyle;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiPropertyOptional()
  hoaFee: number;
}

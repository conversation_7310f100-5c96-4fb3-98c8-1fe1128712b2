import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import ParsingUtils from '../../../../../../utils/parsing.utils';
import { FieldValue } from '../../../../../ai/models/field-value';
import { ArchitectureStyle } from '../../enums/architecture-style.enum';
import { CoolingSystem } from '../../enums/cooling-system.enum';
import { ExteriorType } from '../../enums/exterior-type.enum';
import { FloorCovering } from '../../enums/floor-covering.enum';
import { HeatSourceEnum } from '../../enums/heat-source.enum';
import { HeatingSystemEnum } from '../../enums/heat-system.enum';
import { RoofType } from '../../enums/roof-type.enum';
import { ViewType } from '../../enums/view-type.enum';
import { PropertySpecifications } from './property-specifications.entity';
import { Property } from '../../entities/property.entity';
import { PropertySpecificationsDto } from './property-specifications.dto';

@Injectable()
export class PropertySpecificationsService {
  constructor(
    @InjectRepository(PropertySpecifications)
    private propertySpecificationsRepository: Repository<PropertySpecifications>,

    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async create(propertyId: string, payload: PropertySpecificationsDto): Promise<PropertySpecifications> {
    const result = await this.propertySpecificationsRepository
      .createQueryBuilder()
      .insert()
      .values({ ...payload })
      .returning('*')
      .execute();

    const propertySpecifications = Object.assign(new PropertySpecifications(), { ...result.raw[0] });

    await this.propertyRepository.update(propertyId, {
      specifications: { id: propertySpecifications.id },
    });

    return propertySpecifications;
  }

  async update(specificationsId: string, payload: PropertySpecificationsDto): Promise<PropertySpecifications> {
    const result = await this.propertySpecificationsRepository
      .createQueryBuilder()
      .update({ ...payload })
      .where({ id: specificationsId })
      .returning('*')
      .execute();

    return Object.assign(new PropertySpecifications(), { ...result.raw[0] });
  }

  getUpdatedSpecifications(specifications: PropertySpecifications, data: FieldValue[]): PropertySpecifications {
    if (!specifications) {
      specifications = new PropertySpecifications();
    }

    return this.mapFieldValuesToEntity(specifications, data);
  }

  mapFieldValuesToEntity(specifications: PropertySpecifications, data: any): PropertySpecifications {
    const specificationsCopy = { ...specifications };

    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;
      try {
        switch (item.fieldName.split('.').reverse()[0]) {
          case 'type':
            specificationsCopy.propertyType = item.value;
            break;
          case 'squareFeet':
            specificationsCopy.squareFeet = item.value;
            break;
          case 'bedrooms':
            specificationsCopy.bedrooms = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'fullBathrooms':
            specificationsCopy.fullBathrooms = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'halfBathrooms':
            specificationsCopy.halfBathrooms = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'yearBuilt':
            specificationsCopy.yearBuilt = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'numberOfFloors':
            specificationsCopy.numberOfFloors = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'apartmentFloor':
            specificationsCopy.apartmentFloor = item.value;
            break;
          case 'breakfastNook':
            specificationsCopy.breakfastNook = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'diningRoom':
            specificationsCopy.diningRoom = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'familyRoom':
            specificationsCopy.familyRoom = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'laundryRoom':
            specificationsCopy.laundryRoom = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'library':
            specificationsCopy.library = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'masterBath':
            specificationsCopy.masterBath = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'mudRoom':
            specificationsCopy.mudRoom = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'office':
            specificationsCopy.office = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'pantry':
            specificationsCopy.pantry = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'recreationRoom':
            specificationsCopy.recreationRoom = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'workshop':
            specificationsCopy.workshop = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'solariumAtrium':
            specificationsCopy.solariumAtrium = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'sunRoom':
            specificationsCopy.sunRoom = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'walkInCloset':
            specificationsCopy.walkInCloset = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'totalRooms':
            specificationsCopy.totalRooms = ParsingUtils.parseStringToNumber(item.value);
            break;
          case 'heatSource':
            specificationsCopy.heatSource = item.value as HeatSourceEnum;
            break;
          case 'heatingSystem':
            specificationsCopy.heatingSystem = item.value as HeatingSystemEnum;
            break;
          case 'coolingSystem':
            specificationsCopy.coolingSystem = item.value as CoolingSystem;
            break;
          case 'roofType':
            specificationsCopy.roofType = item.value as RoofType;
            break;
          case 'viewType':
            specificationsCopy.viewType = item.value as ViewType;
            break;
          case 'exteriorType':
            specificationsCopy.exteriorType = item.value as ExteriorType;
            break;
          case 'floorCovering':
            specificationsCopy.floorCovering = item.value as FloorCovering;
            break;
          case 'architectureStyle':
            specificationsCopy.architectureStyle = item.value as ArchitectureStyle;
            break;
          default:
            break;
        }
      } catch (error) {
        throw new Error(error);
      }
    }

    return specificationsCopy;
  }
}

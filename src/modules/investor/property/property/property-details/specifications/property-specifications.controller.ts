import { Body, Controller, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { PropertySpecificationsService } from './property-specifications.service';
import { PropertySpecificationsDto } from './property-specifications.dto';
import { PropertySpecifications } from './property-specifications.entity';
import { IsPropertySpecificationsOwnerGuard } from './is-property-specifications-owner.guard';

@ApiTags('property-specifications')
@Controller('property/:propertyId/specifications')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class PropertySpecificationsController {
  constructor(private readonly propertySpecificationsService: PropertySpecificationsService) {}

  @Post()
  @ApiOkResponse({
    description: 'Create property specifications',
  })
  async create(
    @Param('propertyId') propertyId: string,
    @Body() body: PropertySpecificationsDto,
  ): Promise<PropertySpecificationsDto> {
    const propertySpecifications = await this.propertySpecificationsService.create(propertyId, body);

    return PropertySpecifications.convertToDto(propertySpecifications);
  }

  @Patch(':propertySpecificationsId')
  @ApiOkResponse({
    description: 'Update property specifications',
  })
  @ApiParam({
    name: 'propertySpecificationsId',
    required: true,
  })
  @UseGuards(IsPropertySpecificationsOwnerGuard)
  async update(
    @Param('propertyId') _propertyId: string,
    @Param('propertySpecificationsId') propertySpecificationsId: string,
    @Body() body: PropertySpecificationsDto,
  ): Promise<PropertySpecificationsDto> {
    const propertySpecifications = await this.propertySpecificationsService.update(propertySpecificationsId, body);

    return PropertySpecifications.convertToDto(propertySpecifications);
  }
}

import { FieldValueWithType } from '../../../../../ai/models/field-value-with-type';

export class PropertySpecificationsWithTypesDto {
  propertyType: FieldValueWithType;
  squareFeet: FieldValueWithType;
  fullBathrooms: FieldValueWithType;
  halfBathrooms: FieldValueWithType;
  bedrooms: FieldValueWithType;
  yearBuilt: FieldValueWithType;
  numberOfFloors: FieldValueWithType;
  apartmentFloor: FieldValueWithType;
  floorPlanLink: FieldValueWithType;
  breakfastNook: FieldValueWithType;
  diningRoom: FieldValueWithType;
  familyRoom: FieldValueWithType;
  laundryRoom: FieldValueWithType;
  library: FieldValueWithType;
  masterBath: FieldValueWithType;
  mudRoom: FieldValueWithType;
  office: FieldValueWithType;
  pantry: FieldValueWithType;
  recreationRoom: FieldValueWithType;
  workshop: FieldValueWithType;
  solariumAtrium: FieldValueWithType;
  sunRoom: FieldValueWithType;
  walkInCloset: FieldValueWithType;
  totalRooms: FieldValueWithType;
  heatingSystem: FieldValueWithType;
  heatSource: FieldValueWithType;
}

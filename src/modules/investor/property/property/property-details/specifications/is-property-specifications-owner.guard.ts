import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PropertySpecifications } from './property-specifications.entity';

@Injectable()
export class IsPropertySpecificationsOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(PropertySpecifications)
    private readonly propertySpecificationsRepository: Repository<PropertySpecifications>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const propertySpecificationsId = request.params.propertySpecificationsId || request.body.propertySpecificationsId;
    const propertyId = request.params.propertyId || request.body.propertyId;

    if (!propertySpecificationsId) {
      throw new NotFoundException('No property specifications id provided');
    }

    if (!propertyId) {
      throw new NotFoundException('No property id provided');
    }

    const userOwnsPropertySpecifications = await this.propertySpecificationsRepository
      .createQueryBuilder('propertySpecifications')
      .innerJoin('propertySpecifications.property', 'property')
      .where('propertySpecifications.id = :propertySpecificationsId', { propertySpecificationsId })
      .andWhere('property.id = :propertyId', { propertyId })
      .getCount()
      .then((count) => count > 0);

    if (!userOwnsPropertySpecifications) {
      throw new ForbiddenException('User does not own this property specifications');
    }

    return true;
  }
}

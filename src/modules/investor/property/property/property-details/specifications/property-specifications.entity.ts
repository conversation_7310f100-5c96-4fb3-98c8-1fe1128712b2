import { <PERSON>umn, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { PropertyTypeEnum } from '../../enums/property-type.enum';
import { PropertySpecificationsDto } from './property-specifications.dto';
import { PropertySpecificationsWithTypesDto } from './property-specifications-with-types.dto';
import { HeatSourceEnum } from '../../enums/heat-source.enum';
import { HeatingSystemEnum } from '../../enums/heat-system.enum';
import { CoolingSystem } from '../../enums/cooling-system.enum';
import { RoofType } from '../../enums/roof-type.enum';
import { ViewType } from '../../enums/view-type.enum';
import { ExteriorType } from '../../enums/exterior-type.enum';
import { FloorCovering } from '../../enums/floor-covering.enum';
import { ArchitectureStyle } from '../../enums/architecture-style.enum';
import { Property } from '../../entities/property.entity';

@Entity()
export class PropertySpecifications {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToOne(() => Property, (property) => property.specifications, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @Expose()
  @Column({ type: 'enum', enum: PropertyTypeEnum, nullable: true })
  propertyType: PropertyTypeEnum;

  @Expose()
  @Column({ type: 'int', nullable: true })
  squareFeet: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  lotSize: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  bedrooms: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  fullBathrooms: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  halfBathrooms: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  yearBuilt: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  numberOfFloors: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  apartmentFloor: number;

  @Expose()
  @Column({ type: 'varchar', length: 2083, nullable: true })
  floorPlanLink: string;

  @Expose()
  @Column({ type: 'int', nullable: true })
  breakfastNook: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  diningRoom: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  familyRoom: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  laundryRoom: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  library: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  masterBath: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  mudRoom: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  office: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  pantry: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  recreationRoom: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  workshop: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  solariumAtrium: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  sunRoom: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  walkInCloset: number;

  @Expose()
  @Column({ type: 'int', nullable: true })
  totalRooms: number;

  @Expose()
  @Column({ type: 'varchar', enum: HeatSourceEnum, nullable: true })
  heatSource: HeatSourceEnum;

  @Expose()
  @Column({ type: 'enum', enum: HeatingSystemEnum, nullable: true })
  heatingSystem: HeatingSystemEnum;

  @Expose()
  @Column({ type: 'enum', enum: CoolingSystem, nullable: true })
  coolingSystem: CoolingSystem;

  @Expose()
  @Column({ type: 'enum', enum: RoofType, nullable: true })
  roofType: RoofType;

  @Expose()
  @Column({ type: 'enum', enum: ViewType, nullable: true })
  viewType: ViewType;

  @Expose()
  @Column({ type: 'enum', enum: ExteriorType, nullable: true })
  exteriorType: ExteriorType;

  @Expose()
  @Column({ type: 'enum', enum: FloorCovering, nullable: true })
  floorCovering: FloorCovering;

  @Expose()
  @Column({ type: 'enum', enum: ArchitectureStyle, nullable: true })
  architectureStyle: ArchitectureStyle;

  @Expose()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  hoaFee: number;

  @Column({ type: 'boolean', default: false })
  public isDeleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;

  public static convertToDto(specs: PropertySpecifications, excludeNullValues = false): PropertySpecificationsDto {
    if (!specs || !specs.id) {
      return null;
    }

    const result = <PropertySpecificationsDto>instanceToPlain(specs, {
      excludeExtraneousValues: true,
    });

    if (excludeNullValues) {
      Object.keys(result).forEach((key) => result[key] === null && delete result[key]);
    }

    return result;
  }

  public static getDtoWithTypes(specs: PropertySpecifications): PropertySpecificationsWithTypesDto {
    return {
      propertyType: {
        value: specs.propertyType,
        type: 'enum',
        enum: {
          type: 'PropertyTypeEnum',
          values: Object.values(PropertyTypeEnum),
        },
      },
      squareFeet: {
        value: specs.squareFeet,
        type: 'number',
      },
      fullBathrooms: {
        value: specs.fullBathrooms,
        type: 'number',
      },
      halfBathrooms: {
        value: specs.halfBathrooms,
        type: 'number',
      },
      bedrooms: {
        value: specs.bedrooms,
        type: 'number',
      },
      yearBuilt: {
        value: specs.yearBuilt,
        type: 'number',
      },
      numberOfFloors: {
        value: specs.numberOfFloors,
        type: 'number',
      },
      apartmentFloor: {
        value: specs.apartmentFloor,
        type: 'number',
      },
      floorPlanLink: {
        value: specs.floorPlanLink,
        type: 'string',
      },
      breakfastNook: {
        value: specs.breakfastNook,
        type: 'number',
      },
      diningRoom: {
        value: specs.diningRoom,
        type: 'number',
      },
      familyRoom: {
        value: specs.familyRoom,
        type: 'number',
      },
      laundryRoom: {
        value: specs.laundryRoom,
        type: 'number',
      },
      library: {
        value: specs.library,
        type: 'number',
      },
      masterBath: {
        value: specs.masterBath,
        type: 'number',
      },
      mudRoom: {
        value: specs.mudRoom,
        type: 'number',
      },
      office: {
        value: specs.office,
        type: 'number',
      },
      pantry: {
        value: specs.pantry,
        type: 'number',
      },
      recreationRoom: {
        value: specs.recreationRoom,
        type: 'number',
      },
      workshop: {
        value: specs.workshop,
        type: 'number',
      },
      solariumAtrium: {
        value: specs.solariumAtrium,
        type: 'number',
      },
      sunRoom: {
        value: specs.sunRoom,
        type: 'number',
      },
      walkInCloset: {
        value: specs.walkInCloset,
        type: 'number',
      },
      totalRooms: {
        value: specs.totalRooms,
        type: 'number',
      },
      heatSource: {
        value: specs.heatSource,
        type: 'enum',
        enum: {
          type: 'HeatSourceEnum',
          values: Object.values(HeatSourceEnum),
        },
      },
      heatingSystem: {
        value: specs.heatingSystem,
        type: 'enum',
        enum: {
          type: 'HeatingSystemEnum',
          values: Object.values(HeatingSystemEnum),
        },
      },
    };
  }
}

import { Body, Controller, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { IsPropertyOwnerGuard } from '../../../../../../guards/investor/is-property-owner-guard';
import { HasRoles } from '../../../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../../../shared/auth/guards/roles.guard';
import { Role } from '../../../../../shared/auth/models/roles-enum';
import { AmenitiesService } from './amenities.service';
import { AmenitiesDto } from './amenities.dto';
import { Amenities } from './amenities.entity';
import { IsAmenitiesOwnerGuards } from './is-amenities-owner.guards';

@ApiTags('amenities')
@Controller('property/:propertyId/amenities')
@ApiParam({
  name: 'propertyId',
  required: true,
})
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@UseGuards(IsPropertyOwnerGuard)
export class AmenitiesController {
  constructor(private readonly amenitiesService: AmenitiesService) {}

  @Post()
  @ApiOkResponse({
    description: 'Create amenities',
  })
  async create(@Param('propertyId') propertyId: string, @Body() body: AmenitiesDto): Promise<AmenitiesDto> {
    const amenities = await this.amenitiesService.create(propertyId, body);

    return Amenities.convertToDto(amenities);
  }

  @Patch(':amenitiesId')
  @ApiOkResponse({
    description: 'Update amenities',
  })
  @ApiParam({
    name: 'amenitiesId',
    required: true,
  })
  @UseGuards(IsAmenitiesOwnerGuards)
  async update(
    @Param('propertyId') _propertyId: string,
    @Param('amenitiesId') amenitiesId: string,
    @Body() body: AmenitiesDto,
  ): Promise<AmenitiesDto> {
    const amenities = await this.amenitiesService.update(amenitiesId, body);

    return Amenities.convertToDto(amenities);
  }
}

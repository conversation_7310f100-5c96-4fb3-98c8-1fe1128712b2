import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AmenitiesDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  isFurnished: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  canBeFurnished: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  canBeUnfurnished: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasBarbecueArea: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasBasement: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasBasketBallCourt: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasBusinessCenter: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasCableSatellite: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasChildCare: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasClubDiscount: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasConcierge: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasControlledAccess: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasCourtyard: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  isCourtyardShared: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  isCourtyardFenced: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasDeck: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasDisabledAccess: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasDock: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasDoorman: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasElevator: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasFencedYard: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasFitnessCenter: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasGarden: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasGatedEntry: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasGreenHouse: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasHotTubSpa: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasHouseKeeping: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasIntercom: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasJettedBathTub: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasLawn: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasNightPatrol: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasOnSiteMaintenance: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasOnSiteManagement: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasPackageReceiving: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasPlayGround: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasPong: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasPorch: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasRaquetBallCourt: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasSauna: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasSecuritySystem: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasSkylight: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasSportsCourt: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasSprinklerSystem: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasSunDeck: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasTennisCourt: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasTVLounge: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasVolleyBallCourt: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasWetBar: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasDishwasher: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasWasher: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasDryer: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasFreezer: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasGarbageDisposal: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasMicrowave: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasRangeOven: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasRefrigerator: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasTrashCompactor: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasCeilingFan: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasDoublePaneWindows: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasHandrails: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasLargeClosets: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasMotherInLawUnit: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasPatio: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasStorageSpace: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasVaultedCeiling: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasWindowCoverings: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasBalcony: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasFireplace: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasPool: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasDishWasher: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasWasherDryer: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasAirConditioning: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  hasHeating: boolean;
}

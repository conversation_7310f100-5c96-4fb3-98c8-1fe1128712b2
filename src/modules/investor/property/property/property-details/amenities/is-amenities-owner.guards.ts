import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Amenities } from './amenities.entity';

@Injectable()
export class IsAmenitiesOwnerGuards implements CanActivate {
  constructor(
    @InjectRepository(Amenities)
    private readonly amenitiesRepository: Repository<Amenities>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const amenitiesId = request.params.amenitiesId || request.body.amenitiesId;
    const propertyId = request.params.propertyId || request.body.propertyId;

    if (!amenitiesId) {
      throw new NotFoundException('No amenities id provided');
    }

    if (!propertyId) {
      throw new NotFoundException('No property id provided');
    }

    const userOwnsLeaseConditions = await this.amenitiesRepository
      .createQueryBuilder('amenities')
      .innerJoin('amenities.property', 'property')
      .where('amenities.id = :amenitiesId', { amenitiesId })
      .andWhere('property.id = :propertyId', { propertyId })
      .getCount()
      .then((count) => count > 0);

    if (!userOwnsLeaseConditions) {
      throw new ForbiddenException('User does not own this lease conditions');
    }

    return true;
  }
}

import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Expose, instanceToPlain } from 'class-transformer';
import { AmenitiesDto } from './amenities.dto';
import { AmenitiesWithTypesDto } from './amenities-with-types.dto';
import { Property } from '../../entities/property.entity';

@Entity()
export class Amenities {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Property, (property) => property.amenities, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  isFurnished: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  canBeFurnished: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  canBeUnfurnished: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasBarbecueArea: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasBasement: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasBasketBallCourt: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasBusinessCenter: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasCableSatellite: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasChildCare: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasClubDiscount: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasConcierge: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasControlledAccess: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasCourtyard: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  isCourtyardShared: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  isCourtyardFenced: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasDeck: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasDisabledAccess: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasDock: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasDoorman: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasElevator: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasFencedYard: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasFitnessCenter: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasGarden: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasGatedEntry: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasGreenHouse: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasHotTubSpa: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasHouseKeeping: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasIntercom: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasJettedBathTub: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasLawn: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasNightPatrol: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasOnSiteMaintenance: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasOnSiteManagement: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasPackageReceiving: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasPlayGround: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasPong: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasPorch: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasRaquetBallCourt: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasSauna: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasSecuritySystem: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasSkylight: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasSportsCourt: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasSprinklerSystem: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasSunDeck: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasTennisCourt: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasTVLounge: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasVolleyBallCourt: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasWetBar: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasDishwasher: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasWasher: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasDryer: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasFreezer: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasGarbageDisposal: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasMicrowave: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasRangeOven: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasRefrigerator: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasTrashCompactor: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasCeilingFan: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasDoublePaneWindows: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasHandrails: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasLargeClosets: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasMotherInLawUnit: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasPatio: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasStorageSpace: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasVaultedCeiling: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasWindowCoverings: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasBalcony: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasFireplace: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasPool: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasDishWasher: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasWasherDryer: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasAirConditioning: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasHeating: boolean;

  @DeleteDateColumn()
  deletedAt?: Date;

  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;

  public static convertToDto(amenities: Amenities, excludeNullValues = false): AmenitiesDto {
    if (!amenities || !amenities.id) {
      return null;
    }

    const result = <AmenitiesDto>instanceToPlain(amenities, {
      excludeExtraneousValues: true,
    });

    if (excludeNullValues) {
      Object.keys(result).forEach((key) => result[key] === null && delete result[key]);
    }

    return result;
  }

  public static getDtoWithTypes(amenities: Amenities): AmenitiesWithTypesDto {
    return {
      hasBalcony: {
        value: amenities.hasBalcony,
        type: 'boolean',
      },
      hasBarbecueArea: {
        value: amenities.hasBarbecueArea,
        type: 'boolean',
      },
      hasBasement: {
        value: amenities.hasBasement,
        type: 'boolean',
      },
      hasBasketBallCourt: {
        value: amenities.hasBasketBallCourt,
        type: 'boolean',
      },
      hasBusinessCenter: {
        value: amenities.hasBusinessCenter,
        type: 'boolean',
      },
      hasCableSatellite: {
        value: amenities.hasCableSatellite,
        type: 'boolean',
      },
      hasChildCare: {
        value: amenities.hasChildCare,
        type: 'boolean',
      },
      hasClubDiscount: {
        value: amenities.hasClubDiscount,
        type: 'boolean',
      },
      hasConcierge: {
        value: amenities.hasConcierge,
        type: 'boolean',
      },
      hasControlledAccess: {
        value: amenities.hasControlledAccess,
        type: 'boolean',
      },
      hasCourtyard: {
        value: amenities.hasCourtyard,
        type: 'boolean',
      },
      isCourtyardShared: {
        value: amenities.isCourtyardShared,
        type: 'boolean',
      },
      isCourtyardFenced: {
        value: amenities.isCourtyardFenced,
        type: 'boolean',
      },
      hasDeck: {
        value: amenities.hasDeck,
        type: 'boolean',
      },
      hasDisabledAccess: {
        value: amenities.hasDisabledAccess,
        type: 'boolean',
      },
      hasDock: {
        value: amenities.hasDock,
        type: 'boolean',
      },
      hasDoorman: {
        value: amenities.hasDoorman,
        type: 'boolean',
      },
      hasElevator: {
        value: amenities.hasElevator,
        type: 'boolean',
      },
      hasFencedYard: {
        value: amenities.hasFencedYard,
        type: 'boolean',
      },
      hasFitnessCenter: {
        value: amenities.hasFitnessCenter,
        type: 'boolean',
      },
      hasGarden: {
        value: amenities.hasGarden,
        type: 'boolean',
      },
      hasGatedEntry: {
        value: amenities.hasGatedEntry,
        type: 'boolean',
      },
      hasGreenHouse: {
        value: amenities.hasGreenHouse,
        type: 'boolean',
      },
      hasHotTubSpa: {
        value: amenities.hasHotTubSpa,
        type: 'boolean',
      },
      hasHouseKeeping: {
        value: amenities.hasHouseKeeping,
        type: 'boolean',
      },
      hasIntercom: {
        value: amenities.hasIntercom,
        type: 'boolean',
      },
      hasJettedBathTub: {
        value: amenities.hasJettedBathTub,
        type: 'boolean',
      },
      hasLawn: {
        value: amenities.hasLawn,
        type: 'boolean',
      },
      hasNightPatrol: {
        value: amenities.hasNightPatrol,
        type: 'boolean',
      },
      hasOnSiteMaintenance: {
        value: amenities.hasOnSiteMaintenance,
        type: 'boolean',
      },
      hasOnSiteManagement: {
        value: amenities.hasOnSiteManagement,
        type: 'boolean',
      },
      hasPackageReceiving: {
        value: amenities.hasPackageReceiving,
        type: 'boolean',
      },
      hasPlayGround: {
        value: amenities.hasPlayGround,
        type: 'boolean',
      },
      hasPong: {
        value: amenities.hasPong,
        type: 'boolean',
      },
      hasPorch: {
        value: amenities.hasPorch,
        type: 'boolean',
      },
      hasRaquetBallCourt: {
        value: amenities.hasRaquetBallCourt,
        type: 'boolean',
      },
      hasSauna: {
        value: amenities.hasSauna,
        type: 'boolean',
      },
      hasSecuritySystem: {
        value: amenities.hasSecuritySystem,
        type: 'boolean',
      },
      hasSkylight: {
        value: amenities.hasSkylight,
        type: 'boolean',
      },
      hasSportsCourt: {
        value: amenities.hasSportsCourt,
        type: 'boolean',
      },
      hasSprinklerSystem: {
        value: amenities.hasSprinklerSystem,
        type: 'boolean',
      },
      hasSunDeck: {
        value: amenities.hasSunDeck,
        type: 'boolean',
      },
      hasTennisCourt: {
        value: amenities.hasTennisCourt,
        type: 'boolean',
      },
      hasTVLounge: {
        value: amenities.hasTVLounge,
        type: 'boolean',
      },
      hasVolleyBallCourt: {
        value: amenities.hasVolleyBallCourt,
        type: 'boolean',
      },
      hasWetBar: {
        value: amenities.hasWetBar,
        type: 'boolean',
      },
      hasDishwasher: {
        value: amenities.hasDishwasher,
        type: 'boolean',
      },
      hasWasher: {
        value: amenities.hasWasher,
        type: 'boolean',
      },
      hasDryer: {
        value: amenities.hasDryer,
        type: 'boolean',
      },
      hasFreezer: {
        value: amenities.hasFreezer,
        type: 'boolean',
      },
      hasGarbageDisposal: {
        value: amenities.hasGarbageDisposal,
        type: 'boolean',
      },
      hasMicrowave: {
        value: amenities.hasMicrowave,
        type: 'boolean',
      },
      hasRangeOven: {
        value: amenities.hasRangeOven,
        type: 'boolean',
      },
      hasRefrigerator: {
        value: amenities.hasRefrigerator,
        type: 'boolean',
      },
      hasTrashCompactor: {
        value: amenities.hasTrashCompactor,
        type: 'boolean',
      },
      hasCeilingFan: {
        value: amenities.hasCeilingFan,
        type: 'boolean',
      },
      hasDoublePaneWindows: {
        value: amenities.hasDoublePaneWindows,
        type: 'boolean',
      },
      hasHandrails: {
        value: amenities.hasHandrails,
        type: 'boolean',
      },
      hasLargeClosets: {
        value: amenities.hasLargeClosets,
        type: 'boolean',
      },
      hasMotherInLawUnit: {
        value: amenities.hasMotherInLawUnit,
        type: 'boolean',
      },
      hasPatio: {
        value: amenities.hasPatio,
        type: 'boolean',
      },
      hasStorageSpace: {
        value: amenities.hasStorageSpace,
        type: 'boolean',
      },
      hasVaultedCeiling: {
        value: amenities.hasVaultedCeiling,
        type: 'boolean',
      },
      hasWindowCoverings: {
        value: amenities.hasWindowCoverings,
        type: 'boolean',
      },
      hasFireplace: {
        value: amenities.hasFireplace,
        type: 'boolean',
      },
      hasPool: {
        value: amenities.hasPool,
        type: 'boolean',
      },
      hasDishWasher: {
        value: amenities.hasDishWasher,
        type: 'boolean',
      },
      hasWasherDryer: {
        value: amenities.hasWasherDryer,
        type: 'boolean',
      },
      hasAirConditioning: {
        value: amenities.hasAirConditioning,
        type: 'boolean',
      },
      hasHeating: {
        value: amenities.hasHeating,
        type: 'boolean',
      },
      isFurnished: {
        value: amenities.isFurnished,
        type: 'boolean',
      },
      canBeFurnished: {
        value: amenities.canBeFurnished,
        type: 'boolean',
      },
      canBeUnfurnished: {
        value: amenities.canBeUnfurnished,
        type: 'boolean',
      },
    };
  }
}

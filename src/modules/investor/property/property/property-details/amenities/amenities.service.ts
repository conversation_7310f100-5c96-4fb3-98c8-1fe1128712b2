import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import ParsingUtils from '../../../../../../utils/parsing.utils';
import { FieldValue } from '../../../../../ai/models/field-value';
import { Amenities } from './amenities.entity';
import { Property } from '../../entities/property.entity';
import { AmenitiesDto } from './amenities.dto';

@Injectable()
export class AmenitiesService {
  constructor(
    @InjectRepository(Amenities)
    private readonly amenitiesRepository: Repository<Amenities>,

    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async create(propertyId: string, payload: AmenitiesDto): Promise<Amenities> {
    const result = await this.amenitiesRepository
      .createQueryBuilder()
      .insert()
      .values({ ...payload })
      .returning('*')
      .execute();

    const amenities = Object.assign(new Amenities(), { ...result.raw[0] });

    await this.propertyRepository.update(propertyId, {
      amenities: { id: amenities.id },
    });

    return amenities;
  }

  async update(amenitiesId: string, payload: AmenitiesDto): Promise<Amenities> {
    const result = await this.amenitiesRepository
      .createQueryBuilder()
      .update({ ...payload })
      .where({ id: amenitiesId })
      .returning('*')
      .execute();

    return Object.assign(new Amenities(), { ...result.raw[0] });
  }

  async getUpdatedAmenities(amenities: Amenities, data: FieldValue[]): Promise<Amenities> {
    if (!amenities) {
      amenities = new Amenities();
    }

    return this.mapFieldValuesToEntity(amenities, data);
  }

  mapFieldValuesToEntity(amenities: Amenities, data: FieldValue[]): Amenities {
    const amenitiesCopy = { ...amenities };

    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;
      try {
        switch (item.fieldName.split('.').reverse()[0]) {
          case 'hasBalcony':
            amenitiesCopy.hasBalcony = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasFireplace':
            amenitiesCopy.hasFireplace = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasPool':
            amenitiesCopy.hasPool = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasWasherDryer':
            amenitiesCopy.hasWasherDryer = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasAirConditioning':
            amenitiesCopy.hasAirConditioning = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasHeating':
            amenitiesCopy.hasHeating = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'isFurnished':
            amenitiesCopy.isFurnished = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasBarbecueArea':
            amenitiesCopy.hasBarbecueArea = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasBasement':
            amenitiesCopy.hasBasement = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasBasketBallCourt':
            amenitiesCopy.hasBasketBallCourt = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasBusinessCenter':
            amenitiesCopy.hasBusinessCenter = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasCableSatellite':
            amenitiesCopy.hasCableSatellite = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasChildCare':
            amenitiesCopy.hasChildCare = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasClubDiscount':
            amenitiesCopy.hasClubDiscount = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasConcierge':
            amenitiesCopy.hasConcierge = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasControlledAccess':
            amenitiesCopy.hasControlledAccess = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasCourtyard':
            amenitiesCopy.hasCourtyard = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasDeck':
            amenitiesCopy.hasDeck = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasDisabledAccess':
            amenitiesCopy.hasDisabledAccess = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasDock':
            amenitiesCopy.hasDock = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasDoorman':
            amenitiesCopy.hasDoorman = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasElevator':
            amenitiesCopy.hasElevator = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasFencedYard':
            amenitiesCopy.hasFencedYard = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasFitnessCenter':
            amenitiesCopy.hasFitnessCenter = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasGarden':
            amenitiesCopy.hasGarden = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasGatedEntry':
            amenitiesCopy.hasGatedEntry = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasGreenHouse':
            amenitiesCopy.hasGreenHouse = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasHotTubSpa':
            amenitiesCopy.hasHotTubSpa = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasHouseKeeping':
            amenitiesCopy.hasHouseKeeping = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasIntercom':
            amenitiesCopy.hasIntercom = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasJettedBathTub':
            amenitiesCopy.hasJettedBathTub = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasLawn':
            amenitiesCopy.hasLawn = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasNightPatrol':
            amenitiesCopy.hasNightPatrol = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasOnSiteMaintenance':
            amenitiesCopy.hasOnSiteMaintenance = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasOnSiteManagement':
            amenitiesCopy.hasOnSiteManagement = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasPackageReceiving':
            amenitiesCopy.hasPackageReceiving = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasPlayGround':
            amenitiesCopy.hasPlayGround = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasPong':
            amenitiesCopy.hasPong = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasPorch':
            amenitiesCopy.hasPorch = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasRaquetBallCourt':
            amenitiesCopy.hasRaquetBallCourt = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasSauna':
            amenitiesCopy.hasSauna = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasSecuritySystem':
            amenitiesCopy.hasSecuritySystem = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasSkylight':
            amenitiesCopy.hasSkylight = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasSportsCourt':
            amenitiesCopy.hasSportsCourt = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasSprinklerSystem':
            amenitiesCopy.hasSprinklerSystem = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasSunDeck':
            amenitiesCopy.hasSunDeck = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasTennisCourt':
            amenitiesCopy.hasTennisCourt = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasTVLounge':
            amenitiesCopy.hasTVLounge = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasVolleyBallCourt':
            amenitiesCopy.hasVolleyBallCourt = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasWetBar':
            amenitiesCopy.hasWetBar = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasDishwasher':
            amenitiesCopy.hasDishwasher = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasWasher':
            amenitiesCopy.hasWasher = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasDryer':
            amenitiesCopy.hasDryer = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasFreezer':
            amenitiesCopy.hasFreezer = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasGarbageDisposal':
            amenitiesCopy.hasGarbageDisposal = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasMicrowave':
            amenitiesCopy.hasMicrowave = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasRangeOven':
            amenitiesCopy.hasRangeOven = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasRefrigerator':
            amenitiesCopy.hasRefrigerator = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasTrashCompactor':
            amenitiesCopy.hasTrashCompactor = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasCeilingFan':
            amenitiesCopy.hasCeilingFan = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasDoublePaneWindows':
            amenitiesCopy.hasDoublePaneWindows = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasHandrails':
            amenitiesCopy.hasHandrails = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasLargeClosets':
            amenitiesCopy.hasLargeClosets = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasMotherInLawUnit':
            amenitiesCopy.hasMotherInLawUnit = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasPatio':
            amenitiesCopy.hasPatio = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasStorageSpace':
            amenitiesCopy.hasStorageSpace = ParsingUtils.parseStringToBoolean(item.value);
            break;
          case 'hasVaultedCeiling':
            amenitiesCopy.hasVaultedCeiling = ParsingUtils.parseStringToBoolean(item.value);
            break;
          default:
            break;
        }
      } catch (error) {
        throw new HttpException('Invalid field name', 400);
      }
    }
    return amenitiesCopy;
  }
}

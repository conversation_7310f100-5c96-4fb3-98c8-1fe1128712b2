import { FieldValueWithType } from '../../../../../ai/models/field-value-with-type';

export class AmenitiesWithTypesDto {
  hasBalcony: FieldValueWithType;
  isFurnished: FieldValueWithType;
  canBeFurnished: FieldValueWithType;
  canBeUnfurnished: FieldValueWithType;
  hasBarbecueArea: FieldValueWithType;
  hasBasement: FieldValueWithType;
  hasBasketBallCourt: FieldValueWithType;
  hasBusinessCenter: FieldValueWithType;
  hasCableSatellite: FieldValueWithType;
  hasChildCare: FieldValueWithType;
  hasClubDiscount: FieldValueWithType;
  hasConcierge: FieldValueWithType;
  hasControlledAccess: FieldValueWithType;
  hasCourtyard: FieldValueWithType;
  isCourtyardShared: FieldValueWithType;
  isCourtyardFenced: FieldValueWithType;
  hasDeck: FieldValueWithType;
  hasDisabledAccess: FieldValueWithType;
  hasDock: FieldValueWithType;
  hasDoorman: FieldValueWithType;
  hasElevator: FieldValueWithType;
  hasFencedYard: FieldValueWithType;
  hasFitnessCenter: FieldValueWithType;
  hasGarden: FieldValueWithType;
  hasGatedEntry: FieldValueWithType;
  hasGreenHouse: FieldValueWithType;
  hasHotTubSpa: FieldValueWithType;
  hasHouseKeeping: FieldValueWithType;
  hasIntercom: FieldValueWithType;
  hasJettedBathTub: FieldValueWithType;
  hasLawn: FieldValueWithType;
  hasNightPatrol: FieldValueWithType;
  hasOnSiteMaintenance: FieldValueWithType;
  hasOnSiteManagement: FieldValueWithType;
  hasPackageReceiving: FieldValueWithType;
  hasPlayGround: FieldValueWithType;
  hasPong: FieldValueWithType;
  hasPorch: FieldValueWithType;
  hasRaquetBallCourt: FieldValueWithType;
  hasSauna: FieldValueWithType;
  hasSecuritySystem: FieldValueWithType;
  hasSkylight: FieldValueWithType;
  hasSportsCourt: FieldValueWithType;
  hasSprinklerSystem: FieldValueWithType;
  hasSunDeck: FieldValueWithType;
  hasTennisCourt: FieldValueWithType;
  hasTVLounge: FieldValueWithType;
  hasVolleyBallCourt: FieldValueWithType;
  hasWetBar: FieldValueWithType;
  hasDishwasher: FieldValueWithType;
  hasWasher: FieldValueWithType;
  hasDryer: FieldValueWithType;
  hasFreezer: FieldValueWithType;
  hasGarbageDisposal: FieldValueWithType;
  hasMicrowave: FieldValueWithType;
  hasRangeOven: FieldValueWithType;
  hasRefrigerator: FieldValueWithType;
  hasTrashCompactor: FieldValueWithType;
  hasCeilingFan: FieldValueWithType;
  hasDoublePaneWindows: FieldValueWithType;
  hasHandrails: FieldValueWithType;
  hasLargeClosets: FieldValueWithType;
  hasMotherInLawUnit: FieldValueWithType;
  hasPatio: FieldValueWithType;
  hasStorageSpace: FieldValueWithType;
  hasVaultedCeiling: FieldValueWithType;
  hasWindowCoverings: FieldValueWithType;
  hasFireplace: FieldValueWithType;
  hasPool: FieldValueWithType;
  hasDishWasher: FieldValueWithType;
  hasWasherDryer: FieldValueWithType;
  hasAirConditioning: FieldValueWithType;
  hasHeating: FieldValueWithType;
}

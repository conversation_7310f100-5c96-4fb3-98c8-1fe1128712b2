import { BadRequestException, forwardRef, HttpException, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Raw, Repository } from 'typeorm';
import { FollowUpService } from '../../../shared/communication/follow-up/follow-up.service';
import { CompanyService } from '../../../shared/company/company.service';
import { InvestorService } from '../../investor/investor.service';
import { IntelligentEscalationStatus } from '../../property-question/enum/intelligent-escalation.status';
import { ShowingRequestStatus } from '../../showing-request/enums/showing-request-status.enum';
import { ShowingService } from '../../showing/showing.service';
import { GeocodingService } from '../location-discovery/geocoding/geocoding.service';
import { Property } from './entities/property.entity';
import { PropertyStatus } from './enums/property-status.enum';
import { PropertyManagementType } from './enums/property-management-type.enum';
import { PropertyDto } from './model/property.dto';
import { ShowingReqsAndQuestionsCountDto } from './model/showing-reqs-and-questions-count.dto';
import { FieldValue } from '../../../ai/models/field-value';
import { AccessibilityService } from './property-details/accessibility/accessibility.service';
import { AmenitiesService } from './property-details/amenities/amenities.service';
import { IncludedUtilitiesService } from './property-details/included-utilities/included-utilities.service';
import { LeaseConditionsService } from './property-details/lease-conditions/lease-conditions.service';
import { PropertyLocationService } from './property-details/location/property-location.service';
import { ParkingService } from './property-details/parking/parking.service';
import { PetPolicyService } from './property-details/pet-policy/pet-policy.service';
import { RenterRequirementsService } from './property-details/renter-requirements/renter-requirements.service';
import { PropertySpecificationsService } from './property-details/specifications/property-specifications.service';
import { PortfolioDto } from './model/portfolio.dto';
import { AddressLookupService } from '../location-discovery/address-lookup/address-lookup.service';
import { SlackCommunicationService } from '../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { TransUnionService } from '../../../shared/background-check/trans-union/trans-union.service';

import { ShowingAgent } from '../../showing-agent/entities/showing-agent.entity';
import { PropertyAvailabilityService } from '../availability/property-availability.service';
import { PropertyListingService } from '../../property-listing/property-listing.service';
import { AvailabilitySlotsService } from '../../availability/availability-slot.service';
import { DayOfTheWeekEnum } from '../../../../shared/enums/day-of-the-week.enum';

@Injectable()
export class PropertyService {
  constructor(
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    private readonly accessibilityService: AccessibilityService,
    private readonly amenitiesService: AmenitiesService,
    private readonly includedUtilitiesService: IncludedUtilitiesService,
    private readonly parkingService: ParkingService,
    private readonly renterRequirementsService: RenterRequirementsService,
    private readonly petPolicyService: PetPolicyService,
    private readonly propertyLocationService: PropertyLocationService,
    private readonly specificationsService: PropertySpecificationsService,
    private readonly leaseConditionsService: LeaseConditionsService,
    private readonly followUpService: FollowUpService,
    private readonly companyService: CompanyService,
    private readonly addressLookupService: AddressLookupService,
    @Inject(forwardRef(() => ShowingService))
    private readonly showingService: ShowingService,
    @Inject(forwardRef(() => InvestorService))
    private readonly investorService: InvestorService,
    private readonly geocodingService: GeocodingService,
    private readonly slackService: SlackCommunicationService,
    private readonly transUnionService: TransUnionService,
    private readonly propertyAvailabilityService: PropertyAvailabilityService,
    private readonly availabilitySlotsService: AvailabilitySlotsService,
    @Inject(forwardRef(() => PropertyListingService))
    private readonly propertyListingService: PropertyListingService,
  ) {}

  async findById(
    id: string,
    includeAdditionalDetails = false,
    includeOwner = false,
    withImages = true,
  ): Promise<Property> {
    const relations = [];
    if (includeAdditionalDetails) {
      relations.push(
        'petPolicy',
        'includedUtilities',
        'accessibility',
        'parking',
        'renterRequirements',
        'amenities',
        'location',
        'specifications',
        'leaseConditions',
        'owner',
      );

      if (withImages) {
        relations.push('propertyImages', 'propertyImages.file');
      }
    }

    if (includeOwner) {
      relations.push('owner');
    }

    // quick hack to make sure no extra characters make it to ID
    const cleanId = id.replace(/\.+$/, '');

    return this.propertyRepository.findOneOrFail({
      where: { id: cleanId },
      relations: relations,
    });
  }

  async findByAddress(address: string, apartmentNumber: string): Promise<Property> {
    return this.propertyRepository.findOne({
      where: {
        status: In([PropertyStatus.LISTED, PropertyStatus.UNLISTED, PropertyStatus.RENTED_OUT]),
        location: {
          address: Raw((alias) => `${alias} ILIKE :address`, { address }),
          apartmentNumber: apartmentNumber ? apartmentNumber : null,
        },
      },
    });
  }

  async findByDisplayName(displayName: string): Promise<Property> {
    return this.propertyRepository.findOne({
      where: {
        status: In([PropertyStatus.LISTED, PropertyStatus.UNLISTED, PropertyStatus.RENTED_OUT]),
        displayName,
      },
    });
  }

  async createProperty(propertyDto: PropertyDto, ownerId: string): Promise<Property> {
    if (!propertyDto.displayName && (!propertyDto.location || !propertyDto.location.address)) {
      throw new BadRequestException('Property must have a display name or address');
    }

    if (propertyDto.location.longitude && propertyDto.location.latitude) {
      propertyDto.location.timeZone = await this.geocodingService.getTimezone(
        propertyDto.location.latitude,
        propertyDto.location.longitude,
      );
    }

    const lookedUpProperty = await this.addressLookupService.getPropertyDetails({
      address: propertyDto.location.address,
      city: propertyDto.location.city,
      state: propertyDto.location.state,
      zip: propertyDto.location.zip,
    });

    const property = this.propertyRepository.create();
    Object.assign(property, lookedUpProperty, propertyDto);

    // If display name is not provided, use the address as the display name
    if (!property.displayName) {
      property.displayName = propertyDto.location.address;
    }

    const company = await this.companyService.getCompanyByUser(ownerId);
    const investor = await this.investorService.findByUserId(ownerId);

    property.company = company;
    property.owner = investor;

    const createdProperty = await this.saveProperty(property);
    const createdPropertyLocation = await createdProperty.location;

    if (createdPropertyLocation) {
      this.propertyLocationService
        .addNearbyPlaces(createdPropertyLocation)
        .then()
        .catch((e: Error) => console.error('Error adding nearby places', e.message));
    }

    const propertyAddress = propertyDto?.location?.address || propertyDto.displayName;
    this.slackService.sendNewUserRegisteredMessage(
      `🏡 New property "${propertyAddress}" is added by ${(await investor.user).name}`,
    );

    await this.createPropertyAvailability(createdProperty);

    return createdProperty;
  }

  async updatePropertyFromFieldValues(property: Property, propertyData: FieldValue[]) {
    property = this.mapFieldValuesToEntity(property, propertyData);

    property.petPolicy = await this.petPolicyService.getUpdatedPetPolicy(await property.petPolicy, propertyData);

    property.includedUtilities = await this.includedUtilitiesService.getUpdatedIncludedUtilities(
      await property.includedUtilities,
      propertyData,
    );

    property.accessibility = this.accessibilityService.getUpdatedAccessibility(
      await property.accessibility,
      propertyData,
    );

    property.parking = this.parkingService.getUpdatedParking(await property.parking, propertyData);

    property.renterRequirements = this.renterRequirementsService.getUpdatedRenterRequirements(
      await property.renterRequirements,
      propertyData,
    );

    property.amenities = this.amenitiesService.getUpdatedAmenities(await property.amenities, propertyData);

    property.location = this.propertyLocationService.getUpdatedLocation(await property.location, propertyData);

    property.specifications = this.specificationsService.getUpdatedSpecifications(
      await property.specifications,
      propertyData,
    );

    property.leaseConditions = this.leaseConditionsService.getUpdatedLeaseConditions(
      await property.leaseConditions,
      propertyData,
    );

    this.transUnionService
      .updateProperty(property)
      .catch((error) => console.error(`Could not update trans union property. Error: ${error.message}`));

    await this.saveProperty(property);
  }

  async update(propertyId: string, payload: Partial<Property | PropertyDto>): Promise<Property> {
    await this.propertyRepository
      .createQueryBuilder()
      .update({ ...payload })
      .where({ id: propertyId })
      .execute();

    // Fetch the updated property with all relations loaded
    return this.findById(propertyId, true, false, true);
  }

  async saveProperty(property: Property): Promise<Property> {
    return this.propertyRepository.save(property);
  }

  async createNewProperty(property: Partial<Property> = {}): Promise<Property> {
    return this.saveProperty(this.propertyRepository.create(property));
  }

  async delete(propertyId: string): Promise<void> {
    await this.propertyListingService.stopListing(propertyId);

    this.showingService
      .cancelAllShowingsByProperty(propertyId, 'Property has been rented out')
      .catch((e: Error) => console.error('Error cancelling showings', e.message));

    await this.propertyRepository.update(propertyId, {
      status: PropertyStatus.UNLISTED,
      isAvailable: false,
    });

    await this.propertyRepository.softDelete(propertyId);

    this.slackService.sendNewUserRegisteredMessage(`🗑️ Property with ID "${propertyId}" has been deleted.`);
  }

  async getPortfolio(userId: string, page: number, limit: number): Promise<PortfolioDto> {
    const investor = await this.investorService.findByUserId(userId);
    const company = await investor.company;

    return this.getPortfolioByCompany(company.id, page, limit);
  }

  async getPortfolioByCompany(companyId: string, page = 1, limit = 10): Promise<PortfolioDto> {
    const offset = (page - 1) * limit;

    // Get total count of properties
    const total = await this.propertyRepository
      .createQueryBuilder('property')
      .where('property.companyId = :companyId', { companyId })
      .getCount();

    // Query properties with required data
    const queryBuilder = await this.propertyRepository
      .createQueryBuilder('property')
      .leftJoin('property.showingRequests', 'showingRequest', 'showingRequest.status = :showingStatus', {
        showingStatus: ShowingRequestStatus.PENDING,
      })
      .leftJoin('property.questions', 'question', 'question.status = :questionStatus', {
        questionStatus: IntelligentEscalationStatus.PENDING,
      })
      .where('property.companyId = :companyId', { companyId })
      .select([
        'property.id',
        'property.displayName',
        'property.status',
        'property.coverImage',
        'COUNT(DISTINCT showingRequest.id) AS "pendingShowingRequestsCount"',
        'COUNT(DISTINCT question.id) AS "pendingEscalationsCount"',
      ])
      .groupBy('property.id')
      .addGroupBy('property.coverImage')
      .offset(offset)
      .limit(limit)
      .getRawMany();

    // Map the raw results to your desired format
    const data = queryBuilder.map((row: any) => ({
      id: row.property_id,
      displayName: row.property_displayName,
      coverImage: row.property_coverImage,
      status: row.property_status as PropertyStatus,
      pendingShowingRequestsCount: parseInt(row.pendingShowingRequestsCount, 10),
      pendingEscalationsCount: parseInt(row.pendingEscalationsCount, 10),
    }));

    const totalPages = Math.ceil(total / limit);

    return {
      properties: data,
      page,
      limit,
      totalPages,
    };
  }

  async getPendingShowingRequestsAndQuestionCountsByCompany(
    companyId: string,
  ): Promise<ShowingReqsAndQuestionsCountDto> {
    const properties = await this.propertyRepository.find({
      where: { company: { id: companyId } },
      relations: ['questions', 'showingRequests'],
    });

    const counts = await Promise.all(
      properties.map(async (property) => {
        const questions = (await property.questions).filter(
          ({ status }) => status === IntelligentEscalationStatus.PENDING,
        );
        const showingRequests = (await property.showingRequests).filter(
          ({ status }) => status === ShowingRequestStatus.PENDING,
        );
        return {
          id: property.id,
          pendingQuestions: questions.length,
          pendingShowingRequests: showingRequests.length,
        };
      }),
    );

    return counts.reduce((acc, { id, pendingQuestions, pendingShowingRequests }) => {
      acc[id] = {
        pendingQuestions,
        pendingShowingRequests,
      };
      return acc;
    }, {});
  }

  async updateLastShowingAgent(propertyId: string, showingAgentId: string | null): Promise<void> {
    if (!showingAgentId) {
      return;
    }

    await this.propertyRepository.update(propertyId, {
      lastShowingAgent: { id: showingAgentId } as ShowingAgent,
    });
  }

  private mapFieldValuesToEntity(property: Property, data: FieldValue[]): Property {
    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;

      try {
        switch (item.fieldName.split('.').reverse()[0]) {
          case 'description':
            property.description = item.value;
            break;
          case 'allowsVirtualTours':
            const booleanValue = this.stringToBoolean(item.value);
            if (booleanValue !== null) {
              property.allowsVirtualTours = booleanValue;
            }
            break;
          case 'propertyManagementType':
            property.propertyManagementType = item.value as PropertyManagementType;
            break;
          case 'propertyManagementCompanyName':
            property.propertyManagementCompanyName = item.value;
            break;
          default:
            console.log(`No handler for field: ${item.fieldName}`);
            break;
        }
      } catch (error) {
        throw new HttpException('Invalid field name', 400, {
          cause: error.message,
        });
      }
    }

    return property;
  }

  private stringToBoolean(value: string): boolean | null {
    const lowerValue = value.toLowerCase();
    if (lowerValue === 'true') return true;
    if (lowerValue === 'false') return false;
    return null;
  }

  async findAllByCompany(companyId: string): Promise<Property[]> {
    return this.propertyRepository.find({
      where: { company: { id: companyId } },
    });
  }

  private async createPropertyAvailability(property: Property): Promise<void> {
    try {
      const availability = await this.propertyAvailabilityService.create({
        showingDurationInMinutes: 30,
        property: property,
      });

      const location = await property.location;
      const timeZone = location?.timeZone || 'UTC';

      // Create default slots Monday-Saturday 09:00-20:00
      const weekdays = [
        DayOfTheWeekEnum.MONDAY,
        DayOfTheWeekEnum.TUESDAY,
        DayOfTheWeekEnum.WEDNESDAY,
        DayOfTheWeekEnum.THURSDAY,
        DayOfTheWeekEnum.FRIDAY,
        DayOfTheWeekEnum.SATURDAY,
      ];

      for (const weekday of weekdays) {
        await this.availabilitySlotsService.create(
          {
            propertyAvailabilityId: availability.id,
            weekday,
            startTime: '09:00',
            endTime: '20:00',
          },
          timeZone,
          availability.id,
        );
      }
    } catch (error) {
      console.error('Error creating property availability and default slots', error.message);
    }
  }
}

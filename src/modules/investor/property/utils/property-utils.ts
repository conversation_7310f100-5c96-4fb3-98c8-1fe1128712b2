import { IncomeEnum } from '../property/property-details/renter-requirements/income.enum';

class PropertyUtils {
  public getDeposit(deposit: IncomeEnum, rent: number): number {
    if (!deposit) {
      return 0;
    }

    let multiplier: number;

    switch (deposit) {
      case IncomeEnum.X0_5_RENT:
        multiplier = 0.5;
        break;
      case IncomeEnum.X1_RENT:
        multiplier = 1;
        break;
      case IncomeEnum.X2_RENT:
        multiplier = 2;
        break;
      case IncomeEnum.X2_5_RENT:
        multiplier = 2.5;
        break;
      case IncomeEnum.X3_RENT:
        multiplier = 3;
        break;
      case IncomeEnum.X3_5_RENT:
        multiplier = 3.5;
        break;
      case IncomeEnum.NO_REQUIREMENT:
        return 0;
      default:
        throw new Error('Invalid deposit type');
    }

    // Calculate the deposit
    return rent * multiplier;
  }

  getStateAbbreviation(stateName: string): string | null {
    const states: { [key: string]: string } = {
      Alabama: 'AL',
      Alaska: 'AK',
      Arizona: 'AZ',
      Arkansas: 'AR',
      California: 'CA',
      Colorado: 'CO',
      Connecticut: 'CT',
      Delaware: 'DE',
      Florida: 'FL',
      Georgia: 'GA',
      Hawaii: 'HI',
      Idaho: 'ID',
      Illinois: 'IL',
      Indiana: 'IN',
      Iowa: 'IA',
      Kansas: 'KS',
      Kentucky: 'KY',
      Louisiana: 'LA',
      Maine: 'ME',
      Maryland: 'MD',
      Massachusetts: 'MA',
      Michigan: 'MI',
      Minnesota: 'MN',
      Mississippi: 'MS',
      Missouri: 'MO',
      Montana: 'MT',
      Nebraska: 'NE',
      Nevada: 'NV',
      'New Hampshire': 'NH',
      'New Jersey': 'NJ',
      'New Mexico': 'NM',
      'New York': 'NY',
      'North Carolina': 'NC',
      'North Dakota': 'ND',
      Ohio: 'OH',
      Oklahoma: 'OK',
      Oregon: 'OR',
      Pennsylvania: 'PA',
      'Rhode Island': 'RI',
      'South Carolina': 'SC',
      'South Dakota': 'SD',
      Tennessee: 'TN',
      Texas: 'TX',
      Utah: 'UT',
      Vermont: 'VT',
      Virginia: 'VA',
      Washington: 'WA',
      'West Virginia': 'WV',
      Wisconsin: 'WI',
      Wyoming: 'WY',
      'District of Columbia': 'DC',
      'Washington D.C.': 'DC',
      'Washington DC': 'DC',
      'Puerto Rico': 'PR',
    };

    // Normalize the input to match keys in the states object
    const normalizedStateName = stateName.trim().toLowerCase();

    // Find the abbreviation
    for (const [name, abbreviation] of Object.entries(states)) {
      if (name.toLowerCase() === normalizedStateName) {
        return abbreviation;
      }
    }

    // State not found
    return null;
  }

  getAbbreviationState(state: string): string | null {
    const states: { [key: string]: string } = {
      AL: 'Alabama',
      AK: 'Alaska',
      AZ: 'Arizona',
      AR: 'Arkansas',
      CA: 'California',
      CO: 'Colorado',
      CT: 'Connecticut',
      DE: 'Delaware',
      FL: 'Florida',
      GA: 'Georgia',
      HI: 'Hawaii',
      ID: 'Idaho',
      IL: 'Illinois',
      IN: 'Indiana',
      IA: 'Iowa',
      KS: 'Kansas',
      KY: 'Kentucky',
      LA: 'Louisiana',
      ME: 'Maine',
      MD: 'Maryland',
      MA: 'Massachusetts',
      MI: 'Michigan',
      MN: 'Minnesota',
      MS: 'Mississippi',
      MO: 'Missouri',
      MT: 'Montana',
      NE: 'Nebraska',
      NV: 'Nevada',
      NH: 'New Hampshire',
      NJ: 'New Jersey',
      NM: 'New Mexico',
      NY: 'New York',
      NC: 'North Carolina',
      ND: 'North Dakota',
      OH: 'Ohio',
      OK: 'Oklahoma',
      OR: 'Oregon',
      PA: 'Pennsylvania',
      RI: 'Rhode Island',
      SC: 'South Carolina',
      SD: 'South Dakota',
      TN: 'Tennessee',
      TX: 'Texas',
      UT: 'Utah',
      VT: 'Vermont',
      VA: 'Virginia',
      WA: 'Washington',
      WV: 'West Virginia',
      WI: 'Wisconsin',
      WY: 'Wyoming',
    };

    return states[state];
  }

  transformZip(zip?: string): string {
    if (!zip) {
      return '12345';
    }

    // if length is less than 5, pad with 0s
    if (zip.length < 5) {
      return zip.padEnd(5, '0');
    }

    // if more than 5, take the first 5
    return zip.slice(0, 5);
  }
}

export default new PropertyUtils();

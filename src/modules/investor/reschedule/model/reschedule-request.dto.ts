import { instanceToPlain } from 'class-transformer';
import { RenterDto } from '../../../renter/renter/renter.dto';
import { ShowingDto } from '../../showing/model/showing.dto';
import { RescheduleRequest, RescheduleRequestStatus } from '../reschedule-request.entity';
import { ApiProperty } from '@nestjs/swagger';

export class RescheduleRequestDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  comment: string;

  @ApiProperty({
    enum: [
      RescheduleRequestStatus.CONFIRMED,
      RescheduleRequestStatus.DECLINED,
      RescheduleRequestStatus.PENDING,
      RescheduleRequestStatus.RESCHEDULED,
    ],
    enumName: 'RescheduleRequestStatus',
    example: RescheduleRequestStatus.PENDING,
  })
  status: RescheduleRequestStatus;

  @ApiProperty()
  renter: RenterDto;

  @ApiProperty()
  showing: ShowingDto;

  @ApiProperty({ type: () => Date })
  createdAt: Date;
}

export async function convertRescheduleRequestToDto(
  rescheduleRequest: RescheduleRequest,
  withRenter = false,
  withShowing = false,
): Promise<RescheduleRequestDto> {
  const rescheduleRequestDto = <RescheduleRequestDto>(
    instanceToPlain(rescheduleRequest, { excludeExtraneousValues: true })
  );

  if (withRenter) {
    rescheduleRequestDto.renter = <RenterDto>instanceToPlain(await rescheduleRequest.renter, {
      excludeExtraneousValues: true,
    });
  }

  if (withShowing) {
    rescheduleRequestDto.showing = <ShowingDto>instanceToPlain(await rescheduleRequest.showing, {
      excludeExtraneousValues: true,
    });
  }

  return rescheduleRequestDto;
}

import { Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Renter } from '../../renter/renter/renter.entity';
import { Showing } from '../showing/showing.entity';
import { Expose } from 'class-transformer';

export enum RescheduleRequestStatus {
  PENDING = 'Pending',
  CONFIRMED = 'Confirmed',
  DECLINED = 'Declined', // means declined by the renter
  CANCELED = 'Canceled', // means canceled by the investor
  RESCHEDULED = 'Rescheduled',
}

@Entity()
export class RescheduleRequest {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Renter, (renter) => renter.rescheduleRequests, {
    lazy: true,
    nullable: false,
  })
  renter: Promise<Renter> | Renter;

  @Expose()
  @Column({
    type: 'enum',
    enum: RescheduleRequestStatus,
    default: RescheduleRequestStatus.PENDING,
    nullable: false,
  })
  status: RescheduleRequestStatus;

  @ManyToOne(() => Showing, (showing) => showing.rescheduleRequests, {
    lazy: true,
    nullable: false,
  })
  showing: Promise<Showing> | Showing;

  @Expose()
  @Column({ type: 'text', nullable: true })
  comment: string;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

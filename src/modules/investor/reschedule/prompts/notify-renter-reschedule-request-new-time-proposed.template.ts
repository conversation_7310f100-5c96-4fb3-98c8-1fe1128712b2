import { PromptVariable } from '../../../ai/enums/prompt-variable.enum';

/* eslint-disable max-len */
export const notifyRenterRescheduleRequestNewTimeProposedTemplate = `
<task>
Imagine you are a property manager, and you are talking to a potential renter.
The property owner has asked the renter to reschedule the showing request and the renter didn't respond yet.
But then the owner decided that this time does not work for him and proposed a new time.
You need to create a message for the renter to notify about that.

Share all the necessary details like address, proposed time, and date and ask if it works for him.
You will be given today date and the showing date, feel free to use words like "today", "tomorrow", etc.
Please use full month names and days of the week.
</task>

<instructions>
- Try to be concise, do it in up to 4 sentences
- If owner added the comment about rescheduling, you should include it in the message (rephase it to convey the same message)
- Do not mention the property owner.
- Do not say words like "unfortunately" or "disappointing"
- You will be given conversation history for the context, adjust your tone to be aligned with the rest of the conversation
</instructions>

${PromptVariable.RenterName}

<owner_rescheduling_comment>{comment}</owner_rescheduling_comment>

${PromptVariable.PropertyAddress}

<new_proposed_time>{proposedTime}</new_proposed_time>

${PromptVariable.CurrentDate}
${PromptVariable.ChatHistory}
`;

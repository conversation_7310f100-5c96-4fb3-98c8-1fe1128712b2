import { Repository } from 'typeorm';

import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { TimezoneUtils } from '../../../utils/timezone.utils';
import { AiService } from '../../ai/ai.service';
import { LanguageModelsEnum } from '../../ai/enums/language-models.enum';
import { Renter } from '../../renter/renter/renter.entity';
import { ConversationService } from '../../shared/communication/conversation/conversation.service';
import { OutboundCommunicationService } from '../../shared/communication/outbound-communication/outbound-communication.service';
import { Property } from '../property/property/entities/property.entity';
import { notifyRenterShowingRequestCanceledTemplate } from '../showing-request/prompts/notify-renter-showing-request-canceled.template';
import { ShowingStatus } from '../showing/enums/showing-status.enum';
import { Showing } from '../showing/showing.entity';
import { ShowingService } from '../showing/showing.service';
import { RescheduledShowingDto } from './model/rescheduled-showing.dto';
import { notifyRenterRescheduleRequestNewTimeProposedTemplate } from './prompts/notify-renter-reschedule-request-new-time-proposed.template';
import { RescheduleRequest, RescheduleRequestStatus } from './reschedule-request.entity';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import { RentStageEnum } from '../../shared/communication/conversation/enums/rent-stage.enum';
import { MessageType } from '../../shared/communication/conversation/message/message-type.enum';
import { ShowingAgent } from '../showing-agent/entities/showing-agent.entity';
import { TourType } from '../showing/enums/tour-type.enum';

@Injectable()
export class RescheduleService {
  constructor(
    @InjectRepository(RescheduleRequest)
    private readonly rescheduleRepository: Repository<RescheduleRequest>,
    @Inject(forwardRef(() => ShowingService))
    private readonly showingService: ShowingService,
    private readonly aiService: AiService,
    private readonly conversationService: ConversationService,
    private readonly outboundCommunicationService: OutboundCommunicationService,
    private readonly propertyInquiryService: PropertyInquiryService,
  ) {}

  async findOneBy(renterId: string, propertyId: string, status: RescheduleRequestStatus): Promise<RescheduleRequest> {
    return this.rescheduleRepository.findOneBy({
      status,
      renter: { id: renterId },
      showing: {
        property: {
          id: propertyId,
        },
      },
    });
  }

  async createRescheduleRequest(
    property: Property,
    renter: Renter,
    startTime: Date,
    endTime: Date,
    comment: string,
    showingAgentId: string | null,
    tourType: TourType,
  ): Promise<RescheduledShowingDto> {
    const existingShowings = await this.showingService.findShowingsOverlappingWithTimeRange(
      property.id,
      new Date(startTime),
      new Date(endTime),
    );
    const isInPersonTour = tourType === TourType.IN_PERSON;

    let suitableShowing: Showing;

    // If there is an existing showing at this time - use it. If not - create a new one
    // Only makes sense to do this for in-person tours, as virtual tours are personalized and cannot be reused
    if (isInPersonTour && existingShowings.length > 0) {
      suitableShowing = existingShowings[0];
    } else {
      const newShowingBlueprint = new Showing();
      newShowingBlueprint.startTime = new Date(startTime);
      newShowingBlueprint.endTime = new Date(endTime);
      newShowingBlueprint.property = property;
      newShowingBlueprint.status = ShowingStatus.PENDING;
      newShowingBlueprint.showingAgent = { id: showingAgentId } as ShowingAgent;
      newShowingBlueprint.tourType = tourType;

      suitableShowing = await this.showingService.create(newShowingBlueprint);
    }

    const rescheduleRequest = new RescheduleRequest();
    rescheduleRequest.status = RescheduleRequestStatus.PENDING;
    rescheduleRequest.renter = renter;
    rescheduleRequest.showing = Promise.resolve(suitableShowing);
    rescheduleRequest.comment = comment || null;

    await this.rescheduleRepository.save(rescheduleRequest);

    return { newShowingId: suitableShowing.id };
  }

  async cancelRescheduleRequestAsRenter(rescheduleRequest: RescheduleRequest): Promise<void> {
    await this.rescheduleRepository.update(rescheduleRequest.id, {
      status: RescheduleRequestStatus.CANCELED,
    });
  }

  async cancelRescheduleRequestAsOwner(
    rescheduleRequest: RescheduleRequest,
    cancelReason: string | null,
  ): Promise<void> {
    const showing = await rescheduleRequest.showing;
    const property = await showing.property;
    const renter = await rescheduleRequest.renter;
    const inquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, property.id);

    const conversation = await this.conversationService.findByPropertyUser(property.id, renter.user.id);

    if (!conversation) {
      throw new BadRequestException('Conversation not found');
    }

    await this.rescheduleRepository.update(rescheduleRequest.id, {
      status: RescheduleRequestStatus.CANCELED,
    });
    await this.propertyInquiryService.updateStageAndAddCorrespondingEvent(
      inquiry.id,
      RentStageEnum.SHOWING_CANCELLED_BY_OWNER,
    );

    const { address, city, timeZone } = await (await showing.property).location;
    const reply = await this.aiService.getResponseWithChatMemory(
      {
        propertyAddress: address,
        showingTime: TimezoneUtils.convertDateToStringInCityTz(showing.startTime, city, timeZone),
        cancelReason: cancelReason || null,
        renterName: renter.user.name,
      },
      conversation.id,
      notifyRenterShowingRequestCanceledTemplate,
      3,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    await this.conversationService.saveTalloMessage(
      conversation,
      reply,
      MessageType.TEXT,
      renter.user.preferredCommunicationChannel,
    );

    this.outboundCommunicationService
      .sendMessage(renter.user, reply, await conversation.emailMetadata, renter.user.preferredCommunicationChannel)
      .then(() => {
        console.log('Reschedule request canceled notification sent');
      });
  }

  async rescheduleRescheduleRequest(
    rescheduleRequest: RescheduleRequest,
    property: Property,
    startTime: Date,
    endTime: Date,
    comment: string,
    showingAgentId: string | null,
    initiallyAgreedTourType: TourType,
  ): Promise<RescheduledShowingDto> {
    const renter = await rescheduleRequest.renter;
    const conversation = await this.conversationService.findByPropertyUser(property.id, renter.user.id);

    if (!conversation) {
      throw new BadRequestException('Conversation not found');
    }

    const rescheduledShowingDto = await this.createRescheduleRequest(
      property,
      renter,
      startTime,
      endTime,
      comment,
      showingAgentId,
      initiallyAgreedTourType,
    );

    await this.rescheduleRepository.update(rescheduleRequest.id, {
      status: RescheduleRequestStatus.RESCHEDULED,
    });

    const { address, timeZone, city } = await property.location;

    const reply = await this.aiService.getResponseWithChatMemory(
      {
        renterName: renter.user.name,
        comment: comment || null,
        propertyAddress: address,
        proposedTime: TimezoneUtils.convertDateToStringInCityTz(startTime, city, timeZone),
        currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone),
      },
      conversation.id,
      notifyRenterRescheduleRequestNewTimeProposedTemplate,
      3,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    await this.conversationService.saveTalloMessage(
      conversation,
      reply,
      MessageType.TEXT,
      renter.user.preferredCommunicationChannel,
    );

    this.outboundCommunicationService
      .sendMessage(renter.user, reply, await conversation.emailMetadata, renter.user.preferredCommunicationChannel)
      .then(() => {
        console.log('Reschedule request new time proposed notification sent');
      });

    return rescheduledShowingDto;
  }

  async update(id: string, rescheduleRequestData: Partial<RescheduleRequest>): Promise<void> {
    await this.rescheduleRepository.update(id, rescheduleRequestData);
  }
}

import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiModule } from '../../ai/ai.module';
import { ConversationModule } from '../../shared/communication/conversation/conversetion.module';
import { OutboundCommunicationModule } from '../../shared/communication/outbound-communication/outbound-communication.module';
import { ShowingModule } from '../showing/showing.module';
import { RescheduleRequest } from './reschedule-request.entity';
import { RescheduleService } from './reschedule.service';
import { PropertyInquiryModule } from '../property-inquiry/property-inquiry.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([RescheduleRequest]),
    forwardRef(() => ShowingModule),
    AiModule,
    ConversationModule,
    OutboundCommunicationModule,
    PropertyInquiryModule,
  ],
  providers: [RescheduleService],
  exports: [RescheduleService],
})
export class RescheduleModule {}

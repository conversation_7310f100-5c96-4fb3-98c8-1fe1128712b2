import {
  <PERSON><PERSON><PERSON>,
  OneToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  VersionColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Column,
  Index,
} from 'typeorm';
import { Property } from '../property/property/entities/property.entity';
import { DataFeedItemStatusEnum } from './data-feed-item-status.enum';
import { SyndicationPlatform } from './syndication-provider.enum';

@Entity()
@Index(['property', 'platform'], { unique: true })
export class DataFeed {
  @PrimaryGeneratedColumn('uuid')
  id!: number;

  @OneToOne(() => Property)
  @JoinColumn()
  property: Property;

  @Column({ type: 'varchar', length: 12, nullable: false })
  status: DataFeedItemStatusEnum;

  @Index()
  @Column({ type: 'enum', enum: SyndicationPlatform, nullable: false, default: SyndicationPlatform.ZILLOW })
  platform: SyndicationPlatform;

  @Column({ type: 'boolean', default: false })
  syndicateAsAgent: boolean;

  @Column({ type: 'boolean', default: false })
  syndicateAgentNumber: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;

  @VersionColumn()
  version: number;
}

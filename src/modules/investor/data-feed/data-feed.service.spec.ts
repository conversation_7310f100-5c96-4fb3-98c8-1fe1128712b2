import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DataFeedService } from './data-feed.service';
import { DataFeed } from './data-feed.entity';
import { ZillowService } from './zillow/zillow.service';
import { Property } from '../property/property/entities/property.entity';
import { DataFeedItemStatusEnum } from './data-feed-item-status.enum';
import { SyndicationPlatform } from './syndication-provider.enum';

describe('DataFeedService', () => {
  let service: DataFeedService;
  let dataFeedRepository: jest.Mocked<Repository<DataFeed>>;
  let zillowService: jest.Mocked<ZillowService>;

  const mockProperty: Partial<Property> = {
    id: 'property-1',
    displayName: 'Test Property',
    description: 'Test property description',
    updatedAt: new Date(),
  };

  const mockDataFeed: Partial<DataFeed> = {
    id: 'datafeed-1' as any,
    property: mockProperty as Property,
    status: DataFeedItemStatusEnum.ACTIVE,
    platform: SyndicationPlatform.ZILLOW,
    syndicateAsAgent: false,
    syndicateAgentNumber: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    version: 1,
  };

  let mockQueryBuilder: any;

  beforeEach(async () => {
    // Create fresh mock query builder for each test
    mockQueryBuilder = {
      leftJoin: jest.fn().mockReturnThis(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getOne: jest.fn().mockResolvedValue(null),
      getMany: jest.fn().mockResolvedValue([]),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataFeedService,
        {
          provide: getRepositoryToken(DataFeed),
          useValue: {
            find: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
          },
        },
        {
          provide: ZillowService,
          useValue: {
            prepareXml: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<DataFeedService>(DataFeedService);
    dataFeedRepository = module.get(getRepositoryToken(DataFeed));
    zillowService = module.get(ZillowService);

    // Reset mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getZillowFeed', () => {
    it('should always return a feed even when no properties exist', async () => {
      // Arrange
      mockQueryBuilder.getMany.mockResolvedValue([]);
      zillowService.prepareXml.mockResolvedValue('<xml>empty feed</xml>');

      // Act
      const result = await service.getZillowFeed();

      // Assert
      expect(result).toBeDefined();
      expect(result).toBe('<xml>empty feed</xml>');
      expect(dataFeedRepository.createQueryBuilder).toHaveBeenCalledWith('dataFeed');
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith('dataFeed.property', 'property');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('dataFeed.status = :status', {
        status: DataFeedItemStatusEnum.ACTIVE,
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('dataFeed.platform = :platform', {
        platform: SyndicationPlatform.ZILLOW,
      });
      expect(zillowService.prepareXml).toHaveBeenCalledWith([]);
    });

    it('should return XML feed when properties exist', async () => {
      // Arrange
      const mockDataFeeds = [mockDataFeed as DataFeed];
      const expectedXml = '<xml><property>Test Property</property></xml>';

      mockQueryBuilder.getMany.mockResolvedValue(mockDataFeeds);
      zillowService.prepareXml.mockResolvedValue(expectedXml);

      // Act
      const result = await service.getZillowFeed();

      // Assert
      expect(result).toBe(expectedXml);
      expect(zillowService.prepareXml).toHaveBeenCalledWith([mockDataFeed as DataFeed]);
    });

    it('should handle zillow service errors gracefully', async () => {
      // Arrange
      mockQueryBuilder.getMany.mockResolvedValue([mockDataFeed as DataFeed]);
      zillowService.prepareXml.mockRejectedValue(new Error('Zillow service error'));

      // Act & Assert
      await expect(service.getZillowFeed()).rejects.toThrow('Zillow service error');
    });
  });

  describe('getSyndicatedProperties', () => {
    it('should return empty array when no properties exist', async () => {
      // Arrange
      mockQueryBuilder.getMany.mockResolvedValue([]);

      // Act
      const result = await service.getSyndicatedProperties();

      // Assert
      expect(result).toEqual([]);
      expect(dataFeedRepository.createQueryBuilder).toHaveBeenCalledWith('dataFeed');
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith('dataFeed.property', 'property');
    });

    it('should return properties with all required relations', async () => {
      // Arrange
      const mockDataFeeds = [mockDataFeed as DataFeed];
      mockQueryBuilder.getMany.mockResolvedValue(mockDataFeeds);

      // Act
      const result = await service.getSyndicatedProperties();

      // Assert
      expect(result).toEqual(mockDataFeeds);
      expect(result).toHaveLength(1);
      expect(result[0].property).toBeDefined();
    });
  });

  describe('addToDatafeed', () => {
    it('should create new datafeed entry when property does not exist in feed', async () => {
      // Arrange
      mockQueryBuilder.getOne.mockResolvedValue(null);
      dataFeedRepository.save.mockResolvedValue(mockDataFeed as DataFeed);

      // Act
      await service.addToDatafeed(mockProperty as Property);

      // Assert
      expect(dataFeedRepository.createQueryBuilder).toHaveBeenCalledWith('dataFeed');
      expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith('dataFeed.property', 'property');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('property.id = :id', { id: 'property-1' });
      expect(dataFeedRepository.save).toHaveBeenCalledWith({
        property: mockProperty,
        status: DataFeedItemStatusEnum.ACTIVE,
        platform: SyndicationPlatform.ZILLOW,
        syndicateAsAgent: false,
        syndicateAgentNumber: false,
      });
    });

    it('should reactivate inactive property in datafeed', async () => {
      // Arrange
      const inactiveDataFeed = { ...mockDataFeed, status: DataFeedItemStatusEnum.INACTIVE };
      mockQueryBuilder.getOne.mockResolvedValue(inactiveDataFeed);

      // Act
      await service.addToDatafeed(mockProperty as Property);

      // Assert
      expect(dataFeedRepository.update).toHaveBeenCalledWith(inactiveDataFeed.id, {
        status: DataFeedItemStatusEnum.ACTIVE,
      });
      expect(dataFeedRepository.save).not.toHaveBeenCalled();
    });

    it('should not modify already active property in datafeed', async () => {
      // Arrange
      const activeDataFeed = { ...mockDataFeed, status: DataFeedItemStatusEnum.ACTIVE };
      mockQueryBuilder.getOne.mockResolvedValue(activeDataFeed);

      // Act
      await service.addToDatafeed(mockProperty as Property);

      // Assert
      expect(dataFeedRepository.update).not.toHaveBeenCalled();
      expect(dataFeedRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('removeFromDatafeed', () => {
    it('should delete active property from datafeed', async () => {
      // Arrange
      const activeDataFeed = { ...mockDataFeed, status: DataFeedItemStatusEnum.ACTIVE };
      mockQueryBuilder.getMany.mockResolvedValue([activeDataFeed]);

      // Act
      await service.removeFromDatafeed('property-1');

      // Assert
      expect(dataFeedRepository.delete).toHaveBeenCalledWith(activeDataFeed.id);
    });

    it('should not delete inactive property from datafeed', async () => {
      // Arrange
      const inactiveDataFeed = { ...mockDataFeed, status: DataFeedItemStatusEnum.INACTIVE };
      mockQueryBuilder.getMany.mockResolvedValue([inactiveDataFeed]);

      // Act
      await service.removeFromDatafeed('property-1');

      // Assert
      expect(dataFeedRepository.delete).not.toHaveBeenCalled();
    });

    it('should handle non-existent property gracefully', async () => {
      // Arrange
      mockQueryBuilder.getOne.mockResolvedValue(null);

      // Act
      await service.removeFromDatafeed('non-existent-property');

      // Assert
      expect(dataFeedRepository.delete).not.toHaveBeenCalled();
    });
  });

  describe('Zillow Feed Reliability', () => {
    it('should always return a feed regardless of database state', async () => {
      // Test various database states to ensure feed is always returned
      const testCases = [
        { description: 'empty database', mockData: [] },
        { description: 'single property', mockData: [mockDataFeed] },
        { description: 'multiple properties', mockData: [mockDataFeed, { ...mockDataFeed, id: 'datafeed-2' }] },
      ];

      for (const testCase of testCases) {
        // Arrange
        dataFeedRepository.find.mockResolvedValue(testCase.mockData as DataFeed[]);
        zillowService.prepareXml.mockResolvedValue(`<xml>${testCase.description}</xml>`);

        // Act
        const result = await service.getZillowFeed();

        // Assert
        expect(result).toBeDefined();
        expect(typeof result).toBe('string');
        expect(result.length).toBeGreaterThan(0);
        expect(result).toBe(`<xml>${testCase.description}</xml>`);
      }
    });

    it('should handle database connection errors and still attempt to return feed', async () => {
      // Arrange
      mockQueryBuilder.getMany.mockRejectedValue(new Error('Database connection failed'));

      // Act & Assert
      await expect(service.getZillowFeed()).rejects.toThrow('Database connection failed');
      // Note: In a production environment, you might want to handle this gracefully
      // and return an empty feed instead of throwing
    });

    it('should ensure zillow service is always called even with empty data', async () => {
      // Arrange
      mockQueryBuilder.getMany.mockResolvedValue([]);
      zillowService.prepareXml.mockResolvedValue('<xml></xml>');

      // Act
      await service.getZillowFeed();

      // Assert
      expect(zillowService.prepareXml).toHaveBeenCalledTimes(1);
      expect(zillowService.prepareXml).toHaveBeenCalledWith([]);
    });
  });

  describe('Integration scenarios', () => {
    it('should handle the complete flow from adding property to generating feed', async () => {
      // Arrange - Property doesn't exist in feed initially
      mockQueryBuilder.getOne.mockResolvedValueOnce(null);
      dataFeedRepository.save.mockResolvedValue(mockDataFeed as DataFeed);

      // Then exists when getting feed
      dataFeedRepository.find.mockResolvedValue([mockDataFeed as DataFeed]);
      zillowService.prepareXml.mockResolvedValue('<xml>property added</xml>');

      // Act - Add property to feed
      await service.addToDatafeed(mockProperty as Property);

      // Act - Get feed
      const feed = await service.getZillowFeed();

      // Assert
      expect(dataFeedRepository.save).toHaveBeenCalledWith({
        property: mockProperty,
        status: DataFeedItemStatusEnum.ACTIVE,
        platform: SyndicationPlatform.ZILLOW,
        syndicateAsAgent: false,
        syndicateAgentNumber: false,
      });
      expect(feed).toBe('<xml>property added</xml>');
    });
  });
});

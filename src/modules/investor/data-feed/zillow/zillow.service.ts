import { convert } from 'xmlbuilder2';

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { CommunicationChannel } from '../../../shared/communication/conversation/enums/preferred-communication-channel.enum';
import { Property } from '../../property/property/entities/property.entity';
import { DataFeed } from '../data-feed.entity';
import { LeaseTermEnum } from '../../property/property/enums/lease-term.enum';
import { Amenities } from '../../property/property/property-details/amenities/amenities.entity';
import { IncludedUtilities } from '../../property/property/property-details/included-utilities/included-utilities.entity';
import { Parking } from '../../property/property/property-details/parking/parking.entity';
import { PetPolicy } from '../../property/property/property-details/pet-policy/pet-policy.entity';
import { PetPolicyEnum } from '../../property/property/property-details/pet-policy/pet-policy.enum';
import { PropertySpecifications } from '../../property/property/property-details/specifications/property-specifications.entity';
import { zillowArchitectureStyleMap } from './maps/zillow-architecture-style.map';
import { zillowExteriorStyleMap } from './maps/zillow-exterior-style.map';
import { zillowFlorCoveringMap } from './maps/zillow-flor-covering.map';
import { zillowHeatSourceMap } from './maps/zillow-heat-source.map';
import { zillowLeaseTermMap } from './maps/zillow-lease-term.map';
import { zillowParkingTypeMap } from './maps/zillow-parking-type.map';
import { zillowRoofTypeMap } from './maps/zillow-roof-type.map';
import { getZillowListingTag, ZillowListingTag } from './model/zillow-listing-tag';
import PropertyUtils from '../../property/utils/property-utils';
import { ZillowNewLead } from '../../leads/models/zillow-new-lead.dto';
import { PropertyTypeEnum } from '../../property/property/enums/property-type.enum';
import { LeadsService } from '../../leads/leads.service';
import { LeadDto } from '../../leads/models/lead.dto';
import { PropertyInquirySource } from '../../property-inquiry/property-inquiry-source.enum';
import ParsingUtils from '../../../../utils/parsing.utils';

@Injectable()
export class ZillowService {
  constructor(
    private readonly config: ConfigService,
    private readonly leadsService: LeadsService,
  ) {}

  async captureNewLead(lead: ZillowNewLead): Promise<void> {
    try {
      const genericLead = this.convertZillowLeadToGenericLead(lead);
      await this.leadsService.addLead(genericLead);
    } catch (error) {
      console.error('Error capturing new lead', error);
    }
  }

  private convertZillowLeadToGenericLead(lead: ZillowNewLead): LeadDto {
    let message = null;

    if (lead.introduction || lead.message) {
      message =
        (lead.introduction ? lead.introduction + ' ' : '') + (lead.message ? this.removeZillowURL(lead.message) : '');
    }

    let creditScore: string;

    if (lead.creditScoreRangeJson?.creditScoreMin && lead.creditScoreRangeJson?.creditScoreMax) {
      creditScore = lead.creditScoreRangeJson.creditScoreMin + ' - ' + lead.creditScoreRangeJson.creditScoreMax;
    }

    const petDetails = lead.petDetailsJson ? lead.petDetailsJson[0] : null;

    return {
      propertyId: lead.listingId,
      name: this.capitalizeName(lead.name),
      email: lead.email,
      phone: ParsingUtils.formatPhoneNumber(lead.phone),
      message: message,
      employmentStatus: lead.employmentStatus,
      jobTitle: lead.jobTitle,
      monthlyIncome: lead.incomeYearly ? Math.floor(+lead.incomeYearly / 12) : null,
      creditScore: creditScore,
      hasPets: lead.hasPets === 'yes',
      petDescription: petDetails?.description,
      petType: petDetails?.type,
      source: PropertyInquirySource.ZILLOW,
    };
  }

  private capitalizeName(name: string): string {
    return name
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  private removeZillowURL(message: string): string {
    // Splitting the message based on "Message in Zillow Rental Manager:" as the delimiter
    // and taking the first part, which is the desired clean message.
    const parts = message.split('\n\nMessage in Zillow Rental Manager:');

    // The first part of the split will be the clean message before the unwanted text.
    // Trim to remove any leading/trailing whitespace.
    return parts[0].trim();
  }

  async prepareXml(feedItems: DataFeed[]): Promise<string> {
    return convert(await this.prepareXmlObject(feedItems));
  }

  private async prepareXmlObject(feedItems: DataFeed[]) {
    const listings = await Promise.all(feedItems.map((item) => this.getPropertyObject(item)));

    // Build companies: always Tallo, plus unique agent companies for branded items
    const companies: any[] = [
      {
        '@id': 'tallo',
        name: 'Tallo',
        website: 'https://tallo.ai',
        city: 'New York',
        state: 'NY',
        CompanyLogo: {
          '@source': 'https://files.tallo.ai/images/1f573e82-d436-4fd0-8783-c9db51627231.png',
        },
      },
    ];

    const seenCompanyIds = new Set<string>();
    for (const item of feedItems) {
      if (item.syndicateAsAgent) {
        const company = await item.property.company;
        const owner = await item.property.owner;
        const user = await owner.user;
        if (company.id && !seenCompanyIds.has(company.id)) {
          seenCompanyIds.add(company.id);
          companies.push({
            '@id': company.id,
            name: company.name,
            website: 'https://tallo.ai',
            city: owner.city ? owner.city : 'New York',
            state: owner.state ? owner.state : 'NY',
            CompanyLogo: {
              '@source': user.avatar
                ? user.avatar
                : 'https://files.tallo.ai/images/1f573e82-d436-4fd0-8783-c9db51627231.png',
            },
          });
        }
      }
    }

    return {
      talloItems: {
        '@version': '0.5',
        Company: companies.length === 1 ? companies[0] : companies,
        Listing: listings,
      },
    };
  }

  private async getPropertyObject(item: DataFeed): Promise<any> {
    const property: Property = item.property;

    const listingTags: ZillowListingTag[] = [];
    const propertySpecifications = await property.specifications;
    const propertyLocation = await property.location;
    const leaseConditions = await property.leaseConditions;

    listingTags.push(...this.getPetsPolicy(await property.petPolicy));
    listingTags.push(...this.getAmenities(await property.amenities));
    listingTags.push(...this.getIncludedUtilities(await property.includedUtilities));
    listingTags.push(...this.getParking(await property.parking));
    listingTags.push(...this.getPropertySpecs(await property.specifications));

    listingTags.push(...this.getParking(await property.parking));

    const propertyImages = await property.propertyImages;
    const photos = propertyImages
      .filter((pi) => pi.file !== null)
      .map((pi) => pi.file)
      .sort((a, b) => a.order - b.order)
      .map((image) => {
        return {
          '@source': image.url,
        };
      });

    if (property.coverImage) {
      photos.unshift({
        '@source': property.coverImage,
      });
    }

    // Determine branding
    let companyId = 'tallo';
    let providerType = 'propertyManagementSoftware';
    let contactName = 'Emily';
    const contactEmail = this.config.get('TALLO_SENDER_EMAIL');
    let contactPhone = null;

    if (item.syndicateAsAgent) {
      const company = await property.company;
      companyId = company.id;
      providerType = 'agent';
      const owner = await property.owner;
      const user = await owner.user;
      contactName = user.name;
      contactPhone = item.syndicateAgentNumber ? user.phoneNumber : null;
    }

    return {
      '@companyId': companyId,
      '@id': property.id.toString(),
      '@propertyType': this.getZillowPropertyType(propertySpecifications.propertyType),
      '@listingType': 'RENTAL',
      '@type': 'RENTAL',
      providerType,
      description: property.description,
      name: property.displayName,
      price: leaseConditions.rent,
      squareFeet: propertySpecifications.squareFeet,
      city: propertyLocation.city,
      state: propertyLocation.state,
      leaseTerm: this.getZillowLeaseTerm(leaseConditions.possibleLeaseTerms),
      isFurnished: (await property.amenities)?.isFurnished,
      smokingAllowed: property.allowsSmoking,
      zip: propertyLocation.zip,
      country: 'US',
      latitude: propertyLocation.latitude,
      longitude: propertyLocation.longitude,
      deposit: PropertyUtils.getDeposit(leaseConditions.securityDeposit, leaseConditions.rent),
      street: {
        '@hide': propertyLocation.isAddressHidden,
        '#text': propertyLocation.address,
      },
      contactName,
      contactEmail,
      contactPhone,
      contactMethodPreference: CommunicationChannel.EMAIL,
      lastUpdated: this.formatDateToISOWithFractionalSeconds(property.updatedAt),
      numBedrooms: propertySpecifications.bedrooms,
      numFullBaths: propertySpecifications.fullBathrooms,
      numHalfBaths: propertySpecifications.halfBathrooms,
      dateAvailable: this.formatToYYYYMMDD(leaseConditions.desiredLeasingDate),
      ListingPhoto: photos,
      ListingTag: listingTags,
    };
  }

  private getZillowPropertyType(propertyType: PropertyTypeEnum): PropertyTypeEnum {
    if (propertyType === PropertyTypeEnum.STUDIO) {
      return PropertyTypeEnum.APARTMENT;
    } else {
      return propertyType;
    }
  }

  private getZillowLeaseTerm(leaseTerms: LeaseTermEnum[]): string {
    // If there are multiple lease terms, find the longest lease term
    // by comparing their indices in the LeaseTermEnum and return
    // the corresponding Zillow lease term.

    let maxLeaseTermIndex = -1;
    let maxLeaseTerm: LeaseTermEnum = LeaseTermEnum.OTHER;

    for (const leaseTerm of leaseTerms) {
      const leaseTermIndex = Object.values(LeaseTermEnum).indexOf(leaseTerm);

      if (leaseTermIndex > maxLeaseTermIndex) {
        maxLeaseTermIndex = leaseTermIndex;
        maxLeaseTerm = leaseTerm;
      }
    }

    return zillowLeaseTermMap.get(maxLeaseTerm);
  }

  private getParking(parking: Parking): ZillowListingTag[] {
    const zillowParking: ZillowListingTag[] = [];

    if (!parking || !parking.hasParking) {
      return zillowParking;
    }

    if (parking.parkingAvailableSpaces) {
      zillowParking.push(getZillowListingTag('PARKING_SPACES', parking.parkingAvailableSpaces.toString()));
    }

    if (parking.parkingType) {
      zillowParking.push(getZillowListingTag('PARKING_TYPE', zillowParkingTypeMap.get(parking.parkingType)));
    }

    return zillowParking;
  }

  private getPropertySpecs(propertySpecs: PropertySpecifications): ZillowListingTag[] {
    const zillowPropertySpecs: ZillowListingTag[] = [];

    if (!propertySpecs) {
      return zillowPropertySpecs;
    }

    if (propertySpecs.breakfastNook && propertySpecs.breakfastNook > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'BreakfastNook'));
    }

    if (propertySpecs.diningRoom && propertySpecs.diningRoom > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'DiningRoom'));
    }

    if (propertySpecs.familyRoom && propertySpecs.familyRoom > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'FamilyRoom'));
    }

    if (propertySpecs.laundryRoom && propertySpecs.laundryRoom > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'LaundryRoom'));
    }

    if (propertySpecs.library && propertySpecs.library > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'Library'));
    }

    if (propertySpecs.masterBath && propertySpecs.masterBath > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'MasterBath'));
    }

    if (propertySpecs.mudRoom && propertySpecs.mudRoom > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'MudRoom'));
    }

    if (propertySpecs.office && propertySpecs.office > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'Office'));
    }

    if (propertySpecs.pantry && propertySpecs.pantry > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'Pantry'));
    }

    if (propertySpecs.recreationRoom && propertySpecs.recreationRoom > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'RecreationRoom'));
    }

    if (propertySpecs.workshop && propertySpecs.workshop > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'Workshop'));
    }

    if (propertySpecs.solariumAtrium && propertySpecs.solariumAtrium > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'SolariumAtrium'));
    }

    if (propertySpecs.sunRoom && propertySpecs.sunRoom > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'SunRoom'));
    }

    if (propertySpecs.walkInCloset && propertySpecs.walkInCloset > 0) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_TYPE', 'WalkInCloset'));
    }

    if (propertySpecs.totalRooms) {
      zillowPropertySpecs.push(getZillowListingTag('ROOM_COUNT', propertySpecs.totalRooms.toString()));
    }

    if (propertySpecs.yearBuilt) {
      zillowPropertySpecs.push(getZillowListingTag('YEAR_BUILT', propertySpecs.yearBuilt.toString()));
    }

    if (propertySpecs.floorCovering) {
      zillowPropertySpecs.push(
        getZillowListingTag('FLOOR_COVERING', zillowFlorCoveringMap.get(propertySpecs.floorCovering)),
      );
    }

    if (propertySpecs.heatSource) {
      zillowPropertySpecs.push(getZillowListingTag('HEATING_FUEL', zillowHeatSourceMap.get(propertySpecs.heatSource)));
    }

    if (propertySpecs.heatingSystem) {
      zillowPropertySpecs.push(getZillowListingTag('HEATING_SYSTEM', propertySpecs.heatingSystem));
    }

    if (propertySpecs.coolingSystem) {
      zillowPropertySpecs.push(getZillowListingTag('COOLING_SYSTEM', propertySpecs.coolingSystem));
    }

    if (propertySpecs.roofType) {
      zillowPropertySpecs.push(getZillowListingTag('ROOF_TYPE', zillowRoofTypeMap.get(propertySpecs.roofType)));
    }

    if (propertySpecs.viewType) {
      zillowPropertySpecs.push(getZillowListingTag('VIEW_TYPE', propertySpecs.viewType));
    }

    if (propertySpecs.exteriorType) {
      zillowPropertySpecs.push(
        getZillowListingTag('EXTERIOR_TYPE', zillowExteriorStyleMap.get(propertySpecs.exteriorType)),
      );
    }

    if (propertySpecs.architectureStyle) {
      zillowPropertySpecs.push(
        getZillowListingTag('ARCHITECTURE_STYLE', zillowArchitectureStyleMap.get(propertySpecs.architectureStyle)),
      );
    }

    return zillowPropertySpecs;
  }

  private getIncludedUtilities(includedUtilities: IncludedUtilities): ZillowListingTag[] {
    const zillowIncludedUtilities: ZillowListingTag[] = [];

    if (!includedUtilities) {
      return zillowIncludedUtilities;
    }

    if (includedUtilities.water) {
      zillowIncludedUtilities.push(getZillowListingTag('RENT_INCLUDES', 'Water'));
    }

    if (includedUtilities.sewage) {
      zillowIncludedUtilities.push(getZillowListingTag('RENT_INCLUDES', 'Sewage'));
    }

    if (includedUtilities.garbage) {
      zillowIncludedUtilities.push(getZillowListingTag('RENT_INCLUDES', 'Garbage'));
    }

    if (includedUtilities.electricity) {
      zillowIncludedUtilities.push(getZillowListingTag('RENT_INCLUDES', 'Electricity'));
    }

    if (includedUtilities.gas) {
      zillowIncludedUtilities.push(getZillowListingTag('RENT_INCLUDES', 'Gas'));
    }

    if (includedUtilities.internet) {
      zillowIncludedUtilities.push(getZillowListingTag('RENT_INCLUDES', 'Internet'));
    }

    if (includedUtilities.cable) {
      zillowIncludedUtilities.push(getZillowListingTag('RENT_INCLUDES', 'Cable'));
    }

    return zillowIncludedUtilities;
  }

  private getAmenities(amenities: Amenities): ZillowListingTag[] {
    const zillowAmenities: ZillowListingTag[] = [];
    if (!amenities) {
      return [];
    }

    if (amenities.hasBarbecueArea) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'BarbecueArea'));
    }

    if (amenities.hasBasement) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Basement'));
    }

    if (amenities.hasBasketBallCourt) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'BasketBallCourt'));
    }

    if (amenities.hasBusinessCenter) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'BusinessCenter'));
    }

    if (amenities.hasCableSatellite) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'CableSatellite'));
    }

    if (amenities.hasChildCare) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'ChildCare'));
    }

    if (amenities.hasClubDiscount) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'ClubDiscount'));
    }

    if (amenities.hasConcierge) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Concierge'));
    }

    if (amenities.hasControlledAccess) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'ControlledAccess'));
    }

    if (amenities.hasCourtyard) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Courtyard'));
    }

    if (amenities.hasDeck) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Deck'));
    }

    if (amenities.hasDisabledAccess) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'DisabledAccess'));
    }

    if (amenities.hasDock) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Dock'));
    }

    if (amenities.hasDoorman) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Doorman'));
    }

    if (amenities.hasElevator) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Elevator'));
    }

    if (amenities.hasFencedYard) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'FencedYard'));
    }

    if (amenities.hasFitnessCenter) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'FitnessCenter'));
    }

    if (amenities.hasGarden) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Garden'));
    }

    if (amenities.hasGatedEntry) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'GatedEntry'));
    }

    if (amenities.hasGreenHouse) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'GreenHouse'));
    }

    if (amenities.hasHotTubSpa) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'HotTubSpa'));
    }

    if (amenities.hasHouseKeeping) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'HouseKeeping'));
    }

    if (amenities.hasIntercom) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Intercom'));
    }

    if (amenities.hasJettedBathTub) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'JettedBathTub'));
    }

    if (amenities.hasLawn) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Lawn'));
    }

    if (amenities.hasNightPatrol) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'NightPatrol'));
    }

    if (amenities.hasOnSiteMaintenance) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'OnSiteMaintenance'));
    }

    if (amenities.hasOnSiteManagement) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'OnSiteManagement'));
    }

    if (amenities.hasPackageReceiving) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'PackageReceiving'));
    }

    if (amenities.hasPlayGround) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'PlayGround'));
    }

    if (amenities.hasPong) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Pong'));
    }

    if (amenities.hasPorch) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Porch'));
    }

    if (amenities.hasRaquetBallCourt) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'RaquetBallCourt'));
    }

    if (amenities.hasSauna) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Sauna'));
    }

    if (amenities.hasSecuritySystem) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'SecuritySystem'));
    }

    if (amenities.hasSkylight) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'Skylight'));
    }

    if (amenities.hasSportsCourt) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'SportsCourt'));
    }

    if (amenities.hasSprinklerSystem) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'SprinklerSystem'));
    }

    if (amenities.hasSunDeck) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'SunDeck'));
    }

    if (amenities.hasTennisCourt) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'TennisCourt'));
    }

    if (amenities.hasTVLounge) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'TVLounge'));
    }

    if (amenities.hasVolleyBallCourt) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'VolleyBallCourt'));
    }

    if (amenities.hasWetBar) {
      zillowAmenities.push(getZillowListingTag('PROPERTY_AMENITY', 'WetBar'));
    }

    if (amenities.hasDishwasher) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Dishwasher'));
    }

    if (amenities.hasWasher) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Washer'));
    }

    if (amenities.hasDryer) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Dryer'));
    }

    if (amenities.hasFreezer) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Freezer'));
    }

    if (amenities.hasGarbageDisposal) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'GarbageDisposal'));
    }

    if (amenities.hasMicrowave) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Microwave'));
    }

    if (amenities.hasRangeOven) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'RangeOven'));
    }

    if (amenities.hasRefrigerator) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Refrigerator'));
    }

    if (amenities.hasTrashCompactor) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'TrashCompactor'));
    }

    if (amenities.hasCeilingFan) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'CeilingFan'));
    }

    if (amenities.hasDoublePaneWindows) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'DoublePaneWindows'));
    }

    if (amenities.hasHandrails) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Handrails'));
    }

    if (amenities.hasLargeClosets) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'LargeClosets'));
    }

    if (amenities.hasMotherInLawUnit) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'MotherInLawUnit'));
    }

    if (amenities.hasPatio) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Patio'));
    }

    if (amenities.hasStorageSpace) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'StorageSpace'));
    }

    if (amenities.hasVaultedCeiling) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'VaultedCeiling'));
    }

    if (amenities.hasWindowCoverings) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'WindowCoverings'));
    }

    if (amenities.hasBalcony) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Balcony'));
    }

    if (amenities.hasFireplace) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Fireplace'));
    }

    if (amenities.hasPool) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'Pool'));
    }

    if (amenities.hasDishWasher) {
      zillowAmenities.push(getZillowListingTag('MODEL_AMENITY', 'DishWasher'));
    }

    return zillowAmenities;
  }

  private getPetsPolicy(petPolicy: PetPolicy): ZillowListingTag[] {
    const zillowPetPolicy: ZillowListingTag[] = [];

    if (!petPolicy || !petPolicy.allowsPets) {
      return zillowPetPolicy;
    }

    zillowPetPolicy.push(
      getZillowListingTag(
        'DOGS_ALLOWED',
        petPolicy.smallDogsPolicy === PetPolicyEnum.ALLOWED || petPolicy.largeDogsPolicy === PetPolicyEnum.ALLOWED,
      ),
    );

    zillowPetPolicy.push(
      getZillowListingTag('SMALL_DOGS_ALLOWED', petPolicy.smallDogsPolicy === PetPolicyEnum.ALLOWED),
    );

    zillowPetPolicy.push(
      getZillowListingTag('LARGE_DOGS_ALLOWED', petPolicy.largeDogsPolicy === PetPolicyEnum.ALLOWED),
    );

    zillowPetPolicy.push(getZillowListingTag('CATS_ALLOWED', petPolicy.catsPolicy === PetPolicyEnum.ALLOWED));

    return zillowPetPolicy;
  }

  private formatDateToISOWithFractionalSeconds(date) {
    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, '0');
    const day = String(date.getUTCDate()).padStart(2, '0');
    const hour = String(date.getUTCHours()).padStart(2, '0');
    const minute = String(date.getUTCMinutes()).padStart(2, '0');
    const second = String(date.getUTCSeconds()).padStart(2, '0');
    const millisecond = String(date.getUTCMilliseconds()).padStart(3, '0');

    const fractionalSeconds = `${second}.${millisecond}`;

    return `${year}-${month}-${day}T${hour}:${minute}:${fractionalSeconds}+00:00`;
  }

  private formatToYYYYMMDD(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }
}

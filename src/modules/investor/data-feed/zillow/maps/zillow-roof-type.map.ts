import { RoofType } from '../../../property/property/enums/roof-type.enum';

export const zillowRoofTypeMap = new Map<RoofType, string>([
  [RoofType.ASPHALT, 'Asphalt'],
  [RoofType.BUILT_UP, 'BuiltUp'],
  [RoofType.COMPOSITION, 'Composition'],
  [RoofType.METAL, 'Metal'],
  [RoofType.SHAKE_SHINGLE, 'ShakeShingle'],
  [RoofType.SLATE, 'Slate'],
  [RoofType.TILE, 'Tile'],
  [RoofType.OTHER, 'Other'],
]);

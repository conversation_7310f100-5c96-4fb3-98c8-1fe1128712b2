import { ArchitectureStyle } from '../../../property/property/enums/architecture-style.enum';

export const zillowArchitectureStyleMap = new Map<ArchitectureStyle, string>([
  [ArchitectureStyle.BUNGALOW, 'Bungalow'],
  [ArchitectureStyle.CAPE_COD, 'CapeCod'],
  [ArchitectureStyle.COLONIAL, 'Colonial'],
  [ArchitectureStyle.CONTEMPORARY, 'Contemporary'],
  [ArchitectureStyle.CRAFTSMAN, 'Craftsman'],
  [ArchitectureStyle.FRENCH, 'French'],
  [ArchitectureStyle.GEORGIAN, 'Georgian'],
  [ArchitectureStyle.LOFT, 'Loft'],
  [ArchitectureStyle.MODERN, 'Modern'],
  [ArchitectureStyle.QUEEN_ANNE_VICTORIAN, 'QueenAnneVictorian'],
  [ArchitectureStyle.RANCH_RAMBLER, 'RanchRambler'],
  [ArchitectureStyle.SANTA_FE_PUEBLO_STYLE, 'SantaFePuebloStyle'],
  [ArchitectureStyle.SPANISH, 'Spanish'],
  [ArchitectureStyle.SPLIT_LEVEL, 'Split-level'],
  [ArchitectureStyle.TUDOR, 'Tudor'],
  [ArchitectureStyle.OTHER, 'Other'],
]);

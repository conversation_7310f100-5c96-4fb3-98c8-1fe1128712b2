import { LeaseTermEnum } from '../../../property/property/enums/lease-term.enum';

export const zillowLeaseTermMap = new Map<LeaseTermEnum, string>([
  [LeaseTermEnum.MONTHLY, 'Monthly'],
  [LeaseTermEnum.ONE_MONTHS, 'Monthly'],
  [LeaseTermEnum.THREE_MONTHS, 'Monthly'],
  [LeaseTermEnum.SIX_MONTHS, 'SixMonths'],
  [LeaseTermEnum.ONE_YEAR, 'OneYear'],
  [LeaseTermEnum.EIGHTEEN_MONTHS, 'OneYear'],
  [LeaseTermEnum.TWO_YEARS, 'OneYear'],
  [LeaseTermEnum.RENT_TO_OWN, 'ContactForDetails'],
  [LeaseTermEnum.OTHER, 'ContactForDetails'],
]);

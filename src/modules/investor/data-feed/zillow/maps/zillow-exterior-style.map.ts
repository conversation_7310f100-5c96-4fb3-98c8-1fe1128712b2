import { ExteriorType } from '../../../property/property/enums/exterior-type.enum';

export const zillowExteriorStyleMap = new Map<ExteriorType, string>([
  [ExteriorType.BRICK, 'Brick'],
  [ExteriorType.CEMENT_CONCRETE, 'CementConcrete'],
  [ExteriorType.COMPOSITION, 'Composition'],
  [ExteriorType.METAL, 'Metal'],
  [ExteriorType.SHINGLE, 'Shingle'],
  [ExteriorType.STONE, 'Stone'],
  [ExteriorType.STUCCO, 'Stucco'],
  [ExteriorType.VINYL, 'Vinyl'],
  [ExteriorType.WOOD, 'Wood'],
  [ExteriorType.WOOD_PRODUCTS, 'WoodProducts'],
  [ExteriorType.OTHER, 'Other'],
]);

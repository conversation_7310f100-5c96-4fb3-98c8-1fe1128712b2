import { ParkingType } from '../../../property/property/property-details/parking/parking.type';

export const zillowParkingTypeMap = new Map<ParkingType, string>([
  [ParkingType.CARPORT, 'Carport'],
  [ParkingType.GARAGE_ATTACHED, 'GarageAttached'],
  [ParkingType.GARAGE_DETACHED, 'GarageDetached'],
  [ParkingType.OFF_STREET, 'OffStreet'],
  [ParkingType.ON_STREET, 'OnStreet'],
  [ParkingType.NONE, 'None'],
]);

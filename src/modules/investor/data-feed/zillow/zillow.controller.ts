import { Body, Controller, Post } from '@nestjs/common';
import { ZillowNewLead } from '../../leads/models/zillow-new-lead.dto';
import { Public } from '../../../shared/auth/decorators/public-access.decorator';
import { ZillowService } from './zillow.service';
import { ApiBearerAuth } from '@nestjs/swagger';

@Controller('zillow')
export class ZillowController {
  constructor(private readonly zillowService: ZillowService) {}

  // TODO close it to zillow exclusively
  @Post('leads')
  @Public()
  @ApiBearerAuth()
  public async captureNewLead(@Body() lead: ZillowNewLead): Promise<void> {
    console.log('[New zillow lead] \n', lead);

    this.zillowService.captureNewLead(lead).then();
  }
}

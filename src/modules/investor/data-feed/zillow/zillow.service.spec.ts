import { Test, TestingModule } from '@nestjs/testing';
import { ZillowService } from './zillow.service';
import { ConfigService } from '@nestjs/config';
import { LeadsService } from '../../leads/leads.service';
import { ZillowNewLead } from '../../leads/models/zillow-new-lead.dto';
import { PropertyInquirySource } from '../../property-inquiry/property-inquiry-source.enum';
import { EmploymentStatus } from '../../../renter/renter/enums/employment-status.enum';

describe('ZillowService', () => {
  let service: ZillowService;
  let leadsService: jest.Mocked<LeadsService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ZillowService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: LeadsService,
          useValue: {
            addLead: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ZillowService>(ZillowService);
    leadsService = module.get(LeadsService);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('captureNewLead', () => {
    const mockZillowLead: ZillowNewLead = {
      listingId: 'property-1',
      name: 'john doe',
      email: '<EMAIL>',
      phone: '**********',
      introduction: 'Hello',
      message: 'I am interested in this property\n\nMessage in Zillow Rental Manager: Click here',
      employmentStatus: EmploymentStatus.EMPLOYED,
      jobTitle: 'Software Engineer',
      incomeYearly: '120000',
      creditScoreRangeJson: {
        creditScoreMin: 700,
        creditScoreMax: 750,
      },
      hasPets: 'yes',
      petDetailsJson: [
        {
          type: 'dog',
          description: 'Friendly golden retriever',
          size: 'large',
        },
      ],
    };

    it('should convert Zillow lead to generic lead and call LeadsService.addLead', async () => {
      await service.captureNewLead(mockZillowLead);

      expect(leadsService.addLead).toHaveBeenCalledWith({
        propertyId: 'property-1',
        name: 'John Doe', // capitalized
        email: '<EMAIL>',
        phone: '+1**********', // formatted phone number
        message: 'Hello I am interested in this property', // Zillow URL removed
        employmentStatus: EmploymentStatus.EMPLOYED,
        jobTitle: 'Software Engineer',
        monthlyIncome: 10000, // yearly income / 12
        creditScore: '700 - 750',
        hasPets: true,
        petDescription: 'Friendly golden retriever',
        petType: 'dog',
        source: PropertyInquirySource.ZILLOW,
      });
    });

    it('should handle lead without message', async () => {
      const leadWithoutMessage = {
        ...mockZillowLead,
        introduction: undefined,
        message: undefined,
      };

      await service.captureNewLead(leadWithoutMessage);

      expect(leadsService.addLead).toHaveBeenCalledWith(
        expect.objectContaining({
          message: null,
        }),
      );
    });

    it('should handle lead without credit score', async () => {
      const leadWithoutCreditScore = {
        ...mockZillowLead,
        creditScoreRangeJson: undefined,
      };

      await service.captureNewLead(leadWithoutCreditScore);

      expect(leadsService.addLead).toHaveBeenCalledWith(
        expect.objectContaining({
          creditScore: undefined,
        }),
      );
    });

    it('should handle lead without pets', async () => {
      const leadWithoutPets = {
        ...mockZillowLead,
        hasPets: 'no',
        petDetailsJson: undefined,
      };

      await service.captureNewLead(leadWithoutPets);

      expect(leadsService.addLead).toHaveBeenCalledWith(
        expect.objectContaining({
          hasPets: false,
          petDescription: undefined,
          petType: undefined,
        }),
      );
    });

    it('should handle lead without income', async () => {
      const leadWithoutIncome = {
        ...mockZillowLead,
        incomeYearly: undefined,
      };

      await service.captureNewLead(leadWithoutIncome);

      expect(leadsService.addLead).toHaveBeenCalledWith(
        expect.objectContaining({
          monthlyIncome: null,
        }),
      );
    });

    it('should handle errors gracefully', async () => {
      leadsService.addLead.mockRejectedValue(new Error('Test error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await service.captureNewLead(mockZillowLead);

      expect(consoleSpy).toHaveBeenCalledWith('Error capturing new lead', expect.any(Error));
      consoleSpy.mockRestore();
    });
  });

  describe('removeZillowURL', () => {
    it('should remove Zillow URL from message', () => {
      const messageWithURL = 'I am interested in this property\n\nMessage in Zillow Rental Manager: Click here';
      const result = service['removeZillowURL'](messageWithURL);
      expect(result).toBe('I am interested in this property');
    });

    it('should return original message if no Zillow URL', () => {
      const messageWithoutURL = 'I am interested in this property';
      const result = service['removeZillowURL'](messageWithoutURL);
      expect(result).toBe('I am interested in this property');
    });
  });

  describe('capitalizeName', () => {
    it('should capitalize each word in name', () => {
      const result = service['capitalizeName']('john doe smith');
      expect(result).toBe('John Doe Smith');
    });

    it('should handle single name', () => {
      const result = service['capitalizeName']('john');
      expect(result).toBe('John');
    });

    it('should handle mixed case', () => {
      const result = service['capitalizeName']('jOHN dOE');
      expect(result).toBe('John Doe');
    });
  });
});

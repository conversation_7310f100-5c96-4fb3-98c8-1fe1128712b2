import { Repository } from 'typeorm';

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Property } from '../property/property/entities/property.entity';
import { DataFeedItemStatusEnum } from './data-feed-item-status.enum';
import { DataFeed } from './data-feed.entity';
import { ZillowService } from './zillow/zillow.service';
import { SyndicationPlatform } from './syndication-provider.enum';

@Injectable()
export class DataFeedService {
  constructor(
    @InjectRepository(DataFeed)
    private readonly dataFeedRepository: Repository<DataFeed>,
    private readonly zillowService: ZillowService,
  ) {}

  async addToDatafeed(property: Property, platform: SyndicationPlatform = SyndicationPlatform.ZILLOW): Promise<void> {
    const dataFeedPropertyInstance: DataFeed = await this.findByPropertyAndProvider(property.id, platform);

    if (!dataFeedPropertyInstance) {
      this.addPropertyToDataFeed(property, platform).then();
      return;
    }

    if (dataFeedPropertyInstance.status === DataFeedItemStatusEnum.INACTIVE) {
      await this.dataFeedRepository.update(dataFeedPropertyInstance.id, {
        status: DataFeedItemStatusEnum.ACTIVE,
      });
    }
  }

  async removeFromDatafeed(propertyId: string, platform?: SyndicationPlatform): Promise<void> {
    if (platform) {
      const dataFeedPropertyInstance: DataFeed = await this.findByPropertyAndProvider(propertyId, platform);
      if (dataFeedPropertyInstance) {
        await this.dataFeedRepository.delete(dataFeedPropertyInstance.id);
      }
    } else {
      const dataFeedInstances: DataFeed[] = await this.findAllByProperty(propertyId);
      for (const instance of dataFeedInstances) {
        if (instance.status === DataFeedItemStatusEnum.ACTIVE) {
          await this.dataFeedRepository.delete(instance.id);
        }
      }
    }
  }

  async getZillowFeed(): Promise<string> {
    const feedItems = await this.getSyndicatedProperties(SyndicationPlatform.ZILLOW);
    return await this.zillowService.prepareXml(feedItems);
  }

  async getSyndicatedProperties(platform?: SyndicationPlatform): Promise<DataFeed[]> {
    const queryBuilder = this.dataFeedRepository
      .createQueryBuilder('dataFeed')
      .leftJoinAndSelect('dataFeed.property', 'property')
      .leftJoinAndSelect('property.petPolicy', 'petPolicy')
      .leftJoinAndSelect('property.includedUtilities', 'includedUtilities')
      .leftJoinAndSelect('property.accessibility', 'accessibility')
      .leftJoinAndSelect('property.parking', 'parking')
      .leftJoinAndSelect('property.amenities', 'amenities')
      .leftJoinAndSelect('property.renterRequirements', 'renterRequirements')
      .leftJoinAndSelect('property.leaseConditions', 'leaseConditions')
      .leftJoinAndSelect('property.specifications', 'specifications')
      .leftJoinAndSelect('property.location', 'location')
      .leftJoinAndSelect('property.propertyImages', 'propertyImages')
      .leftJoinAndSelect('propertyImages.file', 'file')
      .where('dataFeed.status = :status', { status: DataFeedItemStatusEnum.ACTIVE });

    if (platform) {
      queryBuilder.andWhere('dataFeed.platform = :platform', { platform });
    }

    return await queryBuilder.getMany();
  }

  async getSyndicationPlatformsByProperty(propertyId: string): Promise<string[]> {
    const dataFeeds = await this.findAllByProperty(propertyId);
    return dataFeeds.filter((feed) => feed.status === DataFeedItemStatusEnum.ACTIVE).map((feed) => feed.platform);
  }

  private async addPropertyToDataFeed(
    property: Property,
    provider: SyndicationPlatform = SyndicationPlatform.ZILLOW,
  ): Promise<void> {
    const company = await property.company;
    const syndicateAsAgent = company?.settings?.syndicateAsAgent ?? false;
    const syndicateAgentNumber = company?.settings?.syndicateAgentNumber ?? false;

    await this.dataFeedRepository.save({
      property,
      status: DataFeedItemStatusEnum.ACTIVE,
      platform: provider,
      syndicateAsAgent,
      syndicateAgentNumber,
    });
  }

  private async findByPropertyAndProvider(propertyId: string, provider: SyndicationPlatform): Promise<DataFeed> {
    return await this.dataFeedRepository
      .createQueryBuilder('dataFeed')
      .leftJoin('dataFeed.property', 'property')
      .where('property.id = :id', { id: propertyId })
      .andWhere('dataFeed.platform = :provider', { provider })
      .getOne();
  }

  private async findAllByProperty(propertyId: string): Promise<DataFeed[]> {
    return await this.dataFeedRepository
      .createQueryBuilder('dataFeed')
      .leftJoin('dataFeed.property', 'property')
      .where('property.id = :id', { id: propertyId })
      .getMany();
  }
}

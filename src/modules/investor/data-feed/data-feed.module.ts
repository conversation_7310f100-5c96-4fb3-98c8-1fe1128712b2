import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DataFeedController } from './data-feed.controller';
import { DataFeed } from './data-feed.entity';
import { DataFeedService } from './data-feed.service';
import { ZillowModule } from './zillow/zillow.module';

@Module({
  providers: [DataFeedService],
  controllers: [DataFeedController],
  imports: [ZillowModule, TypeOrmModule.forFeature([DataFeed])],
  exports: [DataFeedService],
})
export class DataFeedModule {}

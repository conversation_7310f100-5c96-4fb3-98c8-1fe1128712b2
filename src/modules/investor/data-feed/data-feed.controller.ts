import { Request, Response } from 'express';

import { <PERSON>, Get, Req, Res } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';

import { Public } from '../../shared/auth/decorators/public-access.decorator';
import { DataFeedService } from './data-feed.service';

@ApiTags('data-feed')
@Controller('feeds')
export class DataFeedController {
  constructor(private readonly syndicationService: DataFeedService) {}

  @Get('zillow')
  @Public()
  @ApiOkResponse({ description: 'Returns changed properties feed' })
  public async getZillowFeed(@Req() req: Request, @Res() res: Response): Promise<void> {
    res.set('Content-Type', 'application/xml');
    res.send(await this.syndicationService.getZillowFeed());
  }
}

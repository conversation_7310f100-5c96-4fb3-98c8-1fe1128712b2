import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { AttestationDto } from '../../../shared/background-check/trans-union/models/attestation-request.dto';

export class SendApplicationRequestDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => AttestationDto)
  @ApiProperty({
    type: () => AttestationDto,
    required: false,
    description: 'Attestation data required for TransUnion screening request',
  })
  attestation?: AttestationDto;
}

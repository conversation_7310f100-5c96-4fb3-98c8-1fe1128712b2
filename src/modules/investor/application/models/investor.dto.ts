import { UserDto } from '../../../shared/user/model/user.dto';
import { ScreeningSensitivity } from '../../property/property/property-details/renter-requirements/screening-sensitivity.enum';
import { InvestorOnboarding } from '../../onboarding/entities/investor-onboarding.entity';

export class InvestorDto {
  user: UserDto;
  address: string;
  screeningSensitivity: ScreeningSensitivity;
  onboarding?: InvestorOnboarding;
}

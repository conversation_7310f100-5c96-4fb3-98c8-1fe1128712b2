import { PromptVariable } from '../../../ai/enums/prompt-variable.enum';
import { AiInstruction } from '../../../ai/prompts/ai-instructions';

export const notifyRenterApplicationIsAcceptedTemplate = `
<task>
You are a property manager communicating with a potential renter.
The renter applied to rent a property, and the owner has just accepted their application.
You need to create a message to notify the renter about the acceptance.

Make sure to include the following information:
- Confirmation that the application has been accepted
- A statement that the owner will contact them soon about the next steps
- Instructions to contact the owner if they do not hear back within a day or two, including the owner's email address: {ownerEmail}

Example:
"Great news! Your application to rent the property has been accepted. The owner will contact you soon about the next steps. If you do not hear back within a day or two, please contact the owner at this email address: {ownerEmail}"
</task>

<instructions>
- Be concise, using up to 3 sentences
- Adjust your tone to align with the rest of the conversation
- Create a message that will make the renter feel good about the conversation and the overall experience
- Do not add any comments or infromation from you as an LLM, only output the message relevant to the task
${AiInstruction.HistoryForContext}
${AiInstruction.DontMakeUpFacts}
${AiInstruction.DontGivePromises}
</instructions>

${PromptVariable.PropertyAddress}
${PromptVariable.ChatHistory}
`;

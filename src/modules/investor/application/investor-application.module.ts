import { Module, forwardRef } from '@nestjs/common';
import { InvestorApplicationController } from './investor-application.controller';
import { InvestorApplicationService } from './investor-application.service';
import { InvestorEntityModule } from '../investor/investor-entity.module';
import { ApplicationBundleModule } from '../../shared/application/application-bundle/application-bundle.module';
import { PropertyInquiryModule } from '../property-inquiry/property-inquiry.module';
import { ApplicationInviteModule } from '../../shared/application/application-invite/application-invite.module';
import { TransUnionModule } from '../../shared/background-check/trans-union/trans-union.module';
import { PropertyModule } from '../property/property/property.module';
import { ApplicationModule } from '../../shared/application/application/application.module';
import { IsOwnerAllowedToWorkWithBundle } from './is-owner-allowed-to-work-with-bundle.guard';
import { RenterOutboundCommsModule } from '../../renter/renter-communication/outbound-communication/renter-outbound-comms.module';
import { OutboundCommunicationModule } from '../../shared/communication/outbound-communication/outbound-communication.module';
import { ConversationModule } from '../../shared/communication/conversation/conversetion.module';
import { LeadsModule } from '../leads/leads.module';
import { PropertyListingModule } from '../property-listing/property-listing.module';

@Module({
  imports: [
    InvestorEntityModule,
    ApplicationBundleModule,
    ApplicationInviteModule,
    ApplicationModule,
    PropertyInquiryModule,
    TransUnionModule,
    PropertyModule,
    PropertyListingModule,
    RenterOutboundCommsModule,
    OutboundCommunicationModule,
    ConversationModule,
    forwardRef(() => LeadsModule),
  ],
  controllers: [InvestorApplicationController],
  providers: [InvestorApplicationService, IsOwnerAllowedToWorkWithBundle],
  exports: [InvestorApplicationService],
})
export class InvestorApplicationModule {}

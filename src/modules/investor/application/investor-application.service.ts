import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { ApplicationBundle } from '../../shared/application/application-bundle/application-bundle.entity';
import { ApplicationBundleService } from '../../shared/application/application-bundle/application-bundle.service';
import { ApplicationInviteService } from '../../shared/application/application-invite/application-invite.service';
import { Application } from '../../shared/application/application/application.entity';
import { ApplicantType } from '../../shared/application/application/enums/applicant-type.enum';
import { TransUnionService } from '../../shared/background-check/trans-union/trans-union.service';
import { PropertyAttestationsResponseDto } from '../../shared/background-check/trans-union/models/property-attestations.dto';
import { AttestationDto } from '../../shared/background-check/trans-union/models/attestation-request.dto';
import { RentStageEnum } from '../../shared/communication/conversation/enums/rent-stage.enum';
import { InvestorService } from '../investor/investor.service';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import { PropertyService } from '../property/property/property.service';
import { TransUnionProductType } from '../../shared/background-check/trans-union/enums/product-type.enum';
import { ApplicationService } from '../../shared/application/application/application.service';
import { RenterOutboundCommsService } from '../../renter/renter-communication/outbound-communication/renter-outbound-comms.service';
import { notifyRenterApplicationIsAcceptedTemplate } from './prompts/notify-renter-application-is-accepted.template';
import { notifyRenterApplicationLinkTemplate } from './prompts/notify-renter-application-link.template';
import { notifyManualLeadApplicationLinkTemplate } from './prompts/notify-manual-lead-application-link.template';
import { OutboundCommunicationService } from '../../shared/communication/outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../shared/communication/outbound-communication/slack/slack-convo-message-builder.class';
import { ConversationService } from '../../shared/communication/conversation/conversation.service';
import { PropertyInquiryEventTypeEnum } from '../property-inquiry/events/property-inquiry-event-type.enum';
import { PropertyListingService } from '../property-listing/property-listing.service';

@Injectable()
export class InvestorApplicationService {
  constructor(
    private readonly applicationBundleService: ApplicationBundleService,
    private readonly applicationInviteService: ApplicationInviteService,
    private readonly applicationService: ApplicationService,
    private readonly propertyService: PropertyService,
    private readonly propertyListingService: PropertyListingService,
    private readonly investorService: InvestorService,
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly transUnionService: TransUnionService,
    private readonly renterOutboundCommsService: RenterOutboundCommsService,
    private readonly outboundCommunicationService: OutboundCommunicationService,
    private readonly slackCommsService: SlackCommunicationService,
    private readonly conversationService: ConversationService,
  ) {}

  async getAllApplicationBundlesByRenterAndOwnerUserId(
    renterId: string,
    ownerId: string,
  ): Promise<ApplicationBundle[]> {
    const investor = await this.investorService.findByUserId(ownerId);
    const company = await investor.company;

    return this.applicationBundleService.getAllApplicationBundlesByRenterAndCompany(renterId, company.id);
  }

  async getReport(applicationId: string, productType: TransUnionProductType, userId: string): Promise<string> {
    const application = await this.applicationService.findApplicationById(applicationId);
    const applicationInvite = await this.applicationInviteService.findByApplicationId(applicationId);
    const applicationBundle = await applicationInvite.applicationBundle;
    const investor = await this.investorService.findByUserId(userId);

    const investorCompany = await investor.company;
    const applicationBundleCompany = await applicationBundle.company;

    if (investorCompany.id !== applicationBundleCompany.id) {
      throw new ForbiddenException('You are not authorized to view this application');
    }

    const response = await this.transUnionService.getScreeningRequestLandlordReport(
      application.screeningRequestRenterId,
      productType,
    );

    if (response.reportsExpireNumberOfDays < 0) {
      throw new BadRequestException('Report has expired');
    }

    return response.reportResponseModelDetails.find(
      (report) => report.providerName.toLowerCase() === productType.toLowerCase(),
    )?.reportData;
  }

  async getPropertyAttestations(propertyId: string, userId: string): Promise<PropertyAttestationsResponseDto> {
    const property = await this.propertyService.findById(propertyId);
    const investor = await this.investorService.findByUserId(userId);
    const propertyCompany = await property.company;
    const investorCompany = await investor.company;

    if (propertyCompany.id !== investorCompany.id) {
      throw new ForbiddenException('You are not authorized to view attestations for this property');
    }

    if (!investor.transUnionLandlordId) {
      if (!investor.address || !investor.city || !investor.state || !investor.zip) {
        throw new BadRequestException('Investor address is missing');
      }

      const landlordId = await this.transUnionService.createLandlord(investor);
      await this.investorService.update(investor, { transUnionLandlordId: landlordId });
      investor.transUnionLandlordId = landlordId;
    }

    if (!property.transUnionPropertyId) {
      const transUnionPropertyId = await this.transUnionService.createProperty(property);

      if (!transUnionPropertyId) {
        throw new BadRequestException('Failed to create property in TransUnion');
      }
      await this.propertyService.update(property.id, { transUnionPropertyId: transUnionPropertyId });
      property.transUnionPropertyId = transUnionPropertyId;
    }

    return this.transUnionService.getPropertyAttestations(investor.transUnionLandlordId, property.transUnionPropertyId);
  }

  async sendApplicationToRenter(
    inquiryId: string,
    currentUserId: string,
    isManualLead: boolean = false,
    attestation?: AttestationDto,
  ): Promise<ApplicationBundle> {
    const inquiry = await this.propertyInquiryService.findById(inquiryId);
    const renter = await inquiry.renter;
    const property = await inquiry.property;
    const company = await property.company;
    const investor = await this.investorService.findByUserId(currentUserId);

    if (await this.applicationBundleService.isBundleExists(renter.id, company.id, property.id)) {
      throw new BadRequestException('Application bundle already exists');
    }

    if (!investor.transUnionLandlordId) {
      throw new BadRequestException('TransUnion landlord ID is missing. Please check property attestations first.');
    }

    if (!property.transUnionPropertyId) {
      throw new BadRequestException('TransUnion property ID is missing. Please check property attestations first.');
    }

    const transUnionScreeningRequestId = await this.transUnionService.createScreeningRequest(
      investor.transUnionLandlordId,
      property.transUnionPropertyId,
      ApplicantType.APPLICANT,
      attestation,
    );

    const applicationBundle = await this.applicationBundleService.createApplicationBundle({
      property: inquiry.property,
      primaryApplicant: inquiry.renter,
      company: await property.company,
    });

    const applicationInvite = await this.applicationInviteService.createApplicationInvite({
      applicationBundle: applicationBundle,
      renter: inquiry.renter,
      type: ApplicantType.APPLICANT,
      invitedBy: await investor.user,
      transUnionScreeningRequestId,
    });

    await this.applicationInviteService.sendApplicationInvite(applicationInvite, applicationBundle);

    await this.propertyInquiryService.updateRentStage(inquiryId, RentStageEnum.APPLICATION_INVITE_SENT);

    const conversation = await this.conversationService.findByPropertyUser(property.id, renter.user.id);

    const template = isManualLead ? notifyManualLeadApplicationLinkTemplate : notifyRenterApplicationLinkTemplate;

    this.renterOutboundCommsService
      .craftMessageAndSend({
        renter,
        propertyId: property.id,
        template: template,
        templateVariables: {
          propertyAddress: (await property.location).address,
        },
      })
      .then(({ message }) => {
        const slackMessageBuilder = new SlackConvoMessageBuilder();

        if (isManualLead) {
          slackMessageBuilder.appendTextLine('🤩 This lead was manually added by the property owner');
          slackMessageBuilder.appendEmptyLine();
        }

        slackMessageBuilder
          .appendTextLine('📨 Application is sent to the renter')
          .appendEmptyLine()
          .appendTalloMessage(message);

        this.slackCommsService.sendMessageToConvosChannel(slackMessageBuilder.build(), conversation);
      });

    return applicationBundle;
  }

  async getApplicationsByBundleId(bundle: ApplicationBundle): Promise<Application[]> {
    const invites = await bundle.applicationInvites;

    return Promise.all(invites.map(async (invite) => await invite.application));
  }

  async resendApplicationToRenter(bundle: ApplicationBundle): Promise<void> {
    await this.applicationInviteService.resendApplicationInviteToPrimaryApplicant(bundle);
  }

  async acceptApplicationBundle(bundle: ApplicationBundle): Promise<ApplicationBundle> {
    const property = await bundle.property;
    const propertyLocation = await property.location;
    const renter = await bundle.primaryApplicant;
    const investor = await property.owner;
    await this.propertyListingService.markAsRentedOut(property.id);
    const propertyInquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, property.id);

    this.propertyInquiryService.addEvent(propertyInquiry.id, PropertyInquiryEventTypeEnum.APPLICATION_ACCEPTED);
    this.propertyInquiryService.update(propertyInquiry.id, {
      stage: RentStageEnum.APPLICATION_ACCEPTED,
    });

    this.renterOutboundCommsService
      .craftMessageAndSend({
        renter,
        propertyId: property.id,
        template: notifyRenterApplicationIsAcceptedTemplate,
        templateVariables: {
          ownerEmail: (await investor.user).email,
          propertyAddress: propertyLocation.address,
        },
      })
      .then(({ message, conversation }) => {
        this.slackCommsService.sendMessageToConvosChannel(
          new SlackConvoMessageBuilder()
            .appendTextLine('💚 Application bundle has been accepted')
            .appendEmptyLine()
            .appendTalloMessage(message)
            .build(),
          conversation,
        );
      });

    return this.applicationBundleService.acceptApplicationBundle(bundle.id);
  }

  async declineApplicationBundle(bundle: ApplicationBundle): Promise<ApplicationBundle> {
    const renter = await bundle.primaryApplicant;
    const property = await bundle.property;
    const propertyLocation = await property.location;

    this.outboundCommunicationService.sendApplicationDeclinedNotification(renter.user, propertyLocation);
    const propertyInquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, property.id);

    this.propertyInquiryService.addEvent(propertyInquiry.id, PropertyInquiryEventTypeEnum.APPLICATION_REJECTED);
    this.propertyInquiryService.update(propertyInquiry.id, {
      stage: RentStageEnum.APPLICATION_REJECTED,
    });

    const conversation = await this.conversationService.findByPropertyUser(property.id, renter.user.id);

    this.slackCommsService.sendMessageToConvosChannel(
      new SlackConvoMessageBuilder().appendTextLine('😓 Application has been declined by the owner').build(),
      conversation,
    );

    return this.applicationBundleService.declineApplicationBundle(bundle.id);
  }
}

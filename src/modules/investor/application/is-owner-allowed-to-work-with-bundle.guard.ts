import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import { InvestorService } from '../investor/investor.service';
import { ApplicationBundleService } from '../../shared/application/application-bundle/application-bundle.service';

@Injectable()
export class IsOwnerAllowedToWorkWithBundle implements CanActivate {
  constructor(
    private readonly investorService: InvestorService,
    private readonly applicationBundleService: ApplicationBundleService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const investor = await this.investorService.findByUserId(request.user.id);
    const company = await investor.company;
    const applicationBundle = await this.applicationBundleService.findById(request.params.applicationBundleId);

    if (company.id !== (await applicationBundle.company).id) {
      throw new ForbiddenException('You are not authorized to work with this application bundle');
    }

    request.company = company;
    request.applicationBundle = applicationBundle;

    return true;
  }
}

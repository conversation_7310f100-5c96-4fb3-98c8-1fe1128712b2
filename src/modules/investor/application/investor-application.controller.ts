import { Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ApplicationBundleDto } from '../../shared/application/application-bundle/application-bundle.dto';
import { ApplicationBundle } from '../../shared/application/application-bundle/application-bundle.entity';
import { Application } from '../../shared/application/application/application.entity';
import { ApplicationDto } from '../../shared/application/application/model/application.dto';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { Role } from '../../shared/auth/models/roles-enum';
import { TransUnionProductType } from '../../shared/background-check/trans-union/enums/product-type.enum';
import { PropertyAttestationsResponseDto } from '../../shared/background-check/trans-union/models/property-attestations.dto';
import { SendApplicationRequestDto } from './models/send-application-request.dto';
import { InvestorApplicationService } from './investor-application.service';
import { IsOwnerAllowedToWorkWithBundle } from './is-owner-allowed-to-work-with-bundle.guard';

@ApiTags('investor-application')
@Controller('investor-application')
@ApiBearerAuth()
@UseGuards(RolesGuard)
@HasRoles(Role.INVESTOR)
export class InvestorApplicationController {
  constructor(private readonly investorApplicationService: InvestorApplicationService) {}

  @Get('application-bundles/renter/:renterId')
  @ApiOkResponse({
    description: 'Get all application bundles by renter',
    type: ApplicationBundleDto,
    isArray: true,
  })
  async getAllApplicationBundlesByRenterAndCompany(
    @Req() req: Request,
    @Param('renterId') renterId: string,
  ): Promise<ApplicationBundleDto[]> {
    const applicationBundles = await this.investorApplicationService.getAllApplicationBundlesByRenterAndOwnerUserId(
      renterId,
      req.user.id,
    );

    const dtoPromises = applicationBundles.map((bundle) => ApplicationBundle.convertToDto(bundle));
    return Promise.all(dtoPromises);
  }

  @Get(':applicationId/report')
  @ApiOkResponse({
    description: 'Get report',
  })
  @ApiQuery({
    name: 'productType',
    enum: TransUnionProductType,
    description: 'Type of report to fetch',
    required: false,
  })
  async getReport(
    @Req() req: Request,
    @Param('applicationId') applicationId: string,
    @Query('productType') productType: TransUnionProductType,
  ): Promise<string> {
    return this.investorApplicationService.getReport(applicationId, productType, req.user.id);
  }

  @Get('property/:propertyId/attestations')
  @ApiOkResponse({
    description: 'Get property attestations from TransUnion',
    type: PropertyAttestationsResponseDto,
  })
  async getPropertyAttestations(
    @Req() req: Request,
    @Param('propertyId') propertyId: string,
  ): Promise<PropertyAttestationsResponseDto> {
    return this.investorApplicationService.getPropertyAttestations(propertyId, req.user.id);
  }

  @Get('application-bundle/:applicationBundleId/applications')
  @ApiOkResponse({
    description: 'Get applications bundle by id',
    type: ApplicationDto,
    isArray: true,
  })
  @UseGuards(IsOwnerAllowedToWorkWithBundle)
  async getApplicationsByBundleId(@Req() req: Request): Promise<ApplicationDto[]> {
    const applications = await this.investorApplicationService.getApplicationsByBundleId(req.applicationBundle);

    const dtoPromises = applications.map((application) => Application.convertToDto(application, true));
    return Promise.all(dtoPromises);
  }

  @Post(':inquiryId')
  @ApiOkResponse({
    description: 'Send application to renter',
  })
  async sendApplicationToRenter(
    @Param('inquiryId') inquiryId: string,
    @Body() body: SendApplicationRequestDto,
    @Req() req: Request,
  ): Promise<ApplicationBundleDto> {
    const applicationBundle = await this.investorApplicationService.sendApplicationToRenter(
      inquiryId,
      req.user.id,
      false,
      body.attestation,
    );

    return ApplicationBundle.convertToDto(applicationBundle);
  }

  @Post(':applicationBundleId/resend')
  @ApiOkResponse({
    description: 'Application resent to renter',
  })
  @ApiQuery({
    name: 'applicationBundleId',
    required: true,
  })
  @UseGuards(IsOwnerAllowedToWorkWithBundle)
  async resendApplicationToRenter(@Req() req: Request): Promise<void> {
    await this.investorApplicationService.resendApplicationToRenter(req.applicationBundle);
  }

  @Post(':applicationBundleId/accept')
  @ApiOkResponse({
    description: 'Approve application bundle',
  })
  @ApiQuery({
    name: 'applicationBundleId',
    required: true,
  })
  @UseGuards(IsOwnerAllowedToWorkWithBundle)
  async acceptBundle(@Req() req: Request): Promise<ApplicationBundleDto> {
    const applicationBundle = await this.investorApplicationService.acceptApplicationBundle(req.applicationBundle);

    return ApplicationBundle.convertToDto(applicationBundle);
  }

  @Post(':applicationBundleId/decline')
  @ApiOkResponse({
    description: 'Decline application bundle',
  })
  @ApiQuery({
    name: 'applicationBundleId',
    required: true,
  })
  @UseGuards(IsOwnerAllowedToWorkWithBundle)
  async declineApplicationBundle(@Req() req: Request): Promise<ApplicationBundleDto> {
    const applicationBundle = await this.investorApplicationService.declineApplicationBundle(req.applicationBundle);

    return ApplicationBundle.convertToDto(applicationBundle);
  }
}

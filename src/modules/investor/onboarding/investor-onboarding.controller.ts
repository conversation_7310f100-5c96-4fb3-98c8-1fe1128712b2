import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';

import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { Role } from '../../shared/auth/models/roles-enum';
import { CanModifyCompanyGuard } from '../../shared/company/guards/can-modify-company.guard';
import { CompanySignUpDto } from '../../shared/company/model/company-sign-up.dto';
import { InvestorOnboardingService } from './investor-onboarding.service';

@ApiTags('investor-onboarding')
@Controller('investor-onboarding')
@ApiBearerAuth()
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
export class InvestorOnboardingController {
  constructor(private readonly onboardingService: InvestorOnboardingService) {}

  @Post('sign-up-info/:companyId')
  @ApiOkResponse({ description: 'Fill sign up information (investor onboarding)' })
  @UseGuards(CanModifyCompanyGuard)
  public async fillCompanySignUpInfo(@Req() req: Request, @Body() body: CompanySignUpDto): Promise<void> {
    await this.onboardingService.fillSignUpInfo(body, req.company, req.user.id, req.user.username);
  }

  @Post('complete/property')
  @ApiOkResponse({ description: 'Mark property onboarding completed' })
  public async completeProperty(@Req() req: Request): Promise<void> {
    await this.onboardingService.completePropertyOnboarding(req.user.id);
  }

  @Post('complete/dashboard')
  @ApiOkResponse({ description: 'Mark dashboard onboarding completed' })
  public async completeDashboard(@Req() req: Request): Promise<void> {
    await this.onboardingService.completeDashboardOnboarding(req.user.id);
  }

  @Post('complete/renter-profile')
  @ApiOkResponse({ description: 'Mark renter profile onboarding completed' })
  public async completeRenterProfile(@Req() req: Request): Promise<void> {
    await this.onboardingService.completeRenterProfileOnboarding(req.user.id);
  }
}

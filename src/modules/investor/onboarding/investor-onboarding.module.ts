import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { InvestorOnboardingController } from './investor-onboarding.controller';
import { InvestorOnboardingService } from './investor-onboarding.service';
import { CompanyModule } from '../../shared/company/company.module';
import { InvestorEntityModule } from '../investor/investor-entity.module';
import { UserModule } from '../../shared/user/user.module';
import { Company } from '../../shared/company/entities/company.entity';
import { OutboundCommunicationModule } from '../../shared/communication/outbound-communication/outbound-communication.module';
import { InvestorOnboarding } from './entities/investor-onboarding.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Company, InvestorOnboarding]),
    forwardRef(() => CompanyModule),
    forwardRef(() => InvestorEntityModule),
    UserModule,
    OutboundCommunicationModule,
  ],
  controllers: [InvestorOnboardingController],
  providers: [InvestorOnboardingService],
  exports: [InvestorOnboardingService],
})
export class InvestorOnboardingModule {}

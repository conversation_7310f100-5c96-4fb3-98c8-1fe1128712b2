import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Company } from '../../shared/company/entities/company.entity';
import { CompanySignUpDto } from '../../shared/company/model/company-sign-up.dto';
import ParsingUtils from '../../../utils/parsing.utils';
import { UserService } from '../../shared/user/user.service';
import { CompanyService } from '../../shared/company/company.service';
import { InvestorService } from '../investor/investor.service';
import { OutboundCommunicationService } from '../../shared/communication/outbound-communication/outbound-communication.service';
import { CompanyPersonaService } from '../../shared/company/company-persona.service';
import { InvestorOnboarding } from './entities/investor-onboarding.entity';

@Injectable()
export class InvestorOnboardingService {
  constructor(
    private readonly userService: UserService,
    private readonly companyService: CompanyService,
    private readonly investorService: InvestorService,
    private readonly outboundCommunicationService: OutboundCommunicationService,
    private readonly companyPersonaService: CompanyPersonaService,
    @InjectRepository(InvestorOnboarding)
    private readonly onboardingRepository: Repository<InvestorOnboarding>,
  ) {}

  async completePropertyOnboarding(userId: string): Promise<void> {
    const investor = await this.investorService.findByUserId(userId);
    await this.onboardingRepository.update(
      { investor: { id: investor.id } as any },
      { propertyOnboardingCompleted: true },
    );
  }

  async completeDashboardOnboarding(userId: string): Promise<void> {
    const investor = await this.investorService.findByUserId(userId);
    await this.onboardingRepository.update(
      { investor: { id: investor.id } as any },
      { dashboardOnboardingCompleted: true },
    );
  }

  async completeRenterProfileOnboarding(userId: string): Promise<void> {
    const investor = await this.investorService.findByUserId(userId);
    await this.onboardingRepository.update(
      { investor: { id: investor.id } as any },
      { renterProfileOnboardingCompleted: true },
    );
  }

  async fillSignUpInfo(dto: CompanySignUpDto, company: Company, userId: string, userEmail: string): Promise<void> {
    await this.userService.updateUser(userId, {
      preferredCommunicationChannel: dto.preferredCommunicationChannel,
    });

    if (dto.phoneNumber) {
      await this.userService.updateUser(userId, {
        phoneNumber: ParsingUtils.formatPhoneNumber(dto.phoneNumber),
      });
    }

    if (dto.companyName || dto.numberOfEmployees || dto.accountType) {
      await this.companyService.updateCompany(company.id, {
        ...(dto.companyName && { name: dto.companyName }),
        ...(dto.numberOfEmployees && { numberOfEmployees: dto.numberOfEmployees }),
        ...(dto.accountType && { companyType: dto.accountType }),
      });
    }

    if (dto.screeningSensitivity) {
      try {
        const investor = await this.investorService.findByUserId(userId);
        await this.investorService.update(investor, {
          preferredScreeningSensitivity: dto.screeningSensitivity,
        });

        await this.onboardingRepository.update(
          { investor: { id: investor.id } as any },
          { initialOnboardingCompleted: true },
        );
      } catch (error) {
        console.error('Error updating investor screening sensitivity or onboarding', error);
      }
    }

    if (dto.perceivedProductValueProposition || dto.numberOfUnitsOverseeing) {
      await this.companyPersonaService.create({
        company: company,
        ...(dto.perceivedProductValueProposition && {
          perceivedProductValueProposition: dto.perceivedProductValueProposition,
        }),
        ...(dto.numberOfUnitsOverseeing && {
          numberOfUnitsOverseeing: dto.numberOfUnitsOverseeing,
        }),
      });
    }

    this.outboundCommunicationService
      .sendNewUserRegisteredNotification(dto, userEmail)
      .catch((error) => console.error('Error sending new user registered notification', error));
  }
}

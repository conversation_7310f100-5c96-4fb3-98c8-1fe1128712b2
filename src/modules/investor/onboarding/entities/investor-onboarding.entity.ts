import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinC<PERSON>umn,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
} from 'typeorm';

import { Investor } from '../../investor/investor.entity';

@Entity()
export class InvestorOnboarding {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Investor, (investor) => investor.investorOnboarding, { lazy: true })
  @JoinColumn()
  investor: Promise<Investor> | Investor;

  @RelationId((io: InvestorOnboarding) => io.investor)
  investorId: string;

  @Column({ type: 'boolean', default: false })
  @Expose()
  initialOnboardingCompleted: boolean;

  @Column({ type: 'boolean', default: false })
  @Expose()
  propertyOnboardingCompleted: boolean;

  @Column({ type: 'boolean', default: false })
  @Expose()
  dashboardOnboardingCompleted: boolean;

  @Column({ type: 'boolean', default: false })
  @Expose()
  renterProfileOnboardingCompleted: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

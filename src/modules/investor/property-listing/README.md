# Property Listing Module

This module handles all property listing management functionality including automatic refreshing, publishing, pausing, and marking properties as rented out.

## Endpoints

### 1. Refresh All Active Listings

**PUT** `/property-listing/refresh`

Manually triggers a refresh of all active property listings that have auto-refresh enabled.

- **Authentication**: Basic Auth
- **Response**:
  ```json
  {
    "processed": 5,
    "errors": []
  }
  ```

### 2. Toggle Auto-Refresh Setting

**PATCH** `/property-listing/:propertyId/auto-refresh`

Allows property owners to enable or disable automatic refresh for a specific property.

- **Authentication**: Bearer <PERSON> (Investor role required)
- **Path Parameters**:
  - `propertyId` (string): The ID of the property to update
- **Request Body**:
  ```json
  {
    "autoRefresh": true
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "autoRefresh": true
  }
  ```

### 3. Publish Property

**PUT** `/property-listing/publish/:propertyId`

Publishes a property to start the data feed and make it available for listing.

- **Authentication**: <PERSON><PERSON> (Investor role required)
- **Path Parameters**:
  - `propertyId` (string): The ID of the property to publish
- **Response**: PropertyDto object

### 4. Pause Property Syndication

**PUT** `/property-listing/pause/:propertyId`

Pauses the property data feed to temporarily stop syndication.

- **Authentication**: Bearer Token (Investor role required)
- **Path Parameters**:
  - `propertyId` (string): The ID of the property to pause
- **Response**: PropertyDto object

### 5. Mark Property as Rented Out

**PUT** `/property-listing/complete/:propertyId`

Marks a property as rented out and completes the listing process.

- **Authentication**: Bearer Token (Investor role required)
- **Path Parameters**:
  - `propertyId` (string): The ID of the property to mark as rented
- **Response**: PropertyDto object

#### Error Responses:

- **404 Not Found**: Property not found
- **403 Forbidden**: User is not the owner of the property

## Features

### Property Listing Management

- **Publish Properties**: Start data feed and make properties available for listing
- **Pause Syndication**: Temporarily stop property syndication
- **Mark as Rented**: Complete the listing process when property is rented
- **Auto-Refresh Control**: Toggle automatic refresh on a per-property basis

### Auto-Refresh Functionality

- Only properties with `autoRefresh: true` are processed during automatic refresh cycles
- New properties default to `autoRefresh: true`
- Property owners can toggle this setting per property
- Automatic description rephrasing and image shuffling to keep listings fresh

### Security

- Uses `IsPropertyOwnerGuard` to ensure users can only modify their own properties
- Role-based access control restricts access to investors only
- Property ownership verification is handled at the guard level, not in service logic

### Database Changes

- Added `autoRefresh` boolean column to the `property` table
- Defaults to `true` for new properties
- Migration: `1754503666019-add_property_autoupdate.ts`

## Usage Examples

### Enable auto-refresh for a property:

```bash
curl -X PATCH \
  'https://api.example.com/property-listing/123e4567-e89b-12d3-a456-426614174000/auto-refresh' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{"autoRefresh": true}'
```

### Disable auto-refresh for a property:

```bash
curl -X PATCH \
  'https://api.example.com/property-listing/123e4567-e89b-12d3-a456-426614174000/auto-refresh' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{"autoRefresh": false}'
```

### Publish a property:

```bash
curl -X PUT \
  'https://api.example.com/property-listing/publish/123e4567-e89b-12d3-a456-426614174000' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### Pause property syndication:

```bash
curl -X PUT \
  'https://api.example.com/property-listing/pause/123e4567-e89b-12d3-a456-426614174000' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### Mark property as rented out:

```bash
curl -X PUT \
  'https://api.example.com/property-listing/complete/123e4567-e89b-12d3-a456-426614174000' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

## Testing

Run the tests for this module:

```bash
npm test -- property-listing-refresh
```

The test suite includes:

- Service method tests
- Error handling scenarios
- Authentication and authorization are tested via e2e tests

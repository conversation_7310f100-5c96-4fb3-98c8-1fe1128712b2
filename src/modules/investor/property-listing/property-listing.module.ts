import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PropertyListingController } from './property-listing.controller';
import { PropertyListingService } from './property-listing.service';
import { Property } from '../property/property/entities/property.entity';
import { PropertyImage } from '../property/property/property-details/images/entities/property-image.entity';
import { AiModule } from '../../ai/ai.module';
import { PlacesLookupModule } from '../property/location-discovery/places-lookup/places-lookup.module';
import { PropertyImagesModule } from '../property/property/property-details/images/property-images.module';
import { PropertyModule } from '../property/property/property.module';
import { DataFeed } from '../data-feed/data-feed.entity';

import { DataFeedModule } from '../data-feed/data-feed.module';
import { SlackCommunicationModule } from '../../shared/communication/outbound-communication/slack/slack-communication.module';
import { ShowingModule } from '../showing/showing.module';
import { PropertyInquiryModule } from '../property-inquiry/property-inquiry.module';
import { TransUnionModule } from '../../shared/background-check/trans-union/trans-union.module';
import { FollowUpModule } from '../../shared/communication/follow-up/follow-up.module';
import { DescriptionModule } from '../property/property/property-details/description/description.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Property, PropertyImage, DataFeed]),
    AiModule,
    PlacesLookupModule,
    PropertyImagesModule,
    forwardRef(() => PropertyModule),
    forwardRef(() => DataFeedModule),
    SlackCommunicationModule,
    forwardRef(() => ShowingModule),
    PropertyInquiryModule,
    TransUnionModule,
    FollowUpModule,
    DescriptionModule,
  ],
  controllers: [PropertyListingController],
  providers: [PropertyListingService],
  exports: [PropertyListingService],
})
export class PropertyListingModule {}

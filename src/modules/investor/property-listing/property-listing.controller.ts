import { <PERSON>, Put, Patch, Param, Body, UseGuards, Req, Get } from '@nestjs/common';
import { ApiBasicAuth, ApiOkResponse, ApiTags, ApiBearerAuth, ApiParam, ApiResponse } from '@nestjs/swagger';
import { Request } from 'express';
import { PropertyListingService } from './property-listing.service';
import { ToggleAutoRefreshDto } from './dto/toggle-auto-refresh.dto';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { Role } from 'src/modules/shared/auth/models/roles-enum';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { BasicAuth } from '../../shared/auth/decorators/basic-auth.decorator';
import { IsPropertyOwnerGuard } from '../../../guards/investor/is-property-owner-guard';
import { convertPropertyToDto, PropertyDto } from '../property/property/model/property.dto';
import { SaveSyndicationDto } from './dto/save-syndication.dto';

@ApiTags('property-listing')
@Controller('property-listing')
export class PropertyListingController {
  constructor(private readonly propertyListingService: PropertyListingService) {}

  @Put('refresh')
  @ApiOkResponse({
    description: 'Refresh all active property listings by rephrasing descriptions and shuffling images',
    schema: {
      type: 'object',
      properties: {
        processed: { type: 'number', description: 'Number of properties successfully processed' },
        errors: {
          type: 'array',
          items: { type: 'string' },
          description: 'Array of error messages for properties that failed to process',
        },
      },
    },
  })
  @BasicAuth()
  @ApiBasicAuth('basic')
  async refreshAllActiveListings(): Promise<{ processed: number; errors: string[] }> {
    return await this.propertyListingService.refreshAllActiveListings();
  }

  @UseGuards(RolesGuard, IsPropertyOwnerGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  @Patch(':propertyId/auto-refresh')
  @ApiParam({
    name: 'propertyId',
    required: true,
    description: 'The ID of the property to update',
  })
  @ApiOkResponse({
    description: 'Toggle automatic refresh setting for a property',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', description: 'Whether the operation was successful' },
        autoRefresh: { type: 'boolean', description: 'The new automatic refresh setting' },
      },
    },
  })
  async toggleAutoRefresh(
    @Param('propertyId') propertyId: string,
    @Body() toggleData: ToggleAutoRefreshDto,
  ): Promise<{ success: boolean; autoRefresh: boolean }> {
    return await this.propertyListingService.toggleAutoRefresh(propertyId, toggleData);
  }

  @UseGuards(RolesGuard, IsPropertyOwnerGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  @Put('publish/:propertyId')
  @ApiParam({
    name: 'propertyId',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Start property data-feed',
  })
  async listProperty(@Req() req: Request, @Body() syndicationDto: SaveSyndicationDto): Promise<PropertyDto> {
    const updatedProperty = await this.propertyListingService.listProperty(req.property, syndicationDto);

    return convertPropertyToDto(updatedProperty, true);
  }

  @UseGuards(RolesGuard, IsPropertyOwnerGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  @Put('pause/:propertyId')
  @ApiParam({
    name: 'propertyId',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Pause property data-feed',
  })
  async pausePropertySyndication(@Param('propertyId') propertyId: string): Promise<PropertyDto> {
    const updatedProperty = await this.propertyListingService.pauseProperty(propertyId);

    return convertPropertyToDto(updatedProperty, true);
  }

  @UseGuards(RolesGuard, IsPropertyOwnerGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  @Put('complete/:propertyId')
  @ApiParam({
    name: 'propertyId',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Mark property as rented out',
  })
  async markAsRentedOut(@Param('propertyId') propertyId: string): Promise<PropertyDto> {
    const updatedProperty = await this.propertyListingService.markAsRentedOut(propertyId);

    return convertPropertyToDto(updatedProperty, true);
  }

  @UseGuards(RolesGuard, IsPropertyOwnerGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  @Get('syndication-platforms/:propertyId')
  @ApiParam({
    name: 'propertyId',
    required: true,
    description: 'The ID of the property to get syndication platforms for',
  })
  @ApiOkResponse({
    description: 'Get list of platforms the property is syndicated to',
    schema: {
      type: 'object',
      properties: {
        platforms: {
          type: 'array',
          items: { type: 'string' },
          description: 'Array of platform names the property is syndicated to',
          example: ['zillow'],
        },
      },
    },
  })
  async getSyndicationPlatforms(@Param('propertyId') propertyId: string): Promise<{ platforms: string[] }> {
    return await this.propertyListingService.getSyndicationPlatforms(propertyId);
  }

  @UseGuards(RolesGuard, IsPropertyOwnerGuard)
  @HasRoles(Role.INVESTOR)
  @ApiBearerAuth()
  @Patch('syndication/:propertyId')
  @ApiParam({
    name: 'propertyId',
    required: true,
    description: 'The ID of the property to update syndication for',
  })
  @ApiOkResponse({
    description: 'Update property syndication platforms without changing status',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', description: 'Whether the operation was successful' },
        platforms: {
          type: 'array',
          items: { type: 'string' },
          description: 'Updated list of platforms the property is syndicated to',
          example: ['zillow'],
        },
      },
    },
  })
  async updateSyndication(
    @Param('propertyId') propertyId: string,
    @Body() syndicationDto: SaveSyndicationDto,
  ): Promise<{ success: boolean; platforms: string[] }> {
    return await this.propertyListingService.updateSyndication(propertyId, syndicationDto);
  }
}

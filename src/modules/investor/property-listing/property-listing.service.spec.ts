import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PropertyListingService } from './property-listing.service';
import { Property } from '../property/property/entities/property.entity';
import { PropertyImageService } from '../property/property/property-details/images/property-image.service';
import { DescriptionService } from '../property/property/property-details/description/description.service';
import { DataFeedService } from '../data-feed/data-feed.service';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { FollowUpService } from '../../shared/communication/follow-up/follow-up.service';
import { ShowingService } from '../showing/showing.service';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import { TransUnionService } from '../../shared/background-check/trans-union/trans-union.service';
import { PropertyService } from '../property/property/property.service';
import { File } from '../../shared/file/entities/file.entity';
import { FileDto } from '../../shared/file/models/file.dto';

// Helper to build a mock File entity
const makeFile = (id: string, order: number): File =>
  ({
    id,
    url: `https://cdn/${id}.jpg`,
    thumbnailUrl: `https://cdn/${id}_thumb.jpg`,
    fileName: `${id}.jpg`,
    fileSize: 1000,
    mimeType: 'image/jpeg',
    order,
  }) as unknown as File;

describe('PropertyListingService - shuffleLastImages via refreshSingleProperty', () => {
  let service: PropertyListingService;

  // Mocks
  const propertyRepository = {
    findOne: jest.fn(),
    update: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn(),
  } as unknown as jest.Mocked<Repository<Property>>;

  const propertyImagesService = {
    updateImageOrder: jest.fn<Promise<File[]>, [string, FileDto[]]>(),
  } as unknown as jest.Mocked<PropertyImageService>;

  const descriptionService = {
    rephraseDescription: jest.fn(),
  } as unknown as jest.Mocked<DescriptionService>;

  const dataFeedService = {} as unknown as jest.Mocked<DataFeedService>;
  const slackService = {} as unknown as jest.Mocked<SlackCommunicationService>;
  const followUpService = {} as unknown as jest.Mocked<FollowUpService>;
  const showingService = {} as unknown as jest.Mocked<ShowingService>;
  const propertyInquiryService = {} as unknown as jest.Mocked<PropertyInquiryService>;
  const transUnionService = {} as unknown as jest.Mocked<TransUnionService>;
  const propertyService = {} as unknown as jest.Mocked<PropertyService>;

  beforeEach(async () => {
    jest.clearAllMocks();

    // Spy on static convertToDto to avoid class-transformer nuances
    jest.spyOn(File, 'convertToDto').mockImplementation(
      (image: File) =>
        ({
          id: image.id,
          url: image.url,
          thumbnailUrl: image.thumbnailUrl,
          fileName: image.fileName,
          fileSize: image.fileSize,
          mimeType: image.mimeType,
          order: image.order,
        }) as FileDto,
    );

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PropertyListingService,
        { provide: getRepositoryToken(Property), useValue: propertyRepository },
        { provide: PropertyImageService, useValue: propertyImagesService },
        { provide: DescriptionService, useValue: descriptionService },
        { provide: DataFeedService, useValue: dataFeedService },
        { provide: SlackCommunicationService, useValue: slackService },
        { provide: FollowUpService, useValue: followUpService },
        { provide: ShowingService, useValue: showingService },
        { provide: PropertyInquiryService, useValue: propertyInquiryService },
        { provide: TransUnionService, useValue: transUnionService },
        { provide: PropertyService, useValue: propertyService },
      ],
    }).compile();

    service = module.get<PropertyListingService>(PropertyListingService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should ignore images that have missing file relations and not call update when <= 2 valid', async () => {
    // Arrange: 3 propertyImages, but only 2 have files (<=2 valid -> no shuffle)
    const propId = 'prop-1';
    const freshProperty = {
      id: propId,
      propertyImages: Promise.resolve([
        { file: makeFile('a', 0) },
        { file: undefined },
        { file: makeFile('b', 1) },
      ] as any),
    } as unknown as Property;

    (propertyRepository.findOne as any).mockResolvedValue(freshProperty);

    // Property passed into refreshSingleProperty can be minimal; description empty to skip rephrase
    const incomingProperty = { id: propId, description: '' } as unknown as Property;

    // Act
    await (service as any).refreshSingleProperty(incomingProperty);

    // Assert
    expect(propertyImagesService.updateImageOrder).not.toHaveBeenCalled();
  });

  it('should reorder last images and call updateImageOrder with valid FileDtos only', async () => {
    // Arrange: 6 propertyImages, 5 valid (one missing file)
    const propId = 'prop-2';
    const validFiles = [makeFile('1', 0), makeFile('2', 1), makeFile('3', 2), makeFile('4', 3), makeFile('5', 4)];

    const freshProperty = {
      id: propId,
      propertyImages: Promise.resolve([
        { file: validFiles[0] },
        { file: validFiles[1] },
        { file: undefined }, // should be ignored
        { file: validFiles[2] },
        { file: validFiles[3] },
        { file: validFiles[4] },
      ] as any),
    } as unknown as Property;

    (propertyRepository.findOne as any).mockResolvedValue(freshProperty);

    const incomingProperty = { id: propId, description: '' } as unknown as Property;

    // Capture the argument passed to updateImageOrder
    let passedImages: FileDto[] | undefined;
    (propertyImagesService.updateImageOrder as jest.Mock).mockImplementation(async (_id, images) => {
      passedImages = images;
      // Return mocked updated File[] (mimic save)
      return images.map((f) => ({ ...f }) as unknown as File);
    });

    // Act
    await (service as any).refreshSingleProperty(incomingProperty);

    // Assert
    expect(propertyImagesService.updateImageOrder).toHaveBeenCalledTimes(1);
    expect(passedImages).toBeDefined();
    expect(passedImages!.length).toBe(5);
    const orders = passedImages!.map((f) => f.order);
    expect(orders).toEqual(Array.from({ length: 5 }, (_, i) => i));
    expect(passedImages!.every((f) => !!f && !!f.id)).toBe(true);
  });
});

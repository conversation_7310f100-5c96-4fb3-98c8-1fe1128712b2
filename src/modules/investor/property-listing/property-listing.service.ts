import { Injectable, forwardRef, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ToggleAutoRefreshDto } from './dto/toggle-auto-refresh.dto';
import { Property } from '../property/property/entities/property.entity';
import { DescriptionService } from '../property/property/property-details/description/description.service';
import { PropertyImageService } from '../property/property/property-details/images/property-image.service';
import { PropertyStatus } from '../property/property/enums/property-status.enum';
import { FileDto } from '../../shared/file/models/file.dto';
import { File } from '../../shared/file/entities/file.entity';
import { DataFeedService } from '../data-feed/data-feed.service';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { FollowUpService } from '../../shared/communication/follow-up/follow-up.service';
import { ShowingService } from '../showing/showing.service';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import { TransUnionService } from '../../shared/background-check/trans-union/trans-union.service';
import { RentStageEnum } from '../../shared/communication/conversation/enums/rent-stage.enum';
import { PropertyService } from '../property/property/property.service';
import { SaveSyndicationDto } from './dto/save-syndication.dto';
import { SyndicationPlatform } from '../data-feed/syndication-provider.enum';

@Injectable()
export class PropertyListingService {
  constructor(
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    private readonly descriptionService: DescriptionService,
    private readonly propertyImagesService: PropertyImageService,
    @Inject(forwardRef(() => DataFeedService))
    private readonly syndicationService: DataFeedService,
    private readonly slackService: SlackCommunicationService,
    private readonly followUpService: FollowUpService,
    private readonly showingService: ShowingService,
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly transUnionService: TransUnionService,
    @Inject(forwardRef(() => PropertyService))
    private readonly propertyService: PropertyService,
  ) {}

  async refreshAllActiveListings(): Promise<{ processed: number; errors: string[] }> {
    const activeProperties = await this.getActiveProperties();
    const errors: string[] = [];
    let processed = 0;

    for (const property of activeProperties) {
      try {
        await this.refreshSingleProperty(property);
        processed++;
      } catch (error) {
        const errorMessage = `Failed to refresh property ${property.id} (${property.displayName}): ${error.message}`;
        console.error(errorMessage);
      }
    }

    return { processed, errors };
  }

  async toggleAutoRefresh(
    propertyId: string,
    toggleData: ToggleAutoRefreshDto,
  ): Promise<{ success: boolean; autoRefresh: boolean }> {
    await this.propertyRepository.update(propertyId, {
      autoRefresh: toggleData.autoRefresh,
    });

    return {
      success: true,
      autoRefresh: toggleData.autoRefresh,
    };
  }

  private async getActiveProperties(): Promise<Property[]> {
    return await this.propertyRepository.find({
      where: {
        status: PropertyStatus.LISTED,
        autoRefresh: true,
      },
      relations: ['propertyImages'],
      select: ['id', 'displayName', 'description'],
    });
  }

  private async refreshSingleProperty(property: Property): Promise<void> {
    let newDescription: string | null = null;
    if (property.description && property.description.trim().length > 0) {
      try {
        newDescription = await this.descriptionService.rephraseDescription(property.description);
      } catch (error) {
        console.warn(`Could not rephrase description for property ${property.id}: ${error.message}`);
      }
    }

    const shuffledImages = await this.shuffleLastImages(property);

    if (newDescription) {
      await this.propertyRepository.update(property.id, {
        description: newDescription,
      });
    }

    if (shuffledImages.length > 0) {
      await this.propertyImagesService.updateImageOrder(property.id, shuffledImages);
    }
  }

  private async shuffleLastImages(property: Property): Promise<FileDto[]> {
    const freshProperty = await this.propertyRepository.findOne({
      where: { id: property.id },
      relations: ['propertyImages', 'propertyImages.file'],
      select: ['id'],
    });

    if (!freshProperty) {
      return [];
    }

    const propertyImages = await freshProperty.propertyImages;

    const images = propertyImages.filter((pi) => !!pi?.file).map((pi) => pi.file);

    if (!images || images.length <= 2) {
      return [];
    }

    const imageDtos = images
      .filter(Boolean)
      .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
      .map((image) => File.convertToDto(image));

    if (imageDtos.length <= 2) {
      return [];
    }

    const numImagesToShuffle = Math.min(4, Math.max(2, Math.floor(imageDtos.length * 0.3)));
    const startIndex = imageDtos.length - numImagesToShuffle;

    const imagesToShuffle = imageDtos.slice(startIndex);
    const staticImages = imageDtos.slice(0, startIndex);

    if (imagesToShuffle.length >= 2) {
      const temp = imagesToShuffle[imagesToShuffle.length - 1];
      imagesToShuffle[imagesToShuffle.length - 1] = imagesToShuffle[imagesToShuffle.length - 2];
      imagesToShuffle[imagesToShuffle.length - 2] = temp;
    }

    if (imagesToShuffle.length >= 3) {
      const firstImage = imagesToShuffle.shift()!;
      imagesToShuffle.push(firstImage);
    }

    const reorderedImages = [...staticImages, ...imagesToShuffle];

    reorderedImages.forEach((image, index) => {
      image.order = index;
    });

    return reorderedImages;
  }

  async listProperty(property: Property, syndicationDto: SaveSyndicationDto): Promise<Property> {
    if (syndicationDto.zillow) {
      await this.syndicationService.addToDatafeed(property, SyndicationPlatform.ZILLOW);
    }

    await this.propertyRepository
      .createQueryBuilder()
      .update({ status: PropertyStatus.LISTED, isAvailable: true, lastListedAt: new Date() })
      .where({ id: property.id })
      .execute();

    const updatedProperty = await this.propertyService.findById(property.id, true, true, true);
    const owner = await updatedProperty.owner;
    const user = await owner.user;

    const address = (await property.location).address;
    this.slackService.sendNewUserRegisteredMessage(`✍🏻 "${address}" is now listed by ${user.name}.`);

    return updatedProperty;
  }

  async getSyndicationPlatforms(propertyId: string): Promise<{ platforms: string[] }> {
    const platforms = await this.syndicationService.getSyndicationPlatformsByProperty(propertyId);
    return { platforms };
  }

  async updateSyndication(
    propertyId: string,
    syndicationDto: SaveSyndicationDto,
  ): Promise<{ success: boolean; platforms: string[] }> {
    const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
    if (!property) {
      throw new Error(`Property with ID ${propertyId} not found`);
    }

    // Handle Zillow syndication
    if (syndicationDto.zillow) {
      // Add to data feed if true and not already there
      await this.syndicationService.addToDatafeed(property, SyndicationPlatform.ZILLOW);
    } else {
      // Remove from data feed if false
      await this.syndicationService.removeFromDatafeed(propertyId, SyndicationPlatform.ZILLOW);
    }

    // Get updated platforms list
    const platforms = await this.syndicationService.getSyndicationPlatformsByProperty(propertyId);

    return {
      success: true,
      platforms,
    };
  }

  async pauseProperty(propertyId: string): Promise<Property> {
    await this.stopListing(propertyId, true);

    const result = await this.propertyRepository
      .createQueryBuilder()
      .update({ status: PropertyStatus.UNLISTED })
      .where({ id: propertyId })
      .returning('*')
      .execute();

    const property = Object.assign(new Property(), { ...result.raw[0] });

    this.transUnionService
      .updateProperty(property)
      .catch((error) => console.error(`Could not update trans union property. Error: ${error.message}`));

    const updatedProperty = await this.propertyService.findById(property.id, true, true, true);
    const owner = await updatedProperty.owner;
    const user = await owner.user;

    const address = result?.raw[0]?.displayName;
    this.slackService.sendNewUserRegisteredMessage(`⏸️ "${address}" listing has been paused by ${user.name}.`);

    return updatedProperty;
  }

  async markAsRentedOut(propertyId: string): Promise<Property> {
    await this.stopListing(propertyId);

    const result = await this.propertyRepository
      .createQueryBuilder()
      .update({ status: PropertyStatus.RENTED_OUT, isAvailable: false })
      .where({ id: propertyId })
      .returning('*')
      .execute();

    const address = result?.raw[0]?.displayName;

    this.showingService
      .cancelAllShowingsByProperty(propertyId, 'Property has been rented out')
      .catch((e: Error) => console.error('Error cancelling showings', e.message));

    this.propertyInquiryService.findAllActiveInquiries(propertyId).then((inquiries) => {
      inquiries.forEach((inquiry) => {
        inquiry.stage = RentStageEnum.STOPPED_PROPERTY_RENTED_OUT;
      });

      this.propertyInquiryService.save(inquiries);
    });

    const propertyForTransUnion = Object.assign(new Property(), { ...result.raw[0] });

    this.transUnionService
      .updateProperty(propertyForTransUnion)
      .catch((error) => console.error(`Could not update trans union property. Error: ${error.message}`));

    const updatedProperty = await this.propertyService.findById(propertyId, true, true, true);
    const owner = await updatedProperty.owner;
    const user = await owner.user;

    this.slackService.sendNewUserRegisteredMessage(`✔️ "${address}" is marked as rented by ${user.name}.`);

    return updatedProperty;
  }

  async stopListing(propertyId: string, keepInvestorFollowups = false): Promise<void> {
    this.followUpService.deleteFollowUpsByProperty(propertyId, keepInvestorFollowups);

    try {
      await this.syndicationService.removeFromDatafeed(propertyId);
    } catch (error) {
      console.error('Error deleting from datafeed', error.message);
    }
  }
}

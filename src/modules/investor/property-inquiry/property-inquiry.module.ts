import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PropertyInquiry } from './property-inquiry.entity';
import { PropertyInquiryService } from './property-inquiry.service';
import { ConversationModule } from '../../shared/communication/conversation/conversetion.module';
import { PropertyInquiryEvent } from './events/property-inquiry-event.entity';
import { InvestorEntityModule } from '../investor/investor-entity.module';
import { PropertyInquiryController } from './property-inquiry.controller';
import { RenterModule } from '../../renter/renter/renter.module';
import { AiObjectiveModule } from '../../renter/renter-communication/ai-objective/ai-objective.module';
import { RenterScreeningModule } from '../../renter/renter-screening/renter-screening.module';

@Module({
  controllers: [PropertyInquiryController],
  imports: [
    TypeOrmModule.forFeature([PropertyInquiry, PropertyInquiryEvent]),
    ConversationModule,
    InvestorEntityModule,
    RenterModule,
    forwardRef(() => RenterScreeningModule),
    forwardRef(() => AiObjectiveModule),
  ],
  providers: [PropertyInquiryService],
  exports: [PropertyInquiryService],
})
export class PropertyInquiryModule {}

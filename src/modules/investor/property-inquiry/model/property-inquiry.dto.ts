import { RentStageEnum } from '../../../shared/communication/conversation/enums/rent-stage.enum';
import { PropertyInquirySource } from '../property-inquiry-source.enum';
import { RenterDto } from '../../../renter/renter/renter.dto';
import { PropertyDto } from '../../property/property/model/property.dto';
import { IsDate, IsEnum, IsObject, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class PropertyInquiryDto {
  @IsString()
  id!: string;

  @IsEnum(RentStageEnum)
  @ApiProperty({ enum: RentStageEnum, enumName: 'RentStageEnum' })
  stage: RentStageEnum;

  @IsObject()
  @ApiProperty()
  renter: RenterDto;

  @IsObject()
  @ApiProperty()
  property: PropertyDto;

  @IsEnum(PropertyInquirySource)
  @ApiProperty({ enum: PropertyInquirySource, enumName: 'PropertyInquirySource' })
  source: PropertyInquirySource;

  @IsDate()
  @ApiProperty()
  createdAt: Date;
}

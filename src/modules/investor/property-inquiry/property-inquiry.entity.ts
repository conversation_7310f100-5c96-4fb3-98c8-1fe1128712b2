import { Expose, instanceToPlain } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Renter } from '../../renter/renter/renter.entity';
import { Conversation } from '../../shared/communication/conversation/entities/conversation.entity';
import { RentStageEnum } from '../../shared/communication/conversation/enums/rent-stage.enum';
import { Property } from '../property/property/entities/property.entity';
import { ShowingRequest } from '../showing-request/showing-request.entity';
import { PropertyInquiryEventTypeEnum } from './events/property-inquiry-event-type.enum';
import { PropertyInquiryEvent } from './events/property-inquiry-event.entity';
import { PropertyInquiryDto } from './model/property-inquiry.dto';
import { PropertyInquirySource } from './property-inquiry-source.enum';
import { AiObjective } from '../../renter/renter-communication/ai-objective/ai-objective.entity';
import {
  aiObjectivePriorityOrder,
  AiObjectiveType,
} from '../../renter/renter-communication/ai-objective/ai-objective-type.enum';
import { PropertyDto } from '../property/property/model/property.dto';

@Entity()
export class PropertyInquiry {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Expose()
  @Column({
    type: 'enum',
    enum: RentStageEnum,
    default: RentStageEnum.INITIAL_CONTACT,
    nullable: true,
  })
  stage: RentStageEnum;

  @Index()
  @ManyToOne(() => Renter, {
    eager: false,
    lazy: true,
  })
  @Expose()
  @JoinColumn()
  renter: Promise<Renter> | Renter;

  @Index()
  @ManyToOne(() => Property, {
    eager: false,
    lazy: true,
  })
  @Expose()
  @JoinColumn()
  property: Promise<Property> | Property;

  @OneToOne(() => Conversation, {
    lazy: true,
    nullable: false,
  })
  @JoinColumn()
  conversation: Promise<Conversation> | Conversation;

  @OneToOne(() => ShowingRequest, {
    lazy: true,
  })
  @JoinColumn()
  showingRequest: Promise<ShowingRequest> | ShowingRequest;

  @OneToMany(() => PropertyInquiryEvent, (event) => event.propertyInquiry, {
    lazy: true,
    cascade: true,
  })
  events: Promise<PropertyInquiryEvent[]> | PropertyInquiryEvent[];

  @OneToMany(() => AiObjective, (aiObjective) => aiObjective.propertyInquiry, {
    eager: false,
    lazy: true,
    cascade: true,
  })
  aiObjectives: Promise<AiObjective[]> | AiObjective[];

  @Expose()
  @Column({ type: 'enum', enum: PropertyInquirySource, default: PropertyInquirySource.ZILLOW })
  source: PropertyInquirySource;

  @Expose()
  @Column({ type: 'boolean', nullable: true, default: null })
  passedInitialScreening: boolean;

  @DeleteDateColumn()
  deletedAt?: Date;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  static async getEvents(inquiry: PropertyInquiry): Promise<PropertyInquiryEventTypeEnum[]> {
    const events = await inquiry.events;
    return events?.length ? events.map((event) => event.type) : [];
  }

  static async didEventHappen(inquiry: PropertyInquiry, event: PropertyInquiryEventTypeEnum): Promise<boolean> {
    return (await PropertyInquiry.getEvents(inquiry)).includes(event);
  }

  static getCurrentPriorityObjective(objectives: AiObjective[]): AiObjective | null {
    const pendingObjectives = objectives.filter((objective) => !objective.completed);

    if (pendingObjectives.length === 0) {
      return null;
    }

    // Find the highest priority objective that is not completed
    for (const priorityType of aiObjectivePriorityOrder) {
      const objective = pendingObjectives.find((obj) => obj.type === priorityType);
      if (objective) {
        return objective;
      }
    }

    // If no priority matches found, return the first incomplete objective
    return pendingObjectives[0];
  }

  static async hasPendingObjectives(inquiry: PropertyInquiry): Promise<boolean> {
    const objectives = await inquiry.aiObjectives;

    if (!objectives?.length) {
      return false;
    }

    return objectives.some((objective) => !objective.completed);
  }

  static async getPendingObjectives(inquiry: PropertyInquiry): Promise<AiObjective[]> {
    return (await inquiry.aiObjectives).filter((objective) => !objective.completed);
  }

  static async getPendingObjectiveByType(inquiry: PropertyInquiry, type: AiObjectiveType): Promise<AiObjective> {
    const pendingObjectives = await PropertyInquiry.getPendingObjectives(inquiry);
    return pendingObjectives.find((objective) => objective.type === type) || null;
  }

  static async convertToDto(inquiry: PropertyInquiry): Promise<PropertyInquiryDto> {
    const inquiryDto = <PropertyInquiryDto>instanceToPlain(inquiry, {
      excludeExtraneousValues: true,
    });

    inquiryDto.property = <PropertyDto>instanceToPlain(await inquiry.property, {
      excludeExtraneousValues: true,
    });
    inquiryDto.renter = Renter.convertToRenterDto(await inquiry.renter);

    return inquiryDto;
  }

  static async isOnlyShowingObjectiveLeft(inquiry: PropertyInquiry): Promise<boolean> {
    const pendingObjectives = await PropertyInquiry.getPendingObjectives(inquiry);

    if (pendingObjectives.length !== 1) {
      return false;
    }

    const onlyObjective = pendingObjectives[0];
    return onlyObjective.type === AiObjectiveType.SCHEDULE_SHOWING;
  }
}

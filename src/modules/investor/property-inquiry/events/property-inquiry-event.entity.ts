import { Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Expose } from 'class-transformer';
import { PropertyInquiry } from '../property-inquiry.entity';
import { PropertyInquiryEventTypeEnum } from './property-inquiry-event-type.enum';

@Entity()
export class PropertyInquiryEvent {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Expose()
  @ManyToOne(() => PropertyInquiry, (propertyInquiry) => propertyInquiry.events, {
    eager: true,
    nullable: false,
  })
  propertyInquiry: PropertyInquiry;

  @Column({
    type: 'enum',
    enum: PropertyInquiryEventTypeEnum,
    nullable: false,
  })
  type: PropertyInquiryEventTypeEnum;

  @Column({ type: 'boolean', default: false })
  public isDeleted: boolean;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;
}

export enum PropertyInquiryEventTypeEnum {
  WAS_ASKED_IF_HAS_REQUIREMENTS_QUESTIONS = 'Was asked if renter has requirements questions',
  WAS_SHARED_REQUIREMENTS = 'Was shared requirements',
  REQUESTED_SHOWING = 'Requested showing',
  SHOWING_DECLINED = 'Showing declined',
  SHOWING_CONFIRMED = 'Showing confirmed',
  RENTER_AGREED_TO_RESCHEDULE = 'Renter Agreed to Reschedule',
  RENTER_AGREED_TO_SHOWING_TIME_DIFFERENT_FROM_RESCHEDULE_REQUEST = 'Renter Agreed to Showing Time Different From Reschedule Request',
  SHOWING_REMINDER_SENT = 'Showing reminder sent',
  SHOWING_COMPLETED = 'Showing completed',
  SHOWING_RESCHEDULE_REQUEST_BY_OWNER = 'Showing reschedule request by Owner',
  SHOWING_RESCHEDULE_REQUEST_BY_RENTER = 'Showing reschedule request by Ren<PERSON>',
  SHOWING_CANCELLED_BY_OWNER = 'Showing cancelled by Owner',
  SHOWING_CANCELLED_BY_RENTER = 'Showing canceled by Ren<PERSON>',
  PROPERTY_RENTED_OUT_BY_OWNER = 'Property rented out by Owner',
  SHOWING_REQUEST_IGNORED_BY_OWNER = 'Showing Request Ignored by Owner',
  APPLICATION_INVITE_SENT = 'Application Invite Sent',
  APPLICATION_STARTED = 'Application Started',
  APPLICATION_COMPLETED = 'Application Completed',
  APPLICATION_ACCEPTED = 'Application Accepted',
  APPLICATION_REJECTED = 'Application Rejected',
  APPLICATION_SUBMITTED = 'Application Submitted',
}

import { Controller, Get, Param, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../shared/auth/models/roles-enum';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { PropertyInquiryService } from './property-inquiry.service';
import { PropertyInquiryDto } from './model/property-inquiry.dto';
import { PropertyInquiry } from './property-inquiry.entity';
import { ConversationDto } from '../../shared/communication/conversation/conversation.dto';
import { TimeFilter } from '../property/statistics/time-filter.enum';

@ApiTags('property-inquiry')
@Controller('property-inquiry')
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
@ApiBearerAuth()
export class PropertyInquiryController {
  constructor(private readonly propertyInquiryService: PropertyInquiryService) {}

  @Get('inquiries/renter/:renterId')
  @ApiOkResponse({
    description: 'Returns renter inquiries for the properties that belong to the investor',
  })
  async getInquiriesByRenter(@Req() req: Request, @Param('renterId') renterId: string): Promise<PropertyInquiryDto[]> {
    const inquiries = await this.propertyInquiryService.findAllByRenterAndOwner(renterId, req.user.id);

    const dtoPromises = inquiries.map((inquiryItem) => PropertyInquiry.convertToDto(inquiryItem));
    return Promise.all(dtoPromises);
  }

  @Get('conversation/:inquiryId')
  @ApiOkResponse({
    description: 'Returns conversation for the property and the renter',
  })
  async getConversationByPropertyAndUser(@Param('inquiryId') inquiryId: string): Promise<ConversationDto> {
    return this.propertyInquiryService.getInquiryConversation(inquiryId);
  }

  @Get('inquiries/property/:propertyId')
  @ApiOkResponse({
    description: 'Returns all inquiries for the property',
  })
  async getInquiriesByProperty(
    @Param('propertyId') propertyId: string,
    @Query('filter') filter: TimeFilter,
  ): Promise<PropertyInquiryDto[]> {
    const inquiries = await this.propertyInquiryService.findAllByProperty(propertyId, false, true, false, filter);

    const dtoPromises = inquiries.map((inquiryItem) => PropertyInquiry.convertToDto(inquiryItem));
    return Promise.all(dtoPromises);
  }
}

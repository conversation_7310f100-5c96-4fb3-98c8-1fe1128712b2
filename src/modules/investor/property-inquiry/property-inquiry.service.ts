import { In, <PERSON><PERSON>han, Repository } from 'typeorm';

import { forwardRef, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { FilterUtils } from '../../../utils/filter.utils';
import { AiObjectiveService } from '../../renter/renter-communication/ai-objective/ai-objective.service';
import { Renter } from '../../renter/renter/renter.entity';
import { RenterService } from '../../renter/renter/renter.service';
import { ConversationDto } from '../../shared/communication/conversation/conversation.dto';
import { ConversationService } from '../../shared/communication/conversation/conversation.service';
import { RentStageEnum } from '../../shared/communication/conversation/enums/rent-stage.enum';
import { InboundEmailsSubjects } from '../../shared/communication/inbound-communication/enums/inbound-emails-subjects';
import { InvestorService } from '../investor/investor.service';
import { Property } from '../property/property/entities/property.entity';
import { TimeFilter } from '../property/statistics/time-filter.enum';
import { PropertyInquiryEventTypeEnum } from './events/property-inquiry-event-type.enum';
import { PropertyInquiryEvent } from './events/property-inquiry-event.entity';
import { PropertyInquirySource } from './property-inquiry-source.enum';
import { PropertyInquiry } from './property-inquiry.entity';
import { AiObjectiveType } from '../../renter/renter-communication/ai-objective/ai-objective-type.enum';
import { IncomeEnum } from '../property/property/property-details/renter-requirements/income.enum';
import { activeInquiryStages } from './configs/ative-inquiry-stages.list';
import { AiObjective } from '../../renter/renter-communication/ai-objective/ai-objective.entity';
import { RenterScreeningService } from '../../renter/renter-screening/renter-screening.service';
import { RenterScreening } from '../../renter/renter-screening/renter-screening.entity';

@Injectable()
export class PropertyInquiryService {
  constructor(
    @InjectRepository(PropertyInquiry)
    private readonly propertyInquiryEntityRepository: Repository<PropertyInquiry>,
    @InjectRepository(PropertyInquiryEvent)
    private readonly propertyInquiryEventRepository: Repository<PropertyInquiryEvent>,
    private readonly conversationService: ConversationService,
    private readonly investorService: InvestorService,
    private readonly renterService: RenterService,
    @Inject(forwardRef(() => AiObjectiveService))
    private readonly aiObjectivesService: AiObjectiveService,
    private readonly renterScreeningService: RenterScreeningService,
  ) {}

  async findById(id: string, withShowing?: boolean, withConversation?: boolean): Promise<PropertyInquiry> {
    const relations = [];

    if (withShowing) relations.push('showing');
    if (withConversation) relations.push('conversation');

    return this.propertyInquiryEntityRepository.findOneOrFail({
      where: { id },
      relations,
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async findAllActiveInquiries(propertyId: string): Promise<PropertyInquiry[]> {
    return this.propertyInquiryEntityRepository.find({
      where: {
        property: { id: propertyId },
        stage: In(activeInquiryStages),
      },
    });
  }

  async findAllByRenterAndOwner(renterId: string, investorUserId: string): Promise<PropertyInquiry[]> {
    const investor = await this.investorService.findByUserId(investorUserId);
    const company = await investor.company;

    return this.propertyInquiryEntityRepository.find({
      where: {
        renter: { id: renterId },
        property: {
          company: { id: company.id },
        },
      },
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async findLatestByRenterAndProperty(renterId: string, propertyId: string): Promise<PropertyInquiry> {
    if (!propertyId) {
      return this.findLatestByRenter(renterId);
    } else {
      return this.findByRenterAndProperty(renterId, propertyId);
    }
  }

  async findByRenterAndProperty(
    renterId: string,
    propertyId: string,
    withConversation?: boolean,
    withShowingRequest?: boolean,
  ): Promise<PropertyInquiry> {
    const relations = [];

    if (withConversation) relations.push('conversation');
    if (withShowingRequest) relations.push('showingRequest');

    return this.propertyInquiryEntityRepository.findOne({
      where: {
        renter: { id: renterId },
        property: { id: propertyId },
      },
      relations,
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async findLatestByRenterPhoneNumber(phoneNumber: string): Promise<PropertyInquiry> {
    const renter = await this.renterService.findByPhoneNumber(phoneNumber);

    if (!renter) {
      throw new NotFoundException(`Renter not found: ${phoneNumber}`);
    }

    return await this.findLatestByRenter(renter.id);
  }

  async addEvent(inquiryId: string, inquiryEvent: PropertyInquiryEventTypeEnum): Promise<void> {
    await this.propertyInquiryEventRepository.insert({
      propertyInquiry: { id: inquiryId },
      type: inquiryEvent,
    });
  }

  async getInquiryConversation(inquiryId: string): Promise<ConversationDto> {
    const inquiry = await this.findById(inquiryId, false, true);

    return this.conversationService.convertToDto(await inquiry.conversation, true);
  }

  async findLatestByRenter(
    renterId: string,
    withConversation?: boolean,
    withShowingRequests?: boolean,
  ): Promise<PropertyInquiry> {
    const relations = [];

    if (withConversation) relations.push('conversation');
    if (withShowingRequests) relations.push('showingRequest');

    return this.propertyInquiryEntityRepository.findOne({
      where: {
        renter: { id: renterId },
      },
      relations,
      order: {
        updatedAt: 'DESC',
      },
    });
  }

  async createNewInquiry(
    property: Property,
    renter: Renter,
    renterMessage: string = null,
    source: PropertyInquirySource,
  ): Promise<PropertyInquiry> {
    const company = await property.company;
    const companySettings = company.settings;
    const location = await property.location;
    const conversation = await this.conversationService.create([renter.user], property);
    conversation.emailMetadata = await this.conversationService.updateEmailMetadata(conversation, {
      subject: `${InboundEmailsSubjects.NEW_PROPERTY_MESSAGE} ${location.address}${location.apartmentNumber ? ' #' + location.apartmentNumber : ''}`,
      previousText: renterMessage,
    });

    const inquiry = this.propertyInquiryEntityRepository.create();
    inquiry.property = property;
    inquiry.renter = renter;
    inquiry.conversation = conversation;
    inquiry.source = source;
    inquiry.stage = property.isAvailable ? RentStageEnum.INITIAL_CONTACT : RentStageEnum.STOPPED_PROPERTY_RENTED_OUT;

    if (companySettings.advancedRenterScreening) {
      await this.attachScreeningAndAddObjectivesToNewlyCreatedInquiry(inquiry, renter, property);
    }

    return this.propertyInquiryEntityRepository.save(inquiry);
  }

  async findAllByProperty(
    propertyId: string,
    withRenter = false,
    withProperty = false,
    withShowingRequests = false,
    filter?: TimeFilter,
  ): Promise<PropertyInquiry[]> {
    const relations = [];

    const startDate = FilterUtils.getStartDateByFilter(filter);

    if (withRenter) relations.push('renter');
    if (withProperty) relations.push('property');
    if (withShowingRequests) relations.push('showingRequest');

    return this.propertyInquiryEntityRepository.find({
      where: {
        property: { id: propertyId },
        createdAt: MoreThan(startDate),
      },
      relations,
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async update(id: string, inquiryData: Partial<PropertyInquiry>): Promise<void> {
    await this.propertyInquiryEntityRepository.update(id, inquiryData);
  }

  async save(inquiries: PropertyInquiry[]): Promise<PropertyInquiry[]> {
    return this.propertyInquiryEntityRepository.save(inquiries);
  }

  async delete(id: string): Promise<void> {
    await this.propertyInquiryEntityRepository.softDelete(id);
  }

  async markRenterAsUnqualified(inquiry: PropertyInquiry): Promise<void> {
    const requirementsNotMetStatus = RentStageEnum.REQUIREMENTS_NOT_MET;
    await this.update(inquiry.id, {
      stage: requirementsNotMetStatus,
      passedInitialScreening: false,
    });

    inquiry.stage = requirementsNotMetStatus;
  }

  async markRenterAsQualified(inquiry: PropertyInquiry): Promise<void> {
    const initialContactStatus = RentStageEnum.INITIAL_CONTACT;
    await this.update(inquiry.id, {
      stage: initialContactStatus,
      passedInitialScreening: true,
    });

    inquiry.stage = initialContactStatus;
  }

  async updateRentStageAndAddEvent(
    id: string,
    stage: RentStageEnum,
    event: PropertyInquiryEventTypeEnum,
  ): Promise<void> {
    await this.updateRentStage(id, stage);
    await this.addEvent(id, event);
  }

  async updateRentStage(id: string, stage: RentStageEnum): Promise<void> {
    console.log(`[Rent Stage] Updated to "${stage}"`);
    await this.propertyInquiryEntityRepository.update(id, { stage });
  }

  async updateStageAndAddCorrespondingEvent(id: string, stage: RentStageEnum): Promise<void> {
    switch (stage) {
      case RentStageEnum.SHOWING_REQUESTED:
        await this.addEvent(id, PropertyInquiryEventTypeEnum.REQUESTED_SHOWING);
        break;
      case RentStageEnum.SHOWING_DECLINED:
        await this.addEvent(id, PropertyInquiryEventTypeEnum.SHOWING_DECLINED);
        break;
      case RentStageEnum.SHOWING_CONFIRMED:
        await this.addEvent(id, PropertyInquiryEventTypeEnum.SHOWING_CONFIRMED);
        break;
      case RentStageEnum.SHOWING_COMPLETED:
        await this.addEvent(id, PropertyInquiryEventTypeEnum.SHOWING_COMPLETED);
        break;
      case RentStageEnum.SHOWING_CANCELLED_BY_OWNER:
        await this.addEvent(id, PropertyInquiryEventTypeEnum.SHOWING_CANCELLED_BY_OWNER);
        break;
      case RentStageEnum.SHOWING_CANCELLED_BY_RENTER:
        await this.addEvent(id, PropertyInquiryEventTypeEnum.SHOWING_CANCELLED_BY_RENTER);
        break;
      case RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER:
        await this.addEvent(id, PropertyInquiryEventTypeEnum.SHOWING_RESCHEDULE_REQUEST_BY_OWNER);
        break;
      case RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_RENTER:
        await this.addEvent(id, PropertyInquiryEventTypeEnum.SHOWING_RESCHEDULE_REQUEST_BY_RENTER);
        break;
      case RentStageEnum.STOPPED_PROPERTY_RENTED_OUT:
        await this.addEvent(id, PropertyInquiryEventTypeEnum.PROPERTY_RENTED_OUT_BY_OWNER);
        break;
      case RentStageEnum.SHOWING_REQUEST_IGNORED_BY_OWNER:
        await this.addEvent(id, PropertyInquiryEventTypeEnum.SHOWING_REQUEST_IGNORED_BY_OWNER);
        break;
    }

    await this.updateRentStage(id, stage);
  }

  private async attachScreeningAndAddObjectivesToNewlyCreatedInquiry(
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
  ): Promise<PropertyInquiry> {
    const aiObjectives: AiObjective[] = [];

    let screening: RenterScreening | null = null;
    try {
      screening = await this.renterScreeningService.getScreeningForRenter(renter, true);
    } catch (error) {
      console.error('Error creating renter screening:', error);
    }

    // Determine which objectives to create based on the screening status
    let createMoveInDateObjective = true;
    let createCreditScoreObjective = true;
    let createIncomeObjective = true;

    if (screening) {
      try {
        const objectivesRequired = await this.renterScreeningService.determineRequiredObjectives(screening);

        if (objectivesRequired.resetScreeningValues) {
          await this.renterScreeningService.resetScreeningValues(screening);
        }

        createMoveInDateObjective = objectivesRequired.createMoveInDateObjective;
        createCreditScoreObjective = objectivesRequired.createCreditScoreObjective;
        createIncomeObjective = objectivesRequired.createIncomeObjective;
      } catch (error) {
        console.error('Error checking renter screening status:', error);
        // If there's an error, proceed with creating all objectives as a fallback
      }
    }

    if (createMoveInDateObjective) {
      aiObjectives.push(this.aiObjectivesService.create(AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE));
    }

    const renterRequirements = await property.renterRequirements;

    if (createCreditScoreObjective && renterRequirements?.minimumCreditScore) {
      aiObjectives.push(this.aiObjectivesService.create(AiObjectiveType.EVALUATE_CREDIT_SCORE));
    }

    if (
      createIncomeObjective &&
      renterRequirements?.minimumIncome &&
      renterRequirements.minimumIncome !== IncomeEnum.NO_REQUIREMENT
    ) {
      aiObjectives.push(this.aiObjectivesService.create(AiObjectiveType.EVALUATE_INCOME));
    }

    aiObjectives.push(this.aiObjectivesService.create(AiObjectiveType.SCHEDULE_SHOWING));

    inquiry.aiObjectives = aiObjectives;

    return inquiry;
  }
}

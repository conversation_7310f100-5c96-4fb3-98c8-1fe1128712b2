import { instance<PERSON><PERSON><PERSON><PERSON> } from 'class-transformer';
import { In, Repository } from 'typeorm';
import { z } from 'zod';

import { HttpException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { AiService } from '../../ai/ai.service';
import { LanguageModelsEnum } from '../../ai/enums/language-models.enum';
import { ChatHistoryBuilder } from '../../ai/utils/chat.utils';
import { RenterDto } from '../../renter/renter/renter.dto';
import { AiResponses } from '../../renter/renter-communication/response-generation/enums/ai-responses';
import { checkIfSimilarQuestionWasAskedBeforeTemplate } from '../../renter/renter-communication/response-generation/prompts/check-if-similar-question-was-asked-before.template';
import { turnUserInputIntoTopicQuestionFormatTemplate } from '../../renter/renter-communication/response-generation/prompts/turn-user-input-into-topic-question-format.template';
import { Renter } from '../../renter/renter/renter.entity';
import { ConversationService } from '../../shared/communication/conversation/conversation.service';
import { MessageService } from '../../shared/communication/conversation/message/message.service';
import { OutboundCommunicationService } from '../../shared/communication/outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../shared/communication/outbound-communication/slack/slack-convo-message-builder.class';
import { Property } from '../property/property/entities/property.entity';
import { convertPropertyToDtoWithTypes } from '../property/property/model/property-with-types.dto';
import { PropertyService } from '../property/property/property.service';
import { IntelligentEscalation } from './entities/property-question.entity';
import { IntelligentEscalationStatus } from './enum/intelligent-escalation.status';
import { PropertyQuestionDto } from './model/property-question.dto';
import { answerPropertyQuestionTemplate } from './prompts/answer-property-question.template';
import { ConditionalAiInstruction } from '../../ai/prompts/ai-instructions';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import { RentStageEnum } from '../../shared/communication/conversation/enums/rent-stage.enum';
import { MessageType } from '../../shared/communication/conversation/message/message-type.enum';
import { StructuredOutputParser } from '@langchain/core/output_parsers';

@Injectable()
export class IntelligentEscalationService {
  constructor(
    @InjectRepository(IntelligentEscalation)
    private readonly intelligentEscalationRepository: Repository<IntelligentEscalation>,
    private readonly aiService: AiService,
    private readonly notificationService: OutboundCommunicationService,
    private readonly conversationService: ConversationService,
    private readonly messageService: MessageService,
    private readonly propertyService: PropertyService,
    private readonly slackCommunicationService: SlackCommunicationService,
    private readonly propertyInquiryService: PropertyInquiryService,
  ) {}

  async getIntelligentEscalations(property: Property): Promise<PropertyQuestionDto[]> {
    const questions = await this.findPendingByProperty(property.id);

    return Promise.all(questions.map((question) => this.convertToDto(question, true)));
  }

  async answerIntelligentEscalation(escalationId: string, answer: string): Promise<void> {
    const escalation = await this.intelligentEscalationRepository.findOneBy({
      id: escalationId,
    });

    if (!escalation) {
      throw new HttpException(`Failed to find question by id: ${escalationId}`, 500);
    }

    const property = await this.propertyService.findById((await escalation.property).id, true, false, false);
    const renters = await escalation.renters;

    this.notifyRentersAboutOwnerAnswer(escalation, answer, renters)
      .then(() => console.log('Notified renters about owner answer'))
      .catch((error) => console.error('Failed to notify renters about owner answer', error));

    await this.intelligentEscalationRepository.update(escalation.id, {
      status: IntelligentEscalationStatus.ANSWERED,
    });

    const chatHistory = new ChatHistoryBuilder();

    chatHistory.appendAiMessage(escalation.questionText);
    chatHistory.appendUserMessage(answer);

    this.aiService
      .parseInputToJson(
        chatHistory.build(),
        JSON.stringify(await convertPropertyToDtoWithTypes(property, true)),
        LanguageModelsEnum.CLAUDE_4_SONNET,
      )
      .then((parsedInput) => {
        this.propertyService.updatePropertyFromFieldValues(property, parsedInput).catch((error) => {
          console.error(
            `
          Error when trying to update property from owner answer.
          Error: "${error.message}".
          Update payload: "${JSON.stringify(parsedInput)}"
        `,
            error,
          );
        });
      });
  }

  async ignoreIntelligentEscalation(escalationId: string): Promise<void> {
    const escalation = await this.intelligentEscalationRepository.findOneBy({
      id: escalationId,
    });

    if (!escalation) {
      throw new NotFoundException(`Failed to find escalation by id: ${escalationId}`);
    }

    await this.intelligentEscalationRepository.update(escalation.id, {
      status: IntelligentEscalationStatus.IGNORED,
    });
  }

  private async notifyRentersAboutOwnerAnswer(
    propertyQuestion: IntelligentEscalation,
    answer: string,
    renters: Renter[],
  ): Promise<void> {
    const propertyId = (await propertyQuestion.property).id;

    await Promise.all(
      renters.map(async (renter) => {
        const conversation = await this.conversationService.findByPropertyUser(propertyId, renter.user.id);

        const inquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, propertyId);
        const inactiveStages = [
          RentStageEnum.SHOWING_DECLINED,
          RentStageEnum.REQUIREMENTS_NOT_MET,
          RentStageEnum.SHOWING_CANCELLED_BY_OWNER,
          RentStageEnum.SHOWING_CANCELLED_BY_RENTER,
          RentStageEnum.SHOWING_REQUEST_IGNORED_BY_OWNER,
          RentStageEnum.STOPPED_PROPERTY_RENTED_OUT,
          RentStageEnum.NO_LONGER_INTERESTED,
        ];

        if (conversation.isEmergencyStopped || inactiveStages.includes(inquiry.stage)) {
          return;
        }

        let messageForTheRenter: string;

        const timeSinceLastMessageMs = await this.messageService.countTimeSinceLastAiMessage(conversation.id);
        const greetingInstruction = ConditionalAiInstruction.greetIf24HoursPassed(timeSinceLastMessageMs);

        try {
          messageForTheRenter = await this.aiService.getResponseWithChatMemory(
            {
              greetingInstruction,
              answer: answer,
              question: propertyQuestion.questionText,
            },
            conversation.id,
            answerPropertyQuestionTemplate,
            5,
            LanguageModelsEnum.CLAUDE_4_SONNET,
          );
        } catch (error) {
          throw new HttpException('Failed to get AI response answering question', 500, {
            cause: error.message,
          });
        }

        await this.conversationService.saveTalloMessage(
          conversation,
          messageForTheRenter,
          MessageType.TEXT,
          renter.user.preferredCommunicationChannel,
        );

        await this.notificationService.sendMessage(
          renter.user,
          messageForTheRenter,
          await conversation.emailMetadata,
          renter.user.preferredCommunicationChannel,
        );

        this.slackCommunicationService.sendMessageToConvosChannel(
          new SlackConvoMessageBuilder()
            .appendTextLine(`✅ Escalation answered | Title: "${propertyQuestion.title}"`)
            .appendEmptyLine()
            .appendRenterMessageAsBullet(propertyQuestion.questionText)
            .appendOwnerMessageAsBullet(answer)
            .appendTalloMessageAsBullet(messageForTheRenter)
            .build(),
          conversation,
        );
      }),
    );
  }

  async escalatePropertyQuestion(property: Property, question: string, renter: Renter): Promise<IntelligentEscalation> {
    const questions = await this.findAllByProperty(property.id, true);
    const existingQuestion = await this.aiService.getResponse(
      {
        question,
        previousQuestions: JSON.stringify(this.getListOfQuestions(questions)),
      },
      checkIfSimilarQuestionWasAskedBeforeTemplate,
      null,
      LanguageModelsEnum.GPT_5_MINI,
    );

    if (existingQuestion.toLowerCase().includes(AiResponses.ASKED_FIRST_TIME)) {
      return this.determineTopicWithAiAndCreateQuestion(question, property, renter);
    } else {
      const similarQuestion = questions.find(({ questionText }) => questionText === existingQuestion);
      if (!similarQuestion) {
        // handle AI error in case if similar question exists, but AI failed to return it
        console.error(
          `AI indicated similar question exists but none was found with exact same text: "${existingQuestion}"`,
        );
        return this.determineTopicWithAiAndCreateQuestion(question, property, renter);
      }

      await this.addRenterToQuestion(similarQuestion, renter);
    }
  }

  async findAllByProperty(propertyId: string, includeRenters: boolean): Promise<IntelligentEscalation[]> {
    const relations = [];

    if (includeRenters) {
      relations.push('renters');
    }

    return this.intelligentEscalationRepository.find({
      where: {
        property: { id: propertyId },
        status: In([IntelligentEscalationStatus.PENDING, IntelligentEscalationStatus.IGNORED]),
      },
      relations,
    });
  }

  async findPendingByProperty(propertyId: string): Promise<IntelligentEscalation[]> {
    return this.intelligentEscalationRepository.find({
      where: {
        property: { id: propertyId },
        status: IntelligentEscalationStatus.PENDING,
      },
      relations: ['renters'],
    });
  }

  async addRenterToQuestion(similarQuestion: IntelligentEscalation, renter: Renter): Promise<void> {
    if (!similarQuestion) {
      throw new InternalServerErrorException('Cannot add renter to undefined question');
    }

    const renters = await similarQuestion.renters;

    if (!renters.map((renter) => renter.id).includes(renter.id)) {
      similarQuestion.renters = Promise.resolve([...renters, renter]);
      await this.save(similarQuestion);
    }
  }

  async determineTopicWithAiAndCreateQuestion(
    question: string,
    property: Property,
    renter: Renter,
  ): Promise<IntelligentEscalation> {
    const newQuestionTopic = <
      {
        topic: string;
      }
    >(<unknown>await this.aiService.getResponse(
      {
        question,
      },
      turnUserInputIntoTopicQuestionFormatTemplate,
      null,
      LanguageModelsEnum.CLAUDE_4_SONNET,
      StructuredOutputParser.fromZodSchema(
        z.object({
          topic: z.string().describe('question topic'),
        }),
      ),
    ));

    return this.escalateQuestion(property, renter, question, newQuestionTopic.topic);
  }

  async escalateQuestion(
    property: Property,
    renter: Renter,
    questionText: string,
    title: string,
  ): Promise<IntelligentEscalation> {
    const propertyQuestion = await this.create({
      property,
      title,
      questionText,
      renters: [renter],
    });

    const investor = await property.owner;
    const user = await investor.user;

    await this.notificationService.sendNewPropertyQuestionNotification(
      propertyQuestion.id,
      property.id,
      user,
      renter,
      await property.location,
    );

    return propertyQuestion;
  }

  async create(questionData: Partial<IntelligentEscalation> = {}): Promise<IntelligentEscalation> {
    const escalation: IntelligentEscalation = this.intelligentEscalationRepository.create();
    Object.assign(escalation, questionData);

    return await this.intelligentEscalationRepository.save(escalation);
  }

  async save(question: IntelligentEscalation): Promise<void> {
    await this.intelligentEscalationRepository.save(question);
  }

  getListOfQuestions(questions: IntelligentEscalation[]): string[] {
    return questions.map((question) => question.questionText);
  }

  async convertToDto(propertyQuestion: IntelligentEscalation, includeRenters: boolean): Promise<PropertyQuestionDto> {
    const propertyQuestionDto = <PropertyQuestionDto>(
      instanceToPlain(propertyQuestion, { excludeExtraneousValues: true })
    );

    if (includeRenters) {
      propertyQuestionDto.renters = (await propertyQuestion.renters).map(
        (renter) => <RenterDto>instanceToPlain(renter, { excludeExtraneousValues: true }),
      );
    }

    return propertyQuestionDto;
  }
}

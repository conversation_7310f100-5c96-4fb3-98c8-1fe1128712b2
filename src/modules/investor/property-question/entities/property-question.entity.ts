import {
  Column,
  CreateDate<PERSON>olumn,
  <PERSON>tity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Expose } from 'class-transformer';
import { Property } from '../../property/property/entities/property.entity';
import { Renter } from '../../../renter/renter/renter.entity';
import { IntelligentEscalationStatus } from '../enum/intelligent-escalation.status';
import { IntelligentEscalationType } from '../enum/intelligent-escalation.type';

@Entity('property_question')
export class IntelligentEscalation {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Property, (property) => property.questions, {
    nullable: false,
    lazy: true,
  })
  property: Promise<Property> | Property;

  @Expose()
  @Column({ type: 'varchar', length: 240, nullable: false })
  title: string;

  @Expose()
  @Column({ type: 'text', nullable: false })
  questionText: string;

  @Expose()
  @Column({
    type: 'enum',
    enum: IntelligentEscalationStatus,
    default: IntelligentEscalationStatus.PENDING,
  })
  status: IntelligentEscalationStatus;

  @Expose()
  @Column({
    type: 'enum',
    enum: IntelligentEscalationType,
    default: IntelligentEscalationType.PROPERTY_QUESTION,
  })
  type: IntelligentEscalationType;

  @Expose()
  @ManyToMany(() => Renter, {
    lazy: true,
  })
  @JoinTable()
  renters: Promise<Renter[]> | Renter[];

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

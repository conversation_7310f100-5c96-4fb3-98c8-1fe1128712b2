import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IntelligentEscalation } from './entities/property-question.entity';
import { IntelligentEscalationService } from './intelligent-escalation.service';
import { AiModule } from '../../ai/ai.module';
import { OutboundCommunicationModule } from '../../shared/communication/outbound-communication/outbound-communication.module';
import { IntelligentEscalationController } from './intelligent-escalation.controller';
import { PropertyModule } from '../property/property/property.module';
import { ConversationModule } from '../../shared/communication/conversation/conversetion.module';
import { Property } from '../property/property/entities/property.entity';
import { PropertyInquiryModule } from '../property-inquiry/property-inquiry.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([IntelligentEscalation, Property]),
    AiModule,
    OutboundCommunicationModule,
    PropertyModule,
    ConversationModule,
    PropertyInquiryModule,
  ],
  controllers: [IntelligentEscalationController],
  providers: [IntelligentEscalationService],
  exports: [IntelligentEscalationService],
})
export class IntelligentEscalationModule {}

import { Body, Controller, Get, Param, Post, Put, Req, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../shared/auth/models/roles-enum';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { IntelligentEscalationService } from './intelligent-escalation.service';
import { IsPropertyOwnerGuard } from '../../../guards/investor/is-property-owner-guard';
import { PropertyQuestionDto } from './model/property-question.dto';

@ApiTags('intelligent-escalation')
@Controller('intelligent-escalation')
@HasRoles(Role.INVESTOR)
@UseGuards(RolesGuard)
export class IntelligentEscalationController {
  constructor(private readonly intelligentEscalationService: IntelligentEscalationService) {}

  @Get(':propertyId')
  @ApiOkResponse({
    description: 'Get escalations by property id',
  })
  @UseGuards(IsPropertyOwnerGuard)
  async getIntelligentEscalations(@Req() req: Request): Promise<PropertyQuestionDto[]> {
    return this.intelligentEscalationService.getIntelligentEscalations(req.property);
  }

  @Post('answer/:escalationId')
  @ApiOkResponse({
    description: 'Answer property question',
  })
  async answerIntelligentEscalation(
    @Param('escalationId') propertyQuestionId: string,
    @Body()
    answer: {
      answer: string;
    },
  ): Promise<void> {
    await this.intelligentEscalationService.answerIntelligentEscalation(propertyQuestionId, answer.answer);
  }

  @Put('ignore/:escalationId')
  @ApiOkResponse({
    description: 'Ignore intelligent escalation',
  })
  async ignoreIntelligentEscalation(@Param('escalationId') escalationId: string): Promise<void> {
    await this.intelligentEscalationService.ignoreIntelligentEscalation(escalationId);
  }
}

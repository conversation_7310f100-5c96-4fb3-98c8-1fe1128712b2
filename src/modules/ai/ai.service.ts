import * as process from 'process';
import { retryAsync } from 'ts-retry';
import { Repository } from 'typeorm';
import { z } from 'zod';

import { ChatAnthropic } from '@langchain/anthropic';
import { PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage } from '@langchain/core/messages';
import { HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Message } from '../shared/communication/conversation/message/message.entity';
import { LanguageModelsEnum, LanguageModelVersion } from './enums/language-models.enum';
import { InputVariables } from './memory/input-variables';
import { PostgresChatTypeMemory } from './memory/postgres-chat-type.memory';
import { FieldValue } from './models/field-value';
import { determineNextStepWithRelatedQuestion } from './prompts/determine-next.step';
import { parseInputTemplate } from './prompts/parse-input.template';
import { Conversation } from '../shared/communication/conversation/entities/conversation.entity';
import { NextStep } from '../renter/renter-communication/response-generation/enums/next-step';
import { ConversationGoal } from '../renter/renter-communication/response-generation/configs/rent-stage-goal/conversation.goal';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatDeepSeek } from '@langchain/deepseek';
import { RentStageEnum } from '../shared/communication/conversation/enums/rent-stage.enum';

export interface NextStepWithRelatedQuestion {
  nextStep: NextStep;
  relatedText: string;
}

@Injectable()
export class AiService {
  private readonly gpt5: ChatOpenAI;
  private readonly gpt5ReasonerLow: ChatOpenAI;
  private readonly gpt5Mini: ChatOpenAI;
  private readonly gpt5Nano: ChatOpenAI;
  private readonly gpt4Mini: ChatOpenAI;
  private readonly gpt4Model: ChatOpenAI;
  private readonly gpt4Creative: ChatOpenAI;
  private readonly claude3Haiku: ChatAnthropic;
  private readonly claude4Sonnet: ChatAnthropic;
  private readonly claude4SonnetReasoner: ChatAnthropic;
  private readonly claude4Opus: ChatAnthropic;
  private readonly deepSeekChat: ChatDeepSeek;
  private readonly deepSeekReasoner: ChatDeepSeek;
  private readonly timeoutTime = 15000;

  private modelMap: Map<any, any>;

  constructor(
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
  ) {
    this.gpt5ReasonerLow = new ChatOpenAI({
      apiKey: process.env.OPEN_AI_API_KEY,
      temperature: 1, // GPT 5 only supports "1" temperature value, otherwise it throws an 400 error
      model: LanguageModelVersion.GPT_5,
      reasoningEffort: 'low',
      timeout: this.timeoutTime,
    });
    this.gpt5 = new ChatOpenAI({
      apiKey: process.env.OPEN_AI_API_KEY,
      temperature: 1, // GPT 5 only supports "1" temperature value, otherwise it throws an 400 error
      model: LanguageModelVersion.GPT_5,
      timeout: this.timeoutTime,
    });
    this.gpt5Mini = new ChatOpenAI({
      apiKey: process.env.OPEN_AI_API_KEY,
      temperature: 1, // GPT 5 only supports "1" temperature value, otherwise it throws an 400 error
      model: LanguageModelVersion.GPT_5_MINI,
      timeout: this.timeoutTime,
    });
    this.gpt5Nano = new ChatOpenAI({
      apiKey: process.env.OPEN_AI_API_KEY,
      temperature: 1, // GPT 5 only supports "1" temperature value, otherwise it throws an 400 error
      model: LanguageModelVersion.GPT_5_NANO,
      timeout: this.timeoutTime,
    });
    this.gpt4Mini = new ChatOpenAI({
      apiKey: process.env.OPEN_AI_API_KEY,
      temperature: 0,
      model: LanguageModelVersion.GPT_4_MINI,
      timeout: this.timeoutTime,
    });
    this.gpt4Model = new ChatOpenAI({
      apiKey: process.env.OPEN_AI_API_KEY,
      temperature: 0,
      model: LanguageModelVersion.GPT_4,
      timeout: this.timeoutTime,
    });
    this.gpt4Creative = new ChatOpenAI({
      apiKey: process.env.OPEN_AI_API_KEY,
      temperature: 0.6,
      model: LanguageModelVersion.GPT_4,
      timeout: this.timeoutTime,
    });
    this.claude3Haiku = new ChatAnthropic({
      apiKey: process.env.ANTHROPIC_AI_API_KEY,
      temperature: 0,
      model: LanguageModelVersion.CLAUDE_3_HAIKU,
      clientOptions: {
        timeout: this.timeoutTime,
      },
    });
    this.claude4Sonnet = new ChatAnthropic({
      apiKey: process.env.ANTHROPIC_AI_API_KEY,
      model: LanguageModelVersion.CLAUDE_4_SONNET,
      clientOptions: {
        timeout: this.timeoutTime,
      },
      temperature: 0,
    });
    this.claude4SonnetReasoner = new ChatAnthropic({
      apiKey: process.env.ANTHROPIC_AI_API_KEY,
      model: LanguageModelVersion.CLAUDE_4_SONNET,
      maxTokens: 2049,
      thinking: {
        type: 'enabled',
        budget_tokens: 2048,
      },
      clientOptions: {
        timeout: this.timeoutTime,
      },
    });
    this.claude4Opus = new ChatAnthropic({
      apiKey: process.env.ANTHROPIC_AI_API_KEY,
      temperature: 0,
      model: LanguageModelVersion.CLAUDE_4_OPUS,
      clientOptions: {
        timeout: this.timeoutTime,
      },
    });
    this.deepSeekReasoner = new ChatDeepSeek({
      apiKey: process.env.DEEP_SEEK_API_KEY,
      temperature: 0,
      model: LanguageModelVersion.DEEP_SEEK_REASONER,
      timeout: this.timeoutTime,
    });

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    this.modelMap = new Map([
      [LanguageModelsEnum.GPT_5, this.gpt5],
      [LanguageModelsEnum.GPT_5_REASONER_LOW, this.gpt5ReasonerLow],
      [LanguageModelsEnum.GPT_5_MINI, this.gpt5Mini],
      [LanguageModelsEnum.GPT_5_NANO, this.gpt5Nano],
      [LanguageModelsEnum.GPT_4_MINI, this.gpt4Mini],
      [LanguageModelsEnum.GPT_4, this.gpt4Model],
      [LanguageModelsEnum.GPT_4_CREATIVE, this.gpt4Creative],
      [LanguageModelsEnum.CLAUDE_3_HAIKU, this.claude3Haiku],
      [LanguageModelsEnum.CLAUDE_4_SONNET, this.claude4Sonnet],
      [LanguageModelsEnum.CLAUDE_4_SONNET_REASONER, this.claude4SonnetReasoner],
      [LanguageModelsEnum.CLAUDE_4_OPUS, this.claude4Opus],
      [LanguageModelsEnum.DEEP_SEEK_CHAT, this.deepSeekChat],
      [LanguageModelsEnum.DEEP_SEEK_REASONER, this.deepSeekReasoner],
    ]);
  }

  async determineNextStepWithRelatedQuestions(
    xmlFormattedMessages: string,
    possibleNextSteps: string[],
    goal: ConversationGoal,
    rentStage: RentStageEnum,
    conversation: Conversation,
    numberOfMessagesFromMemory = 7,
    model: LanguageModelsEnum = LanguageModelsEnum.CLAUDE_4_SONNET,
    template = determineNextStepWithRelatedQuestion,
  ): Promise<NextStepWithRelatedQuestion[]> {
    const memory = new PostgresChatTypeMemory(this.messageRepository, conversation.id, numberOfMessagesFromMemory);

    const promptTemplate = PromptTemplate.fromTemplate(template);
    const chain = promptTemplate.pipe(this.modelMap.get(model));

    const response = <any>await this.callChain(
      {
        input: xmlFormattedMessages,
        possible_next_steps: JSON.stringify(possibleNextSteps),
        chat_history: (await memory.loadMemoryVariables()).chat_history,
        goal,
        rentStage,
      },
      chain,
    );

    let nextStepsWithQuestions: string;

    if (Array.isArray(response) && response[0] && response[0].type === 'thinking') {
      nextStepsWithQuestions = response[1].text;
    } else {
      nextStepsWithQuestions = response;
    }

    const nextStepWithQuestionsObject = <NextStepWithRelatedQuestion[]>JSON.parse(nextStepsWithQuestions);

    return nextStepWithQuestionsObject.map((nextStep) => ({
      nextStep: this.findClosestMatchingStep(nextStep.nextStep, possibleNextSteps),
      relatedText: nextStep.relatedText,
    }));
  }

  async parseInputToJson(
    input: string,
    entityObject: unknown,
    model = LanguageModelsEnum.CLAUDE_3_HAIKU,
    template = parseInputTemplate,
  ): Promise<FieldValue[]> {
    const parser = StructuredOutputParser.fromZodSchema(
      z.array(
        z.object({
          fieldName: z.string().describe('name of the field the answer belongs to'),
          value: z.string().describe('refined answer to be saved in database'),
        }),
      ),
    );

    const prompt = PromptTemplate.fromTemplate(template);
    const chain = prompt.pipe(this.modelMap.get(model)).pipe(parser);

    return <FieldValue[]>await this.callChain(
      {
        input: input,
        data_to_parse: JSON.stringify(entityObject),
        format_instructions: parser.getFormatInstructions(),
      },
      chain,
    );
  }

  async getResponseWithChatMemory(
    inputVariables: InputVariables,
    conversationId: string,
    template: string,
    numberOfMessages = 5,
    model = LanguageModelsEnum.GPT_4,
    outputParser: StructuredOutputParser<any> = null,
    fallbackModel?: LanguageModelsEnum,
  ): Promise<string> {
    const memory = new PostgresChatTypeMemory(this.messageRepository, conversationId, numberOfMessages);

    try {
      return await this.getResponse(inputVariables, template, memory, model, outputParser);
    } catch (e) {
      if (fallbackModel) {
        console.warn(`Primary model ${model} failed, retrying with fallback ${fallbackModel}:`);
        return await this.getResponse(inputVariables, template, memory, fallbackModel, outputParser);
      }
      throw e;
    }
  }

  async getResponse(
    inputVariables: InputVariables,
    template: string,
    memory: PostgresChatTypeMemory,
    model: LanguageModelsEnum = LanguageModelsEnum.GPT_4,
    outputParser: StructuredOutputParser<any> = null,
  ): Promise<any> {
    const defaultVariables = {
      chat_history: memory ? (await memory.loadMemoryVariables()).chat_history : '',
      format_instructions: outputParser ? outputParser.getFormatInstructions() : '',
    };

    const mergedVariables = { ...defaultVariables, ...inputVariables };

    const promptTemplate = PromptTemplate.fromTemplate(template);

    let chain: any;

    if (outputParser) {
      chain = promptTemplate.pipe(this.modelMap.get(model)).pipe(outputParser);
    } else {
      chain = promptTemplate.pipe(this.modelMap.get(model));
    }
    const response = await this.callChain(mergedVariables, chain);

    if (Array.isArray(response) && response[0] && response[0].type === 'thinking') {
      return response[1].text;
    } else {
      return response;
    }
  }

  async analyzeImageWithVision(
    imageDataUrl: string,
    prompt: string,
    model: LanguageModelsEnum = LanguageModelsEnum.GPT_4,
  ): Promise<string> {
    try {
      const chatModel = this.modelMap.get(model);

      const message = new HumanMessage({
        content: [
          {
            type: 'text',
            text: prompt,
          },
          {
            type: 'image_url',
            image_url: {
              url: imageDataUrl,
            },
          },
        ],
      });

      const response = await chatModel.invoke([message]);
      return response.content;
    } catch (error) {
      throw new HttpException(`Failed to analyze image: ${error.message}`, 500);
    }
  }

  private async callChain(inputVariables: InputVariables, chain: any): Promise<unknown> {
    let response: string;

    try {
      await retryAsync(
        async () => {
          const result = await chain.invoke(inputVariables);
          console.log(result.content ? result.content : result);
          response = result.content ? result.content : result;
        },
        { delay: 500, maxTry: 5 },
      );
    } catch (e) {
      throw new HttpException(`Failed to retrieve AI response: ${e}`, 500);
    }

    return response;
  }

  private findClosestMatchingStep(aiResponse: string, steps: string[]): NextStep {
    let maxOverlap = 0;
    let closestStep = null;

    steps.forEach((step) => {
      const aiWords = new Set(aiResponse.toLowerCase().split(/\W+/));
      const stepWords = new Set(step.toLowerCase().split(/\W+/));

      let overlap = 0;
      aiWords.forEach((word) => {
        if (stepWords.has(word)) {
          overlap++;
        }
      });

      if (overlap > maxOverlap) {
        maxOverlap = overlap;
        closestStep = step;
      }
    });

    return closestStep;
  }
}

export class ChatHistoryBuilder {
  private readonly messages = [];

  constructor() {
    this.messages = [];
  }

  appendCustomInformation(content: string): ChatHistoryBuilder {
    this.appendMessage(content);
    return this;
  }

  appendSystemMessage(content: string): ChatHistoryBuilder {
    this.appendMessageWithType('System:', content);
    return this;
  }

  appendAiMessage(content: string): ChatHistoryBuilder {
    this.appendMessageWithType('AI:', content);
    return this;
  }

  appendUserMessage(content: string): ChatHistoryBuilder {
    this.appendMessageWithType('User:', content);
    return this;
  }

  build(): string {
    return this.messages.join('\n');
  }

  private appendMessage(content: string): void {
    this.messages.push(content);
  }

  private appendMessageWithType(type: string, content: string): void {
    this.messages.push(`${type} ${content}`);
  }
}

export enum AiInstruction {
  HistoryForContext = '- You will also receive previous message history for context.',
  LatestRenterMessageMarking = '- The latest renter message will be wrapped in a "<latest_renter_message>" tag.',
  NoSugarcoating = '- Only give a direct and concise response to the question. Avoid polite words, sugarcoating, and any extra text.',
  UseOnlyPromptData = "- Utilize only the data provided in the prompt to formulate your response. Don't make up any facts.",
  OnlyLatestMessageReply = '- Reply only to the latest renter message. Use previous messages for context.',
  PlainTextOutput = '- Output should be in plain text format, do not use email formatting.',
  DontSayWe = '- Avoid saying "we", say "I" instead.',
  DontUseExamplesAsAnswer = "- Examples are provided to help you understand the task better, don't use them as a reference for the actual answer.",
  DontMakeUpFacts = '- Do not make up any facts about the leasing process.',
  DontGivePromises = '- Do not make any promises that you will do something',
  BeFairHousingActCompliant = '- IMPORTANT: make sure your output does not violate the Fair Housing Act, do not say anything that can be considered discriminatory.',
}

// do not change the formatting, spaces are calculated to look normal in the prompts
export const generalAiInstructionsTemplate = `${AiInstruction.NoSugarcoating}
${AiInstruction.LatestRenterMessageMarking}
${AiInstruction.HistoryForContext}
${AiInstruction.OnlyLatestMessageReply}`;

export class ConditionalAiInstruction {
  static greetIf24HoursPassed(timeSinceLastMessageMs: number): string {
    const msIn24Hours = 24 * 60 * 60 * 1000;

    if (timeSinceLastMessageMs > msIn24Hours) {
      return '- You can greet the renter in the beginning of the message.';
    }

    return '- Do not greet the renter in the beginning of the message.';
  }
}

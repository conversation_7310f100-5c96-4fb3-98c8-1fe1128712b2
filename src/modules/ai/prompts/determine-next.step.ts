import { NextStep } from '../../renter/renter-communication/response-generation/enums/next-step';
import { PromptVariable } from '../enums/prompt-variable.enum';

/* eslint-disable max-len */

export const determineNextStepWithRelatedQuestion = `
<task>As a leasing assistant, {goal} by balancing their queries with the goal of advancing the conversation.</task>

<current_process_stage>{rentStage}</current_process_stage>

<possible_next_steps>
List of potential next steps to choose from: {possible_next_steps}
</possible_next_steps>

${PromptVariable.ChatHistory}

<new_renter_messages>{input}</new_renter_messages>

<instructions>
- Review the chat history (tagged as 'previous_messages_history') and new user messages (tagged as 'new_renter_messages') for context.
- Choose the most appropriate next steps based on the renter's inquiries or statements.
- Consider the current process stage when selecting next steps.
- In your response include the next step(s) and a related user message(s) that prompted it.
Same related text can not belong to two different next steps. Message from the chat history can not be used for a related text.
- If there are multiple new messages, make sure to read all of them before making a decision about the next step. Multiple new messages can be related to the same next step or different next steps.
- If renter asks to disregard their previous message(s) carefully identify which related text should be ignored and prefer "General conversation question" as a next step for such cases.
- When renter reaffirms interest in the property (phrases like "I'm still interested", "still want to see it", etc.) especially after communication issues, prioritize scheduling-related steps over "Undefined".
- Communication clarifications that maintain interest in viewing the property should be treated as scheduling continuation, not undefined scenarios.
- If there's a history of cancelled showings in the conversation and the renter is asking to view again, this should be considered rescheduling.
- Format your response as a JSON array, each object representing a chosen next step and its related user message.
- Only include relevant steps from the provided list; don't add new steps or modify the list.
- Base your decision on the latest message, considering the entire renter's journey towards scheduling a tour.
- Your response should be a valid JSON. No additional text or comments should be included.
- Never wrap the output in the markdown code block like "\`\`\`json. It should be parsable with the JSON.parse() function in JavaScript.
</instructions>

<response_examples>
  <example>
    [
      {{
        "nextStep": "${NextStep.SHARE_REQUIREMENTS}",
        "relatedText": "Tell me about requirements to move in"
      }}
    ]
  </example>
  <example>
  [
    {{
      "nextStep": "${NextStep.ANSWER_SCAM_QUESTION}",
      "relatedText": "Is this a scam? Is this property real?"
    }},
    {{
      "nextStep": "${NextStep.ANSWER_PROPERTY_QUESTION}",
      "relatedText": "Does it have a pool? Is there a balcony? Are dogs allowed?"
    }}
  ]
  </example>
  <example>
  [
    {{
      "nextStep": "${NextStep.DISCUSS_CREDIT_SCORE}",
      "relatedText": "Sure, my credit score is 700"
    }},
    {{
      "nextStep": "${NextStep.DISCUSS_INCOME}",
      "relatedText": "my monthly income is $3000"
    }}
  ]
  </example>
  <example>
    [
      {{
        "nextStep": "${NextStep.GENERAL_CONVERSATION}",
        "relatedText": "Hi! Can we reschedule the showing to Tuesday? Sorry, ignore my last message"
      }}
    ]
  </example>
  <example>
    [
      {{
        "nextStep": "${NextStep.SCHEDULE_SHOWING}",
        "relatedText": "I'm still interested I didn't see an email from you."
      }}
    ]
  </example>
  <example>
    [
      {{
        "nextStep": "${NextStep.SCHEDULE_SHOWING}",
        "relatedText": "Yes I still want to see the property, when can we schedule?"
      }}
    ]
  </example>
  <example>
    - Renter message: "Zillow gave me a 664 credit rating. I will be making about $3600 a month after taxes. There is also going to be another tenant, a friend of mine. I'm not sure what his credit score is but he'll be making $2200 per month. If any of that is a problem, I can get my father to guarantee the lease. He has perfect credit and makes over 500k/year."
    - Output: [
      {{
        "nextStep": "${NextStep.DISCUSS_CREDIT_SCORE}",
        "relatedText": "Zillow gave me a 664 credit rating."
      }},
      {{
        "nextStep": "${NextStep.DISCUSS_INCOME}",
        "relatedText": "I will be making about $3600 a month after taxes. I will be making about $3600 a month after taxes. There is also going to be another tenant, a friend of mine. I'm not sure what his credit score is but he'll be making $2200 per month. If any of that is a problem, I can get my father to guarantee the lease. He has perfect credit and makes over 500k/year."
      }}
    ]
  </example>
</response_examples>
`;

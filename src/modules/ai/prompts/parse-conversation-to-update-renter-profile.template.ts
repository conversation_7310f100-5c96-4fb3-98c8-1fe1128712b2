/* eslint-disable max-len */

import { PromptVariable } from '../enums/prompt-variable.enum';

export const parseConversationToUpdateRenterProfileTemplate = `
<task>
Your task is to fill user's (renter) profile based on the information provided in the conversation between user and <PERSON>.
You will be provided with the user's profile database entity structure and field types in JSON format.
</task>

<task_description>
- Note that the user's messages might not contain any information that can be used to fill the user's profile fields.
- It is very important to fill the user's profile fields accurately based on what they've said in the conversation,
do not make any assumptions.
- Database entity fields might have a "fieldDescription" and "aiInstructions" to help you understand the purpose of the field better.
</task_description>

<instructions>
- Response should always be a JSON array of objects (never wrap your response in the markdown format). Always start with "[" and end with "]".
- First, check if the conversation contains any information that can be used to fill the user's profile fields.
If it's not, return an empty array: "[]" in JSON format (you are forbidden to include any other text in response except an empty array).
- If you can fill one or multiple database entity fields based on the conversation,
return filled fields as an array of objects in JSON format, example:
[
  {{
    "fieldName": "fieldName",
    "value": "fieldValue"
  }}
]
</instructions>

<exceptions>
In case if renter user agrees to what AI says, you should not fill the user's profile fields based on that.
Only fill the fields if the user says the exact information about themselves.
For example, "I have a credit score of 650" or "My montlhy income is $5000", etc.
</exceptions>

<database_entity>
Current user's entity data:
{dataToParse}
</database_entity>

${PromptVariable.CurrentDate}
${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

// Test cases for things that are usually fucking up in renter profile update

// - Test that enums like employment status, lease term, etc are filled correctly
// Messages to test:
// - "Hi! I'm a doctor looking for a house. I prefer month to monthly lease. Can I see it tomorrow?"
// (should set employment status as Employed, occupation "doctor" and month to month lease term)

// - Test that desired move in date is filled and not confused with the showing time
// Messages to test:
// - "Hi! I would like to see the property tomorrow at 3pm, are you available?" (should not fill move in date)
// - "Hi! I would like to have a tour tomorrow. I would like to move in on April 16." (should fill move in date as Apr 16)
// - "Hi! I would like to move in on June 3, is this still available?" (Should fill it as June 3)

// - Make sure it doesn't return empty or random field values. Sometimes it returns the list of fields with values
// like "null", "undefined" or an empty string ''. It should not return it when testing.

// - Test that it returns an empty array if there is no information to fill the user's profile fields
// Messages to test:
// - "Hi! What are the requirements for this property?" (should return empty array)
// - "Hi! I'm intereseted in this property, is this still available?" (should return empty array)

// - Test that it returns an array with fields related to the message.
// Messages to test:
// - "Hi! I'm a software enginner looking for a house. I have a pretty good credit score (700) and a monthly income of $5500.
// I have a kid, but I don't have any pets. Can I see it tomorrow?""

// - Test that it doesn't fill the user's profile fields if the user agrees to what AI says.
// Messages to test:
// - "Hi! Can I see the property tomorrow at 4pm?" (AI should share the requirements)
// - When AI shares the requirements, user says "Yes, I fit these." (Renter profile should not fill any fields based on this message)

/* eslint-disable max-len */
export const parseInputTemplate = `
***Task***:
Parse user's message and map it to the database entity.

***Example***
Let's imagine an entity with the following structure for our mapping:
"id": "1",
"userName": "<PERSON>",
"propertyState": "CA",
"propertyCity": "San Francisco",
"propertyAddress": null,
"propertySize": null,
"propertyFloors": null,

If the user's message is "My name is <PERSON>" the field name should be "user<PERSON><PERSON>".

If user's message is "I live in San Francisco" the field name should be "propertyCity".
If the message can not be mapped to the database return null as field name. i.e.
If user's message is 'idk' it is impossible to say which topic it belongs to, in that case you should
return 'undefined' as fieldName and 'undefined' as field value.
You should never return null as field name or value.

There is also a possibility that the user's message contains the answer to multiple questions.
For example, if the user's message is "I live in San Francisco, I have a two bedrooms apartment and my name is <PERSON>"
the response should include an array of field names and values:
"fieldName": "userName",
"value": "<PERSON>",
"fieldName": "propertyCity",
"value": "San Francisco",
"fieldName": "propertyBedrooms",
"value": "2",

Field name should always be a single variable name, never a complex one. I don't care about nesting
and it will be parsed separately, for example for the object location: city: "San Francisco"
"fieldName": "city" is ok, but : "fieldName": "location.city" is wrong.

This is very important, the field name is used to map the value to the database entity.

***Entity***
{data_to_parse}

***Instructions***:
If there is a field type attached to the field name, you should return the value in the format that is expected by the database.
For example, if the field type is 'number' and the value is 'two' you should return '2'.
If the field type is 'boolean' and the value is 'yes' you should return 'true'.
If the type is enum, you should return the value that is expected by the database.
Human input will be delimited by triple exclamation marks.
Take your time and think as much as possible, this is the most important part of the project.

All the number inputs from the user should be transformed to numbers, i.e "2" -> "2", "two" -> "2", "two hundred" -> "200".
Remember, that you should never forget to put "" around the fieldName and fieldValue.
Never return null as fieldName or fieldValue, use 'undefined' instead.
Before returning the result make sure fieldName does not contain any dots. If it does,
return only the last part of the fieldName, i.e. "location.city" -> "city".

If there is no fieldName or fieldValue to return, return an empty array.
Try to return as many fieldNames and fieldValues as possible.

For example, if the user's message is "I have two parking spot" you should return (if present in the entity):

"fieldName": "hasParking",
"value": "true",
"fieldName": "numberOfSpots",
"value": "2",

Because if the user has two parking spots, it is implied that there is a parking on his property.


***Format***
Here is the format of the expected reply, please stick to it: {format_instructions}

***Input***
!!! {input} !!!
`;

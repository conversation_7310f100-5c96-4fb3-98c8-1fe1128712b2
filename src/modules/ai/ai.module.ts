import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AiService } from './ai.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Conversation } from '../shared/communication/conversation/entities/conversation.entity';
import { Message } from '../shared/communication/conversation/message/message.entity';

@Module({
  imports: [HttpModule, TypeOrmModule.forFeature([Conversation, Message])],
  providers: [AiService],
  exports: [AiService],
})
export class AiModule {}

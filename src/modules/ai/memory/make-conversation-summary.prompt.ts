export const makeConversationSummaryPrompt = `
  Progressively summarize the lines of conversation provided,
  adding onto the previous summary returning a new summary.
  You can reformat existing summary lines, but you cannot delete any information from it.
  Please try to keep all the information in the summary.
  *** EXAMPLE ***
  Current summary:
  The human asks what the AI thinks of artificial intelligence.
  The AI thinks artificial intelligence is a force for good.
                                                         New lines of conversation:
  Human: Why do you think artificial intelligence is a force for good?
  AI: Because artificial intelligence will help humans reach their full potential.
  New summary:
  The human asks what the AI thinks of artificial intelligence.
  The AI thinks artificial intelligence is a force for good because it will help humans reach their full potential.
  END OF EXAMPLE

  *** Current summary ***
  {summary}

  *** New messages in the conversation ***
  Human: {new_lines_human}
  AI: {new_lines_ai}

*** New summary ***`;

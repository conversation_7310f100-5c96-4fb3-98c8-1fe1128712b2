import { Repository } from 'typeorm';
import { Message } from '../../shared/communication/conversation/message/message.entity';
import { ChatHistoryBuilder } from '../utils/chat.utils';
import { BaseMemory, MemoryVariables } from '@langchain/core/memory';

export class PostgresChatTypeMemory extends BaseMemory {
  constructor(
    private readonly repository: Repository<Message>,
    private readonly conversationId: string,
    private readonly numberOfMessages: number,
  ) {
    super();
  }

  // Load memory variables
  async loadMemoryVariables(): Promise<MemoryVariables> {
    const messages = await this.repository.find({
      where: { conversations: { id: this.conversationId } },
      order: { createdAt: 'DESC' },
      take: this.numberOfMessages,
    });
    messages.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());

    const chatHistoryBuilder = new ChatHistoryBuilder();

    messages.forEach((message) => {
      if (message.isSystem) {
        chatHistoryBuilder.appendAiMessage(message.content);
      } else {
        chatHistoryBuilder.appendUserMessage(message.content);
      }
    });

    return {
      chat_history: chatHistoryBuilder.build(),
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  async saveContext(): Promise<void> {}

  // Get memory keys
  get memoryKeys(): string[] {
    return [this.conversationId];
  }
}

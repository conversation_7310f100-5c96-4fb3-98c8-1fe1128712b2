import { Controller, Get, Param } from '@nestjs/common';
import { ApiOkResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { Public } from '../../shared/auth/decorators/public-access.decorator';
import { convertPropertyToDto, PropertyDto } from '../../investor/property/property/model/property.dto';
import { PublicPropertyService } from './public-property.service';

@ApiTags('public-property')
@Controller('public-property')
@Public()
export class PublicPropertyController {
  constructor(private readonly propertyService: PublicPropertyService) {}
  @Get(':propertyId')
  @ApiParam({
    name: 'propertyId',
    required: true,
  })
  @ApiOkResponse({
    description: 'Get a public property by id',
  })
  async getPublicProperty(@Param('propertyId') propertyId: string): Promise<PropertyDto> {
    const property = await this.propertyService.getPublicProperty(propertyId);

    return convertPropertyToDto(property, true);
  }
}

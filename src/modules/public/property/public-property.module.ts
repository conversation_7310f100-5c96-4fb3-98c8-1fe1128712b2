import { Module } from '@nestjs/common';
import { PropertyModule } from '../../investor/property/property/property.module';
import { PublicPropertyService } from './public-property.service';
import { PublicPropertyController } from './public-property.controller';

@Module({
  imports: [PropertyModule],
  providers: [PublicPropertyService],
  controllers: [PublicPropertyController],
})
export class PublicPropertyModule {}

import { Injectable } from '@nestjs/common';
import { PropertyService } from '../../investor/property/property/property.service';
import { Property } from '../../investor/property/property/entities/property.entity';
import { PropertyStatus } from '../../investor/property/property/enums/property-status.enum';

@Injectable()
export class PublicPropertyService {
  constructor(private readonly propertyService: PropertyService) {}

  async getPublicProperty(id: string): Promise<Property> {
    const property = await this.propertyService.findById(id, true, false, true);

    const allowedPropertyStatuses = [PropertyStatus.LISTED, PropertyStatus.UNLISTED, PropertyStatus.RENTED_OUT];

    if (!allowedPropertyStatuses.includes(property.status)) {
      throw new Error('Property is not public');
    }

    return property;
  }
}

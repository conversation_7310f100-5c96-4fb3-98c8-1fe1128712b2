import { Injectable } from '@nestjs/common';
import { StatsD } from 'hot-shots';
import { AnalyticsEvent } from './analytics.constants';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AnalyticsService {
  private client: StatsD;

  constructor(private readonly config: ConfigService) {
    const environment = this.config.get('ENV');
    this.client = new StatsD({
      port: 8125,
      globalTags: { environment },
    });
  }

  incrementMultiple(metrics: AnalyticsEvent[]) {
    metrics.forEach((metric) => {
      this.increment(metric);
    });
  }

  increment(metricName: string, tags?: string[]) {
    this.client.increment(metricName, 1, tags, (error) => {
      if (error) {
        console.error(`Failed to send metric: ${error.message}`);
      }
    });
  }

  gauge(metricName: string, value: number, tags?: string[]) {
    this.client.gauge(metricName, value, tags, (error) => {
      if (error) {
        console.error(`Failed to send gauge: ${error.message}`);
      }
    });
  }

  timing(metricName: string, timeInMs: number, tags?: string[]) {
    this.client.timing(metricName, timeInMs, tags, (error) => {
      if (error) {
        console.error(`Failed to send timing: ${error.message}`);
      }
    });
  }

  histogram(metricName: string, value: number, tags?: string[]) {
    this.client.histogram(metricName, value, tags, (error) => {
      if (error) {
        console.error(`Failed to send histogram: ${error.message}`);
      }
    });
  }
}

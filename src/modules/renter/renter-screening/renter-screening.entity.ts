import { Expose, instanceToPlain } from 'class-transformer';
import { Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Renter } from '../renter/renter.entity';
import { RenterScreeningDto } from './renter-screening.dto';

@Entity()
export class RenterScreening {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => Renter, (renter) => renter.renterScreening, {
    nullable: false,
    lazy: true,
  })
  renter: Promise<Renter> | Renter;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: true })
  creditScore: string | null;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: true })
  monthlyIncome: string | null;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: true })
  desiredMoveInDate: string | null;

  /**
   * Points of interest for the owner reviewing the profile.
   * Could have helpful decision making notes like:
   * - "Has a cosigner with 700 credit score"
   * - "Willing to pay a higher deposit"
   * - "International student, doesn't have a credit score history"
   */
  @Expose()
  @Column('text', { array: true, nullable: false, default: [] })
  convoHighlights: string[];

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @Expose()
  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  getHighlightsAsList(): string {
    return this.convoHighlights.length > 0
      ? this.convoHighlights.map((highlight) => `- ${highlight}`).join('\n')
      : 'N/A';
  }

  isUpdatedInPast30Days(): boolean {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    return this.updatedAt >= oneMonthAgo;
  }

  /**
   * Reset all screening values
   */
  resetValues(): void {
    this.creditScore = null;
    this.monthlyIncome = null;
    this.desiredMoveInDate = null;
    this.convoHighlights = [];
  }

  static async convertToDto(screening: RenterScreening): Promise<RenterScreeningDto> {
    const screeningDto = <RenterScreeningDto>instanceToPlain(screening, {
      excludeExtraneousValues: true,
    });

    return screeningDto;
  }
}

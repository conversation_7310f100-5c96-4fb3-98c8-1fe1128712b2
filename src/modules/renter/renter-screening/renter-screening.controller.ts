import { Controller, Get, NotFoundException, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { Role } from '../../shared/auth/models/roles-enum';
import { RenterScreeningDto } from './renter-screening.dto';
import { RenterScreening } from './renter-screening.entity';
import { RenterScreeningService } from './renter-screening.service';

@ApiTags('renter-screening')
@Controller('renter-screening')
@ApiBearerAuth()
@UseGuards(RolesGuard)
export class RenterScreeningController {
  constructor(private readonly renterScreeningService: RenterScreeningService) {}

  @Get(':screeningId')
  @HasRoles(Role.INVESTOR)
  @ApiOperation({ summary: 'Get screening data by screening ID' })
  @ApiParam({ name: 'screeningId', type: String, description: 'ID of the screening' })
  @ApiResponse({ status: 200, description: 'Screening data retrieved successfully', type: RenterScreeningDto })
  @ApiResponse({ status: 404, description: 'Screening not found' })
  async getScreeningById(@Param('screeningId') screeningId: string): Promise<RenterScreeningDto> {
    const screening = await this.renterScreeningService.findById(screeningId);

    if (!screening) {
      throw new NotFoundException(`Screening with ID ${screeningId} not found`);
    }

    return RenterScreening.convertToDto(screening);
  }
}

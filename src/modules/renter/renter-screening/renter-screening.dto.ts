import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class RenterScreeningDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  creditScore: string | null;

  @Expose()
  @ApiProperty()
  monthlyIncome: string | null;

  @Expose()
  @ApiProperty({ example: '2025-05-31' })
  desiredMoveInDate: string | null;

  @Expose()
  @ApiProperty({ isArray: true, type: () => String })
  convoHighlights: string[];

  @Expose()
  @ApiProperty()
  createdAt: string;

  @Expose()
  @ApiProperty()
  updatedAt: string;
}

import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AiService } from '../../ai/ai.service';
import { Property } from '../../investor/property/property/entities/property.entity';
import { IncomeEnum } from '../../investor/property/property/property-details/renter-requirements/income.enum';
import { AiObjectiveType } from '../renter-communication/ai-objective/ai-objective-type.enum';
import { AiObjective } from '../renter-communication/ai-objective/ai-objective.entity';
import { Renter } from '../renter/renter.entity';
import { RenterService } from '../renter/renter.service';
import { RenterScreening } from './renter-screening.entity';
import { ScreeningSensitivity } from '../../investor/property/property/property-details/renter-requirements/screening-sensitivity.enum';
import { RenterQualificationResult } from './model/renter-qualification-result.interface';
import { LanguageModelsEnum } from '../../ai/enums/language-models.enum';
import { getQualifyRenterTemplate } from './prompts/qualify-renter.template';
import { Conversation } from '../../shared/communication/conversation/entities/conversation.entity';
import { RenterAiQualificationParams } from './model/renter-ai-qualification-params.interface';
import { getAvailabilityPromptInstruction } from '../renter-communication/response-generation/utils/get-availability-prompt-instruction.function';
import { TimezoneUtils } from '../../../utils/timezone.utils';
import { calculateRequiredMonthlyRent } from '../../investor/property/property/property-details/renter-requirements/calculate-required-monthly-rent.util';
import { DateUtils } from '../../../utils/date.utils';
import { PropertyInquiryService } from '../../investor/property-inquiry/property-inquiry.service';
import { RentStageEnum } from '../../shared/communication/conversation/enums/rent-stage.enum';
import { PropertyInquiry } from '../../investor/property-inquiry/property-inquiry.entity';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../shared/communication/outbound-communication/slack/slack-convo-message-builder.class';
import { LeaseConditions } from '../../investor/property/property/property-details/lease-conditions/lease-conditions.entity';
import { RenterRequirements } from '../../investor/property/property/property-details/renter-requirements/renter-requirements.entity';
import { FollowUpService } from '../../shared/communication/follow-up/follow-up.service';

interface RenterQualificationAiOutput {
  qualified: boolean;
  renterRejectionReason: string;
}

export const skipQualificationOutput: RenterQualificationResult = {
  qualified: false,
  renterRejectionReason: null,
  qualificationSkipped: true,
};

@Injectable()
export class RenterScreeningService {
  constructor(
    @InjectRepository(RenterScreening)
    private readonly renterScreeningRepository: Repository<RenterScreening>,
    private readonly renterService: RenterService,
    @Inject(forwardRef(() => PropertyInquiryService))
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly aiService: AiService,
    private readonly slackService: SlackCommunicationService,
    private readonly followUpService: FollowUpService,
  ) {}

  /**
   * Creates a new screening for a renter
   * @param renter The renter to create a screening for
   * @returns The created screening
   */
  async createScreening(renter: Renter): Promise<RenterScreening> {
    const screening = new RenterScreening();
    screening.renter = renter;

    const savedScreening = await this.renterScreeningRepository.save(screening);

    // Update the renter with the new screening ID to establish the bidirectional relationship
    await this.renterService.update(renter.id, { renterScreeningId: savedScreening.id });

    return savedScreening;
  }

  findById(id: string): Promise<RenterScreening | null> {
    return this.renterScreeningRepository.findOneBy({ id });
  }

  /**
   * Gets the screening for a renter, creates one if it doesn't exist (only if createIfMissing is true)
   * @param renter The renter to get the screening for
   * @param createIfMissing Whether to create a screening if one doesn't exist
   * @returns The renter's screening or null if none exists and createIfMissing is false
   */
  async getScreeningForRenter(renter: Renter, createIfMissing: boolean = false): Promise<RenterScreening | null> {
    let screening = await renter.renterScreening;
    if (screening) {
      return screening;
    }

    const freshRenter = await this.renterService.findById(renter.id, true);

    if (!freshRenter) {
      return null;
    }

    screening = await freshRenter.renterScreening;

    if (!screening && createIfMissing) {
      return this.createScreening(freshRenter);
    }

    return screening || null;
  }

  async updateScreeningFromObjective(objective: AiObjective, renter: Renter): Promise<void> {
    const screening = await this.getScreeningForRenter(renter);

    if (!screening) {
      return;
    }

    switch (objective.type) {
      case AiObjectiveType.EVALUATE_CREDIT_SCORE:
        screening.creditScore = objective.value;
        break;
      case AiObjectiveType.EVALUATE_INCOME:
        screening.monthlyIncome = objective.value;
        break;
      case AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE:
        screening.desiredMoveInDate = objective.value;
        break;
    }

    await this.renterScreeningRepository.save(screening);
  }

  async determineRequiredObjectives(screening: RenterScreening): Promise<{
    createCreditScoreObjective: boolean;
    createIncomeObjective: boolean;
    createMoveInDateObjective: boolean;
    resetScreeningValues: boolean;
  }> {
    if (!screening.isUpdatedInPast30Days()) {
      // If screening is updated more than 30 days ago, reset all values
      return {
        createCreditScoreObjective: true,
        createIncomeObjective: true,
        createMoveInDateObjective: true,
        resetScreeningValues: true,
      };
    }

    // If screening is recent but some fields are missing, create objectives for missing fields
    return {
      createCreditScoreObjective: !screening.creditScore,
      createIncomeObjective: !screening.monthlyIncome,
      createMoveInDateObjective: !screening.desiredMoveInDate,
      resetScreeningValues: false,
    };
  }

  async resetScreeningValues(screening: RenterScreening): Promise<RenterScreening> {
    screening.resetValues();
    return this.renterScreeningRepository.save(screening);
  }

  async updateConvoHighlights(renter: Renter, newHighlights: string[]): Promise<RenterScreening | null> {
    const screening = await this.getScreeningForRenter(renter);

    if (!screening) {
      return null;
    }

    let updated = false;
    for (const highlight of newHighlights) {
      if (highlight.trim() && !screening.convoHighlights.includes(highlight)) {
        screening.convoHighlights.push(highlight);
        updated = true;
      }
    }

    // Only save if changes were made
    return updated ? await this.renterScreeningRepository.save(screening) : screening;
  }

  /**
   * When objective-related strategy runs, we need to requalify the renter
   */
  async requalifyRenterForProperty(
    convo: Conversation,
    inquiry: PropertyInquiry,
    property: Property,
    renter: Renter,
  ): Promise<RenterQualificationResult> {
    if (inquiry.stage === RentStageEnum.REQUIREMENTS_NOT_MET) {
      return this.qualifyRenterForProperty(convo, inquiry, property, renter);
    }

    return skipQualificationOutput;
  }

  async qualifyRenterForProperty(
    convo: Conversation,
    inquiry: PropertyInquiry,
    property: Property,
    renter: Renter,
  ): Promise<RenterQualificationResult> {
    // skip in case if renter has already been qualified or unqualified
    if (inquiry.stage === RentStageEnum.INITIAL_CONTACT && inquiry.passedInitialScreening !== null) {
      return skipQualificationOutput;
    }

    // skip if there are any other pending objectives except showing
    if (!(await PropertyInquiry.isOnlyShowingObjectiveLeft(inquiry))) {
      return skipQualificationOutput;
    }

    const company = await property.company;
    const companySettings = company.settings;
    const renterRequirements = await property.renterRequirements;

    if (
      !companySettings.advancedRenterScreening ||
      !renterRequirements ||
      renterRequirements?.screeningSensitivity === ScreeningSensitivity.OWNER_REVIEW
    ) {
      return skipQualificationOutput;
    }

    const renterScreening = await renter.renterScreening;
    if (!renterScreening) {
      return skipQualificationOutput;
    }
    const shouldQualifyIncome =
      renterScreening.monthlyIncome &&
      renterRequirements?.minimumIncome &&
      renterRequirements.minimumIncome !== IncomeEnum.NO_REQUIREMENT;

    const leaseConditions = await property.leaseConditions;
    const shouldQualifyCreditScore = Boolean(renterScreening.creditScore && renterRequirements?.minimumCreditScore);
    const shouldQualifyMoveInDate = Boolean(renterScreening.desiredMoveInDate && leaseConditions.desiredLeasingDate);

    if (!shouldQualifyIncome && !shouldQualifyCreditScore && !shouldQualifyMoveInDate) {
      return skipQualificationOutput;
    }

    const { city, timeZone } = await property.location;
    const availabilityInstruction = getAvailabilityPromptInstruction(
      TimezoneUtils.getCurrentCityDate(city, timeZone),
      leaseConditions.desiredLeasingDate,
    );

    const currentDateInCityUtc = TimezoneUtils.getCurrentCityDate(city, timeZone);
    const currentDate = DateUtils.toAiReadableFormat(currentDateInCityUtc);

    const aiOutput = await this.qualifyRenterUsingAi(convo.id, {
      leaseConditions,
      renterRequirements,
      renterScreening,
      renter,

      shouldQualifyIncome,
      shouldQualifyCreditScore,
      shouldQualifyMoveInDate,
      propertyAvailabilityStatus: availabilityInstruction,
      currentDate,
    });

    if (!aiOutput) {
      return skipQualificationOutput;
    }

    const { qualified, renterRejectionReason } = aiOutput;

    if (!qualified) {
      await this.propertyInquiryService.markRenterAsUnqualified(inquiry);
      await this.followUpService.deleteRenterFollowUpsByStatus(convo.id);
    } else {
      await this.propertyInquiryService.markRenterAsQualified(inquiry);
    }

    try {
      await this.sendScreeningResultToSlack(
        qualified,
        renterScreening,
        renter,
        convo,
        leaseConditions,
        renterRequirements,
      );
    } catch (error) {
      console.error('Error sending screening result to Slack', error);
    }

    return {
      qualified,
      renterRejectionReason,
      qualificationSkipped: false,
    };
  }

  private async qualifyRenterUsingAi(
    convoId: string,
    params: RenterAiQualificationParams,
  ): Promise<RenterQualificationAiOutput | null> {
    let aiOutput: RenterQualificationAiOutput | undefined;

    try {
      aiOutput = JSON.parse(
        await this.aiService.getResponseWithChatMemory(
          {
            renterCreditScore: params.renterScreening.creditScore,
            renterMonthlyIncome: params.renterScreening.monthlyIncome,
            renterDesiredMoveInDate: params.renterScreening.desiredMoveInDate,
            minCreditScore: params.renterRequirements?.minimumCreditScore,
            currentRent: params.leaseConditions.rent,
            minIncome: calculateRequiredMonthlyRent(
              params.leaseConditions.rent,
              params.renterRequirements?.minimumIncome,
            ),
            monthlyIncomeMultiplier: params.renterRequirements?.minimumIncome,
            propertyAvailabilityDate: params.propertyAvailabilityStatus,
            currentDate: params.currentDate,
            convoHighlights: params.renterScreening.getHighlightsAsList(),
          },
          convoId,
          getQualifyRenterTemplate(params),
          0,
          LanguageModelsEnum.CLAUDE_4_SONNET_REASONER,
        ),
      );

      return aiOutput;
    } catch (error) {
      console.error('Error qualifying renter', error);

      return null;
    }
  }

  private async sendScreeningResultToSlack(
    quailifed: boolean,
    screening: RenterScreening,
    renter: Renter,
    conversation: Conversation,
    leaseConditions: LeaseConditions,
    renterRequirements: RenterRequirements,
  ): Promise<void> {
    const qualificationStatus = quailifed
      ? 'Result: Qualified for this property ✔️'
      : 'Result: not qualified for this property 🚫';
    const slackMessage = new SlackConvoMessageBuilder()
      .appendTextLine(`📑 Screening results for ${renter.user.name}:`)
      .appendEmptyLine()
      .appendBulletItem(qualificationStatus)
      .appendBulletItem(`Credit score: ${screening.creditScore}`)
      .appendBulletItem(`Income: ${screening.monthlyIncome}`)
      .appendBulletItem(`Desired move in date: ${screening.desiredMoveInDate}`);

    if (screening.convoHighlights?.length) {
      slackMessage.appendEmptyLine().appendTextLine('Convo highlights:');
      screening.convoHighlights.forEach((highlight) => {
        slackMessage.appendBulletItem(highlight);
      });
    }

    const incomeRequirement = renterRequirements?.minimumIncome || 'N/A';
    const calculatedRent = renterRequirements?.minimumIncome
      ? ` ($${calculateRequiredMonthlyRent(leaseConditions?.rent, renterRequirements?.minimumIncome)} in total)`
      : '';

    slackMessage
      .appendEmptyLine()
      .appendTextLine(`Property requirements (mode: "${renterRequirements.screeningSensitivity}"):`)
      .appendBulletItem(`Credit: ${renterRequirements?.minimumCreditScore || 'N/A'}`)
      .appendBulletItem(`Income: ${incomeRequirement}${calculatedRent}`)
      .appendBulletItem(`Property available since: ${leaseConditions.desiredLeasingDate || 'N/A'}`);

    await this.slackService.sendMessageToConvosChannel(slackMessage.build(), conversation);
  }
}

import { BadRequestException } from '@nestjs/common';
import { ScreeningSensitivity } from '../../../investor/property/property/property-details/renter-requirements/screening-sensitivity.enum';
import { RenterAiQualificationParams } from '../model/renter-ai-qualification-params.interface';
import { AiInstruction } from '../../../ai/prompts/ai-instructions';
import { renterMoveInDateAcceptablePeriod } from '../renter-move-in-date-acceptable-period.const';
import { PromptVariable } from '../../../ai/enums/prompt-variable.enum';

/**
 * Main function to generate the qualify renter template based on company screening settings
 */
export function getQualifyRenterTemplate(params: RenterAiQualificationParams): string {
  if (params.renterRequirements.screeningSensitivity === ScreeningSensitivity.OWNER_REVIEW) {
    throw new BadRequestException('Screening sensitivity "Owner Review" is not supported for this template.');
  }

  const template = `
<your_role>
  You are a leasing manager. You have a new renter who wants to rent your property.
</your_role>

<task>
  You need to determine if the renter qualifies for the property based on their screening results and the property requirements.
  - "property_requirements" section contains the property requirements.
  - "renter_screening_results" section contains the renter's screening results.

  If renter is not qualified, you need to generate a message that will explain why they do not qualify
  (for example because of credit score, income, etc) and say that you won't be able to proceed with the showing. When generating the message consider examples, but note that you might have multiple reasons for rejection, so you can combine them in one message.
  IMPORTANT: make sure your output does not violate the Fair Housing Act, do not say anything that can be considered discriminatory.

  In case if you are not sure if the renter qualifies because data seems ambiguous, consider them qualified and return "qualified" as true and empty string rejection reason.
  Exception: if a minimum credit score is required and the renter has no credit score, no credit history, or otherwise cannot provide a verifiable credit score, this is NOT ambiguous—consider them not qualified (unless cosigners are accepted AND a cosigner's verifiable score meets the minimum; in that case use the higher score).
</task>

<property_requirements>
${getRequirementsTemplate(params)}
${PromptVariable.CurrentDate}
</property_requirements>

<renter_screening_results>
This is the information renter provided during the screening process:
  - Credit Score: "{renterCreditScore}"
  - Monthly Income: "{renterMonthlyIncome}"
  - Desired Move-In Date: "{renterDesiredMoveInDate}"

Other hilights from the conversation with the renter that may help you make a decision:
{convoHighlights}
</renter_screening_results>

<output_format>
  - Do not wrap the output in markdown code blocks like "\`\`\`json" - it should be parsable with js JSON.parse() function.
  - Return JSON object with the following structure:
  {{
    "qualified": boolean,            // true if the renter qualifies, false otherwise
    "renterRejectionReason": string  // reason for rejection if not qualified, empty string if qualified
  }}
</output_format>

${getOutputExamplesTemplate(params)}

<instructions>
${AiInstruction.NoSugarcoating}
${AiInstruction.UseOnlyPromptData}
${AiInstruction.DontGivePromises}
</instructions>
`;

  return template;
}

function getRequirementsTemplate(params: RenterAiQualificationParams): string {
  const templateMap: Record<ScreeningSensitivity, (params: RenterAiQualificationParams) => string> = {
    [ScreeningSensitivity.STRICT]: getStrictRequirementsTemplate,
    [ScreeningSensitivity.MODERATE]: getModerateRequirementsTemplate,
    [ScreeningSensitivity.OWNER_REVIEW]: getOwnerReviewRequirementsTemplate,
  };

  const templateFunction = templateMap[params.renterRequirements.screeningSensitivity];
  return templateFunction(params);
}

const noVerifiableCreditScoreRule = `IMPORTANT: If the renter says they don't have a credit score, have no credit history, pay everything in cash, or cannot provide a verifiable credit score, treat this as NOT meeting the minimum credit score requirement (this is not considered ambiguous). Consider them unqualified unless cosigners are accepted for this property AND a cosigner's verifiable credit score meets the minimum; in that case, use the higher score to qualify.
Treat any phrasing like "no credit score", "no credit", "N/A", "unknown", "pay in cash only", "never had a credit card", "no credit history", or "can't provide a score" as no verifiable credit score.`;

function getStrictRequirementsTemplate(params: RenterAiQualificationParams): string {
  const requirements: string[] = [];

  if (params.shouldQualifyCreditScore) {
    requirements.push(`
<credit_score>
Should have {minCreditScore} or higher credit score.
${getCosignerNote(params.renterRequirements.acceptsCosigners)}
Renters with lower credit score considered unqualified.
${noVerifiableCreditScoreRule}
</credit_score>
    `);
  }

  if (params.shouldQualifyIncome) {
    requirements.push(`<income>${getIncomeRequirements(params)}</income>`);
  }

  if (params.shouldQualifyMoveInDate) {
    requirements.push(getMoveInDateRequirement(params));
  }

  return `
  The renter must meet ALL of the following requirements:
  ${requirements.join('\n  ')}`;
}

const moderateCreditScoreDownTolerance = 30;
const moderateIncomeDownTolerance = 500;

function getModerateRequirementsTemplate(params: RenterAiQualificationParams): string {
  const requirements: string[] = [];

  if (params.shouldQualifyCreditScore) {
    requirements.push(`
<credit_score>
Should have {minCreditScore} or higher credit score, but note that scores down to ${params.renterRequirements.minimumCreditScore - moderateCreditScoreDownTolerance} could be considered qualified (return qualified as true, but never tell the renter that we consider it as qualified when writing a rejection reason). Renters with lower credit score considered unqualified.
${getCosignerNote(params.renterRequirements.acceptsCosigners)}
${noVerifiableCreditScoreRule}
</credit_score>`);
  }

  if (params.shouldQualifyIncome) {
    requirements.push(`<income>${getIncomeRequirements(params)}</income>`);
  }

  if (params.shouldQualifyMoveInDate) {
    requirements.push(getMoveInDateRequirement(params));
  }

  return `
  The renter should meet the following requirements:
  ${requirements.join('\n  ')}`;
}

function getOwnerReviewRequirementsTemplate(): string {
  return 'No requirements - all applications will be reviewed by the owner. Consider the renter qualified, return "qualified" as true and empty string rejection reason.';
}

function getCosignerNote(acceptsCosigners: boolean): string {
  return acceptsCosigners
    ? 'If the renter has a cosigner and we know their credit score the one with the higher score will be used to qualify the renter.'
    : 'Cosigners are not accepted for this property.';
}

function getIncomeRequirements(params: RenterAiQualificationParams): string {
  if (params.renter.hasHousingVoucher) {
    // Temproray consider all renters with housing vouchers as qualified for income requirement
    // We'll need to add a dynamic objective for sec 8 to ask more questions about section 8, like $ it covers, etc.
    return 'This renter has a Section 8 (housing choice) voucher, so the income requirement is not applicable. Consider the renter quailifed if they meet all other requirements.';
  }

  const strictIncomeRequirement =
    'Should have at least ${minIncome} monthly income (current rent is "${currentRent}" and requirement is "{monthlyIncomeMultiplier}"). Consider total household income as well. Otherwise, the renter is considered unqualified.';
  const moderateIncomeRequirement = `Should have at least $\{minIncome} monthly income (current rent is "$\{currentRent}" and requirement is "{monthlyIncomeMultiplier}"). Note that if requirement is at least 2x rent, then incomes down to minus $${moderateIncomeDownTolerance} still can be considered qualified (return qualified as true, but never tell the renter that we consider it as qualified when writing a rejection reason). Consider total household income as well. Otherwise, the renter is considered unqualified.`;

  const incomeRequirement =
    params.renterRequirements.screeningSensitivity === ScreeningSensitivity.STRICT
      ? strictIncomeRequirement
      : moderateIncomeRequirement;

  return `${incomeRequirement}`;
}

function getMoveInDateRequirement(params: RenterAiQualificationParams): string {
  return `
<move_in_date>
  Property availability status: "${params.propertyAvailabilityStatus}".

  If property is available now, the renter should be able to move in in the next ${renterMoveInDateAcceptablePeriod} days from current date (${PromptVariable.CurrentDate}) to be considered qualified.

  If property is not available right now, but will be available in the future, the rules are as follows:
    - If property available in ${renterMoveInDateAcceptablePeriod}+ days from current date: consider the renter qualified if they can move in during next 14 days since property availability date.
    - If property available in <=${renterMoveInDateAcceptablePeriod} days from current date: consider the renter qualified if they can move in within ${renterMoveInDateAcceptablePeriod} days from current date.
    - Others will be considered unqualified.
</move_in_date>`;
}

function getOutputExamplesTemplate(params: RenterAiQualificationParams): string {
  // Calculate dynamic values for examples based on actual requirements
  const actualMinCreditScore = params.renterRequirements.minimumCreditScore;
  const actualTolerantCreditScore = actualMinCreditScore - moderateCreditScoreDownTolerance;

  // For income examples, we'll use a standard example but show the tolerance pattern
  const exampleMinIncome = 4000;
  const exampleTolerantIncome = exampleMinIncome - moderateIncomeDownTolerance;

  switch (params.renterRequirements.screeningSensitivity) {
    case ScreeningSensitivity.STRICT:
      return `
<output_examples>
<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: 650
- Monthly income: $4000
- Desired move-in date: 2024-12-15

Expected Output:
{{
  "qualified": true,
  "renterRejectionReason": ""
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: N/A (no credit history)
- Monthly income: $4500
- Desired move-in date: 2024-12-20

Expected Output:
{{
  "qualified": false,
  "renterRejectionReason": "I appreciate your interest in our property. This rental requires a verifiable credit score of at least 650 as part of our screening criteria. Unfortunately, without a verifiable credit score, we're unable to proceed with the application process. Thank you for considering this property."
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now
- Accepts cosigners: true

Renter Info:
- Credit score: N/A (no credit history)
- Monthly income: $4200
- Desired move-in date: 2024-12-20
- Conversation highlights: "Has a co-signer with 720 credit score"

Expected Output:
{{
  "qualified": true,
  "renterRejectionReason": ""
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: 640 (below requirement)
- Monthly income: $4500
- Desired move-in date: 2024-12-20

Expected Output:
{{
  "qualified": false,
  "renterRejectionReason": "I've reviewed your information with our team. I know credit scores don't tell the whole story, but unfortunately we're unable to move forward with scores below 650. I genuinely appreciate the time you've taken to explore this property."
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: 700
- Monthly income: $3800 (below requirement)
- Desired move-in date: 2024-12-10

Expected Output:
{{
  "qualified": false,
  "renterRejectionReason": "Thank you for your interest in our property. I've discussed your application with our team, and I understand that income can fluctuate, but we require a minimum of $4000 monthly income and won't be able to proceed with the showing. I genuinely appreciate the time you've taken to explore this property."
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: 670
- Monthly income: $4200
- Desired move-in date: 2025-02-15 (more than 40 days from current date)

Expected Output:
{{
  "qualified": false,
  "renterRejectionReason": "I've discussed your application with our team. Regarding move in date, we need someone who can move in closer to when the property becomes available and won't be proceeding with a showing at this time. If your timeline becomes more flexible or you'd like to check availability closer to your desired move-in date, please feel free to reach out. I genuinely appreciate your interest in our property."
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now
- Accepts cosigners: true

Renter Info:
- Credit score: 620 (below requirement)
- Monthly income: $4200
- Desired move-in date: 2024-12-20
- Conversation highlights: "Has a co-signer with 720 credit score"

Expected Output:
{{
  "qualified": true,
  "renterRejectionReason": ""
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: 620 (below requirement)
- Monthly income: $3500 (below requirement)
- Desired move-in date: 2025-03-01 (well beyond 40 days)

Expected Output:
{{
  "qualified": false,
  "renterRejectionReason": "Thank you for your interest in our property. After reviewing your application with our team, we're unable to move forward as you don't meet several of our requirements. We require a minimum credit score of 650 and monthly income of $4000, and we're looking for someone who can move in within the next 40 days. I genuinely appreciate the time you've taken to explore this property."
}}
</example>
</output_examples>
`;

    case ScreeningSensitivity.MODERATE:
      return `
<output_examples>
Try to stick to the following examples whenever possible. If rejection text includes some info about tolerances, then it should not be included in the rejection reason.

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: 650
- Monthly income: $4800
- Desired move-in date: 2024-12-15

Expected Output:
{{
  "qualified": true,
  "renterRejectionReason": ""
}}
</example>

<example>
Property Requirements:
- Minimum credit score: ${actualMinCreditScore}
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: N/A (no credit history)
- Monthly income: $4500
- Desired move-in date: 2024-12-20

Expected Output:
{{
  "qualified": false,
  "renterRejectionReason": "I've reviewed your information with our team. For this property, we require a verifiable credit score of at least ${actualMinCreditScore}, though I could potentially present your application to the owner if you have a verifiable score of at least ${actualTolerantCreditScore}. Since we don't have a verifiable credit score to present, we won't be able to move forward with scheduling a showing. I genuinely appreciate the time you've taken to explore this property."
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now
- Accepts cosigners: true

Renter Info:
- Credit score: N/A (no credit history)
- Monthly income: $4200
- Desired move-in date: 2024-12-20
- Conversation highlights: "Has a co-signer with 720 credit score"

Expected Output:
{{
  "qualified": true,
  "renterRejectionReason": ""
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: 630 (within ${moderateCreditScoreDownTolerance}-points tolerance)
- Monthly income: $3500 (within $${moderateIncomeDownTolerance} tolerance)
- Desired move-in date: 2024-12-20

Expected Output:
{{
  "qualified": true,
  "renterRejectionReason": ""
}}
</example>

<example>
Property Requirements:
- Minimum credit score: ${actualMinCreditScore}
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: ${actualMinCreditScore - 100} (below requirement)
- Monthly income: $4500
- Desired move-in date: 2024-12-15

Expected Output:
{{
  "qualified": false,
  "renterRejectionReason": "I've reviewed your information with our team. I understand that credit scores don't tell the whole story, but unfortunately we're unable to move forward due to the credit score requirement. For this property, we require a minimum of ${actualMinCreditScore}, though I could potentially present your application to the owner if your score is at least ${actualTolerantCreditScore}. I genuinely appreciate the time you've taken to explore this property."
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: 680
- Monthly income: $2800 (below requirement)
- Desired move-in date: 2024-12-10

Expected Output:
{{
  "qualified": false,
  "renterRejectionReason": "After reviewing your application with our team, I need to share that this property requires $4000 in monthly income. While I recognize that income situations can vary, I could potentially present applications with $${exampleTolerantIncome} monthly income to the property owner for review. Unfortunately, anything below that threshold means we won't be able to move forward with scheduling a showing. Thank you for taking the time to consider this property."
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: 670
- Monthly income: $2900 (individual income below requirement)
- Desired move-in date: 2024-12-15
- Conversation highlights: "Total household income with spouse is $4200"

Expected Output:
{{
  "qualified": true,
  "renterRejectionReason": ""
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: 660
- Monthly income: $4200
- Desired move-in date: 2025-02-15 (more than 40 days from current date)

Expected Output:
{{
  "qualified": false,
  "renterRejectionReason": "I've discussed your application with our team. Regarding move in date, we need someone who can move in closer to when the property becomes available and won't be proceeding with a showing at this time. If your timeline becomes more flexible or you'd like to check availability closer to your desired move-in date, please feel free to reach out. I genuinely appreciate your interest in our property."
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: 2025-01-15

Renter Info:
- Credit score: 670
- Monthly income: $4500
- Desired move-in date: 2025-01-15 (matches property availability)

Expected Output:
{{
  "qualified": true,
  "renterRejectionReason": ""
}}
</example>

<example>
Property Requirements:
- Minimum credit score: 650
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now
- Accepts cosigners: true

Renter Info:
- Credit score: 600 (below requirement)
- Monthly income: $4200
- Desired move-in date: 2024-12-20
- Conversation highlights: "Has a co-signer with 720 credit score"

Expected Output:
{{
  "qualified": true,
  "renterRejectionReason": ""
}}
</example>

<example>
Property Requirements:
- Minimum credit score: ${actualMinCreditScore}
- Monthly income requirement: 2x rent ($4000 for $2000 rent)
- Move-in date: within 40 days from current date (2024-12-01)
- Property available: Now

Renter Info:
- Credit score: ${actualMinCreditScore - 100} (below requirement)
- Monthly income: $2500 (below requirement)
- Desired move-in date: 2025-03-01 (well beyond 40 days)

Expected Output:
{{
  "qualified": false,
  "renterRejectionReason": "Thank you for your interest in our property. After reviewing your application with our team, we're unable to move forward as you don't meet several of our requirements. For this property, we require a minimum credit score of ${actualMinCreditScore} (though scores of ${actualTolerantCreditScore}+ could potentially be presented to the owner) and monthly income of $4000 (with potential consideration for $${exampleTolerantIncome}+), and we're looking for someone who can move in within the next 40 days. Unfortunately, your current situation falls outside these ranges. I genuinely appreciate the time you've taken to explore this property."
}}
</example>
</output_examples>
`;

    default:
      return '';
  }
}

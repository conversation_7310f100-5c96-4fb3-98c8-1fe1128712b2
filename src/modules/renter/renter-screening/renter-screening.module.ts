import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RenterScreening } from './renter-screening.entity';
import { RenterScreeningService } from './renter-screening.service';
import { RenterModule } from '../renter/renter.module';
import { RenterScreeningController } from './renter-screening.controller';
import { AiModule } from 'src/modules/ai/ai.module';
import { PropertyInquiryModule } from '../../investor/property-inquiry/property-inquiry.module';
import { FollowUpModule } from '../../shared/communication/follow-up/follow-up.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([RenterScreening]),
    RenterModule,
    AiModule,
    FollowUpModule,
    forwardRef(() => PropertyInquiryModule),
  ],
  controllers: [RenterScreeningController],
  providers: [RenterScreeningService],
  exports: [RenterScreeningService],
})
export class RenterScreeningModule {}

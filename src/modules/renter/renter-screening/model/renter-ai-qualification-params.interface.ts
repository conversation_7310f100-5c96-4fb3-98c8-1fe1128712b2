import { LeaseConditions } from '../../../investor/property/property/property-details/lease-conditions/lease-conditions.entity';
import { RenterRequirements } from '../../../investor/property/property/property-details/renter-requirements/renter-requirements.entity';
import { Renter } from '../../renter/renter.entity';
import { RenterScreening } from '../renter-screening.entity';

export interface RenterAiQualificationParams {
  leaseConditions: LeaseConditions;
  renterRequirements: RenterRequirements;
  renterScreening: RenterScreening;
  renter: Renter;

  shouldQualifyIncome: boolean;
  shouldQualifyCreditScore: boolean;
  shouldQualifyMoveInDate: boolean;
  propertyAvailabilityStatus: string;
  currentDate: string;
}

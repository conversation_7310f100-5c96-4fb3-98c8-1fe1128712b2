export interface RenterQualificationResult {
  /**
   * Indicates whether the renter has been qualified based on the screening criteria.
   */
  qualified: boolean;

  /**
   * Reason for rejection if the renter is not qualified.
   */
  renterRejectionReason: string | null;

  /**
   * Indicates whether the qualification process was skipped, e.g.,
   * owner opted out of screening or no requirements were set, etc.
   */
  qualificationSkipped: boolean;
}

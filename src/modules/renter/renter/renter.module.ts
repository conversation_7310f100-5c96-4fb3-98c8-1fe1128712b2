import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiModule } from '../../ai/ai.module';
import { UserModule } from '../../shared/user/user.module';
import { RenterController } from './renter.controller';
import { Renter } from './renter.entity';
import { RenterService } from './renter.service';

@Module({
  imports: [TypeOrmModule.forFeature([Renter]), UserModule, AiModule],
  controllers: [RenterController],
  providers: [RenterService],
  exports: [RenterService],
})
export class RenterModule {}

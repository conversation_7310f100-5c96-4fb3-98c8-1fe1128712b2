import { Injectable } from '@nestjs/common';
import { Renter } from '../renter.entity';
import { FieldValue } from '../../../ai/models/field-value';
import { EmploymentStatus } from '../enums/employment-status.enum';
import ParsingUtils from '../../../../utils/parsing.utils';
import { RenterWithTypesDto } from '../models/renter-with-types.dto';
import { Conversation } from '../../../shared/communication/conversation/entities/conversation.entity';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { TimezoneUtils } from '../../../../utils/timezone.utils';
import { parseConversationToUpdateRenterProfileTemplate } from '../../../ai/prompts/parse-conversation-to-update-renter-profile.template';
import { LanguageModelsEnum } from '../../../ai/enums/language-models.enum';
import { AiService } from '../../../ai/ai.service';
import { RenterService } from '../renter.service';

@Injectable()
export class RenterProfileUpdateService {
  constructor(
    private readonly aiService: AiService,
    private readonly renterService: RenterService,
  ) {}

  async updateRenterProfileFromRenterResponse(
    renterMessagesAsXml: string,
    renter: Renter,
    conversation: Conversation,
    property: Property,
  ): Promise<Renter> {
    const { city, timeZone } = await property.location;
    const renterProfile = Renter.getDtoWithTypes(renter);
    let renterFieldsToUpdate: FieldValue[] = [];

    try {
      renterFieldsToUpdate = <FieldValue[]>JSON.parse(
        <string>await this.aiService.getResponseWithChatMemory(
          {
            input: renterMessagesAsXml,
            currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone),
            dataToParse: JSON.stringify(renterProfile),
          },
          conversation.id,
          parseConversationToUpdateRenterProfileTemplate,
          5,
          LanguageModelsEnum.GPT_4_MINI,
        ),
      );
    } catch (error) {
      if (!error?.message?.includes('Unexpected token')) {
        console.error('Error when trying to update the renter profile (haiku)', error);
      } else {
        console.log('[Skip renter profile update] Error:', error);
      }

      return renter;
    }

    const containsDesiredMoveInDate = renterFieldsToUpdate.some((field) => field.fieldName === 'desiredMoveInDate');
    const containsPhoneNumber = renterFieldsToUpdate.some((field) => field.fieldName === 'phoneNumber');

    if (containsDesiredMoveInDate || containsPhoneNumber) {
      renterFieldsToUpdate = renterFieldsToUpdate.filter((field) => {
        return field.fieldName !== 'desiredMoveInDate' && field.fieldName !== 'phoneNumber';
      });

      const lightweightRenterProfile = {
        desiredMoveInDate: renterProfile.desiredMoveInDate,
      };

      let sonnetParsedRenterFields: FieldValue[] = [];

      try {
        sonnetParsedRenterFields = <FieldValue[]>JSON.parse(
          <string>await this.aiService.getResponseWithChatMemory(
            {
              input: renterMessagesAsXml,
              currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone),
              dataToParse: JSON.stringify(lightweightRenterProfile),
            },
            conversation.id,
            parseConversationToUpdateRenterProfileTemplate,
            5,
            LanguageModelsEnum.CLAUDE_4_SONNET,
          ),
        );
      } catch (error) {
        if (!error?.message?.includes('Unexpected token')) {
          console.error('Error when trying to update the renter profile (sonnet)', error);
        } else {
          console.log('[Removed complex fields from renter update payload] Sonnet check error:', error);
        }
      }

      renterFieldsToUpdate = renterFieldsToUpdate.concat(sonnetParsedRenterFields);
    }

    renterFieldsToUpdate = this.filterOutEmptyValues(renterFieldsToUpdate);

    let updatedRenter: Renter;

    try {
      updatedRenter = this.updateRenterFromJson(renter, renterFieldsToUpdate);

      if (this.checkIfRenterShouldBeUpdatedInDb(renterProfile, updatedRenter)) {
        console.log(
          `[Update renter profile] Message: "${renterMessagesAsXml}". Fields to update: "${JSON.stringify(renterFieldsToUpdate)}".`,
        );
        await this.renterService.save(updatedRenter);
      } else {
        console.log(`[Skip renter profile update] Message: "${renterMessagesAsXml}".`);
      }
    } catch (error) {
      console.error('Error when trying to update the renter profile', error);

      return renter;
    }

    return updatedRenter;
  }

  updateRenterFromJson(renter: Renter, data: FieldValue[]): Renter {
    for (const item of data) {
      if (!item.value || item.value === 'undefined') continue;

      switch (item.fieldName) {
        case 'employmentStatus':
          renter.employmentStatus = item.value as EmploymentStatus;
          break;
        case 'hasChildren':
          renter.hasChildren = Boolean(item.value);
          break;
        case 'numberOfChildren':
          renter.numberOfChildren = ParsingUtils.parseStringToNumber(item.value);
          break;
        case 'occupation':
          renter.occupation = item.value as string;
          break;
        case 'hasPets':
          renter.hasPets = Boolean(item.value);
          break;
        case 'petType':
          renter.petType = item.value as string;
          break;
        case 'petBreed':
          renter.petBreed = item.value as string;
          break;
        case 'petSize':
          renter.petSize = item.value as string;
          break;
        case 'petWeightPounds':
          renter.petWeightPounds = Math.round(item.value as unknown as number);
          break;
        case 'petDescription':
          renter.petDescription = item.value as string;
          break;
        case 'isSmoker':
          renter.isSmoker = Boolean(item.value);
          break;
        case 'hasHousingVoucher':
          renter.hasHousingVoucher = Boolean(item.value);
          break;
        case 'hasCosigner':
          renter.hasCosigner = Boolean(item.value);
          break;
        case 'creditScore':
          renter.creditScore = item.value as string;
          break;
        case 'monthlyIncome':
          renter.monthlyIncome = ParsingUtils.parseStringToNumber(item.value);
          break;
        case 'desiredLeaseTerm':
          renter.desiredLeaseTerm = item.value as string;
          break;
        case 'desiredMoveInDate':
          renter.desiredMoveInDate = new Date(item.value);
          break;
        case 'phoneNumber':
          renter.user.phoneNumber = item.value as string;
          break;
        default:
          console.warn(`Unknown field: ${item.fieldName}`);
      }
    }

    return renter;
  }

  private filterOutEmptyValues(data: FieldValue[]): FieldValue[] {
    return data.filter((item) => {
      return item.value !== null && item.value !== undefined && item.value !== 'N/A' && item.value !== '';
    });
  }

  private checkIfRenterShouldBeUpdatedInDb(existingRenter: RenterWithTypesDto, updatedRenter: Renter): boolean {
    return (
      existingRenter.employmentStatus.value !== updatedRenter.employmentStatus ||
      existingRenter.hasChildren.value !== updatedRenter.hasChildren ||
      existingRenter.numberOfChildren.value !== updatedRenter.numberOfChildren ||
      existingRenter.occupation.value !== updatedRenter.occupation ||
      existingRenter.hasPets.value !== updatedRenter.hasPets ||
      existingRenter.petType.value !== updatedRenter.petType ||
      existingRenter.petBreed.value !== updatedRenter.petBreed ||
      existingRenter.petSize.value !== updatedRenter.petSize ||
      existingRenter.petWeightPounds.value !== updatedRenter.petWeightPounds ||
      existingRenter.petDescription.value !== updatedRenter.petDescription ||
      existingRenter.isSmoker.value !== updatedRenter.isSmoker ||
      existingRenter.creditScore.value !== updatedRenter.creditScore ||
      existingRenter.monthlyIncome.value !== updatedRenter.monthlyIncome ||
      existingRenter.desiredLeaseTerm.value !== updatedRenter.desiredLeaseTerm ||
      existingRenter.desiredMoveInDate.value !== updatedRenter.desiredMoveInDate ||
      existingRenter.hasHousingVoucher.value !== updatedRenter.hasHousingVoucher ||
      existingRenter.hasCosigner.value !== updatedRenter.hasCosigner ||
      existingRenter.phoneNumber.value !== updatedRenter.user.phoneNumber
    );
  }
}

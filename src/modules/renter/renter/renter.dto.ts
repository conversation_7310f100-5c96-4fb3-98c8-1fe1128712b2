import { instance<PERSON><PERSON><PERSON><PERSON> } from 'class-transformer';
import { IsBoolean, IsEmail, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

import { ApiPropertyOptional } from '@nestjs/swagger';

import { User } from '../../shared/user/entities/user.entity';
import { EmploymentStatus } from './enums/employment-status.enum';
import { Renter } from './renter.entity';
import { IsUSPhoneNumber } from '../../../utils/validators/us-phone-number.validator';

export class RenterDto {
  id: string;
  name: string;
  occupation: string;
  monthlyIncome: number;
  creditScore: string;
  email: string;
  user: User;
  createdAt: Date;
  hasPets: boolean;
  petType: string;
  petBreed: string;
  petSize: string;
  petWeightPounds: number;
  petDescription: string;
  isSmoker: boolean;
  hasChildren: boolean;
  numberOfChildren: number;
  desiredMoveInDate: Date;
  desiredLeaseTerm: string;
  renterScreeningId: string;
}

export class UpdateRenterDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ example: 'Pablo Escobar' })
  name: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ example: 'Sweets Diller' })
  occupation: string;

  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({ example: 3500 })
  monthlyIncome: number;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ example: '650-660' })
  creditScore: string;

  @IsString()
  @IsOptional()
  @IsUSPhoneNumber()
  @ApiPropertyOptional({ example: '18143148427' })
  phoneNumber: string;

  @IsString()
  @IsOptional()
  @IsEmail()
  @ApiPropertyOptional({ example: '<EMAIL>' })
  email: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ example: false })
  confirmedToFitRequirements: boolean;

  @IsOptional()
  @ApiPropertyOptional({ example: 'Employed' })
  employmentStatus?: EmploymentStatus;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ example: false })
  hasChildren: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ example: false })
  hasPets: boolean;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ example: 'Dog' })
  petType: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ example: 'Poodle' })
  petBreed: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ example: 'Large' })
  petSize: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ example: '25' })
  petWeightPounds: number;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ example: 'Very cute' })
  petDescription: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ example: false })
  isSmoker: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ example: false })
  wasNotifiedAboutRequirements: boolean;

  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({ example: 2 })
  numberOfChildren: number;
}

export function convertToRenterToDto(renter: Renter): RenterDto {
  return <RenterDto>instanceToPlain(renter, { excludeExtraneousValues: true });
}

export function convertToUpdateRenterDto(renter: Renter): UpdateRenterDto {
  return <UpdateRenterDto>instanceToPlain(renter, { excludeExtraneousValues: true });
}

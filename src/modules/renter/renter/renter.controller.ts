import { Body, Controller, Get, Param, Patch, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { RenterDto, UpdateRenterDto, convertToRenterToDto } from './renter.dto';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { HasRoles } from '../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../shared/auth/models/roles-enum';
import { RenterService } from './renter.service';

@ApiTags('renter')
@Controller('renter')
@ApiBearerAuth()
@UseGuards(RolesGuard)
export class RenterController {
  constructor(private readonly renterService: RenterService) {}

  @Get(':id')
  @ApiOkResponse({
    description: 'Get renter profile by ID',
    type: RenterDto,
  })
  @HasRoles(Role.INVESTOR)
  async findByRenterId(@Param('id') id: string): Promise<RenterDto> {
    const renter = await this.renterService.findById(id, true);

    return convertToRenterToDto(renter);
  }

  @Get('by-email/:email')
  @ApiOkResponse({
    description: 'Get renter profile by email',
    type: RenterDto,
  })
  @HasRoles(Role.INVESTOR)
  async findByRenterByEmail(@Param('email') email: string): Promise<RenterDto> {
    const renter = await this.renterService.findByEmail(email);

    return convertToRenterToDto(renter);
  }

  @Patch('update/:id')
  @ApiOkResponse({ description: 'Update renter fields' })
  @ApiBody({
    type: UpdateRenterDto,
  })
  @HasRoles(Role.RENTER)
  async updateRenter(@Param('id') id: string, @Body() body: UpdateRenterDto): Promise<void> {
    return await this.renterService.update(id, body);
  }
}

import { Expose, instanceTo<PERSON>lain } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
} from 'typeorm';

import { RescheduleRequest } from '../../investor/reschedule/reschedule-request.entity';
import { ShowingRequest } from '../../investor/showing-request/showing-request.entity';
import { User } from '../../shared/user/entities/user.entity';
import { EmploymentStatus } from './enums/employment-status.enum';
import { RenterWithTypesDto } from './models/renter-with-types.dto';
import { RenterDto } from './renter.dto';
import { RenterScreening } from '../renter-screening/renter-screening.entity';

@Entity()
export class Renter {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @OneToOne(() => User, {
    eager: true,
    cascade: ['update'],
  })
  @Expose()
  @JoinColumn()
  user: User;

  @RelationId((renter: Renter) => renter.user)
  userId: string;

  @Expose()
  @Column({ type: 'enum', enum: EmploymentStatus, nullable: true })
  employmentStatus: EmploymentStatus;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasPets: boolean;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  petType: string;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  petBreed: string;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  petSize: string;

  @Expose()
  @Column({ type: 'integer', nullable: true })
  petWeightPounds: number;

  @Expose()
  @Column({ type: 'text', nullable: true })
  petDescription: string;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  isSmoker: boolean;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: true })
  occupation: string;

  @Expose()
  @Column({ type: 'integer', nullable: true })
  monthlyIncome: number;

  @Expose()
  @Column({ type: 'varchar', length: 120, nullable: true })
  creditScore: string;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasChildren: boolean;

  @Expose()
  @Column({ type: 'integer', nullable: true })
  numberOfChildren: number;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  desiredLeaseTerm: string;

  @Expose()
  @Column({ type: 'date', nullable: true })
  desiredMoveInDate: Date;

  @OneToMany(() => ShowingRequest, (showingRequest) => showingRequest.renter, {
    lazy: true,
  })
  showingRequests: Promise<ShowingRequest[]> | ShowingRequest[];

  @OneToMany(() => RescheduleRequest, (rescheduleRequest) => rescheduleRequest.renter, {
    lazy: true,
  })
  rescheduleRequests: Promise<RescheduleRequest[]> | RescheduleRequest[];

  @OneToOne(() => RenterScreening, {
    lazy: true,
    cascade: true,
    nullable: true,
  })
  @JoinColumn()
  renterScreening: Promise<RenterScreening | null> | RenterScreening | null;

  @Expose()
  @Column({ nullable: true })
  renterScreeningId: string | null;

  @Expose()
  @Column({ type: 'varchar', nullable: true })
  transUnionRenterId: string;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasHousingVoucher: boolean;

  @Expose()
  @Column({ type: 'boolean', nullable: true })
  hasCosigner: boolean;

  @DeleteDateColumn()
  deletedAt?: Date;

  @Expose()
  @CreateDateColumn({ type: 'timestamp' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  public updatedAt!: Date;

  // TODO complete pet details
  public static getDtoWithTypes(renter: Renter): RenterWithTypesDto {
    return {
      employmentStatus: {
        value: renter.employmentStatus,
        type: 'enum',
        enum: {
          type: 'EmploymentStatus',
          values: Object.values(EmploymentStatus),
        },
        fieldDescription: 'For capturing if the renter has a job, owns a business, studies or unemployed.',
        aiInstructions:
          "Assume the value based on the conversation. Fill it in only if renter mentiones their occupation, job, business, studies or unemployment. Do not assume it if renter doesn't mention their job.",
      },
      hasPets: {
        value: renter.hasPets,
        type: 'boolean',
        fieldDescription: 'For capturing if the renter has pets or not',
        aiInstructions: 'Should be filled if renter is asking pet related questions',
      },
      petType: {
        value: renter.petType,
        type: 'string',
        fieldDescription: 'For capturing the type of pet, could be dog, cat and any other pet type',
        aiInstructions: 'Should be filled if renter mentions the type of pet they have or want to have in the property',
      },
      petBreed: {
        value: renter.petBreed,
        type: 'string',
      },
      petSize: {
        value: renter.petSize,
        type: 'string',
        aiInstructions:
          'Could be small or large, you can assume the size based on the pet breed or when renter mentions the size',
      },
      petWeightPounds: {
        value: renter.petWeightPounds,
        type: 'number',
      },
      petDescription: {
        value: renter.petDescription,
        type: 'string',
      },
      isSmoker: {
        value: renter.isSmoker,
        type: 'boolean',
      },
      occupation: {
        value: renter.occupation,
        type: 'string',
      },
      monthlyIncome: {
        value: renter.monthlyIncome,
        type: 'number',
      },
      creditScore: {
        value: renter.creditScore,
        type: 'string',
      },
      hasChildren: {
        value: renter.hasChildren,
        type: 'boolean',
      },
      numberOfChildren: {
        value: renter.numberOfChildren,
        type: 'number',
      },
      desiredMoveInDate: {
        type: 'Date string: YYYY-MM-DD',
        value: renter.desiredMoveInDate,
        fieldDescription:
          'The date when the renter wants to move in to the property. It is important to differentiate the "desired move in date" and the "showing date". Desired move in date is the date when the renter wants to move in. Showing date is the date when the renter wants to have a tour of the property, just to see it, it doesn\'t mean that he will move in on this date.',
        aiInstructions:
          'Fill it only when the renter uses words like "move in", "move in date", "move in day" etc. Do not assume the date if the renter doesn\'t mention these. It is safe to omit if you are not sure if the renter is talking about the move in date or the showing date.',
      },
      desiredLeaseTerm: {
        value: renter.desiredLeaseTerm,
        type: 'string',
        fieldDescription:
          'For capturing the term of the lease the renter is looking for. Could be "Month to Month", "1 Months", "2 Months" etc.',
        aiInstructions:
          "Only fill it in if the renter mentions the lease terms, do not assume it if the renter doesn't mention it.",
      },
      phoneNumber: {
        value: renter.user.phoneNumber,
        type: 'string',
        fieldDescription: 'Phone number of the renter',
        aiInstructions: `The phone number should always be saved in the following format: +1234567890. If renter provides it in a different format, you should convert it to the above format. IMPORTANT: Only update it if renter is explicit that this is his number, not some one else's`,
      },
      hasHousingVoucher: {
        value: renter.hasHousingVoucher,
        type: 'boolean',
        fieldDescription: 'For capturing if the renter has a housing voucher',
        aiInstructions:
          'Fill it in if the renter asks if housing voucher/section 8/subsidised rental is accepted or mentions that they have a housing voucher/section 8/subsidised rental.',
      },
      hasCosigner: {
        value: renter.hasCosigner,
        type: 'boolean',
        fieldDescription: 'For capturing if the renter has a cosigner',
        aiInstructions: 'Fill it in if the renter mentions that they have a cosigner.',
      },
    };
  }

  static convertToRenterDto(renter: Renter): RenterDto {
    return <RenterDto>instanceToPlain(renter, { excludeExtraneousValues: true });
  }
}

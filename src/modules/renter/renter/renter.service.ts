import { instance<PERSON><PERSON><PERSON><PERSON> } from 'class-transformer';
import { Repository } from 'typeorm';

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Role } from '../../shared/auth/models/roles-enum';
import { CommunicationChannel } from '../../shared/communication/conversation/enums/preferred-communication-channel.enum';
import { UserService } from '../../shared/user/user.service';
import { UpdateRenterDto } from './renter.dto';
import { Renter } from './renter.entity';

@Injectable()
export class RenterService {
  constructor(
    @InjectRepository(Renter)
    private readonly renterRepository: Repository<Renter>,
    private readonly userService: UserService,
  ) {}

  async create(
    name: string,
    email: string,
    phoneNumber: string = null,
    preferredCommunicationChannel = CommunicationChannel.EMAIL,
    renterData: Partial<Renter> = {},
  ): Promise<Renter> {
    const capitalizedName = name
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    let user = await this.userService.findByEmail(email);

    if (!user) {
      user = await this.userService.createUser({
        email,
        roles: [Role.RENTER],
        name: capitalizedName,
        phoneNumber,
        preferredCommunicationChannel,
      });
    } else if (!user.roles.includes(Role.RENTER)) {
      user.roles.push(Role.RENTER);
      await this.userService.updateUser(user.id, { roles: user.roles });
    }

    const renter: Renter = this.renterRepository.create({
      ...renterData,
    });

    renter.user = user;

    return this.renterRepository.save(renter);
  }

  async update(id: string, updateRenter: Partial<Renter>): Promise<void> {
    await this.renterRepository.update(id, updateRenter);
  }

  async save(renter: Renter): Promise<void> {
    await this.renterRepository.save(renter);
  }

  async findByUserId(userId: string): Promise<Renter> {
    return this.renterRepository.findOneBy({
      user: { id: userId },
    });
  }

  async findByEmail(email: string): Promise<Renter> {
    return this.renterRepository.findOneBy({
      user: {
        email,
      },
    });
  }

  async findByEmailAndAddRenterRoleIfNeeded(email: string): Promise<Renter> {
    const renter = await this.findByEmail(email);
    await this.addRenterRoleIfItsMissing(renter);

    return renter;
  }

  async findById(id: string, withRelations = false): Promise<Renter> {
    return this.renterRepository.findOne({
      where: { id },
      relations: withRelations ? ['user', 'renterScreening'] : [],
    });
  }

  async findByEmailOrPhone(email: string, phoneNumber: string): Promise<Renter> {
    return this.renterRepository.findOne({
      where: [{ user: { email: email } }, { user: { phoneNumber: phoneNumber } }],
    });
  }

  async findByEmailOrPhoneAndAddRenterRoleIfNeeded(email: string, phoneNumber: string): Promise<Renter> {
    const renter = await this.findByEmailOrPhone(email, phoneNumber);
    await this.addRenterRoleIfItsMissing(renter);

    return renter;
  }

  async findByPhoneNumber(phoneNumber: string): Promise<Renter> {
    return this.renterRepository.findOneBy({
      user: {
        phoneNumber,
      },
    });
  }

  convertToUpdateRenterDto(renter: Renter): UpdateRenterDto {
    return <UpdateRenterDto>instanceToPlain(renter, { excludeExtraneousValues: true });
  }

  private async addRenterRoleIfItsMissing(renter: Renter | null): Promise<void> {
    if (renter && !renter.user.roles.includes(Role.RENTER)) {
      renter.user.roles.push(Role.RENTER);
      await this.userService.updateUser(renter.user.id, { roles: renter.user.roles });
    }
  }
}

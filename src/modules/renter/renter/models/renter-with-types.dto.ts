import { FieldValueWithType } from '../../../ai/models/field-value-with-type';

export interface RenterWithTypesDto {
  employmentStatus: FieldValueWithType;
  isSmoker: FieldValueWithType;
  hasPets: FieldValueWithType;
  petType: FieldValueWithType;
  petBreed: FieldValueWithType;
  petSize: FieldValueWithType;
  petWeightPounds: FieldValueWithType;
  petDescription: FieldValueWithType;
  occupation: FieldValueWithType;
  monthlyIncome: FieldValueWithType;
  creditScore: FieldValueWithType;
  hasChildren: FieldValueWithType;
  numberOfChildren: FieldValueWithType;
  desiredMoveInDate: FieldValueWithType;
  desiredLeaseTerm: FieldValueWithType;
  hasHousingVoucher: FieldValueWithType;
  hasCosigner: FieldValueWithType;
  phoneNumber: FieldValueWithType;
}

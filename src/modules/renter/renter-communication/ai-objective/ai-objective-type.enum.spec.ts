import { aiObjectivePriorityOrder, AiObjectiveType } from './ai-objective-type.enum';

describe('AiObjectiveType', () => {
  it('all objective types should have a specific priority in the list', () => {
    const amountOfObjectives = Object.keys(AiObjectiveType).length;
    const amountOfObjectivesInPriorityList = aiObjectivePriorityOrder.length;

    expect(amountOfObjectivesInPriorityList).toBe(amountOfObjectives);
  });
});

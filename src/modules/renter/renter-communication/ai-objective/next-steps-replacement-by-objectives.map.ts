import { NextStep } from '../response-generation/enums/next-step';
import { AiObjectiveType } from './ai-objective-type.enum';
import { aiObjectiveWithRelatedNextStepMap } from './ai-objective-with-related-next-step.map';

interface NextStepReplacementMapValue {
  blocksStep: NextStep;
  replacesWith: NextStep;
  // Objectives that can be executed at the same time, e.g. ask about credit & income
  worksInParallelWith?: AiObjectiveType[];
}

/**
 * Map that defines the replacement of the next steps based on the objectives.
 */
export const nextStepsReplacementByObjectivesMap = new Map<AiObjectiveType, NextStepReplacementMapValue>([
  [
    AiObjectiveType.ASK_IF_RENTER_HAS_REQUIREMENTS_QUESTION,
    {
      blocksStep: NextStep.SCHEDULE_SHOWING,
      replacesWith: aiObjectiveWithRelatedNextStepMap.get(AiObjectiveType.ASK_IF_RENTER_HAS_REQUIREMENTS_QUESTION),
    },
  ],
  [
    AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE,
    {
      blocksStep: NextStep.SCHEDULE_SHOWING,
      replacesWith: aiObjectiveWithRelatedNextStepMap.get(AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE),
    },
  ],
  [
    AiObjectiveType.EVALUATE_CREDIT_SCORE,
    {
      blocksStep: NextStep.SCHEDULE_SHOWING,
      replacesWith: aiObjectiveWithRelatedNextStepMap.get(AiObjectiveType.EVALUATE_CREDIT_SCORE),
      worksInParallelWith: [AiObjectiveType.EVALUATE_INCOME],
    },
  ],
  [
    AiObjectiveType.EVALUATE_INCOME,
    {
      blocksStep: NextStep.SCHEDULE_SHOWING,
      replacesWith: aiObjectiveWithRelatedNextStepMap.get(AiObjectiveType.EVALUATE_INCOME),
      worksInParallelWith: [AiObjectiveType.EVALUATE_CREDIT_SCORE],
    },
  ],
]);

import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiModule } from '../../../ai/ai.module';
import { PropertyInquiryModule } from '../../../investor/property-inquiry/property-inquiry.module';
import { RenterScreeningModule } from '../../renter-screening/renter-screening.module';
import { AiObjective } from './ai-objective.entity';
import { AiObjectiveService } from './ai-objective.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([AiObjective]),
    AiModule,
    forwardRef(() => PropertyInquiryModule),
    RenterScreeningModule,
  ],
  providers: [AiObjectiveService],
  exports: [AiObjectiveService],
})
export class AiObjectiveModule {}

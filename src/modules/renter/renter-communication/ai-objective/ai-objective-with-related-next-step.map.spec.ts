import { AiObjectiveType } from './ai-objective-type.enum';
import { aiObjectiveWithRelatedNextStepMap } from './ai-objective-with-related-next-step.map';

describe('aiObjectiveWithRelatedNextStepMap', () => {
  it('should have the related next step for each ai objective', () => {
    const amountOfObjectives = Object.keys(AiObjectiveType).length;
    const amountOfKeysInMap = aiObjectiveWithRelatedNextStepMap.size;

    expect(amountOfKeysInMap).toBe(amountOfObjectives);
  });
});

import { NextStep } from '../response-generation/enums/next-step';
import { AiObjectiveType } from './ai-objective-type.enum';

/**
 * This map is used to determine relation between Next Step and Ai Objective.
 * It is used to prevent tallo from asking question related to objective if the same next step is already executed.
 */
export const aiObjectiveWithRelatedNextStepMap = new Map<AiObjectiveType, NextStep>([
  [AiObjectiveType.ASK_IF_RENTER_HAS_REQUIREMENTS_QUESTION, NextStep.ASK_IF_RENTER_HAS_REQUIREMENTS_QUESTION],
  [AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE, NextStep.DISCUSS_MOVE_IN_DATE],
  [AiObjectiveType.EVALUATE_CREDIT_SCORE, NextStep.DISCUSS_CREDIT_SCORE],
  [AiObjectiveType.EVALUATE_INCOME, NextStep.DISCUSS_INCOME],
  [AiObjectiveType.SCHEDULE_SHOWING, NextStep.SCHEDULE_SHOWING],
]);

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AiService, NextStepWithRelatedQuestion } from '../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../ai/enums/language-models.enum';
import { PropertyInquiry } from '../../../investor/property-inquiry/property-inquiry.entity';
import { NextStep } from '../response-generation/enums/next-step';
import { objectiveQuestionOpportunityCheckTemplate } from '../response-generation/prompts/objective-question-opportunity-check.template';
import { AiObjectiveType } from './ai-objective-type.enum';
import { aiObjectiveWithRelatedNextStepMap } from './ai-objective-with-related-next-step.map';
import { AiObjective } from './ai-objective.entity';
import { NextStepsAdjustedToObjectives } from './next-steps-adjusted-to-objectives.interface';
import { nextStepsReplacementByObjectivesMap } from './next-steps-replacement-by-objectives.map';
import { RenterScreeningService } from '../../renter-screening/renter-screening.service';
import { Renter } from '../../renter/renter.entity';
import { TimezoneUtils } from '../../../../utils/timezone.utils';

@Injectable()
export class AiObjectiveService {
  constructor(
    @InjectRepository(AiObjective)
    private readonly aiObjectiveRepository: Repository<AiObjective>,
    private readonly aiService: AiService,
    private readonly renterScreeningService: RenterScreeningService,
  ) {}

  create(type: AiObjectiveType): AiObjective {
    return this.aiObjectiveRepository.create({ type });
  }

  async save(aiObjective: Partial<AiObjective>): Promise<AiObjective> {
    const newAiObjective = this.aiObjectiveRepository.create(aiObjective);
    Object.assign(newAiObjective, aiObjective);
    return this.aiObjectiveRepository.save(newAiObjective);
  }

  async update(id: string, objectivePartialData: Partial<AiObjective>): Promise<void> {
    await this.aiObjectiveRepository.update(id, objectivePartialData);
  }

  async updateObjectiveAndSyncItInItsInquiry(
    renter: Renter,
    objective: AiObjective,
    payload: Partial<Pick<AiObjective, 'completed' | 'value' | 'requirementCompensatingFactors' | 'notes'>>,
  ): Promise<void> {
    await this.update(objective.id, payload);
    this.syncObjectiveAttributes(objective, payload);

    try {
      if (payload.completed && payload.value) {
        await this.renterScreeningService.updateScreeningFromObjective(objective, renter);
      }

      if (payload.requirementCompensatingFactors?.length > 0) {
        await this.renterScreeningService.updateConvoHighlights(renter, payload.requirementCompensatingFactors);
      }
    } catch (error) {
      console.error('Error updating renter screening from objective:', error);
    }
  }

  /**
   * Use to determine if some next steps are blocked by the current objectives.
   *
   * @param currentPriorityObjective current main objective, can be obtained from inquiry
   * @param allObjectives current pending objectives, can be obtained from inquiry
   * @param nextSteps next steps that AI come up with based on the incoming messages
   * @returns modified next steps. if any of the steps are blocked, they will be replaced with the replacement step
   * that is defined in the aiObjectiveBlockingMap
   */
  replaceNextStepsIfObjectiveRequiresAdjustment(
    currentPriorityObjective: AiObjectiveType,
    allObjectives: AiObjectiveType[],
    nextSteps: NextStepWithRelatedQuestion[],
  ): NextStepsAdjustedToObjectives {
    if (!currentPriorityObjective || !allObjectives.length || !nextSteps.length) {
      return {
        adjustedNextSteps: nextSteps,
      };
    }

    const executedObjectives: AiObjectiveType[] = [];
    const nextStepsAdjustedByObjectives = [...nextSteps];

    // Find all objectives that block steps
    for (const objective of allObjectives) {
      const blockingConfig = nextStepsReplacementByObjectivesMap.get(objective);

      if (blockingConfig) {
        const hasBlockedStep = nextSteps.some((step) => step.nextStep === blockingConfig.blocksStep);

        if (hasBlockedStep) {
          executedObjectives.push(objective);
        }
      }
    }

    if (executedObjectives.length > 0) {
      const priorityConfig = nextStepsReplacementByObjectivesMap.get(currentPriorityObjective);
      const blockedStepIndex = nextStepsAdjustedByObjectives.findIndex(
        (step) => step.nextStep === priorityConfig?.blocksStep,
      );

      if (blockedStepIndex !== -1) {
        // Replace blocked step with priority objective's step
        nextStepsAdjustedByObjectives[blockedStepIndex] = {
          ...nextStepsAdjustedByObjectives[blockedStepIndex],
          nextStep: priorityConfig.replacesWith,
        };

        // Handle parallel objectives
        if (priorityConfig.worksInParallelWith) {
          const parallelObjectives = allObjectives.filter((obj) => priorityConfig.worksInParallelWith.includes(obj));

          parallelObjectives.forEach((parallelObj) => {
            const parallelConfig = nextStepsReplacementByObjectivesMap.get(parallelObj);
            nextStepsAdjustedByObjectives.push({
              ...nextStepsAdjustedByObjectives[blockedStepIndex],
              nextStep: parallelConfig.replacesWith,
            });
          });
        }
      }
    }

    return {
      adjustedNextSteps: nextStepsAdjustedByObjectives,
    };
  }

  async evaluateObjectiveQuestionOpportunity(
    answers: string[],
    renterMessagesAsXml: string,
    convoId: string,
    inquiry: PropertyInquiry,
    executedNextSteps: NextStep[],
  ): Promise<NextStep[]> {
    if (!(await PropertyInquiry.hasPendingObjectives(inquiry))) {
      return [];
    }

    let city = 'New York';
    let timeZone = 'America/New_York';

    try {
      const property = await inquiry.property;
      if (property && property.location) {
        const location = await property.location;
        if (location) {
          city = location.city || city;
          timeZone = location.timeZone || timeZone;
        }
      }
    } catch (error) {
      console.error('Error accessing property location:', error);
    }

    const pendingObjectives = await PropertyInquiry.getPendingObjectives(inquiry);

    const currentObjective = PropertyInquiry.getCurrentPriorityObjective(pendingObjectives);
    if (!pendingObjectives.length || !currentObjective) {
      return [];
    }

    const nextStepRelatedToCurrentPriorityObjective = aiObjectiveWithRelatedNextStepMap.get(currentObjective.type);
    if (executedNextSteps.includes(nextStepRelatedToCurrentPriorityObjective)) {
      return [];
    }

    const executableParallelObjectives = this.getExecutableParallelObjectives(
      currentObjective,
      pendingObjectives,
      executedNextSteps,
    );

    const objectivesToProcess = [currentObjective, ...executableParallelObjectives];
    const additionalNextStepsToExecuteBasedOnObjectives: NextStep[] = [];

    const objectiveNamesAsStrings = objectivesToProcess.map((objective) => `"${objective.type}"`).join(', ');

    try {
      const aiResponse = JSON.parse(
        <string>await this.aiService.getResponseWithChatMemory(
          {
            answers: answers.length ? this.formatAnswersToXml(answers) : '-',
            input: renterMessagesAsXml,
            objectives: objectiveNamesAsStrings,
            currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone),
          },
          convoId,
          objectiveQuestionOpportunityCheckTemplate,
          8,
          LanguageModelsEnum.CLAUDE_4_SONNET_REASONER,
        ),
      );

      if (aiResponse.addObjectiveQuestion) {
        objectivesToProcess.forEach((objective) => {
          additionalNextStepsToExecuteBasedOnObjectives.push(aiObjectiveWithRelatedNextStepMap.get(objective.type));
        });
      }
    } catch (error) {
      console.log('Error parsing objective question response:', error);
    }

    return additionalNextStepsToExecuteBasedOnObjectives;
  }

  private getExecutableParallelObjectives(
    currentObjective: AiObjective,
    allObjectives: AiObjective[],
    executedNextSteps: NextStep[],
  ): AiObjective[] {
    const config = nextStepsReplacementByObjectivesMap.get(currentObjective.type);
    if (!config?.worksInParallelWith) {
      return [];
    }

    return allObjectives.filter((obj) => {
      if (!config.worksInParallelWith.includes(obj.type)) {
        return false;
      }

      // Check if parallel objective can be executed
      const nextStep = aiObjectiveWithRelatedNextStepMap.get(obj.type);

      return !executedNextSteps.includes(nextStep);
    });
  }

  private formatAnswersToXml(answers: string[]): string {
    if (!Array.isArray(answers) || answers.length === 0) {
      return '';
    }

    return answers.map((answer) => `<answers_from_ai>${answer}</answers_from_ai>`).join('');
  }

  // Helper to update entity fields only for provided payload values.
  private syncObjectiveAttributes(objective: AiObjective, payload: Partial<AiObjective>): void {
    Object.keys(payload).forEach((key) => {
      if (payload[key] !== undefined) {
        objective[key] = payload[key];
      }
    });
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AiObjectiveType } from './ai-objective-type.enum';
import { AiObjective } from './ai-objective.entity';
import { AiObjectiveService } from './ai-objective.service';
import { AiService, NextStepWithRelatedQuestion } from '../../../ai/ai.service';
import { PropertyInquiryService } from '../../../investor/property-inquiry/property-inquiry.service';
import { NextStep } from '../response-generation/enums/next-step';
import { PropertyInquiry } from '../../../investor/property-inquiry/property-inquiry.entity';
import { NextStepsAdjustedToObjectives } from './next-steps-adjusted-to-objectives.interface';
import { RenterScreeningService } from '../../renter-screening/renter-screening.service';

describe('AiObjectiveService', () => {
  let service: AiObjectiveService;

  const mockAiObjectiveRepository = {
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
  };

  const mockAiService = {
    getResponseWithChatMemory: jest.fn(),
  };

  const mockPropertyInquiryService = {
    addEvent: jest.fn(),
  };

  const mockRenterScreeningService: Partial<RenterScreeningService> = {
    updateScreeningFromObjective: jest.fn(),
    updateConvoHighlights: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiObjectiveService,
        {
          provide: getRepositoryToken(AiObjective),
          useValue: mockAiObjectiveRepository,
        },
        {
          provide: AiService,
          useValue: mockAiService,
        },
        {
          provide: PropertyInquiryService,
          useValue: mockPropertyInquiryService,
        },
        {
          provide: RenterScreeningService,
          useValue: mockRenterScreeningService,
        },
      ],
    }).compile();

    service = module.get<AiObjectiveService>(AiObjectiveService);

    // Clear mocks and set up default behavior for each test
    jest.clearAllMocks();

    // Default mock behavior - tests can override this if needed
    mockAiService.getResponseWithChatMemory.mockResolvedValue('{ "addObjectiveQuestion": true }');
  });

  describe('Next steps replacements: "replaceNextStepsIfObjectiveRequiresAdjustment"', () => {
    const scheduleShowingNextStep: NextStepWithRelatedQuestion = {
      nextStep: NextStep.SCHEDULE_SHOWING,
      relatedText: 'Can you show me the property?',
    };
    const nextSteps: NextStepWithRelatedQuestion[] = [scheduleShowingNextStep];

    it('returns unmodified state when objectives do not block any next steps', () => {
      const result = service.replaceNextStepsIfObjectiveRequiresAdjustment(
        AiObjectiveType.SCHEDULE_SHOWING,
        [AiObjectiveType.SCHEDULE_SHOWING],
        nextSteps,
      );

      const expectedOutput: NextStepsAdjustedToObjectives = {
        adjustedNextSteps: nextSteps,
      };

      expect(result).toEqual(expectedOutput);
    });

    it('returns unmodified state when next steps do not match any blocked steps', () => {
      const randomNextSteps: NextStepWithRelatedQuestion[] = [
        { nextStep: NextStep.ANSWER_SCAM_QUESTION, relatedText: 'Is this scam?' },
        { nextStep: NextStep.GENERAL_CONVERSATION, relatedText: 'How are you?' },
      ];

      const result = service.replaceNextStepsIfObjectiveRequiresAdjustment(
        AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE,
        [
          AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE,
          AiObjectiveType.EVALUATE_CREDIT_SCORE,
          AiObjectiveType.EVALUATE_INCOME,
          AiObjectiveType.SCHEDULE_SHOWING,
        ],
        [
          { nextStep: NextStep.ANSWER_SCAM_QUESTION, relatedText: 'Is this scam?' },
          { nextStep: NextStep.GENERAL_CONVERSATION, relatedText: 'How are you?' },
        ],
      );

      const expectedOutput: NextStepsAdjustedToObjectives = {
        adjustedNextSteps: randomNextSteps,
      };

      expect(result).toEqual(expectedOutput);
    });

    it('returns unmodified state when no priority objective is provided', () => {
      const result = service.replaceNextStepsIfObjectiveRequiresAdjustment(
        null,
        [AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE],
        nextSteps,
      );

      const expectedOutput: NextStepsAdjustedToObjectives = {
        adjustedNextSteps: nextSteps,
      };

      expect(result).toEqual(expectedOutput);
    });

    it('returns unmodified state when no pending objectives are provided', () => {
      const result = service.replaceNextStepsIfObjectiveRequiresAdjustment(
        AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE,
        [],
        nextSteps,
      );

      const expectedOutput: NextStepsAdjustedToObjectives = {
        adjustedNextSteps: nextSteps,
      };

      expect(result).toEqual(expectedOutput);
    });

    it('returns unmodified state when no next steps are provided', () => {
      const result = service.replaceNextStepsIfObjectiveRequiresAdjustment(
        AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE,
        [AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE],
        [],
      );

      const expectedOutput: NextStepsAdjustedToObjectives = {
        adjustedNextSteps: [],
      };

      expect(result).toEqual(expectedOutput);
    });

    it('blocks and replaces next step when single objective matches', () => {
      const result = service.replaceNextStepsIfObjectiveRequiresAdjustment(
        AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE,
        [
          AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE,
          AiObjectiveType.EVALUATE_CREDIT_SCORE,
          AiObjectiveType.EVALUATE_INCOME,
          AiObjectiveType.SCHEDULE_SHOWING,
        ],
        nextSteps,
      );

      const expectedOutput: NextStepsAdjustedToObjectives = {
        adjustedNextSteps: [
          {
            nextStep: NextStep.DISCUSS_MOVE_IN_DATE,
            relatedText: scheduleShowingNextStep.relatedText,
          },
        ],
      };

      expect(result).toEqual(expectedOutput);
    });

    it('uses priority objective replacement when multiple objectives block same step', () => {
      const result = service.replaceNextStepsIfObjectiveRequiresAdjustment(
        AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE,
        [
          AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE,
          AiObjectiveType.EVALUATE_CREDIT_SCORE,
          AiObjectiveType.EVALUATE_INCOME,
          AiObjectiveType.SCHEDULE_SHOWING,
        ],
        nextSteps,
      );

      const expectedOutput: NextStepsAdjustedToObjectives = {
        adjustedNextSteps: [
          {
            nextStep: NextStep.DISCUSS_MOVE_IN_DATE,
            relatedText: scheduleShowingNextStep.relatedText,
          },
        ],
      };

      expect(result).toEqual(expectedOutput);
    });

    it('replaces each blocked step when multiple objectives block different steps', () => {
      const result = service.replaceNextStepsIfObjectiveRequiresAdjustment(
        AiObjectiveType.EVALUATE_CREDIT_SCORE,
        [AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE, AiObjectiveType.EVALUATE_CREDIT_SCORE],
        nextSteps,
      );

      const expectedOutput: NextStepsAdjustedToObjectives = {
        adjustedNextSteps: [
          {
            nextStep: NextStep.DISCUSS_CREDIT_SCORE,
            relatedText: scheduleShowingNextStep.relatedText,
          },
        ],
      };

      expect(result).toEqual(expectedOutput);
    });

    // For example, asking renter about credit score and income at the same time
    it('should replace next steps but also make sure parallel objectives are handled correctly', () => {
      const result = service.replaceNextStepsIfObjectiveRequiresAdjustment(
        AiObjectiveType.EVALUATE_CREDIT_SCORE,
        [AiObjectiveType.EVALUATE_CREDIT_SCORE, AiObjectiveType.EVALUATE_INCOME, AiObjectiveType.SCHEDULE_SHOWING],
        [
          {
            nextStep: NextStep.ANSWER_PROPERTY_QUESTION,
            relatedText: 'Is there a garage?',
          },
          {
            nextStep: NextStep.ANSWER_SCAM_QUESTION,
            relatedText: 'Is this a scam?',
          },
          scheduleShowingNextStep,
        ],
      );

      const expectedOutput: NextStepsAdjustedToObjectives = {
        adjustedNextSteps: [
          {
            nextStep: NextStep.ANSWER_PROPERTY_QUESTION,
            relatedText: 'Is there a garage?',
          },
          {
            nextStep: NextStep.ANSWER_SCAM_QUESTION,
            relatedText: 'Is this a scam?',
          },
          {
            nextStep: NextStep.DISCUSS_CREDIT_SCORE,
            relatedText: scheduleShowingNextStep.relatedText,
          },
          {
            nextStep: NextStep.DISCUSS_INCOME,
            relatedText: scheduleShowingNextStep.relatedText,
          },
        ],
      };

      expect(result).toEqual(expectedOutput);
    });
  });

  describe('Adding next steps dynamically based on objectives: "evaluateObjectiveQuestionOpportunity"', () => {
    const answersToPreviouslyExecutedNextSteps = ['Yes, dogs are allowed.', 'The application fee is $45.'];
    const renterMessages =
      '<message_1>Are dogs Allowed?</message_1><message_2>How much is the application fee?</message_2>';

    let moveInDateObjective: AiObjective;
    let creditScoreObjective: AiObjective;
    let incomeObjective: AiObjective;
    let schedulingObjective: AiObjective;

    let inquiry: PropertyInquiry;

    beforeEach(() => {
      moveInDateObjective = new AiObjective();
      moveInDateObjective.id = 'move-in-date-id';
      moveInDateObjective.type = AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE;
      moveInDateObjective.completed = false;

      creditScoreObjective = new AiObjective();
      creditScoreObjective.id = 'credit-score-id';
      creditScoreObjective.type = AiObjectiveType.EVALUATE_CREDIT_SCORE;
      creditScoreObjective.completed = false;

      incomeObjective = new AiObjective();
      incomeObjective.id = 'income-id';
      incomeObjective.type = AiObjectiveType.EVALUATE_INCOME;
      incomeObjective.completed = false;

      schedulingObjective = new AiObjective();
      schedulingObjective.id = 'scheduling-id';
      schedulingObjective.type = AiObjectiveType.SCHEDULE_SHOWING;
      schedulingObjective.completed = false;

      inquiry = new PropertyInquiry();
      inquiry.id = 'inquiry-id';

      // Create a proper mock for the aiObjectives promise
      const objectives = [moveInDateObjective, creditScoreObjective, incomeObjective, schedulingObjective];

      // Mock the property with location for the service
      inquiry.property = Promise.resolve({
        location: Promise.resolve({
          city: 'New York',
          timeZone: 'America/New_York',
        }),
      } as any);

      inquiry.aiObjectives = Promise.resolve(objectives);
    });

    it('should not return any additonal next steps in case if there are no pending objectives', async () => {
      moveInDateObjective.completed = true;
      creditScoreObjective.completed = true;
      incomeObjective.completed = true;
      schedulingObjective.completed = true;

      const result = await service.evaluateObjectiveQuestionOpportunity(
        answersToPreviouslyExecutedNextSteps,
        renterMessages,
        'convoId',
        inquiry,
        [NextStep.ANSWER_PROPERTY_QUESTION, NextStep.ANSWER_APPLICATION_PROCESS_QUESTION],
      );

      expect(result).toEqual([]);
      // AI service should not be called when there are no pending objectives
      expect(mockAiService.getResponseWithChatMemory).not.toHaveBeenCalled();
    });

    it('should not return any additonal next steps in case if next step related to the objective is already executed', async () => {
      moveInDateObjective.completed = true;
      creditScoreObjective.completed = true;
      incomeObjective.completed = true;

      const result = await service.evaluateObjectiveQuestionOpportunity(
        answersToPreviouslyExecutedNextSteps,
        renterMessages,
        'convoId',
        inquiry,
        [NextStep.SCHEDULE_SHOWING],
      );

      expect(result).toEqual([]);
      // AI service should not be called when the related next step is already executed
      expect(mockAiService.getResponseWithChatMemory).not.toHaveBeenCalled();
    });

    it('should not return any additonal next steps in case if the next step is blocked by the executed objectives', async () => {
      const result = await service.evaluateObjectiveQuestionOpportunity(
        ['We can find date and time, but first when are you planning to move in?'],
        'I want to tour it',
        'convoId',
        inquiry,
        [NextStep.DISCUSS_MOVE_IN_DATE],
      );

      expect(result).toEqual([]);
      // AI service should not be called when the next step is already executed
      expect(mockAiService.getResponseWithChatMemory).not.toHaveBeenCalled();
    });

    it('should return "schedule showing" next step in case if all objectives are completed and the only one left is scheduling', async () => {
      moveInDateObjective.completed = true;
      creditScoreObjective.completed = true;
      incomeObjective.completed = true;

      const result = await service.evaluateObjectiveQuestionOpportunity(
        answersToPreviouslyExecutedNextSteps,
        'I want to tour it',
        'convoId',
        inquiry,
        [NextStep.DISCUSS_INCOME],
      );

      expect(result).toEqual([NextStep.SCHEDULE_SHOWING]);
      expect(mockAiService.getResponseWithChatMemory).toHaveBeenCalledTimes(1);
    });

    it('should return "discuss income" objective because this is the objective that could be executed in parallel with "discuss credit score"', async () => {
      moveInDateObjective.completed = true;

      const result = await service.evaluateObjectiveQuestionOpportunity(
        ['Our requirements are...'],
        'What are your requirements?',
        'convoId',
        inquiry,
        [NextStep.SHARE_REQUIREMENTS],
      );

      expect(result).toEqual([NextStep.DISCUSS_CREDIT_SCORE, NextStep.DISCUSS_INCOME]);
      expect(mockAiService.getResponseWithChatMemory).toHaveBeenCalledTimes(1);
    });

    it('should add first priority objective "ask about move in date" when convo is started', async () => {
      const result = await service.evaluateObjectiveQuestionOpportunity(
        ['Yes, dogs are allowed.'],
        'Are dogs allowed?',
        'convoId',
        inquiry,
        [NextStep.ANSWER_PROPERTY_QUESTION],
      );

      expect(result).toEqual([NextStep.DISCUSS_MOVE_IN_DATE]);
      expect(mockAiService.getResponseWithChatMemory).toHaveBeenCalledTimes(1);
    });

    it('should not add any additional next steps if AI says that its not appropriate in current context', async () => {
      // Override default behavior for this specific test
      mockAiService.getResponseWithChatMemory.mockResolvedValueOnce('{ "addObjectiveQuestion": false }');

      const result = await service.evaluateObjectiveQuestionOpportunity(
        ['Why?'],
        'I have all of you guys',
        'convoId',
        inquiry,
        [NextStep.GENERAL_CONVERSATION],
      );

      expect(result).toEqual([]);
      expect(mockAiService.getResponseWithChatMemory).toHaveBeenCalledTimes(1);
    });

    it('(1) should add next priority objective next steps in case if renter hit the scenario for current priority objective and completed it', async () => {
      moveInDateObjective.completed = true;

      const result = await service.evaluateObjectiveQuestionOpportunity(
        ['Thanks for the information about your move in timeline'],
        'My lease ends in April and we would like to move in by May',
        'convoId',
        inquiry,
        [NextStep.DISCUSS_MOVE_IN_DATE],
      );

      expect(result).toEqual([NextStep.DISCUSS_CREDIT_SCORE, NextStep.DISCUSS_INCOME]);
      expect(mockAiService.getResponseWithChatMemory).toHaveBeenCalledTimes(1);
    });

    it('(2) should add next priority objective next steps in case if renter hit the scenario for current priority objective and completed it', async () => {
      moveInDateObjective.completed = true;
      creditScoreObjective.completed = true;

      const result = await service.evaluateObjectiveQuestionOpportunity(
        ['Your credit is great!'],
        'My credit is 700',
        'convoId',
        inquiry,
        [NextStep.DISCUSS_CREDIT_SCORE],
      );

      expect(result).toEqual([NextStep.DISCUSS_INCOME]);
      expect(mockAiService.getResponseWithChatMemory).toHaveBeenCalledTimes(1);
    });

    it('should continue discussing the same objective if its not yet completed', async () => {
      moveInDateObjective.completed = true;
      creditScoreObjective.completed = true;

      const result = await service.evaluateObjectiveQuestionOpportunity(
        ['Your credit is great!'],
        'My credit is 700',
        'convoId',
        inquiry,
        [NextStep.DISCUSS_INCOME],
      );

      expect(result).toEqual([]);
      // AI service should not be called when the related next step is already executed
      expect(mockAiService.getResponseWithChatMemory).not.toHaveBeenCalled();
    });
  });
});

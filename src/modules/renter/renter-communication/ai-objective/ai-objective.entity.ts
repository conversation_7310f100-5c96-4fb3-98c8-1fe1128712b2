import { <PERSON>umn, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { AiObjectiveType } from './ai-objective-type.enum';
import { PropertyInquiry } from '../../../investor/property-inquiry/property-inquiry.entity';

@Entity()
export class AiObjective {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'enum', enum: AiObjectiveType })
  type: AiObjectiveType;

  /**
   * Flag to indicate if the objective is completed and no further actions are needed.
   */
  @Column({ type: 'boolean', default: false })
  completed: boolean;

  /**
   * The result of questioning, could be any end result of the objective, like credit score value,
   * income value, section 8 voucher
   */
  @Column({ type: 'varchar', length: 120, nullable: true })
  value: string | null;

  /**
   * Additional points to consider when renter doesn't meet the requirement. For example, renter has lower credit score
   * but has a co-signer, or is willing to pay a higher deposit. Or also could provide a reason why credit is lower atm.
   */
  @Column('text', { array: true, nullable: false, default: [] })
  requirementCompensatingFactors: string[];

  /**
   * Ai internal notes to keep track of the objectives, for example "Doesnt have a cosigner", etc
   */
  @Column('text', { array: true, nullable: false, default: [] })
  notes: string[];

  @ManyToOne(() => PropertyInquiry, (propertyInquiry) => propertyInquiry.aiObjectives, {
    nullable: false,
  })
  propertyInquiry: PropertyInquiry;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;
}

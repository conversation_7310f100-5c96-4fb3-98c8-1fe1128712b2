import { Injectable, NotAcceptableException } from '@nestjs/common';
import { AiService } from '../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../ai/enums/language-models.enum';
import { ConversationService } from '../../../shared/communication/conversation/conversation.service';
import { MessageType } from '../../../shared/communication/conversation/message/message-type.enum';
import { OutboundCommunicationService } from '../../../shared/communication/outbound-communication/outbound-communication.service';
import { RenterMessageCraftingOutput } from './interfaces/renter-message-crafting-output.interface';
import { RenterMessageCraftingParams } from './interfaces/renter-message-crafting-params.interface';
import { validateDeclineReasonForFhaViolationsTemplate } from './prompts/validate-decline-reason-for-fha-violations.template';

interface FairHousingViolationCheckAiOutput {
  fhaViolationDetected: boolean;
  fhaViolationReason: string;
}

@Injectable()
export class RenterOutboundCommsService {
  constructor(
    private readonly conversationService: ConversationService,
    private readonly outboundCommunicationService: OutboundCommunicationService,
    private readonly aiService: AiService,
  ) {}

  async validateDeclineReasonForFairHousingViolations(
    declineReason: string,
  ): Promise<{ safeToIncludeReason: boolean }> {
    let aiOutput: FairHousingViolationCheckAiOutput | undefined;

    try {
      aiOutput = JSON.parse(
        await this.aiService.getResponse(
          {
            declineReason,
          },
          validateDeclineReasonForFhaViolationsTemplate,
          null,
          LanguageModelsEnum.CLAUDE_3_HAIKU,
        ),
      );
    } catch (error) {
      console.error('Error parsing AI response for fair housing violation check:', error);

      return { safeToIncludeReason: false };
    }

    if (aiOutput.fhaViolationDetected) {
      throw new NotAcceptableException(aiOutput.fhaViolationReason);
    }

    return { safeToIncludeReason: true };
  }

  async craftMessageAndSend(
    paramsWithPartialValues: RenterMessageCraftingParams,
  ): Promise<RenterMessageCraftingOutput> {
    const params = this.buildParamsWithDefaultValues(paramsWithPartialValues);

    const conversation = await this.conversationService.findByPropertyUser(params.propertyId, params.renter.user.id);

    const message = await this.aiService.getResponseWithChatMemory(
      params.templateVariables,
      conversation.id,
      params.template,
      params.numberOfHistoryMessages,
      params.model,
    );

    await this.conversationService.saveTalloMessage(
      conversation,
      message,
      MessageType.TEXT,
      params.renter.user.preferredCommunicationChannel,
    );

    this.outboundCommunicationService
      .sendMessage(
        params.renter.user,
        message,
        await conversation.emailMetadata,
        params.renter.user.preferredCommunicationChannel,
      )
      .then(() => console.log(`[Outbound comms with renter] ${params.renter.user.preferredCommunicationChannel} sent`));

    return {
      conversation,
      message,
    };
  }

  private buildParamsWithDefaultValues(params: RenterMessageCraftingParams): Required<RenterMessageCraftingParams> {
    return {
      ...params,
      model: params.model || LanguageModelsEnum.CLAUDE_4_SONNET,
      numberOfHistoryMessages: params.numberOfHistoryMessages || 5,
      templateVariables: params.templateVariables || {},
    };
  }
}

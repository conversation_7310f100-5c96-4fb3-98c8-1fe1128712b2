import { LanguageModelsEnum } from '../../../../ai/enums/language-models.enum';
import { InputVariables } from '../../../../ai/memory/input-variables';
import { Renter } from '../../../renter/renter.entity';

export interface RenterMessageCraftingParams {
  renter: Renter;
  template: string;
  propertyId: string;
  templateVariables?: InputVariables;
  model?: LanguageModelsEnum;
  numberOfHistoryMessages?: number;
}

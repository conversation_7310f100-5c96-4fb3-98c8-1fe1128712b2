import { Modu<PERSON> } from '@nestjs/common';

import { AiModule } from '../../../ai/ai.module';
import { ConversationModule } from '../../../shared/communication/conversation/conversetion.module';
import { OutboundCommunicationModule } from '../../../shared/communication/outbound-communication/outbound-communication.module';
import { RenterOutboundCommsService } from './renter-outbound-comms.service';

@Module({
  imports: [OutboundCommunicationModule, ConversationModule, AiModule],
  providers: [RenterOutboundCommsService],
  exports: [RenterOutboundCommsService],
})
export class RenterOutboundCommsModule {}

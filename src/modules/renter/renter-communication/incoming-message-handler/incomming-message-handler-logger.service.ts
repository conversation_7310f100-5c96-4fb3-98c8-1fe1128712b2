import { Injectable } from '@nestjs/common';
import { Renter } from '../../renter/renter.entity';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../shared/communication/conversation/entities/conversation.entity';
import { SlackConvoMessageParams } from '../../../shared/communication/outbound-communication/slack/slack-convo-message-params.interface';
import { SlackCommunicationService } from '../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { ConversationService } from '../../../shared/communication/conversation/conversation.service';

@Injectable()
export class IncomingMessageHandlerLoggerService {
  constructor(
    private readonly slackCommunicationService: SlackCommunicationService,
    private readonly conversationService: ConversationService,
  ) {}

  async logConvoIsStoppedMessageToSlack(
    renterMessages: string[],
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<void> {
    await this.logConvoMessageToSlack({
      renterMessages,
      property,
      renter,
      aiResponse: '⛔️ Conversation with this renter has been stopped manually',
      conversation,
    });
  }

  async logConvoMessageToSlack(params: SlackConvoMessageParams): Promise<void> {
    await this.slackCommunicationService.buildAndSendMessageToConvosChannel(params).then(async (result) => {
      if (!params.conversation.slackLoggingThreadId && result.threadId) {
        await this.conversationService.saveSlackLoggingThreadId(params.conversation, result.threadId);
      }
    });
  }

  async logConvoHasUnimplementedStrategyMessageToSlack(
    renterMessages: string[],
    property: Property,
    renter: Renter,
    conversation: Conversation,
  ): Promise<void> {
    const slackMessage = '⛔️ Renter message involves scenario we havent implemented yet (no reply sent to renter) ⛔️';
    await this.logConvoMessageToSlack({
      renterMessages,
      property,
      renter,
      conversation,
      aiResponse: slackMessage,
    });
  }
}

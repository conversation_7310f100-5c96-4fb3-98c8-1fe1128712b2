export const getDefaultWelcomeMessageFull = (name: string): string => {
  return `Hi ${name}! I'm <PERSON>. I noticed your inquiry and wanted to touch base in case you've got any questions or if you're keen to check out the property in person. Let me know what works for you.`;
};

export const getWelcomeMessageForManuallyAddedLead = (name: string, address: string): string => {
  return `Hi ${name}, I'm <PERSON>. I received your contact information and wanted to reach out in regards to ${address}. I can answer any questions you may have about the property. Also, would you like to schedule a showing to see it?`;
};

export const getDefaultWelcomeMessageShort = (name: string, address: string): string => {
  return `Hi ${name}! I'm <PERSON>. I noticed your interest in the property at ${address} and wanted to get in touch. I'm here to assist you with any queries or details you need`;
};

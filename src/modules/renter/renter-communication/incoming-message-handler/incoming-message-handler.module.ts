import { Module } from '@nestjs/common';

import { AiModule } from '../../../ai/ai.module';
import { PropertyInquiryModule } from '../../../investor/property-inquiry/property-inquiry.module';
import { ConversationModule } from '../../../shared/communication/conversation/conversetion.module';
import { FollowUpModule } from '../../../shared/communication/follow-up/follow-up.module';
import { OutboundCommunicationModule } from '../../../shared/communication/outbound-communication/outbound-communication.module';
import { UserModule } from '../../../shared/user/user.module';
import { RenterProfileUpdateModule } from '../../renter/renter-profile-update/renter-profile-update.module';
import { RenterModule } from '../../renter/renter.module';
import { ResponseGenerationModule } from '../response-generation/response-generation.module';
import { IncomingMessageHandlerService } from './incoming-message-handler.service';
import { IncomingMessageHandlerLoggerService } from './incomming-message-handler-logger.service';

@Module({
  imports: [
    OutboundCommunicationModule,
    ConversationModule,
    ResponseGenerationModule,
    PropertyInquiryModule,
    UserModule,
    FollowUpModule,
    AiModule,
    RenterModule,
    RenterProfileUpdateModule,
  ],
  providers: [IncomingMessageHandlerService, IncomingMessageHandlerLoggerService],
  exports: [IncomingMessageHandlerService],
})
export class IncomingMessageHandlerModule {}

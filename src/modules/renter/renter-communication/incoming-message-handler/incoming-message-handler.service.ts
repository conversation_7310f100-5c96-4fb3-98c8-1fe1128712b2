import { Injectable } from '@nestjs/common';
import { PropertyInquirySource } from '../../../investor/property-inquiry/property-inquiry-source.enum';
import { PropertyInquiry } from '../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { ConversationService } from '../../../shared/communication/conversation/conversation.service';
import { Conversation } from '../../../shared/communication/conversation/entities/conversation.entity';
import { CommunicationChannel } from '../../../shared/communication/conversation/enums/preferred-communication-channel.enum';
import { MessageType } from '../../../shared/communication/conversation/message/message-type.enum';
import { FollowUpService } from '../../../shared/communication/follow-up/follow-up.service';
import { OutboundCommunicationService } from '../../../shared/communication/outbound-communication/outbound-communication.service';
import { CompanySettings } from '../../../shared/company/entities/company-settings.entity';
import { RenterProfileUpdateService } from '../../renter/renter-profile-update/renter-profile-update.service';
import { Renter } from '../../renter/renter.entity';
import { AiStrategyExecutionCode } from '../response-generation/enums/ai-strategy-execution-code';
import { ResponseGenerationService } from '../response-generation/response-generation.service';
import {
  getDefaultWelcomeMessageFull,
  getWelcomeMessageForManuallyAddedLead,
} from './default-messages/get-default-welcome-message';
import { IncomingMessageHandlerLoggerService } from './incomming-message-handler-logger.service';
import { FollowUpTypeEnum } from '../../../shared/communication/follow-up/enums/follow-up-type.enum';

@Injectable()
export class IncomingMessageHandlerService {
  constructor(
    private readonly conversationService: ConversationService,
    private readonly followUpService: FollowUpService,
    private readonly responseGenerationService: ResponseGenerationService,
    private readonly outboundCommunicationService: OutboundCommunicationService,
    private readonly loggerService: IncomingMessageHandlerLoggerService,
    private readonly renterProfileUpdateService: RenterProfileUpdateService,
  ) {}

  async answerToRenter(
    renterMessages: string[],
    propertyInquiry: PropertyInquiry,
    incomingCommsChannel?: CommunicationChannel,
  ): Promise<void> {
    const conversation = await propertyInquiry.conversation;
    const renter = await propertyInquiry.renter;
    const property = await propertyInquiry.property;
    const company = await property.company;
    const companySettings = company.settings;

    if (conversation.isEmergencyStopped) {
      await this.loggerService.logConvoIsStoppedMessageToSlack(renterMessages, renter, property, conversation);

      return undefined;
    }

    const renterMessagesAsXml = this.formatRenterMessagesAsXml(renterMessages);

    await this.renterProfileUpdateService.updateRenterProfileFromRenterResponse(
      renterMessagesAsXml,
      renter,
      conversation,
      property,
    );

    const answer: string | void = await this.generateAnswerToRenterMessages(
      renterMessages,
      renterMessagesAsXml,
      renter,
      property,
      conversation,
      propertyInquiry,
      companySettings,
    );

    if (answer) {
      this.sendAnswerToRenter(
        incomingCommsChannel ? incomingCommsChannel : renter.user.preferredCommunicationChannel,
        answer,
        renter,
        conversation,
      )
        .then()
        .catch((error) => console.error('Error when sending new message notification', error));
    }
  }

  private async sendAnswerToRenter(
    incomingCommsChannel: CommunicationChannel,
    answer: string,
    renter: Renter,
    conversation: Conversation,
  ): Promise<void> {
    this.outboundCommunicationService
      .sendMessage(renter.user, answer, await conversation.emailMetadata, incomingCommsChannel)
      .then(() => console.log('Message sent to renter'))
      .catch((error) => console.error('Error when sending new message notification', error));
  }

  async generateAnswerToRenterMessages(
    renterMessages: string[],
    renterMessagesAsXml: string,
    renter: Renter,
    property: Property,
    conversation: Conversation,
    propertyInquiry: PropertyInquiry,
    companySettings: CompanySettings,
  ): Promise<string | void> {
    if ((await conversation.messages).length === 1) {
      this.followUpService.createFirstFollowUpsForRenter(conversation, propertyInquiry);
    } else {
      await this.followUpService.refreshFollowUpsScheduledTime(conversation, propertyInquiry);
    }

    if (renterMessages.length === 0 || renterMessages.every((msg) => msg.trim() === '')) {
      return this.exitWithoutAnswering(
        AiStrategyExecutionCode.DO_NOTHING,
        renterMessages,
        renter,
        property,
        conversation,
      );
    }

    const aiGeneratedAnswer = await this.responseGenerationService.generateAiMessage(
      renterMessagesAsXml,
      companySettings,
      renter,
      property,
      propertyInquiry,
      conversation,
    );

    if (aiGeneratedAnswer === AiStrategyExecutionCode.STOP_AI_DOES_NOT_KNOW_WHAT_TO_DO) {
      await this.followUpService.deleteRenterFollowUpsByStatusAndType(
        conversation.id,
        FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      );
      await this.loggerService.logConvoHasUnimplementedStrategyMessageToSlack(
        renterMessages,
        property,
        renter,
        conversation,
      );
    } else if (aiGeneratedAnswer === AiStrategyExecutionCode.DO_NOTHING) {
      return this.exitWithoutAnswering(aiGeneratedAnswer, renterMessages, renter, property, conversation);
    } else if (aiGeneratedAnswer === AiStrategyExecutionCode.ESCALATED_WITHOUT_ANSWER) {
      return this.exitWithoutAnswering(
        'Question has been escalated to the owner 🙋',
        renterMessages,
        renter,
        property,
        conversation,
      );
    } else {
      try {
        await this.conversationService.saveTalloMessage(
          conversation,
          aiGeneratedAnswer,
          MessageType.TEXT,
          renter.user.preferredCommunicationChannel,
        );
      } catch (error) {
        console.error('Error when saving tallo message', error);
      }

      await this.loggerService.logConvoMessageToSlack({
        renterMessages,
        property,
        renter,
        conversation,
        aiResponse: aiGeneratedAnswer,
      });

      return aiGeneratedAnswer;
    }
  }

  async welcomeRenter(
    renter: Renter,
    property: Property,
    conversation: Conversation,
    source: PropertyInquirySource,
  ): Promise<void> {
    const location = await property.location;
    const talloMessageText = await this.generateNewRenterWelcomeMessage(renter.user.name, location.address, source);
    const savedMessage = await this.conversationService.saveTalloMessage(
      conversation,
      talloMessageText,
      MessageType.TEXT,
      renter.user.preferredCommunicationChannel,
    );

    await this.sendWelcomeMessage(renter, conversation, savedMessage.content);
  }

  private async sendWelcomeMessage(renter: Renter, conversation: Conversation, emailText: string): Promise<void> {
    this.outboundCommunicationService
      .sendMessage(renter.user, emailText, await conversation.emailMetadata, renter.user.preferredCommunicationChannel)
      .then();
  }

  private async generateNewRenterWelcomeMessage(
    renterName: string,
    propertyAddress: string,
    source: PropertyInquirySource,
  ): Promise<string> {
    const name = renterName.split(' ')[0];
    if (source === PropertyInquirySource.MANUALLY_ADDED) {
      return getWelcomeMessageForManuallyAddedLead(name, propertyAddress);
    } else {
      return getDefaultWelcomeMessageFull(name);
    }
  }
  private formatRenterMessagesAsXml(renterMessages: string[]): string {
    let xmlFormattedMessages = '';

    renterMessages.forEach((messageText, i) => {
      xmlFormattedMessages += `<message_${i + 1}>${messageText}</message_${i + 1}>`;
    });

    return xmlFormattedMessages;
  }

  private async exitWithoutAnswering(
    slackMessageToLog: string,
    renterMessages: string[],
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<void> {
    await this.followUpService.deleteRenterFollowUpsByStatusAndType(
      conversation.id,
      FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
    );

    await this.loggerService.logConvoMessageToSlack({
      renterMessages,
      property,
      renter,
      conversation,
      aiResponse: slackMessageToLog,
    });
  }
}

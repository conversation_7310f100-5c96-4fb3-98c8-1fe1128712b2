import { Injectable } from '@nestjs/common';

import { PropertyInquirySource } from '../../investor/property-inquiry/property-inquiry-source.enum';
import { PropertyInquiry } from '../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../investor/property/property/entities/property.entity';
import { ConversationService } from '../../shared/communication/conversation/conversation.service';
import { Conversation } from '../../shared/communication/conversation/entities/conversation.entity';
import { CommunicationChannel } from '../../shared/communication/conversation/enums/preferred-communication-channel.enum';
import { Renter } from '../renter/renter.entity';
import { ParsedGmailEmail } from '../../shared/communication/inbound-communication/gmail/interfaces/parsed-gmail-email.interface';
import { IncomingMessageHandlerService } from './incoming-message-handler/incoming-message-handler.service';
import { EmailParserService } from '../../shared/communication/inbound-communication/email-parser/email-parser.service';
import { PropertyInquiryService } from '../../investor/property-inquiry/property-inquiry.service';
import { Role } from '../../shared/auth/models/roles-enum';
import { RenterService } from '../renter/renter.service';
import { MessageService } from '../../shared/communication/conversation/message/message.service';

@Injectable()
export class RenterCommunicationService {
  constructor(
    private readonly conversationService: ConversationService,
    private readonly emailParserService: EmailParserService,
    private readonly inquiryService: PropertyInquiryService,
    private readonly renterService: RenterService,
    private readonly messageService: MessageService,
    private readonly incomingMessageHandlerService: IncomingMessageHandlerService,
  ) {}

  async answerToAllUnansweredMessages(): Promise<void> {
    const conversations = await this.conversationService.findAllUnansweredConversations();

    await Promise.all(
      conversations.map(async (conversation) => {
        try {
          await this.conversationService.markConversationAsInProgress(conversation.id);

          const renterMessages = (await conversation.messages).map((message) => message.content);
          const user = (await conversation.users).find((user) => user.roles.includes(Role.RENTER));
          const renter = await this.renterService.findByUserId(user.id);
          const property = await conversation.property;

          const inquiry = await this.inquiryService.findByRenterAndProperty(renter.id, property.id);
          await this.answerToRenter(renterMessages, inquiry);

          await this.conversationService.markConversationAsAnswered(conversation.id);
          const messages = await conversation.messages;
          messages.forEach((message) => {
            message.isAnswered = true;
          });
          await this.messageService.updateMultiple(messages);
        } catch (error) {
          console.error(`Error processing conversation ${conversation.id}:`, error);
          await this.conversationService.markConversationAsError(conversation.id);
        }
      }),
    );
  }

  async answerToRenterEmails(inquiry: PropertyInquiry, renterEmails: ParsedGmailEmail[]): Promise<void> {
    const renterMessages = await Promise.all(
      renterEmails.map(async (email) => this.emailParserService.extractRenterMessageFromRawEmail(email.emailText)),
    );

    const conversation = await inquiry.conversation;
    const lastEmail = renterEmails[renterEmails.length - 1];

    await this.conversationService.updateEmailMetadata(conversation, {
      subject: lastEmail.subject,
      lastMessageId: lastEmail.messageId,
      references: lastEmail.references,
      previousText: renterMessages[renterMessages.length - 1],
    });

    await this.answerToRenter(renterMessages, inquiry, CommunicationChannel.EMAIL);
  }

  async answerToRenter(
    renterMessages: string[],
    propertyInquiry: PropertyInquiry,
    incomingCommsChannel?: CommunicationChannel,
  ): Promise<void> {
    await this.incomingMessageHandlerService.answerToRenter(renterMessages, propertyInquiry, incomingCommsChannel);
  }

  async welcomeRenter(
    renter: Renter,
    property: Property,
    conversation: Conversation,
    source: PropertyInquirySource,
  ): Promise<void> {
    await this.incomingMessageHandlerService.welcomeRenter(renter, property, conversation, source);
  }
}

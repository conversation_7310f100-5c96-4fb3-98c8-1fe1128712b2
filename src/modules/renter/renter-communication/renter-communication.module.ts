import { Modu<PERSON> } from '@nestjs/common';

import { ConversationModule } from '../../shared/communication/conversation/conversetion.module';
import { RenterCommunicationService } from './renter-communication.service';
import { IncomingMessageHandlerModule } from './incoming-message-handler/incoming-message-handler.module';
import { EmailParserModule } from '../../shared/communication/inbound-communication/email-parser/email-parser.module';
import { PropertyInquiryModule } from '../../investor/property-inquiry/property-inquiry.module';
import { RenterModule } from '../renter/renter.module';
import { RenterCommunicationController } from './renter-communication.controller';

@Module({
  imports: [ConversationModule, IncomingMessageHandlerModule, EmailParserModule, PropertyInquiryModule, RenterModule],
  providers: [RenterCommunicationService],
  controllers: [RenterCommunicationController],
  exports: [RenterCommunicationService],
})
export class RenterCommunicationModule {}

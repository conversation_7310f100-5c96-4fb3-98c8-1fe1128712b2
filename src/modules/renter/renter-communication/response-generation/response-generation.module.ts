import { Module, forwardRef } from '@nestjs/common';

import { AiModule } from '../../../ai/ai.module';
import { AvailabilityModule } from '../../../investor/availability/availability.module';
import { PropertyAvailabilityModule } from '../../../investor/property/availability/property-availability-module';
import { PropertyInquiryModule } from '../../../investor/property-inquiry/property-inquiry.module';
import { IntelligentEscalationModule } from '../../../investor/property-question/intelligent-escalation.module';
import { RescheduleModule } from '../../../investor/reschedule/reschedule.module';
import { ShowingRequestModule } from '../../../investor/showing-request/showing-request.module';
import { ShowingModule } from '../../../investor/showing/showing.module';
import { ConversationModule } from '../../../shared/communication/conversation/conversetion.module';
import { FollowUpModule } from '../../../shared/communication/follow-up/follow-up.module';
import { OutboundCommunicationModule } from '../../../shared/communication/outbound-communication/outbound-communication.module';
import { CalendarModule } from '../../../shared/calendar/calendar.module';
import { UserModule } from '../../../shared/user/user.module';
import { RenterModule } from '../../renter/renter.module';
import { AiObjectiveModule } from '../ai-objective/ai-objective.module';
import { ResponseGenerationService } from './response-generation.service';
import { ResponseGenerationHelperService } from './services/response-generation-helper.service';
import { ResponseGenerationStrategyExecutorService } from './services/response-generation-strategy-executor.service';
import { AnswerTalloQuestionStrategy } from './strategies/answer-tallo-question/answer-tallo-question.strategy';
import { CallForHelpStrategy } from './strategies/call-for-help/call-for-help.strategy';
import { CancelShowingStrategy } from './strategies/cancel-showing/cancel-showing.strategy';
import { DiscussCreditScoreStrategy } from './strategies/discuss-requirements/discuss-credit-score/discuss-credit-score.strategy';
import { DiscussIncomeStrategy } from './strategies/discuss-requirements/discuss-income/discuss-income.strategy';
import { AskIfRenterHasRequirementsQuestionsStrategy } from './strategies/discuss-requirements/strategies/ask-if-renter-has-questions/ask-if-renter-has-requirements-questions.strategy';
import { AnswerApplicationQuestionStrategy } from './strategies/discuss-requirements/strategies/discuss-application-process/answer-application-question-strategy.service';
import { SectionEightStrategy } from './strategies/discuss-requirements/strategies/section-eight/section-eight.strategy';
import { ShareRequirementsStrategy } from './strategies/discuss-requirements/strategies/share-requirements/share-requirements.strategy';
import { DiscussShowingProcessStrategy } from './strategies/discuss-showing-process/discuss-showing-process.strategy';
import { DiscussSubleaseStrategy } from './strategies/discuss-sublease/discuss-sublease.strategy';
import { DoNothingStrategy } from './strategies/do-nothing/do-nothing.strategy';
import { DoYouHaveOtherPropertiesStrategy } from './strategies/do-you-have-other-properties/do-you-have-other-properties.strategy';
import { GeneralChatStrategy } from './strategies/general-chat/general-chat.strategy';
import { HandleDiscriminationConcernsStrategy } from './strategies/handle-discrimination-concerns/handle-discrimination-concerns.strategy';
import { WhyShowingRequestPendingStrategy } from './strategies/how-long-do-I-wait-question/why-showing-request-pending-strategy.service';
import { AnswerPersonalQuestionStrategy } from './strategies/pesronal-question/answer-personal-question.strategy';
import { AnswerPropertyQuestionStrategy } from './strategies/property-question/answer-property-question.strategy';
import { RenterAsksIfShowingIsStillConfirmedStrategy } from './strategies/renter-asks-if-showing-is-still-confirmed/renter-asks-if-showing-is-still-confirmed.strategy';
import { RenterConfirmsShowingStrategy } from './strategies/renter-confirmed-showing/renter-confirms-showing.strategy';
import { RenterHasSeenPropertyStrategy } from './strategies/renter-has-seen-property/renter-has-seen-property.strategy';
import { HandleShowingAttendanceIssuesStrategy } from './strategies/handle-showing-attendance-issues/handle-showing-attendance-issues.strategy';
import { RenterNoLongerInterestedStrategy } from './strategies/renter-no-longer-interested/renter-no-longer-interested.strategy';
import { AnswerIsThisAScamQuestion } from './strategies/scam-question/answer-is-this-a-scam-question';
import { RescheduleShowingStrategy } from './strategies/scheduling-strategies/reschedule-showing/reschedule-showing.strategy';
import { ScheduleShowingStrategy } from './strategies/scheduling-strategies/schedule-showing/schedule-showing.strategy';
import { SchedulingStrategiesSharedService } from './strategies/scheduling-strategies/shared/scheduling-strategies-shared.service';
import { ShareMorePhotosStrategy } from './strategies/share-more-photos/share-more-photos.strategy';
import { HandleCanceledByOwnerRenterStrategy } from './strategies/terminal-strategies/handle-canceled-by-owner-renter/handle-canceled-by-owner-renter-strategy.service';
import { HandleDeclinedRenterStrategy } from './strategies/terminal-strategies/handle-declined-renter/handle-declined-renter.strategy';
import { HandleShowingRequestIgnoredByOwnerStrategy } from './strategies/terminal-strategies/handle-showing-request-ignored-by-owner/handle-showing-request-ignored-by-owner.strategy';
import { PropertyRentedOutStrategy } from './strategies/terminal-strategies/property-rented-out/property-rented-out.strategy';
import { WelcomeRenterStrategy } from './strategies/wecome-renter/welcome-renter.strategy';
import { WrongPhoneNumberStrategy } from './strategies/wrong-phone-number/wrong-phone-number.strategy';
import { SharePropertyLinkStrategy } from './strategies/share-property-link/share-property-link-strategy';
import { DiscussMoveInDateStrategy } from './strategies/discuss-requirements/discuss-move-in-date/discuss-move-in-date.strategy';
import { SwitchCommunicationChannelStrategy } from './strategies/switch-communication-channel/switch-communication-channel.strategy';
import { CollectPostShowingFeedbackStrategy } from './strategies/collect-property-feedback/collect-property-feedback.strategy';
import { RenterWantsToApplyStrategy } from './strategies/apply-to-property/renter-wants-to-apply.strategy';
import { SpeakToHumanStrategy } from './strategies/speak-to-human/speak-to-human.strategy';
import { HandleRenterWaitingForTheInfoStrategy } from './strategies/confirm-ai-working-on-finding-answer/handle-renter-wating-for-the-info-strategy.service';
import { ShowingAgentModule } from '../../../investor/showing-agent/showing-agent.module';
import { RenterScreeningModule } from '../../renter-screening/renter-screening.module';
import { HandleUnqualifiedRenterStrategy } from './strategies/terminal-strategies/handle-unqualified-renter/handle-unqualified-renter.strategy';
import { SmsCommunicationModule } from '../../../shared/communication/outbound-communication/sms/sms-communication.module';

@Module({
  imports: [
    forwardRef(() => ShowingModule),
    forwardRef(() => RenterModule),
    ShowingRequestModule,
    RescheduleModule,
    AiModule,
    AiObjectiveModule,
    IntelligentEscalationModule,
    AvailabilityModule,
    PropertyAvailabilityModule,
    FollowUpModule,
    ConversationModule,
    OutboundCommunicationModule,
    CalendarModule,
    PropertyInquiryModule,
    UserModule,
    ShowingAgentModule,
    RenterScreeningModule,
    SmsCommunicationModule,
  ],
  providers: [
    ResponseGenerationService,
    ResponseGenerationStrategyExecutorService,
    ResponseGenerationHelperService,
    // Strategies
    CallForHelpStrategy,
    DoNothingStrategy,
    GeneralChatStrategy,
    WhyShowingRequestPendingStrategy,
    AnswerPersonalQuestionStrategy,
    AnswerPropertyQuestionStrategy,
    RenterNoLongerInterestedStrategy,
    AnswerIsThisAScamQuestion,
    WrongPhoneNumberStrategy,
    WelcomeRenterStrategy,
    ShareRequirementsStrategy,
    SectionEightStrategy,
    AskIfRenterHasRequirementsQuestionsStrategy,
    CancelShowingStrategy,
    DiscussSubleaseStrategy,
    RenterAsksIfShowingIsStillConfirmedStrategy,
    HandleShowingAttendanceIssuesStrategy,
    DoYouHaveOtherPropertiesStrategy,
    AnswerApplicationQuestionStrategy,
    SchedulingStrategiesSharedService,
    ScheduleShowingStrategy,
    RescheduleShowingStrategy,
    HandleCanceledByOwnerRenterStrategy,
    HandleDeclinedRenterStrategy,
    HandleUnqualifiedRenterStrategy,
    HandleShowingRequestIgnoredByOwnerStrategy,
    PropertyRentedOutStrategy,
    DiscussShowingProcessStrategy,
    ShareMorePhotosStrategy,
    RenterHasSeenPropertyStrategy,
    RenterConfirmsShowingStrategy,
    AnswerTalloQuestionStrategy,
    DiscussCreditScoreStrategy,
    DiscussIncomeStrategy,
    DiscussMoveInDateStrategy,
    HandleDiscriminationConcernsStrategy,
    SharePropertyLinkStrategy,
    SwitchCommunicationChannelStrategy,
    CollectPostShowingFeedbackStrategy,
    RenterWantsToApplyStrategy,
    SpeakToHumanStrategy,
    HandleRenterWaitingForTheInfoStrategy,
  ],
  exports: [ResponseGenerationService, ResponseGenerationStrategyExecutorService, ResponseGenerationHelperService],
})
export class ResponseGenerationModule {}

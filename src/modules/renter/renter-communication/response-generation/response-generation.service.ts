import { HttpException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';

import { TimezoneUtils } from '../../../../utils/timezone.utils';
import { AiService, NextStepWithRelatedQuestion } from '../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../ai/enums/language-models.enum';
import { PropertyInquiry } from '../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../shared/communication/conversation/entities/conversation.entity';
import { MessageService } from '../../../shared/communication/conversation/message/message.service';
import { FollowUpService } from '../../../shared/communication/follow-up/follow-up.service';
import { CompanySettings } from '../../../shared/company/entities/company-settings.entity';
import { Renter } from '../../renter/renter.entity';
import { AiObjectiveService } from '../ai-objective/ai-objective.service';
import { NextStepsAdjustedToObjectives } from '../ai-objective/next-steps-adjusted-to-objectives.interface';
import { terminalStrategies } from '../configs/terminal-strategies';
import { rentStageGoalMap } from './configs/rent-stage-goal/rent-stage-goal.map';
import { rentStageStrategiesMap } from './configs/rent-stage-strategies/rent-stage-strageties.map';
import { AiStrategyExecutionCode } from './enums/ai-strategy-execution-code';
import { NextStep } from './enums/next-step';
import { buildFinalAnswerTemplate } from './prompts/build-final-answer-template';
import { ResponseGenerationHelperService } from './services/response-generation-helper.service';
import { ResponseGenerationStrategyExecutorService } from './services/response-generation-strategy-executor.service';
import { RentStageEnum } from '../../../shared/communication/conversation/enums/rent-stage.enum';
import { RenterScreeningService } from '../../renter-screening/renter-screening.service';

@Injectable()
export class ResponseGenerationService {
  constructor(
    private readonly aiService: AiService,
    private readonly renterQnADependenciesService: ResponseGenerationStrategyExecutorService,
    private readonly renterQnAHelperService: ResponseGenerationHelperService,
    private readonly followUpService: FollowUpService,
    private readonly messageService: MessageService,
    private readonly aiObjectiveService: AiObjectiveService,
    private readonly renterScreeningService: RenterScreeningService,
  ) {}

  async generateAiMessage(
    renterMessagesAsXml: string,
    companySettings: CompanySettings,
    renter: Renter,
    property: Property,
    inquiry: PropertyInquiry,
    conversation: Conversation,
  ): Promise<string> {
    const objectives = await inquiry.aiObjectives;
    console.log(`[Rent Stage] ${inquiry.stage}`);
    console.log(
      '[Current Objectives]',
      objectives.filter((o) => !o.completed).map((o) => o.type),
    );
    console.log('[Priority Objective]', PropertyInquiry.getCurrentPriorityObjective(await inquiry.aiObjectives)?.type);

    const goal = rentStageGoalMap.get(inquiry.stage)(inquiry);
    const possibleNextSteps = await rentStageStrategiesMap.get(inquiry.stage)(inquiry, companySettings);
    const nextSteps = await this.aiService.determineNextStepWithRelatedQuestions(
      renterMessagesAsXml,
      possibleNextSteps,
      goal,
      inquiry.stage,
      conversation,
      6,
      LanguageModelsEnum.CLAUDE_4_SONNET_REASONER,
    );

    if (nextSteps.length === 0) {
      throw new NotFoundException(`No strategy found for messages: ${renterMessagesAsXml}`);
    }

    if (await this.renterQnAHelperService.checkIfContainsUnimplementedStrategies(nextSteps)) {
      return AiStrategyExecutionCode.STOP_AI_DOES_NOT_KNOW_WHAT_TO_DO;
    }

    if (nextSteps.length === 1 && nextSteps[0].nextStep === NextStep.DO_NOTHING) {
      return AiStrategyExecutionCode.DO_NOTHING;
    }

    await this.renterQnAHelperService.addWelcomeStrategyIfNeeded(nextSteps, conversation);

    let nextStepsResult: NextStepsAdjustedToObjectives = {
      adjustedNextSteps: nextSteps,
    };

    // First combine duplicates before objective adjustment (for cases when the same next step is required for different parts of the text)
    nextStepsResult = this.combineDuplicates(nextStepsResult);

    if (companySettings.advancedRenterScreening) {
      nextStepsResult = this.aiObjectiveService.replaceNextStepsIfObjectiveRequiresAdjustment(
        PropertyInquiry.getCurrentPriorityObjective(await inquiry.aiObjectives)?.type,
        (await PropertyInquiry.getPendingObjectives(inquiry)).map(({ type }) => type),
        nextStepsResult.adjustedNextSteps,
      );

      const nextStepsToLog = nextStepsResult.adjustedNextSteps.map(({ nextStep }) => nextStep).join(', ');
      console.log('[Next Steps Adjusted By Objectives]', `Next steps: "${nextStepsToLog}"`);
    }

    // Combine duplicates again after objective adjustments because it might produce duplicates after the adjustment
    nextStepsResult = this.combineDuplicates(nextStepsResult);

    if (this.checkIfContainsTerminalStrategies(nextStepsResult.adjustedNextSteps)) {
      this.followUpService.deleteRenterFollowUpsByStatus(conversation.id);
    }

    const answerPromises = nextStepsResult.adjustedNextSteps.map(({ nextStep, relatedText }) => {
      return this.renterQnADependenciesService.executeStrategy(
        nextStep,
        inquiry,
        relatedText,
        renter,
        property,
        conversation,
      );
    });

    let answers = await Promise.all(answerPromises).catch((e) => {
      throw new HttpException('Failed to execute strategies', 500, e.message);
    });

    if (answers.length) {
      answers = answers.filter((answer) => {
        return ![AiStrategyExecutionCode.EXECUTED_WITHOUT_ANSWER, AiStrategyExecutionCode.DO_NOTHING].includes(
          answer as AiStrategyExecutionCode,
        );
      });

      if (answers.length && answers.every((answer) => answer === AiStrategyExecutionCode.ESCALATED_WITHOUT_ANSWER)) {
        return AiStrategyExecutionCode.ESCALATED_WITHOUT_ANSWER;
      }

      if (answers.length === 0) {
        return AiStrategyExecutionCode.DO_NOTHING;
      }
    } else if (answers.length === 0) {
      throw new HttpException('Failed to execute strategies', 500);
    }

    // We only run objectives and qualify if the inquiry is in the initial contact stage
    if (companySettings.advancedRenterScreening && inquiry.stage === RentStageEnum.INITIAL_CONTACT) {
      const qualificationResult = await this.renterScreeningService.qualifyRenterForProperty(
        conversation,
        inquiry,
        property,
        renter,
      );

      if (!qualificationResult.qualified && !qualificationResult.qualificationSkipped) {
        answers.push(qualificationResult.renterRejectionReason);
      } else {
        const additionalNextStepsForObjectives: NextStep[] =
          await this.aiObjectiveService.evaluateObjectiveQuestionOpportunity(
            answers,
            renterMessagesAsXml,
            conversation.id,
            inquiry,
            nextStepsResult.adjustedNextSteps.map(({ nextStep }) => nextStep),
          );

        console.log(
          '[Additional Next Steps For Objectives]',
          additionalNextStepsForObjectives.map((s) => s).join(', '),
        );

        const answerPromises = additionalNextStepsForObjectives.map((nextStep) => {
          return this.renterQnADependenciesService.executeStrategy(
            nextStep,
            inquiry,
            renterMessagesAsXml,
            renter,
            property,
            conversation,
          );
        });

        const answersRelatedToObjectives = await Promise.all(answerPromises).catch((e) => {
          throw new InternalServerErrorException('Failed to execute strategies related to objectives', e.message);
        });

        answersRelatedToObjectives.forEach((answer) => answers.push(answer));
      }
    }

    console.log('[Answers to combine in Final Builder]', answers);

    const { city, timeZone } = await property.location;
    const currentDate = TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone);

    const aiFinalAnswer = await this.aiService.getResponseWithChatMemory(
      {
        currentDate,
        input: renterMessagesAsXml,
        answersToCombine: answers,
        timeSinceLastMessage: await this.messageService.countTimeSinceLastAiMessage(conversation.id),
        conversationGoal: goal,
      },
      conversation.id,
      buildFinalAnswerTemplate,
      10,
      LanguageModelsEnum.GPT_5_REASONER_LOW,
      null,
      LanguageModelsEnum.CLAUDE_4_SONNET_REASONER,
    );

    if (!aiFinalAnswer) {
      throw new HttpException('Could not form an answer', 500);
    }

    return aiFinalAnswer;
  }

  private checkIfContainsTerminalStrategies(nextSteps: NextStepWithRelatedQuestion[]): boolean {
    const listOfStrategies = nextSteps.map(({ nextStep }) => nextStep);

    return listOfStrategies.some((strategy) => terminalStrategies.includes(strategy));
  }

  private combineDuplicates(input: NextStepsAdjustedToObjectives): NextStepsAdjustedToObjectives {
    const combinedMap = new Map<string, string>();

    input.adjustedNextSteps.forEach(({ nextStep, relatedText }) => {
      if (combinedMap.has(nextStep)) {
        // for cases when the same next step is required for different parts of the text
        combinedMap.set(nextStep, combinedMap.get(nextStep)! + '. ' + relatedText);
      } else {
        combinedMap.set(nextStep, relatedText);
      }
    });

    const combinedNextSteps: NextStepWithRelatedQuestion[] = [];
    combinedMap.forEach((relatedText, nextStep) => {
      combinedNextSteps.push({ nextStep, relatedText } as NextStepWithRelatedQuestion);
    });

    return {
      adjustedNextSteps: combinedNextSteps,
    };
  }
}

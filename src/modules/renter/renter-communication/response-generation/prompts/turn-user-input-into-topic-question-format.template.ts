export const turnUserInputIntoTopicQuestionFormatTemplate = `
***Task***:
Read the question coming from the user and summarize it into a topic question format.
Make topic as short as possible, ideally using one word. For example: "Pool", "Parking", "Pet Policy", etc
You can rephrase the question if needed to make it more concise and clear,
but make sure you don't lose the essence of the question.

***Instructions***:
The question will be flagged with triple exclamation marks.

*** Format***
Please follow the following format: {format_instructions}

***User Input***:
Question: !!! {question} !!!`;

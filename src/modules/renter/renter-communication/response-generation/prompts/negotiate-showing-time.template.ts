import { PromptVariable } from '../../../../ai/enums/prompt-variable.enum';
import { AiInstruction } from '../../../../ai/prompts/ai-instructions';
import { TourType } from '../../../../investor/showing/enums/tour-type.enum';

/* eslint-disable max-len */
export const negotiateShowingTimeTemplate = `
<task>
Arrange a property viewing time that suits both the renter and fits within the available showing slots. Also determine the appropriate tour type based on the property's capabilities and renter's preferences.
</task>

<task_description>
- IMPORTANT: Ignore everything that is not related to the showing, don't include anything about in your answer (credit, income, complaints, etc)
- The AI can only propose times within the given owner availability slots.
- If renter says he needs time to think, consult with someone, time to make a decision, etc, simply say it's ok and don't share any slots
- For broad time requests (not specific time) like "Tuesday evening" or "this weekend", if there are multiple showing times to choose from, or
  slot length is more than 2 hours, share your availability and let the renter choose a suitable time.
- Never make assumptions about the time; always propose a specific time (up to a minute) before confirming.
- If the renter requests a specific time (day and time) that fits in the availability slot, don't share availability, and agree to it straight away. Use the phrase "Let's tentatively pencil this in, but give me a few moments to check with the team and get back to you shortly." Set "wasProposedTimeAgreedOn" to true.
- Never consider time found unless the renter proposed or agreed to a certain time (day and time).
- Combine slots for simple language, i.e., If you have availability every day at the same time, say "I'm available every day at this time".
- If it becomes clear that there is no suitable time, apologize and say you can't find a suitable time.
- Don't rush and take your time answering the question; prioritize the exceptions and constraints above all when proposing the time.
- If the latest message does not contain a date, try to find the latest date mentioned and propose the closest slot.
- If the renter asks to move the showing to specific date & time and there is an availability slot, agree to it straight away. Use the phrase "Let's tentatively pencil this in, but give me a few moments to check with the team and get back to you shortly." Set "wasProposedTimeAgreedOn" to true.
- Do not repeat "Let's tentatively pencil this in" phrase multiple times in a row, only use it once per conversation. Other times say something different with the same meaning (because we need to confirm the time with the team), for example:
  - "Perfect! Let me confirm this with the team and I'll get back to you."
  - "Sounds good! Give me a chance to verify this time slot and I'll circle back."
  - "Excellent! Let me just confirm availability with the team and I'll follow up."
</task_description>

<tour_type_logic>
- Available tour types for this property:
  - In-person tours: {allowsInPersonTours}
  - Virtual tours: {allowsVirtualTours}
- CRITICAL: You can only offer tour types that are marked as "true" for this property
- Previously booked tour type: {previousTourType}
- Default behavior:
  - If this is a rescheduling scenario and "Previously booked tour type" is provided, maintain the same tour type unless the renter explicitly requests a different one
  - If in-person tours are true and renter doesn't specify tour type (and no previously booked tour type), assume they want an in-person tour
  - If renter explicitly asks for virtual tour and virtual tours are true, set tour type to virtual
  - If renter asks for virtual tour but virtual tours are false, politely explain virtual tours aren't available and offer in-person tour (if available)
  - If only virtual tours are available (virtual tours are true and in-person tours are false), default to virtual tours
- Tour type determination:
  - "${TourType.IN_PERSON}" for physical property visits
  - "${TourType.VIRTUAL}" for online/video tours
- Always include the tour type in your response when confirming a showing
</tour_type_logic>

<key_points>
- Dates and times in responses should be in human-readable format without adjusting to time zones.
- If the slot date is soon, simply say Tomorrow, this Saturday, this Thursday, etc. Otherwise, add the date too.
- If the slot date is on the weekend, say this weekend, next weekend, etc.
- If startTime is next week, say next week, next weekend, etc.
</key_points>

<instructions>
- Always stick to the output format provided in the "response_format" tag.
- Align the renter's preference with the available showing times as closely as possible.
${AiInstruction.NoSugarcoating}
${AiInstruction.LatestRenterMessageMarking}. If message is missing or empty just suggest a time from available slots.
${AiInstruction.HistoryForContext}
${AiInstruction.DontUseExamplesAsAnswer}
- If renter says "tomorrow", use ${PromptVariable.CurrentDate} to determine the date renter is referring to before calculating the showing time.
- If the renter's message contains a date, refer to it for the showing time and ignore the rest. If not, try to find the latest date mentioned in the conversation and use it for the showing time.
- Don't try to guess; even if the renter says "afternoon" and you have a slot, choose a time and propose it first.
- If you don't have a slot to accommodate the renter's request, apologize and inform them about your availability.
- If renter asks for today, propose the closes available slot if it that specific slot was not proposed before
- If you tried to find a suitable time, and it's getting obvious that there is no suitable time, apologize and say you can't find it.
- If the time proposed by the renter does not work, offer the nearest available slot or ask them to propose another time.
- Do not extrapolate; if there is time next week, it doesn't mean the same time is available this week.
- Do not say 'hi' or welcome the renter in any other way.
- If the renter asks about a specific day but not a time, propose a time within the available slots. Proactively suggest a time if the renter doesn't specify one.
- You are going to be given a list of existing calendar events, count them as unavailable and do not agree/propose any times that overlap with them.
You can propose times arount it instead
- If you see a calendar event with a type "Showing", try to preoritize scheduling the showing before or after this event.
If you are trying to stack showings back to back take the "showing_duration_minutes" variable into account.
For example if the showing takes 15 minutes, and there is a showing from 3 to 3:15, you can propose 2:45 or 3:15 for the renter.
Back to back showings are beneficial for the person who will be showing the property.
But don't push too much and allow other slots too
</instructions>

<constraints>
The AI cannot agree to or confirm times outside the available slots.
</constraints>

<available_showing_slots>{availableTimes}</available_showing_slots>

<existing_calendar_events>{existingCalendarEvents}</existing_calendar_events>

<showing_duration_minutes>{showingDurationInMinutes}</showing_duration_minutes>

<tomorrow_date>{tomorrowDate}</tomorrow_date>
${PromptVariable.CurrentDate}

<renter_city>{propertyCity}</renter_city>

<output_format>
- Return JSON object with the following structure:
{{
  "response": string, // Response to the renter's message
  "proposedTime": string, // JS Date
  "wasProposedTimeAgreedOn": boolean, // Was the proposed time agreed by both parties?
  "tourType": string // Either "${TourType.IN_PERSON}" or "${TourType.VIRTUAL}" based on property capabilities and renter preference
}}.
- Do not wrap the output in a markdown code block like "\`\`\`json", it should be parsable with js JSON.parse() function.
- tourType must be one of: "${TourType.IN_PERSON}", "${TourType.VIRTUAL}"
- Only set tourType to types that are available for this property (marked as true)
</output_format>

<examples>
  <example>
    Renter message: "Can we see the property tomorrow?"
    Current_date: "Mon, Jan 1, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: false
    Showing_slots: [
      {{
        "startTime": "Tue, Jan 2, 2024, 11:00AM",
        "endTime": "Tue, Jan 2, 2024, 03:00PM"
      }}
    ],
    {{
      "response": "I have availability from 11AM to 3PM. What about 11AM?",
      "proposedTime": "2024-01-02T11:00:00.000Z",
      "wasProposedTimeAgreedOn": false,
      "tourType": "${TourType.IN_PERSON}"
    }}
  </example>

  <example>
    Renter message: "Can we do a virtual tour tomorrow at 2PM?"
    Current_date: "Mon, Jan 1, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: true
    Showing_slots: [
      {{
        "startTime": "Tue, Jan 2, 2024, 01:00PM",
        "endTime": "Tue, Jan 2, 2024, 06:00PM"
      }}
    ],
    {{
      "response": "Perfect! Let's tentatively pencil in a virtual tour for 2PM tomorrow. Give me a few moments to check with the team and get back to you shortly.",
      "proposedTime": "2024-01-02T14:00:00.000Z",
      "wasProposedTimeAgreedOn": true,
      "tourType": "${TourType.VIRTUAL}"
    }}
  </example>

  <example>
    Renter message: "Can we do a virtual tour tomorrow?"
    Current_date: "Mon, Jan 1, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: false
    Showing_slots: [
      {{
        "startTime": "Tue, Jan 2, 2024, 01:00PM",
        "endTime": "Tue, Jan 2, 2024, 06:00PM"
      }}
    ],
    {{
      "response": "I'm sorry, but virtual tours aren't available for this property. However, I can arrange a tour from 1PM to 6PM tomorrow. What time works for you?",
      "proposedTime": "",
      "wasProposedTimeAgreedOn": false,
      "tourType": "${TourType.IN_PERSON}"
    }}
  </example>

  <example>
    Renter message: "Can we see the property tomorrow at 11:00?"
    Current_date: "Mon, Jan 1, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: true
    Showing_slots: [
      {{
        "startTime": "Tue, Jan 2, 2024, 01:00PM",
        "endTime": "Tue, Jan 2, 2024, 06:00PM"
      }}
    ],
    {{
      "response": "I can't make it at 11 AM. I'm only available from 1PM to 6PM",
      "proposedTime": "",
      "wasProposedTimeAgreedOn": false,
      "tourType": "${TourType.IN_PERSON}"
    }}
  </example>

  <example>
    Renter message: "Let's book the tour for Thursday, 5:30pm"
    Current_date: "Mon, Jan 1, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: false
    Showing_slots: [
      {{
        "startTime": "Thu, Jan 4, 2024, 02:00PM",
        "endTime": "Thu, Jan 4, 2024, 06:00PM"
      }}
    ],
    {{
      "response": "Yes, 5:30PM on Thursday works. Let's tentatively pencil this in, but give me a few moments to check with the team and get back to you shortly",
      "proposedTime": "2024-01-04T17:30:00.000Z",
      "wasProposedTimeAgreedOn": true,
      "tourType": "${TourType.IN_PERSON}"
    }}
  </example>

  <example>
    Renter message: "Let's book the virtual tour for Friday"
    Current_date: "Mon, Jan 1, 2024, 08:00AM"
    In-person tours: false
    Virtual tours: true
    Showing_slots: [
      {{
        "startTime": "Fri, Jan 5, 2024, 02:00PM",
        "endTime": "Fri, Jan 5, 2024, 02:15PM"
      }}
    ],
    {{
      "response": "I can do a virtual tour on Friday at 2PM",
      "proposedTime": "2024-01-05T14:00:00.000Z",
      "wasProposedTimeAgreedOn": false,
      "tourType": "${TourType.VIRTUAL}"
    }}
  </example>

  <example>
    Renter message: "Let's book the tour for Wednesday"
    Current_date: "Mon, Jan 1, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: true
    Showing_slots: [
      {{
        "startTime": "Tue, Jan 2, 2024, 02:00PM",
        "endTime": "Tue, Jan 2, 2024, 06:00PM"
      }},
      {{
        "startTime": "Thu, Jan 4, 2024, 02:00PM",
        "endTime": "Thu, Jan 4, 2024, 06:00PM"
      }}
    ],
    {{
      "response": "I'm not available on Wednesday. I have availability on Tuesday and Thursday from 2PM to 6PM",
      "proposedTime": "",
      "wasProposedTimeAgreedOn": false,
      "tourType": "${TourType.IN_PERSON}"
    }}
  </example>

  <example>
    Renter message: "Can I see it on the weekend?"
    Current_date: "Mon, Jan 1, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: true
    Showing_slots: [
      {{
        "startTime": "Sat, Jan 6, 2024, 11:00AM",
        "endTime": "Sat, Jan 6, 2024, 06:00PM"
      }},
      {{
        "startTime": "Sun, Jan 7, 2024, 11:00AM",
        "endTime": "Sun, Jan 7, 2024, 06:00PM"
      }}
    ],
    {{
      "response": "I have availability this weekend from 11AM to 6PM",
      "proposedTime": "",
      "wasProposedTimeAgreedOn": false,
      "tourType": "${TourType.IN_PERSON}"
    }}
  </example>

  <example>
    Renter message: "Can I see it on the weekend?"
    Current_date: "Sat, Jan 6, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: true
    Showing_slots: [
      {{
        "startTime": "Sat, Jan 13, 2024, 11:00AM",
        "endTime": "Sat, Jan 13, 2024, 06:00PM"
      }},
      {{
        "startTime": "Sun, Jan 14, 2024, 11:00AM",
        "endTime": "Sun, Jan 14, 2024, 06:00PM"
      }}
    ],
    {{
      "response": "I have availability next weekend from 11AM to 6PM",
      "proposedTime": "",
      "wasProposedTimeAgreedOn": false,
      "tourType": "${TourType.IN_PERSON}"
    }}
  </example>

  <example>
    Renter message: "What availability do you have on the weekend?"
    Current_date: "Sat, Jan 6, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: true
    Showing_slots: [
      {{
        "startTime": "Sat, Jan 13, 2024, 11:00AM",
        "endTime": "Sat, Jan 13, 2024, 06:00PM"
      }},
      {{
        "startTime": "Sun, Jan 14, 2024, 11:00AM",
        "endTime": "Sun, Jan 14, 2024, 06:00PM"
      }}
    ],
    existing_calendar_events: [
      {{
        "startTime": "Sat, Jan 13, 2024, 1:00PM",
        "endTime": "Sat, Jan 13, 2024, 2:00PM"
      }},
      {{
        "startTime": "Sun, Jan 14, 2024, 11:00AM",
        "endTime": "Sun, Jan 14, 2024, 12:00PM"
      }}
    ],
    showingDurationInMinutes: 60
    {{
      "response": "On Saturday I'm available from 11AM to 1PM and from 2PM to 6PM. On Sunday I'm available from 12PM to 6PM.",
      "proposedTime": "",
      "wasProposedTimeAgreedOn": false,
      "tourType": "${TourType.IN_PERSON}"
    }}
  </example>

  <example>
    Renter message: "Can I see it on the weekend?"
    Current_date: "Sat, Jan 6, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: true
    Showing_slots: [
      {{
        "startTime": "Sat, Jan 13, 2024, 11:00AM",
        "endTime": "Sat, Jan 13, 2024, 06:00PM"
      }},
      {{
        "startTime": "Sun, Jan 14, 2024, 11:00AM",
        "endTime": "Sun, Jan 14, 2024, 06:00PM"
      }}
    ],
    existing_calendar_events: [
      {{
        "startTime": "Sat, Jan 13, 2024, 1:00PM",
        "endTime": "Sat, Jan 13, 2024, 1:15PM"
      }},
      {{
        "startTime": "Sun, Jan 14, 2024, 11:30AM",
        "endTime": "Sun, Jan 14, 2024, 11:45PM"
      }}
    ],
    showingDurationInMinutes: 15
    {{
      "response": "Sure, would 1:15 work for you?",
      "proposedTime": "",
      "wasProposedTimeAgreedOn": false,
      "tourType": "${TourType.IN_PERSON}"
    }}
  </example>

  <example>
    Renter message: "Can we reschedule to tomorrow?"
    Current_date: "Mon, Jan 1, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: true
    Previously booked tour type: "${TourType.VIRTUAL}"
    Showing_slots: [
      {{
        "startTime": "Tue, Jan 2, 2024, 11:00AM",
        "endTime": "Tue, Jan 2, 2024, 03:00PM"
      }}
    ],
    {{
      "response": "I have availability from 11AM to 3PM tomorrow for a virtual tour. What about 11AM?",
      "proposedTime": "2024-01-02T11:00:00.000Z",
      "wasProposedTimeAgreedOn": false,
      "tourType": "${TourType.VIRTUAL}"
    }}
  </example>

  <example>
    Renter message: "Can we do this in person instead tomorrow at 2PM?"
    Current_date: "Mon, Jan 1, 2024, 08:00AM"
    In-person tours: true
    Virtual tours: true
    Previously booked tour type: "${TourType.VIRTUAL}"
    Showing_slots: [
      {{
        "startTime": "Tue, Jan 2, 2024, 01:00PM",
        "endTime": "Tue, Jan 2, 2024, 06:00PM"
      }}
    ],
    {{
      "response": "Perfect! Let's tentatively pencil in an in-person tour for 2PM tomorrow. Give me a few moments to check with the team and get back to you shortly.",
      "proposedTime": "2024-01-02T14:00:00.000Z",
      "wasProposedTimeAgreedOn": true,
      "tourType": "${TourType.IN_PERSON}"
    }}
  </example>
</examples>

${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}

<exceptions>
- If you agreed on the date, but not the time and the renter asks "What time should I arrive?" do not set "wasProposedTimeAgreedOn" to "true" until the renter agrees on a specific time.
</exceptions>
`;

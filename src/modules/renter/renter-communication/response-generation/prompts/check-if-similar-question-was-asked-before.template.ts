/* eslint-disable max-len */
export const checkIfSimilarQuestionWasAskedBeforeTemplate = `
<task>
You are analyzing property inquiries to detect duplicate questions. Your goal is to determine if a new question is semantically similar to any previously asked questions about a property.
</task>

<instructions>
1. Compare the new question against the list of previous questions.
2. If you find a semantically similar question in the previous questions:
  - Return the EXACT text of the most similar previous question without any modifications.
  - Do not add quotes, brackets, or any formatting.
3. If no similar question exists or the list is empty:
  - Return exactly "this is the first time this question is asked" (without quotes).
4. Focus on semantic similarity, not just keyword matching:
  - "What pets are allowed?" and "What's the pet policy?" address the same topic.
  - "How much is the rent?" and "What's the monthly payment?" address the same topic.
5. Do not look for exact matches only - understand the intent behind questions.
</instructions>

<new_question_asked_by_user>{question}</new_question_asked_by_user>
<list_of_previous_questions>{previousQuestions}</list_of_previous_questions>

<examples>
  <example>
    - New question: "What is the pet policy?"
    - Previous questions: ["What is the pet policy at the property?", "How much is the rent?"]
    - Output: What is the pet policy at the property?
  </example>
  <example>
    - New question: "Are dogs allowed?"
    - Previous questions: ["What is the pet policy at the property?", "How much is the rent?"]
    - Output: What is the pet policy at the property?
  </example>
  <example>
    - New question: "What is the pet policy?"
    - Previous questions: []
    - Output: this is the first time this question is asked
  </example>
  <example>
    - New question: "How much does it cost per month?"
    - Previous questions: ["What amenities are included?", "What is the monthly rent?"]
    - Output: What is the monthly rent?
  </example>
</examples>
`;

import { Injectable } from '@nestjs/common';
import { NextStepWithRelatedQuestion } from '../../../../ai/ai.service';
import { Conversation } from '../../../../shared/communication/conversation/entities/conversation.entity';
import { unimplementedStrategies } from '../../configs/unimplemented-strategies';
import { NextStep } from '../enums/next-step';

@Injectable()
export class ResponseGenerationHelperService {
  async checkIfContainsUnimplementedStrategies(nextSteps: NextStepWithRelatedQuestion[]): Promise<boolean> {
    const strategyNames = nextSteps.map(({ nextStep }) => nextStep);
    return strategyNames.some((strategy) =>
      unimplementedStrategies.some((unimplemented) => unimplemented.includes(strategy)),
    );
  }

  async addWelcomeStrategyIfNeeded(
    nextSteps: NextStepWithRelatedQuestion[],
    conversation: Conversation,
  ): Promise<void> {
    const messages = await conversation.messages;

    if (messages.length === 1) {
      nextSteps.unshift({ nextStep: NextStep.WELCOME_USER, relatedText: '' });
    }
  }
}

import { Injectable } from '@nestjs/common';

import { PropertyInquiry } from '../../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../renter/renter.entity';
import { NextStep } from '../enums/next-step';
import { HandleRenterMessageStrategy } from '../interfaces/handle-renter-message.strategy';
import { AnswerTalloQuestionStrategy } from '../strategies/answer-tallo-question/answer-tallo-question.strategy';
import { CallForHelpStrategy } from '../strategies/call-for-help/call-for-help.strategy';
import { CancelShowingStrategy } from '../strategies/cancel-showing/cancel-showing.strategy';
import { DiscussCreditScoreStrategy } from '../strategies/discuss-requirements/discuss-credit-score/discuss-credit-score.strategy';
import { DiscussIncomeStrategy } from '../strategies/discuss-requirements/discuss-income/discuss-income.strategy';
import { AskIfRenterHasRequirementsQuestionsStrategy } from '../strategies/discuss-requirements/strategies/ask-if-renter-has-questions/ask-if-renter-has-requirements-questions.strategy';
import { AnswerApplicationQuestionStrategy } from '../strategies/discuss-requirements/strategies/discuss-application-process/answer-application-question-strategy.service';
import { SectionEightStrategy } from '../strategies/discuss-requirements/strategies/section-eight/section-eight.strategy';
import { ShareRequirementsStrategy } from '../strategies/discuss-requirements/strategies/share-requirements/share-requirements.strategy';
import { DiscussShowingProcessStrategy } from '../strategies/discuss-showing-process/discuss-showing-process.strategy';
import { DiscussSubleaseStrategy } from '../strategies/discuss-sublease/discuss-sublease.strategy';
import { DoNothingStrategy } from '../strategies/do-nothing/do-nothing.strategy';
import { DoYouHaveOtherPropertiesStrategy } from '../strategies/do-you-have-other-properties/do-you-have-other-properties.strategy';
import { GeneralChatStrategy } from '../strategies/general-chat/general-chat.strategy';
import { HandleDiscriminationConcernsStrategy } from '../strategies/handle-discrimination-concerns/handle-discrimination-concerns.strategy';
import { WhyShowingRequestPendingStrategy } from '../strategies/how-long-do-I-wait-question/why-showing-request-pending-strategy.service';
import { AnswerPersonalQuestionStrategy } from '../strategies/pesronal-question/answer-personal-question.strategy';
import { AnswerPropertyQuestionStrategy } from '../strategies/property-question/answer-property-question.strategy';
import { RenterAsksIfShowingIsStillConfirmedStrategy } from '../strategies/renter-asks-if-showing-is-still-confirmed/renter-asks-if-showing-is-still-confirmed.strategy';
import { RenterConfirmsShowingStrategy } from '../strategies/renter-confirmed-showing/renter-confirms-showing.strategy';
import { RenterHasSeenPropertyStrategy } from '../strategies/renter-has-seen-property/renter-has-seen-property.strategy';
import { HandleShowingAttendanceIssuesStrategy } from '../strategies/handle-showing-attendance-issues/handle-showing-attendance-issues.strategy';
import { RenterNoLongerInterestedStrategy } from '../strategies/renter-no-longer-interested/renter-no-longer-interested.strategy';
import { AnswerIsThisAScamQuestion } from '../strategies/scam-question/answer-is-this-a-scam-question';
import { RescheduleShowingStrategy } from '../strategies/scheduling-strategies/reschedule-showing/reschedule-showing.strategy';
import { ScheduleShowingStrategy } from '../strategies/scheduling-strategies/schedule-showing/schedule-showing.strategy';
import { ShareMorePhotosStrategy } from '../strategies/share-more-photos/share-more-photos.strategy';
import { HandleCanceledByOwnerRenterStrategy } from '../strategies/terminal-strategies/handle-canceled-by-owner-renter/handle-canceled-by-owner-renter-strategy.service';
import { HandleDeclinedRenterStrategy } from '../strategies/terminal-strategies/handle-declined-renter/handle-declined-renter.strategy';
import { HandleShowingRequestIgnoredByOwnerStrategy } from '../strategies/terminal-strategies/handle-showing-request-ignored-by-owner/handle-showing-request-ignored-by-owner.strategy';
import { PropertyRentedOutStrategy } from '../strategies/terminal-strategies/property-rented-out/property-rented-out.strategy';
import { WelcomeRenterStrategy } from '../strategies/wecome-renter/welcome-renter.strategy';
import { WrongPhoneNumberStrategy } from '../strategies/wrong-phone-number/wrong-phone-number.strategy';
import { SharePropertyLinkStrategy } from '../strategies/share-property-link/share-property-link-strategy';
import { DiscussMoveInDateStrategy } from '../strategies/discuss-requirements/discuss-move-in-date/discuss-move-in-date.strategy';
import { SwitchCommunicationChannelStrategy } from '../strategies/switch-communication-channel/switch-communication-channel.strategy';
import { CollectPostShowingFeedbackStrategy } from '../strategies/collect-property-feedback/collect-property-feedback.strategy';
import { RenterWantsToApplyStrategy } from '../strategies/apply-to-property/renter-wants-to-apply.strategy';
import { SpeakToHumanStrategy } from '../strategies/speak-to-human/speak-to-human.strategy';
import { HandleRenterWaitingForTheInfoStrategy } from '../strategies/confirm-ai-working-on-finding-answer/handle-renter-wating-for-the-info-strategy.service';
import { HandleUnqualifiedRenterStrategy } from '../strategies/terminal-strategies/handle-unqualified-renter/handle-unqualified-renter.strategy';

/*
  This service is only needed to remove the boilerplate of the service
  initialization to the separate service. It does only two things:
  1. Initializes the strategies
  2. Executes the strategy based on the strategy name
 */
@Injectable()
export class ResponseGenerationStrategyExecutorService {
  private readonly strategies: Record<NextStep, HandleRenterMessageStrategy>;

  constructor(
    private readonly answerPropertyQuestionStrategy: AnswerPropertyQuestionStrategy,
    private readonly answerIsThisAScamQuestion: AnswerIsThisAScamQuestion,
    private readonly answerPersonalQuestionStrategy: AnswerPersonalQuestionStrategy,
    private readonly welcomeRenterStrategy: WelcomeRenterStrategy,
    private readonly wrongPhoneNumberStrategy: WrongPhoneNumberStrategy,
    private readonly discussWhyShowingRequestPendingStrategy: WhyShowingRequestPendingStrategy,
    private readonly generalChatStrategy: GeneralChatStrategy,
    private readonly callForHelpStrategy: CallForHelpStrategy,
    private readonly scheduleShowingStrategy: ScheduleShowingStrategy,
    private readonly renterNoLongerInterestedStrategy: RenterNoLongerInterestedStrategy,
    private readonly doNothingStrategy: DoNothingStrategy,
    private readonly shareRequirementsStrategy: ShareRequirementsStrategy,
    private readonly askIfRenterHasRequirementsQuestion: AskIfRenterHasRequirementsQuestionsStrategy,
    private readonly cancelShowingStrategy: CancelShowingStrategy,
    private readonly discussSubleaseStrategy: DiscussSubleaseStrategy,
    private readonly renterAsksIfShowingIsStillConfirmedStrategy: RenterAsksIfShowingIsStillConfirmedStrategy,
    private readonly renterIsLateToTheShowingStrategy: HandleShowingAttendanceIssuesStrategy,
    private readonly answerSection8Question: SectionEightStrategy,
    private readonly doYouHaveOtherPropertiesStrategy: DoYouHaveOtherPropertiesStrategy,
    private readonly discussApplicationProcessStrategy: AnswerApplicationQuestionStrategy,
    private readonly rescheduleShowingStrategy: RescheduleShowingStrategy,
    private readonly handleDeclinedRenterStrategy: HandleDeclinedRenterStrategy,
    private readonly handleUnqualifiedRenterStrategy: HandleUnqualifiedRenterStrategy,
    private readonly handleCanceledRenterStrategy: HandleCanceledByOwnerRenterStrategy,
    private readonly handleShowingRequestIgnoredByOwnerStrategy: HandleShowingRequestIgnoredByOwnerStrategy,
    private readonly propertyRentedOutStrategy: PropertyRentedOutStrategy,
    private readonly discussShowingProcessStrategy: DiscussShowingProcessStrategy,
    private readonly shareMorePhotosStrategy: ShareMorePhotosStrategy,
    private readonly renterHasSeenPropertyStrategy: RenterHasSeenPropertyStrategy,
    private readonly renterConfirmsShowing: RenterConfirmsShowingStrategy,
    private readonly answerTalloQuestionStrategy: AnswerTalloQuestionStrategy,
    private readonly discussCreditScoreStrategy: DiscussCreditScoreStrategy,
    private readonly discussIncomeStrategy: DiscussIncomeStrategy,
    private readonly discussMoveInDateStrategy: DiscussMoveInDateStrategy,
    private readonly handleDiscriminationConcerns: HandleDiscriminationConcernsStrategy,
    private readonly sharePropertyLink: SharePropertyLinkStrategy,
    private readonly collectPropertyFeedbackStrategy: CollectPostShowingFeedbackStrategy,
    private readonly renterWantsToApplyStrategy: RenterWantsToApplyStrategy,
    private readonly switchCommunicationChannel: SwitchCommunicationChannelStrategy,
    private readonly speakToHumanStrategy: SpeakToHumanStrategy,
    private readonly confirmAiWorkingOnFindingAnswerStrategy: HandleRenterWaitingForTheInfoStrategy,
  ) {
    this.strategies = {
      [NextStep.WELCOME_USER]: this.welcomeRenterStrategy,
      [NextStep.ANSWER_PROPERTY_QUESTION]: this.answerPropertyQuestionStrategy,
      [NextStep.ANSWER_SCAM_QUESTION]: this.answerIsThisAScamQuestion,
      [NextStep.ANSWER_PERSONAL_QUESTION]: this.answerPersonalQuestionStrategy,
      [NextStep.WRONG_PHONE_NUMBER]: this.wrongPhoneNumberStrategy,
      [NextStep.ANSWER_WHY_SHOWING_REQUEST_PENDING]: this.discussWhyShowingRequestPendingStrategy,
      [NextStep.GENERAL_CONVERSATION]: this.generalChatStrategy,
      [NextStep.UNDEFINED]: this.callForHelpStrategy,
      [NextStep.WISH_GOOD_LUCK]: this.renterNoLongerInterestedStrategy,
      [NextStep.DO_NOTHING]: this.doNothingStrategy,
      [NextStep.RESCHEDULE_SHOWING]: this.rescheduleShowingStrategy,
      [NextStep.SCHEDULE_SHOWING]: this.scheduleShowingStrategy,
      [NextStep.SHARE_REQUIREMENTS]: this.shareRequirementsStrategy,
      [NextStep.CANCEL_SHOWING]: this.cancelShowingStrategy,
      [NextStep.RENTER_CONFIRMS_SHOWING]: this.renterConfirmsShowing,
      [NextStep.ASK_IF_RENTER_HAS_REQUIREMENTS_QUESTION]: this.askIfRenterHasRequirementsQuestion,
      [NextStep.ANSWER_APPLICATION_PROCESS_QUESTION]: this.discussApplicationProcessStrategy,
      [NextStep.DISCUSS_SUBLEASE]: this.discussSubleaseStrategy,
      [NextStep.CHECK_IF_SHOWING_IS_STILL_CONFIRMED]: this.renterAsksIfShowingIsStillConfirmedStrategy,
      [NextStep.HANDLE_SHOWING_ATTENDANCE_ISSUES]: this.renterIsLateToTheShowingStrategy,
      [NextStep.ANSWER_SECTION_8_QUESTION]: this.answerSection8Question,
      [NextStep.DO_YOU_HAVE_OTHER_PROPERTIES]: this.doYouHaveOtherPropertiesStrategy,
      [NextStep.HANDLE_DECLINED_RENTER]: this.handleDeclinedRenterStrategy,
      [NextStep.HANDLE_UNQUALIFIED_RENTER]: this.handleUnqualifiedRenterStrategy,
      [NextStep.HANDLE_CANCELED_RENTER]: this.handleCanceledRenterStrategy,
      [NextStep.NOTIFY_PROPERTY_RENTED_OUT]: this.propertyRentedOutStrategy,
      [NextStep.DISCUSS_SHOWING_PROCESS]: this.discussShowingProcessStrategy,
      [NextStep.SHARE_MORE_PHOTOS]: this.shareMorePhotosStrategy,
      [NextStep.RENTER_HAS_SEEN_PROPERTY]: this.renterHasSeenPropertyStrategy,
      [NextStep.ANSWER_TALLO_QUESTION]: this.answerTalloQuestionStrategy,
      [NextStep.DISCUSS_CREDIT_SCORE]: this.discussCreditScoreStrategy,
      [NextStep.DISCUSS_INCOME]: this.discussIncomeStrategy,
      [NextStep.DISCUSS_MOVE_IN_DATE]: this.discussMoveInDateStrategy,
      [NextStep.HANDLE_DISCRIMINATION_CONCERNS]: this.handleDiscriminationConcerns,
      [NextStep.SHARE_PROPERTY_LINK]: this.sharePropertyLink,
      [NextStep.COLLECT_PROPERTY_FEEDBACK]: this.collectPropertyFeedbackStrategy,
      [NextStep.RENTER_WANTS_TO_APPLY_FOR_THE_PROPERTY]: this.renterWantsToApplyStrategy,
      [NextStep.SWITCH_COMMUNICATION_CHANNEL]: this.switchCommunicationChannel,
      [NextStep.SPEAK_TO_HUMAN]: this.speakToHumanStrategy,
      [NextStep.HANDLE_RENTER_WAITING_FOR_THE_INFO]: this.confirmAiWorkingOnFindingAnswerStrategy,
      [NextStep.HANDLE_SHOWING_REQUEST_IGNORED_BY_THE_OWNER]: this.handleShowingRequestIgnoredByOwnerStrategy,
      // TODO implement
      [NextStep.ANSWER_LEGAL_QUESTION]: null,
      [NextStep.DISCUSS_B2B_PARTNERSHIP]: null,
    };
  }

  public async executeStrategy(
    strategy: NextStep,
    inquiry: PropertyInquiry,
    renterQuestion: string,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    return this.strategies[strategy].execute(renterQuestion, inquiry, renter, property, conversation);
  }
}

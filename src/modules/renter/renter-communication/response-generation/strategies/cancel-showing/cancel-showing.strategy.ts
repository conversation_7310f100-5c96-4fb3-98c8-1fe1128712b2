import { Injectable } from '@nestjs/common';

import { TimezoneUtils } from '../../../../../../utils/timezone.utils';
import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { RescheduleRequestStatus } from '../../../../../investor/reschedule/reschedule-request.entity';
import { RescheduleService } from '../../../../../investor/reschedule/reschedule.service';
import { ShowingRequestStatus } from '../../../../../investor/showing-request/enums/showing-request-status.enum';
import { ShowingRequestService } from '../../../../../investor/showing-request/showing-request.service';
import { ShowingService } from '../../../../../investor/showing/showing.service';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';
import { FollowUpTypeEnum } from '../../../../../shared/communication/follow-up/enums/follow-up-type.enum';
import { OutboundCommunicationService } from '../../../../../shared/communication/outbound-communication/outbound-communication.service';
import { SlackCommunicationService } from '../../../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { cancelShowingTemplate } from './prompt/cancel-showing.template';
import { parseShowingCanceledReasonTemplate } from './prompt/parse-showing-canceled-reason.template';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { ShowingStatus } from '../../../../../investor/showing/enums/showing-status.enum';
import { ShowingCalendarService } from '../../../../../investor/showing/showing-calendar.service';
import { SmsCommunicationService } from '../../../../../shared/communication/outbound-communication/sms/sms-communication.service';

@Injectable()
export class CancelShowingStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly showingRequestService: ShowingRequestService,
    private readonly showingService: ShowingService,
    private readonly showingCalendarService: ShowingCalendarService,
    private readonly communicationService: OutboundCommunicationService,
    private readonly smsNotificationService: SmsCommunicationService,
    private readonly rescheduleService: RescheduleService,
    private readonly slackService: SlackCommunicationService,
    private readonly followUpService: FollowUpService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const showingRequest = await this.showingRequestService.findByRenterAndProperty(renter.id, property.id);

    if (showingRequest.status === ShowingRequestStatus.RESCHEDULED) {
      const pendingRescheduleRequest = await this.rescheduleService.findOneBy(
        renter.id,
        property.id,
        RescheduleRequestStatus.PENDING,
      );

      if (pendingRescheduleRequest) {
        await this.rescheduleService.cancelRescheduleRequestAsRenter(pendingRescheduleRequest);
      }
    }

    const cancellationReason = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
      },
      conversation.id,
      parseShowingCanceledReasonTemplate,
      0,
      LanguageModelsEnum.GPT_4,
    );

    await this.showingRequestService.cancelRequest(
      property.id,
      showingRequest.id,
      cancellationReason,
      ShowingRequestStatus.CANCELLED_BY_RENTER,
    );

    const showing = await showingRequest.showing;
    const propertyLocation = await property.location;

    const propertyOwner = await property.owner;
    const ownerUser = await propertyOwner.user;

    // Delete the investor follow-up for the showing request since the renter cancelled
    await this.followUpService.deleteInvestorFollowUpsByInquiry(
      inquiry,
      ownerUser.id,
      FollowUpTypeEnum.INVESTOR_SHOWING_REQUEST_FOLLOW_UP,
    );

    const showingAgent = await showing.showingAgent;
    const showingDate = TimezoneUtils.convertDateToStringInCityTz(
      showing.startTime,
      propertyLocation.city,
      propertyLocation.timeZone,
    );
    const propertyAddress = propertyLocation.address;

    if (showingAgent && showingAgent.phone && showingAgent.phone !== ownerUser.phoneNumber) {
      await this.smsNotificationService.sendRenterCancelledShowingNotification(
        showingAgent.phone,
        showingAgent.firstName,
        showingDate,
        propertyAddress,
      );
    }

    await this.communicationService.sendRenterCancelledShowingNotification(
      property.id,
      ownerUser,
      renter,
      TimezoneUtils.convertDateToStringInCityTz(showing.startTime, propertyLocation.city, propertyLocation.timeZone),
      propertyLocation.address,
      ownerUser.preferredCommunicationChannel,
    );

    const newShowingStatus = await this.showingService.updateStatusBasedOnRequests(showing);

    if (newShowingStatus === ShowingStatus.CANCELED) {
      this.showingCalendarService.deleteEvent(showing);
    } else {
      this.showingCalendarService.updateEvent(showing);
    }

    const response = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
      },
      conversation.id,
      cancelShowingTemplate,
      5,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    this.slackService.sendMessageToConvosChannel('😢 Showing is canceled by the renter', conversation);

    return 'Cancel showing: ' + response;
  }
}

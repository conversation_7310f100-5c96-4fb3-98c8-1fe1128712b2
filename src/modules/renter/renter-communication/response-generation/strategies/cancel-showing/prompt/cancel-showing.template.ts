import { generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';
import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';

/* eslint-disable max-len */
export const cancelShowingTemplate = `
<task>
<PERSON><PERSON> decided to cancel the showing.
Tell him that you have successfully cancelled the showing.

Also, try to assess the reason for cancellation.
- If renter doesn't explain the reason or he is canceling because he can't make it to the showing at this specific time - ask if he wants to reschedule the showing as well.
- If renter said he is not interested in the property anymore or something else that signals he is unlikely to reschedule the showing - say something like "feel free to reach out if you change your mind".
</task>

<instructions>
${generalAiInstructionsTemplate}
- You only have one property, so you can't suggest booking a showing for a different property.
</instructions>

${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

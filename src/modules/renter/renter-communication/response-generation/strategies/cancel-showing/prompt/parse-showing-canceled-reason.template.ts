import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';

export const parseShowingCanceledReasonTemplate = `
<task>
You will be given a renter message.
Parse the renter's message to understand the reason for canceling the showing.
If it's empty, return empty string.
</task>

<response_format>
- Parsed reason should be returned as a string.
</response_format>

${PromptVariable.RenterMessage}
`;

import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { AiService } from '../../../../../ai/ai.service';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { answerIsThisAScamQuestionTemplate } from './prompts/answer-is-this-a-scam-question.template';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { Injectable } from '@nestjs/common';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';

@Injectable()
export class AnswerIsThisAScamQuestion implements HandleRenterMessageStrategy {
  constructor(private readonly aiService: AiService) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    return this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        renterName: renter.user.name,
        propertyAddress: (await property.location).address,
      },
      conversation.id,
      answerIsThisAScamQuestionTemplate,
      6,
    );
  }
}

import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { AiService } from '../../../../../ai/ai.service';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { answerPersonalQuestionTemplate } from './prompts/answer-personal-question.template';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { Injectable } from '@nestjs/common';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';

@Injectable()
export class AnswerPersonalQuestionStrategy implements HandleRenterMessageStrategy {
  constructor(private readonly aiService: AiService) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    _renter: Renter,
    _property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const response = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
      },
      conversation.id,
      answerPersonalQuestionTemplate,
      3,
    );

    return 'Personal question: ' + response;
  }
}

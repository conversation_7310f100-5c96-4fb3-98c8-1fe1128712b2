import { z } from 'zod';

import { Injectable } from '@nestjs/common';

import { TimezoneUtils } from '../../../../../../utils/timezone.utils';
import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { IntelligentEscalationService } from '../../../../../investor/property-question/intelligent-escalation.service';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';
import { Renter } from '../../../../renter/renter.entity';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { getRequiredDataFromTheObjectTemplate } from '../../prompts/get-required-data-from-the-object.template';
import { getAvailabilityPromptInstruction } from '../../utils/get-availability-prompt-instruction.function';
import { answerRenterQuestionTemplate } from './prompts/answer-renter-question.template';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { sharedRequirementsInstructionsTemplate } from '../discuss-requirements/prompts/shared-requirements-instructions.template';
import { ConfigService } from '@nestjs/config';
import { FollowUpTypeEnum } from '../../../../../shared/communication/follow-up/enums/follow-up-type.enum';

@Injectable()
export class AnswerPropertyQuestionStrategy implements HandleRenterMessageStrategy {
  private readonly baseUrl = this.config.get('RENTER_APP_URL') + '/property/';

  constructor(
    private readonly aiService: AiService,
    private readonly propertyQuestionService: IntelligentEscalationService,
    private readonly followUpService: FollowUpService,
    private readonly config: ConfigService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    // Create deep copies of all property-related objects to avoid modifying the originals
    const amenities = { ...(await property.amenities) };
    const location = { ...(await property.location) };
    const leaseConditions = { ...(await property.leaseConditions) };
    const renterRequirements = { ...(await property.renterRequirements) };
    const petPolicy = { ...(await property.petPolicy) };
    const specifications = { ...(await property.specifications) };
    const accessibility = { ...(await property.accessibility) };
    const parking = { ...(await property.parking) };
    const includedUtilities = { ...(await property.includedUtilities) };
    const propertyLink = this.baseUrl + property.id;

    // delete "isUtilitiesIncludedInRent" field if it's true
    if (includedUtilities?.isUtilitiesIncludedInRent === true) {
      const hasIncludedUtilities = [
        includedUtilities.water,
        includedUtilities.sewage,
        includedUtilities.garbage,
        includedUtilities.electricity,
        includedUtilities.gas,
        includedUtilities.internet,
        includedUtilities.cable,
      ].some((value) => value === true);

      if (hasIncludedUtilities) {
        delete includedUtilities.isUtilitiesIncludedInRent;
      }
    }

    // Create a property copy with the deep-copied objects
    const propertyCopy = {
      amenities,
      location,
      leaseConditions,
      renterRequirements,
      petPolicy,
      specifications,
      accessibility,
      parking,
      includedUtilities,
      allowsSmoking: property.allowsSmoking,
      maximumOccupancy: property.maximumOccupancy,
      marketingDescription: property.description,
      propertyManagementType: property.propertyManagementType,
      propertyManagementCompanyName: property.propertyManagementCompanyName,
    };

    const availabilityInstruction = getAvailabilityPromptInstruction(
      TimezoneUtils.getCurrentCityDate(propertyCopy.location.city, propertyCopy.location.timeZone),
      propertyCopy.leaseConditions.desiredLeasingDate,
    );

    propertyCopy.leaseConditions.desiredLeasingDate = availabilityInstruction as unknown as Date;

    let propertyData = await this.aiService.getResponse(
      {
        question: renterMessage,
        datasource: JSON.stringify(propertyCopy),
      },
      getRequiredDataFromTheObjectTemplate,
      null,
      LanguageModelsEnum.GPT_4_MINI,
    );

    const parser = StructuredOutputParser.fromZodSchema(
      z.object({
        aiReply: z.string().describe('AI reply to the renter question'),
        questionsAiWasNotAbleToAnswer: z.array(z.string()).describe("Questions AI wasn't able to answer"),
      }),
    );

    // remove possible explanation from the AI response
    if (propertyData.includes('"data": null')) {
      propertyData = '{ "data": null }';
      console.log('Overriden the property data');
    }

    const responseContent = <
      {
        aiReply: string;
        questionsAiWasNotAbleToAnswer: string[];
      }
    >(<unknown>await this.aiService.getResponseWithChatMemory(
      {
        propertyLink,
        input: renterMessage,
        datasource: propertyData,
        additionalInfo: sharedRequirementsInstructionsTemplate,
      },
      conversation.id,
      answerRenterQuestionTemplate,
      6,
      LanguageModelsEnum.CLAUDE_4_SONNET,
      parser,
    ));

    if (responseContent.questionsAiWasNotAbleToAnswer?.length) {
      // we delete follow-ups for this conversation since we are escalating the question
      this.followUpService.deleteRenterFollowUpsByStatusAndType(
        conversation.id,
        FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      );

      for (let index = 0; index < responseContent.questionsAiWasNotAbleToAnswer.length; index++) {
        const propertyQuestion = responseContent.questionsAiWasNotAbleToAnswer[index];
        await this.propertyQuestionService.escalatePropertyQuestion(property, propertyQuestion, renter);
      }
    }

    return 'Property question: ' + responseContent.aiReply;
  }
}

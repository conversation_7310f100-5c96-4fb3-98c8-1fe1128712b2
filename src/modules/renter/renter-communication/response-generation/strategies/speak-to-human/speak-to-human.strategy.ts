import { Injectable } from '@nestjs/common';
import { AiService } from '../../../../../ai/ai.service';
import { PropertyInquiryEventTypeEnum } from '../../../../../investor/property-inquiry/events/property-inquiry-event-type.enum';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { ShowingRequestService } from '../../../../../investor/showing-request/showing-request.service';
import { Showing } from '../../../../../investor/showing/showing.entity';
import { ShowingAgent } from '../../../../../investor/showing-agent/entities/showing-agent.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { AiStrategyExecutionCode } from '../../enums/ai-strategy-execution-code';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { speakToHumanTemplate } from './prompts/speak-to-human.template';
import { speakToHumanShowingConfirmedTemplate } from './prompts/speak-to-human-showing-confirmed.template';
import { speakToHumanApplicationSentTemplate } from './prompts/speak-to-human-application-sent.template';

@Injectable()
export class SpeakToHumanStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly showingRequestService: ShowingRequestService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const showingConfirmed = await PropertyInquiry.didEventHappen(
      inquiry,
      PropertyInquiryEventTypeEnum.SHOWING_CONFIRMED,
    );

    const applicationSent = await PropertyInquiry.didEventHappen(
      inquiry,
      PropertyInquiryEventTypeEnum.APPLICATION_INVITE_SENT,
    );

    if (showingConfirmed && !applicationSent) {
      try {
        const showingRequest = await this.showingRequestService.findByRenterAndProperty(renter.id, property.id);

        if (showingRequest) {
          const showing: Showing = await showingRequest.showing;
          const showingAgent: ShowingAgent = await showing.showingAgent;

          if (showingAgent) {
            const response = await this.aiService.getResponseWithChatMemory(
              {
                input: renterMessage,
                renterName: renter.user.name,
                showingAgentName: `${showingAgent.firstName} ${showingAgent.lastName}`,
                showingAgentPhone: showingAgent.phone,
                executionCode: AiStrategyExecutionCode.DO_NOTHING,
              },
              conversation.id,
              speakToHumanShowingConfirmedTemplate,
              8,
              LanguageModelsEnum.GPT_4,
            );

            if (response === AiStrategyExecutionCode.DO_NOTHING) {
              return AiStrategyExecutionCode.DO_NOTHING;
            }

            return 'Human handoff: ' + response;
          }
        }
      } catch (error) {
        console.error('Error getting showing agent details:', error);
      }
    } else if (applicationSent) {
      try {
        const propertyOwner = await property.owner;
        const ownerUser = await propertyOwner.user;

        if (ownerUser) {
          const response = await this.aiService.getResponseWithChatMemory(
            {
              input: renterMessage,
              renterName: renter.user.name,
              propertyOwnerName: ownerUser.name,
              propertyOwnerPhone: ownerUser.phoneNumber,
              propertyOwnerEmail: ownerUser.email,
              executionCode: AiStrategyExecutionCode.DO_NOTHING,
            },
            conversation.id,
            speakToHumanApplicationSentTemplate,
            8,
            LanguageModelsEnum.GPT_4,
          );

          if (response === AiStrategyExecutionCode.DO_NOTHING) {
            return AiStrategyExecutionCode.DO_NOTHING;
          }

          return 'Human handoff: ' + response;
        }
      } catch (error) {
        console.error('Error getting property owner details:', error);
      }
    }

    const currentUserCommsChannel = renter.user.preferredCommunicationChannel;

    const response = await this.aiService.getResponseWithChatMemory(
      {
        currentUserCommsChannel,
        input: renterMessage,
        renterName: renter.user.name,
        executionCode: AiStrategyExecutionCode.DO_NOTHING,
      },
      conversation.id,
      speakToHumanTemplate,
      8,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    if (response === AiStrategyExecutionCode.DO_NOTHING) {
      return AiStrategyExecutionCode.DO_NOTHING;
    }

    return 'Human handoff: ' + response;
  }
}

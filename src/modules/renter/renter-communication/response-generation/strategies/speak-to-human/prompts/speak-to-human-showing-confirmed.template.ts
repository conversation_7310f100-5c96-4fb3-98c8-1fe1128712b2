import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';
import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';

export const speakToHumanShowingConfirmedTemplate = `
<task>
Provide the showing agent's contact information to the renter who wants to speak with a human.
</task>

<examples>
- "You can contact [agent name], who will be doing a showing, at [agent phone]."
</examples>

<instructions>
${generalAiInstructionsTemplate}
${AiInstruction.DontSayWe}
${AiInstruction.PlainTextOutput}

Additional guidelines:
- Include the showing agent's name and phone number in your response
- Keep the response concise and helpful
- Don't apologize for being AI
</instructions>

<showing_agent_contact_details>
Name: {showingAgentName}
Phone: {showingAgentPhone}
</showing_agent_contact_details>

${PromptVariable.RenterName}
${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

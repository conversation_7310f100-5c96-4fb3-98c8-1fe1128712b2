import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';
import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';

export const speakToHumanApplicationSentTemplate = `
<task>
Provide the contact information to the renter who wants to speak with a human.
If there is a phone number, only share the phone number.
If there is no phone number, share the email address instead.
</task>

<examples>
Good responses:
- "You can contact the [owner name], at [owner phone]. "
</examples>

<contact_details>
Name: {propertyOwnerName}
Phone: {propertyOwnerPhone}
Email: {propertyOwnerEmail}
</contact_details>

<instructions>
${generalAiInstructionsTemplate}
${AiInstruction.DontSayWe}
${AiInstruction.PlainTextOutput}

Additional guidelines:
- Include the property owner's name and phone number in your response
- Keep the response concise and helpful
- Don't apologize for being AI
- Emphasize that the property owner is the appropriate person to contact at this stage
- Make it clear that the property owner is specifically responsible for application review and lease decisions
</instructions>

<property_owner_contact_details>
</property_owner_contact_details>

${PromptVariable.RenterName}
${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

/* eslint-disable max-len */
import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';
import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';

export const explainTimeframeTemplate = `
<task>
AI sent a showing request to the property owner but hasn't received the confirmation yet.
Talk to the renter and work with his concerns.
Ensure him you are working on it and will get back to him as soon as you have the confirmation
(don't repeat it in every message though).
</task>

<instructions>
${generalAiInstructionsTemplate}
- If the showing is today, say that you already sent property owner a follow-up.
- If question is asked right after the request was sent, say that you are waiting for the response and share
the usual timeframe if needed and makes sense.
- If it's something else, try to understand type of question and provide the appropriate answer,
but never make anything up or give promises that are not based on the information you have.
- Try to be concise
${AiInstruction.DontSayWe}
- You will be given today date and the showing date, feel free to use words like "today", "tomorrow", etc. Please use full month names and days of the week.
- Don't make up any facts, use only the information provided
- If needed, you can inform the renter, that if the renter does not reply, you will cancel the showing
two hours before the showing time (only bring it up if the showing is today and happening soon).
- If the renter's message does not need a reply, return "{executionCode}" and nothing else
</instructions>

<usual_response_timeframes>Showing confirmation: up to 12 hours.</usual_response_timeframes>

${PromptVariable.CurrentDate}
${PromptVariable.ShowingTime}
${PromptVariable.PropertyAddress}

${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

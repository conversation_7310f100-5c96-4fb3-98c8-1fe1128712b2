import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { AiService } from '../../../../../ai/ai.service';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { explainTimeframeTemplate } from './prompts/explain-timeframe.template';
import { Injectable } from '@nestjs/common';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { ShowingRequest } from '../../../../../investor/showing-request/showing-request.entity';
import { Showing } from '../../../../../investor/showing/showing.entity';
import { ShowingRequestService } from '../../../../../investor/showing-request/showing-request.service';
import { TimezoneUtils } from '../../../../../../utils/timezone.utils';
import { AiStrategyExecutionCode } from '../../enums/ai-strategy-execution-code';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';

@Injectable()
export class WhyShowingRequestPendingStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly showingRequestService: ShowingRequestService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const showingRequest: ShowingRequest = await this.showingRequestService.findByRenterAndProperty(
      renter.id,
      property.id,
    );

    const showing: Showing = await showingRequest.showing;
    const location = await property.location;

    const response = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        propertyAddress: location.address,
        currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(location.city, location.timeZone),
        showingTime: TimezoneUtils.convertDateToStringInCityTz(showing.startTime, location.city, location.timeZone),
        executionCode: AiStrategyExecutionCode.EXECUTED_WITHOUT_ANSWER,
      },
      conversation.id,
      explainTimeframeTemplate,
      5,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    if (response === AiStrategyExecutionCode.EXECUTED_WITHOUT_ANSWER) {
      return AiStrategyExecutionCode.EXECUTED_WITHOUT_ANSWER;
    } else {
      return 'Timeframe question: ' + response;
    }
  }
}

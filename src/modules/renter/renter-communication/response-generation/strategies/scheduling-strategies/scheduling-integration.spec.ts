import { Test, TestingModule } from '@nestjs/testing';
import { ScheduleShowingStrategy } from './schedule-showing/schedule-showing.strategy';
import { RescheduleShowingStrategy } from './reschedule-showing/reschedule-showing.strategy';
import { SchedulingStrategiesSharedService } from './shared/scheduling-strategies-shared.service';
import { TimeRange } from './shared/interfaces/time-range';
import {
  CalendarEventResponseDto,
  CalendarEventStatus,
} from '../../../../../shared/calendar/models/calendar-event.dto';
import { ShowingRequestStatus } from '../../../../../investor/showing-request/enums/showing-request-status.enum';
import { RentStageEnum } from '../../../../../shared/communication/conversation/enums/rent-stage.enum';
import { CalendarService } from '../../../../../shared/calendar/calendar.service';
import { ShowingRequestService } from '../../../../../investor/showing-request/showing-request.service';
import { TourType } from '../../../../../investor/showing/enums/tour-type.enum';
import { ShowingService } from '../../../../../investor/showing/showing.service';
import { PropertyInquiryService } from '../../../../../investor/property-inquiry/property-inquiry.service';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';
import { AiService } from '../../../../../ai/ai.service';
import { AvailabilitySlotsService } from '../../../../../investor/availability/availability-slot.service';
import { PropertyAvailabilityService } from '../../../../../investor/property/availability/property-availability.service';
import { RescheduleService } from '../../../../../investor/reschedule/reschedule.service';
import { SlackCommunicationService } from '../../../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { AiObjectiveService } from '../../../ai-objective/ai-objective.service';
import { ShowingAgentService } from '../../../../../investor/showing-agent/showing-agent.service';
import { PropertyLocation } from '../../../../../investor/property/property/property-details/location/property-location.entity';

describe('Scheduling Integration Tests - End-to-End Workflows', () => {
  let scheduleShowingStrategy: ScheduleShowingStrategy;
  let rescheduleShowingStrategy: RescheduleShowingStrategy;
  let schedulingStrategiesSharedService: SchedulingStrategiesSharedService;
  let calendarService: any;
  let showingRequestService: any;
  let propertyInquiryService: any;
  let aiService: any;

  // Mock data setup
  const mockUser = {
    id: 'user-123',
    name: 'Test Renter',
    email: '<EMAIL>',
    phoneNumber: '+1234567890',
  };

  const mockRenter = {
    id: 'renter-123',
    userId: 'user-123',
    user: Promise.resolve(mockUser),
  };

  const mockInvestor = {
    id: 'investor-123',
    user: Promise.resolve({
      id: 'investor-user-123',
      name: 'Test Investor',
      email: '<EMAIL>',
    }),
  };

  const mockLocation = {
    city: 'New York',
    timeZone: 'America/New_York',
    address: '123 Test St, New York, NY',
  };

  const mockProperty = {
    id: 'property-123',
    location: Promise.resolve(mockLocation),
    owner: Promise.resolve(mockInvestor),
  };

  const mockConversation = {
    id: 'conversation-123',
    renter: mockRenter,
    property: mockProperty,
  };

  const mockPropertyInquiry = {
    id: 'inquiry-123',
    renter: mockRenter,
    property: mockProperty,
    stage: RentStageEnum.INITIAL_CONTACT,
    aiObjectives: Promise.resolve([]), // Add missing aiObjectives property
  };

  const mockAvailabilitySlots = [
    {
      id: 'slot-1',
      weekday: 1, // Monday
      startTime: '14:00:00+00',
      endTime: '18:00:00+00',
    },
  ];

  const mockPropertyAvailability = {
    id: 'availability-1',
    showingDurationInMinutes: 60,
    availabilitySlots: Promise.resolve(mockAvailabilitySlots),
  };

  beforeEach(async () => {
    // Create standard Jest mocks
    calendarService = {
      getCalendarEvents: jest.fn(),
      createCalendarEvent: jest.fn(),
      updateCalendarEvent: jest.fn(),
      deleteCalendarEvent: jest.fn(),
    };
    showingRequestService = {
      create: jest.fn(),
      sendNewRequestToPropertyOwner: jest.fn(),
      findByRenterAndProperty: jest.fn(),
      rescheduleShowingRequest: jest.fn(),
    };
    propertyInquiryService = {
      findByRenterAndProperty: jest.fn(),
      updateStageAndAddCorrespondingEvent: jest.fn(),
      update: jest.fn(),
    };
    aiService = {
      getResponseWithChatMemory: jest.fn(),
    };

    // Set up default mock behaviors
    calendarService.getCalendarEvents.mockResolvedValue([]);
    showingRequestService.findByRenterAndProperty.mockResolvedValue(null);
    propertyInquiryService.findByRenterAndProperty.mockResolvedValue(mockPropertyInquiry);
    aiService.getResponseWithChatMemory.mockResolvedValue([
      { startTime: new Date('2024-01-15T14:00:00Z'), endTime: new Date('2024-01-15T15:00:00Z') },
    ]);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleShowingStrategy,
        {
          provide: RescheduleShowingStrategy,
          useValue: {
            execute: jest
              .fn()
              .mockResolvedValue("Showing time: Perfect! I've rescheduled your showing to Wednesday at 2:00 PM."),
          },
        },
        {
          provide: SchedulingStrategiesSharedService,
          useValue: {
            parseRenterDesiredShowingTimes: jest.fn(),
            determineSuitableShowingTime: jest.fn(),
            generateResponseMessage: jest.fn(),
            getSuitableTimeFromOwnerInitiatedRescheduleRequest: jest.fn(),
            getExistingCalendarEvents: jest.fn(),
          },
        },
        { provide: CalendarService, useValue: calendarService },
        { provide: ShowingRequestService, useValue: showingRequestService },
        {
          provide: ShowingService,
          useValue: {
            findShowingsOverlappingWithTimeRange: jest.fn().mockResolvedValue([]),
            updateStatusBasedOnRequests: jest.fn(),
            rescheduleShowing: jest.fn(),
            findById: jest.fn().mockResolvedValue({ id: 'showing-123', showingAgentId: null }),
          },
        },
        { provide: PropertyInquiryService, useValue: propertyInquiryService },
        {
          provide: FollowUpService,
          useValue: {
            deleteRenterFollowUps: jest.fn(),
            deleteRenterFollowUpsByStatus: jest.fn(),
            deleteRenterFollowUpsByStatusAndType: jest.fn(),
            createPostShowingFollowUps: jest.fn(),
            findFollowUpByUserAndType: jest.fn(),
          },
        },
        { provide: AiService, useValue: aiService },
        { provide: AvailabilitySlotsService, useValue: { findAvailabilitySlotsInRange: jest.fn() } },
        {
          provide: PropertyAvailabilityService,
          useValue: { findByProperty: jest.fn().mockResolvedValue(mockPropertyAvailability) },
        },
        { provide: RescheduleService, useValue: { findOneBy: jest.fn(), acceptRescheduleRequest: jest.fn() } },
        { provide: SlackCommunicationService, useValue: { sendMessageToConvosChannel: jest.fn() } },
        { provide: AiObjectiveService, useValue: { updateObjectiveAndSyncItInItsInquiry: jest.fn() } },
        { provide: ShowingAgentService, useValue: { findOne: jest.fn().mockResolvedValue(null) } },
      ],
    }).compile();

    scheduleShowingStrategy = module.get<ScheduleShowingStrategy>(ScheduleShowingStrategy);
    rescheduleShowingStrategy = module.get<RescheduleShowingStrategy>(RescheduleShowingStrategy);
    schedulingStrategiesSharedService = module.get<SchedulingStrategiesSharedService>(
      SchedulingStrategiesSharedService,
    );

    // Clear mocks and restore default behaviors after module creation
    jest.clearAllMocks();

    // Restore default mock behaviors that tests can rely on
    calendarService.getCalendarEvents.mockResolvedValue([]);
    showingRequestService.findByRenterAndProperty.mockResolvedValue(null);
    showingRequestService.create.mockResolvedValue({ id: 'showing-request-123', status: ShowingRequestStatus.PENDING });
    showingRequestService.sendNewRequestToPropertyOwner.mockResolvedValue(undefined);
    showingRequestService.rescheduleShowingRequest.mockResolvedValue(undefined);
    propertyInquiryService.findByRenterAndProperty.mockResolvedValue(mockPropertyInquiry);
    propertyInquiryService.updateStageAndAddCorrespondingEvent.mockResolvedValue(undefined);
    aiService.getResponseWithChatMemory.mockResolvedValue([
      { startTime: new Date('2024-01-15T14:00:00Z'), endTime: new Date('2024-01-15T15:00:00Z') },
    ]);

    // Set up default behaviors for the shared service mocks
    (schedulingStrategiesSharedService.parseRenterDesiredShowingTimes as jest.Mock).mockResolvedValue([
      { startTime: new Date('2024-01-15T14:00:00Z'), endTime: new Date('2024-01-15T15:00:00Z') },
    ]);
    (schedulingStrategiesSharedService.determineSuitableShowingTime as jest.Mock).mockResolvedValue([
      { startTime: new Date('2024-01-15T14:00:00Z'), endTime: new Date('2024-01-15T15:00:00Z') },
    ]);
    (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValue({
      response: 'Great! I can schedule your showing for Monday at 2pm.',
      proposedTime: new Date('2024-01-15T14:00:00Z'),
      wasProposedTimeAgreedOn: true,
      tourType: TourType.IN_PERSON,
    });
    (schedulingStrategiesSharedService.getExistingCalendarEvents as jest.Mock).mockResolvedValue([]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Scheduling Workflow with Real Calendar Integration', () => {
    it('should complete end-to-end scheduling with calendar conflict detection', async () => {
      // Setup: Renter has calendar events that conflict with some desired times
      const conflictingCalendarEvents: CalendarEventResponseDto[] = [
        {
          id: 'conflict-event-1',
          title: 'Doctor Appointment',
          startTime: '2024-01-15T14:30:00Z',
          endTime: '2024-01-15T15:30:00Z',
          status: CalendarEventStatus.CONFIRMED,
          description: 'Annual checkup',
          location: 'Medical Center',
        },
      ];

      const renterDesiredTimes: TimeRange[] = [
        {
          startTime: new Date('2024-01-15T14:00:00Z'), // Conflicts with doctor appointment
          endTime: new Date('2024-01-15T16:00:00Z'),
        },
        {
          startTime: new Date('2024-01-16T14:00:00Z'), // No conflict
          endTime: new Date('2024-01-16T16:00:00Z'),
        },
      ];

      const suitableTimes: TimeRange[] = [
        {
          startTime: new Date('2024-01-16T14:00:00Z'), // Only non-conflicting time
          endTime: new Date('2024-01-16T15:00:00Z'),
        },
      ];

      // Override default behaviors for this specific test
      (schedulingStrategiesSharedService.parseRenterDesiredShowingTimes as jest.Mock).mockResolvedValueOnce(
        renterDesiredTimes,
      );
      (schedulingStrategiesSharedService.determineSuitableShowingTime as jest.Mock).mockResolvedValueOnce(
        suitableTimes,
      );
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce({
        response: 'I can schedule your showing for Tuesday at 2:00 PM. Does this work for you?',
        proposedTime: new Date('2024-01-16T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      });

      // Mock calendar service to return conflicting events (for verification)
      (calendarService.getCalendarEvents as jest.Mock).mockImplementation((userId, startTime) => {
        // Return conflict only for the first time range
        if (startTime.getTime() === renterDesiredTimes[0].startTime.getTime()) {
          return Promise.resolve(conflictingCalendarEvents);
        }
        return Promise.resolve([]);
      });

      // Mock showing request creation
      const mockShowingRequest = {
        id: 'showing-request-123',
        status: ShowingRequestStatus.PENDING,
      };
      (showingRequestService.create as jest.Mock).mockResolvedValueOnce(mockShowingRequest);

      // Spy on console.log to verify calendar integration logging
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Execute the complete workflow
      const result = await scheduleShowingStrategy.execute(
        'I would like to schedule a showing for Monday 2-4pm or Tuesday 2-4pm',
        mockPropertyInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify shared service methods were called
      expect(schedulingStrategiesSharedService.parseRenterDesiredShowingTimes).toHaveBeenCalledWith(
        'I would like to schedule a showing for Monday 2-4pm or Tuesday 2-4pm',
        mockConversation.id,
        mockLocation,
      );

      expect(schedulingStrategiesSharedService.determineSuitableShowingTime).toHaveBeenCalledWith(
        mockLocation,
        mockProperty,
        renterDesiredTimes,
        expect.any(Date),
      );

      expect(schedulingStrategiesSharedService.generateResponseMessage).toHaveBeenCalledWith(
        'I would like to schedule a showing for Monday 2-4pm or Tuesday 2-4pm',
        suitableTimes,
        expect.any(Array), // existingCalendarEvents from calendar integration
        mockLocation,
        mockConversation,
        mockProperty,
      );

      // Verify showing request was created
      expect(showingRequestService.create).toHaveBeenCalledWith({
        status: ShowingRequestStatus.PENDING,
        renter: mockRenter,
        property: mockProperty,
      });

      // Verify property inquiry stage was updated
      expect(propertyInquiryService.updateStageAndAddCorrespondingEvent).toHaveBeenCalledWith(
        mockPropertyInquiry.id,
        RentStageEnum.SHOWING_REQUESTED,
      );

      // Verify notification was sent to property owner
      expect(showingRequestService.sendNewRequestToPropertyOwner).toHaveBeenCalledWith(
        mockProperty,
        mockRenter,
        mockShowingRequest,
        expect.any(Date), // UTC converted time
        mockPropertyInquiry,
        TourType.IN_PERSON, // tourType from response
      );

      expect(result).toBe(
        'Schedule showing: I can schedule your showing for Tuesday at 2:00 PM. Does this work for you?',
      );

      consoleSpy.mockRestore();
    });

    it('should handle calendar not connected scenario gracefully', async () => {
      const renterDesiredTimes: TimeRange[] = [
        {
          startTime: new Date('2024-01-15T14:00:00Z'),
          endTime: new Date('2024-01-15T16:00:00Z'),
        },
      ];

      // Override default behaviors for this specific test
      (schedulingStrategiesSharedService.parseRenterDesiredShowingTimes as jest.Mock).mockResolvedValueOnce(
        renterDesiredTimes,
      );
      (schedulingStrategiesSharedService.determineSuitableShowingTime as jest.Mock).mockResolvedValueOnce(
        renterDesiredTimes,
      );
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce({
        response: 'I can schedule your showing for Monday at 2:00 PM.',
        proposedTime: new Date('2024-01-15T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      });

      (showingRequestService.create as jest.Mock).mockResolvedValueOnce({
        id: 'showing-request-123',
      });

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Should not throw error, but handle gracefully
      const result = await scheduleShowingStrategy.execute(
        'Schedule showing for Monday 2pm',
        mockPropertyInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify shared service methods were called
      expect(schedulingStrategiesSharedService.parseRenterDesiredShowingTimes).toHaveBeenCalled();
      expect(schedulingStrategiesSharedService.generateResponseMessage).toHaveBeenCalled();

      // Workflow should still complete successfully
      expect(showingRequestService.create).toHaveBeenCalled();
      expect(result).toContain('Schedule showing:');

      consoleSpy.mockRestore();
    });
  });

  describe('Reschedule Workflow Integration', () => {
    it('should complete end-to-end reschedule workflow with calendar integration', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const result = await rescheduleShowingStrategy.execute(
        'Can we reschedule to Wednesday at 2pm?',
        mockPropertyInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify reschedule strategy was called
      expect(rescheduleShowingStrategy.execute).toHaveBeenCalledWith(
        'Can we reschedule to Wednesday at 2pm?',
        mockPropertyInquiry,
        mockRenter,
        mockProperty,
        mockConversation,
      );

      expect(result).toBe("Showing time: Perfect! I've rescheduled your showing to Wednesday at 2:00 PM.");

      consoleSpy.mockRestore();
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should handle AI service failures gracefully', async () => {
      // Override default behavior to simulate shared service failure
      (schedulingStrategiesSharedService.parseRenterDesiredShowingTimes as jest.Mock).mockRejectedValueOnce(
        new Error('AI service unavailable'),
      );

      await expect(
        scheduleShowingStrategy.execute(
          'Schedule showing',
          mockPropertyInquiry as any,
          mockRenter as any,
          mockProperty as any,
          mockConversation as any,
        ),
      ).rejects.toThrow('AI service unavailable');
    });

    it('should handle calendar service failures without breaking workflow', async () => {
      const renterDesiredTimes: TimeRange[] = [
        {
          startTime: new Date('2024-01-15T14:00:00Z'),
          endTime: new Date('2024-01-15T16:00:00Z'),
        },
      ];

      // Override default behaviors for this specific test
      (schedulingStrategiesSharedService.parseRenterDesiredShowingTimes as jest.Mock).mockResolvedValueOnce(
        renterDesiredTimes,
      );
      (schedulingStrategiesSharedService.determineSuitableShowingTime as jest.Mock).mockResolvedValueOnce(
        renterDesiredTimes,
      );
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce({
        response: 'Scheduling completed despite calendar error',
        proposedTime: new Date('2024-01-15T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      });

      (showingRequestService.create as jest.Mock).mockResolvedValueOnce({
        id: 'showing-request-123',
      });

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      // Should handle calendar error gracefully and continue
      const result = await scheduleShowingStrategy.execute(
        'Schedule showing',
        mockPropertyInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify shared service methods were called
      expect(schedulingStrategiesSharedService.parseRenterDesiredShowingTimes).toHaveBeenCalled();
      expect(schedulingStrategiesSharedService.generateResponseMessage).toHaveBeenCalled();

      expect(showingRequestService.create).toHaveBeenCalled();
      expect(result).toContain('Scheduling completed despite calendar error');

      consoleSpy.mockRestore();
    });
  });

  describe('Performance and Concurrency', () => {
    it('should handle multiple concurrent calendar API calls efficiently', async () => {
      const multipleDesiredTimes: TimeRange[] = [
        { startTime: new Date('2024-01-15T14:00:00Z'), endTime: new Date('2024-01-15T16:00:00Z') },
        { startTime: new Date('2024-01-16T14:00:00Z'), endTime: new Date('2024-01-16T16:00:00Z') },
        { startTime: new Date('2024-01-17T14:00:00Z'), endTime: new Date('2024-01-17T16:00:00Z') },
      ];

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.determineSuitableShowingTime as jest.Mock).mockResolvedValueOnce(
        multipleDesiredTimes,
      );

      const startTime = Date.now();

      await schedulingStrategiesSharedService.determineSuitableShowingTime(
        mockLocation as PropertyLocation,
        mockInvestor as any,
        multipleDesiredTimes,
        new Date(),
      );

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Verify the shared service method was called
      expect(schedulingStrategiesSharedService.determineSuitableShowingTime).toHaveBeenCalledWith(
        mockLocation,
        mockInvestor,
        multipleDesiredTimes,
        expect.any(Date),
      );

      // Should complete within reasonable time (less than 5 seconds for this test)
      expect(executionTime).toBeLessThan(5000);
    });
  });
});

import { AiInstruction } from '../../../../../../../ai/prompts/ai-instructions';
import { PromptVariable } from '../../../../../../../ai/enums/prompt-variable.enum';

export const parseDesiredRenterTimeTemplate = `
<task>
Parse the message from the renter and extract the desired showing date and time.
CRITICAL: This template ONLY extracts SHOWING/TOUR dates, NOT move-in dates.
</task>

<task_description>
- CRITICAL CONTEXT DISTINCTION: This template extracts SHOWING/TOUR scheduling information ONLY. Do NOT confuse move-in dates with showing dates.
  - If the conversation context shows the AI is asking about move-in dates (e.g., "when are you hoping to move in?", "what's your target move-in date?"), and the renter responds with dates, those are MOVE-IN dates, NOT showing dates.
  - If renter mentions move-in dates while the conversation is about move-in requirements, ignore those dates for showing purposes and return the next 7 days range instead.
  - Only extract dates that are clearly about SHOWING/TOUR scheduling (e.g., "Monday tour", "can we see it tomorrow", "showing on Friday").
- If renter didn't specify when he wants to see the property, or said he wants to see it today,
you should return date range of the next 7 days.
- Never return a date in the past.
- If renter said the specific time, startTime and endTime should be the same.
- If renter said the range of time, startTime and endTime should be different.
- If renter said something like 'Morning' or 'Afternoon', you should turn it into a range of time.
- If renter said something like 'Tomorrow', you should turn it into a next day date.
Same goes for 'Day after tomorrow', 'Next week', 'Next month', etc.
- CRITICAL WEEKDAY HANDLING: When a renter mentions a weekday without specifying "next" or a specific date (e.g., "Monday", "what about Monday", "Monday tour"), assume they mean the NEAREST upcoming occurrence of that weekday. Do NOT assume a distant future date.
  - Example: If today is Friday September 5th and renter says "Monday", they mean Monday September 8th (next Monday), NOT Monday September 15th.
  - Only use future weeks if the renter explicitly says "next Monday" or provides a specific date.
- CONVERSATION CONTEXT: Always review the full conversation history to understand date context from previous messages.
  - If a renter previously mentioned a specific date (e.g., "next Monday" or "September 15th") and then in a later message only mentions time (e.g., "3pm", "in the afternoon"), apply that time to the previously mentioned date.
  - If the current message lacks date context but a previous message established a date, use that established date with the new time information.
- If renter said something like 'Weekend', you should find the next weekend date
and turn it into a range of time (Saturday 0 AM - Sunday 23:59 PM)
- If renter said 'ASAP', you should return the next 7 days range.
- If desired date is in the past, empty, or question is irrelevant, ignore it and return the next 7 days range instead.
- If renter said 'Evening', you should return an array of dates with each start time at 6 PM and end time at 8 PM.
- If renter asks for tomorrow slot that is less than 24 hours from now, return the next 7 days range.
</task_description>

<exceptions>
- If the desired time is Today, ignore it and return the next 7 days range instead.
Prioritize this rule over all others.
</exceptions>

${PromptVariable.CurrentDate}
${PromptVariable.ChatHistory}

<instructions>
- Dates should be formatted as javascript ISO format in zulu time zone.
- Dates should not be adjusted to timezones (
e.g, if renter says 4PM, it should be returned as 'YYYY-MM-DDT16:00:00.000Z',
if renter says 'Tomorrow', it should be returned as 'YYYY-MM-DDT00:00:00.000Z',
if renter says 'Tomorrow 17:00', it should be returned as 'YYYY-MM-DDT17:00:00.000Z'
)
- Never return an empty date range, always fall back to the next 7 days range.
- Returned value should be a javascript date
${AiInstruction.LatestRenterMessageMarking}
- Don't write any code or explain your reply. Just write two dates.
</instructions>

<response_format>
Here is the format of the expected reply, please stick to it: {format_instructions}
</response_format>

<examples>
<example>
Renter message: I want to see the property tomorrow at 4 PM
Current Date: "Wed, Apr 17, 2024, 10:00AM"
 Response:
 [
 {{
  "startTime": "2024-04-18T16:00:00.000Z",
  "endTime": "2024-04-18T16:00:00.000Z"
 }}
 ]
</example>
<example>
Renter message: I want to see the property this weekend
Current Date: "Tue, May 7, 2024, 03:00PM"
Response:
[
 {{
  "startTime": "2024-05-11T00:00:00.000Z",
  "endTime": "2024-05-11T23:59:59.000Z"
 }},
 {{
  "startTime": "2024-05-12T00:00:00.000Z",
  "endTime": "2024-05-12T23:59:59.000Z"
 }}
 ]
 </example>
 <example>
Renter message: Can I see it in the morning or on the weekend?
Current Date: "Fri, Jun 14, 2024, 08:00AM"
Response:
[
 {{
  "startTime": "2024-06-15T00:00:00.000Z",
  "endTime": "2024-06-15T23:59:00.000Z"
 }},
 {{
  "startTime": "2024-06-16T00:00:00.000Z",
  "endTime": "2024-06-16T23:59:00.000Z"
 }},
 {{
  "startTime": "2024-06-17T06:00:00.000Z",
  "endTime": "2024-06-17T09:00:00.000Z"
 }},
 {{
  "startTime": "2024-06-18T06:00:00.000Z",
  "endTime": "2024-06-18T09:00:00.000Z"
 }},
 {{
  "startTime": "2024-06-19T06:00:00.000Z",
  "endTime": "2024-06-19T09:00:00.000Z"
 }},
 {{
  "startTime": "2024-06-20T06:00:00.000Z",
  "endTime": "2024-06-20T09:00:00.000Z"
 }},
 {{
  "startTime": "2024-06-21T06:00:00.000Z",
  "endTime": "2024-06-21T09:00:00.000Z"
 }}
 ]
</example>
<example>
Renter message: Hey what about Monday tour
Current Date: "Fri, Sep 5, 2025, 08:00AM"
Response:
[
 {{
  "startTime": "2025-09-08T00:00:00.000Z",
  "endTime": "2025-09-08T23:59:59.000Z"
 }}
]
</example>
<example>
Chat History:
- Renter: "Can we schedule for next Monday?"
- AI: "I have availability on Monday from 10AM to 6PM. What time works for you?"
- Renter: "3pm works"

Current Date: "Fri, Sep 5, 2025, 08:00AM"
Response:
[
 {{
  "startTime": "2025-09-08T15:00:00.000Z",
  "endTime": "2025-09-08T15:00:00.000Z"
 }}
]
</example>
</examples>

${PromptVariable.RenterMessage}
`;

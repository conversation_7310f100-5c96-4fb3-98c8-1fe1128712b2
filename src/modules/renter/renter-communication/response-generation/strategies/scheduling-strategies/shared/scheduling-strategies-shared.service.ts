import { Injectable } from '@nestjs/common';
import { addMinutes } from 'date-fns';
import { z } from 'zod';
import { DateUtils } from '../../../../../../../utils/date.utils';
import { TimezoneUtils } from '../../../../../../../utils/timezone.utils';
import { AiService } from '../../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../../ai/enums/language-models.enum';
import { AvailabilitySlot } from '../../../../../../investor/availability/availability-slot.entity';
import { AvailabilitySlotsService } from '../../../../../../investor/availability/availability-slot.service';
import { PropertyAvailabilityService } from '../../../../../../investor/property/availability/property-availability.service';
import { Property } from '../../../../../../investor/property/property/entities/property.entity';
import { PropertyLocation } from '../../../../../../investor/property/property/property-details/location/property-location.entity';
import { Showing } from '../../../../../../investor/showing/showing.entity';
import { TourType } from '../../../../../../investor/showing/enums/tour-type.enum';
import { Conversation } from '../../../../../../shared/communication/conversation/entities/conversation.entity';
import { CalendarService } from '../../../../../../shared/calendar/calendar.service';
import { negotiateShowingTimeTemplate } from '../../../prompts/negotiate-showing-time.template';
import { TimeRange } from './interfaces/time-range';
import { parseDesiredRenterTimeTemplate } from './prompts/parse-desired-renter-time.template';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { CalendarTimeRange } from './interfaces/calendar-time-range';

@Injectable()
export class SchedulingStrategiesSharedService {
  constructor(
    private readonly aiService: AiService,
    private readonly availabilityService: AvailabilitySlotsService,
    private readonly propertyAvailabilityService: PropertyAvailabilityService,
    private readonly calendarService: CalendarService,
  ) {}

  async generateResponseMessage(
    renterMessage: string,
    availableTimes: TimeRange[],
    existingCalendarEvents: CalendarTimeRange[],
    location: PropertyLocation,
    conversation: Conversation,
    property: Property,
    initiallySelectedTourType?: TourType,
  ): Promise<{
    response: string;
    proposedTime: Date;
    wasProposedTimeAgreedOn: boolean;
    tourType: TourType;
  }> {
    const formatedTimes = this.formatTimeRangeToAiReadableFormat(availableTimes);
    const currentDateInCityUtc = TimezoneUtils.getCurrentCityDate(location.city, location.timeZone);
    const propertyAvailability = await this.propertyAvailabilityService.findByProperty(property.id);

    const currentDate = DateUtils.toAiReadableFormat(currentDateInCityUtc);
    const tomorrowDateInCityInAiReadableFormat = DateUtils.getNextDayFormatted(currentDateInCityUtc);

    return JSON.parse(
      await this.aiService.getResponseWithChatMemory(
        {
          input: renterMessage,
          availableTimes: JSON.stringify(formatedTimes),
          existingCalendarEvents: JSON.stringify(existingCalendarEvents),
          showingDurationInMinutes: propertyAvailability.showingDurationInMinutes,
          currentDate: currentDate,
          tomorrowDate: tomorrowDateInCityInAiReadableFormat,
          propertyCity: location.city,
          allowsInPersonTours: property.allowsInPersonTours,
          allowsVirtualTours: Boolean(property.allowsVirtualTours), // to avoid issues with AI and null values
          previousTourType: initiallySelectedTourType || 'N/A',
        },
        conversation.id,
        negotiateShowingTimeTemplate,
        12,
        LanguageModelsEnum.CLAUDE_4_SONNET_REASONER,
      ),
    );
  }

  async parseRenterDesiredShowingTimes(
    renterMessage: string,
    conversationId: string,
    location: PropertyLocation,
  ): Promise<TimeRange[]> {
    const currentDate = TimezoneUtils.getCurrentCityDateInAiReadableFormat(location.city, location.timeZone);

    const parser = StructuredOutputParser.fromZodSchema(
      z.array(
        z.object({
          startTime: z.string(),
          endTime: z.string(),
        }),
      ),
    );

    return <TimeRange[]>(<unknown>await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        currentDate,
      },
      conversationId,
      parseDesiredRenterTimeTemplate,
      6,
      LanguageModelsEnum.CLAUDE_4_SONNET,
      parser,
    ));
  }

  formatTimeRangeToAiReadableFormat(range: TimeRange[]): any[] {
    const formatedTimes = [];
    range.forEach((time) => {
      formatedTimes.push({
        startTime: DateUtils.toAiReadableFormat(time.startTime),
        endTime: DateUtils.toAiReadableFormat(time.endTime),
      });
    });

    return formatedTimes;
  }

  async determineSuitableShowingTime(
    location: PropertyLocation,
    property: Property,
    renterDesiredShowingTimes: TimeRange[],
    currentDateUtc: Date,
  ): Promise<TimeRange[]> {
    const suitableTimes: TimeRange[] = [];
    const propertyAvailability = await this.propertyAvailabilityService.findByProperty(property.id);
    const availabilitySlots = await propertyAvailability.availabilitySlots;

    // TODO https://linear.app/tallo-ai/issue/ENG-421/make-ai-avare-of-showings-and-prioritize-that-time-slots
    // suitableTimes.push(
    // ...(await this.findSuitableTimesByExistingShowings(context, renterDesiredShowingTimes)),
    // );

    // if there are now showings, we find the time slots that match both investor and renter availability
    if (suitableTimes.length === 0) {
      suitableTimes.push(
        ...(await this.findSuitableAvailabilitySlotsInTimeRange(
          availabilitySlots,
          renterDesiredShowingTimes,
          currentDateUtc,
        )),
      );
    }

    // if there is no overlap we simply find all the slots that are available for the investor in the next 7 days
    if (suitableTimes.length === 0) {
      const currentDate = TimezoneUtils.getCurrentCityDate(location.city, location.timeZone);
      const nextWeek = new Date(currentDate);
      nextWeek.setDate(currentDate.getDate() + 7);

      suitableTimes.push(
        ...(await this.findSuitableAvailabilitySlotsInTimeRange(
          availabilitySlots,
          [
            {
              startTime: currentDate,
              endTime: nextWeek,
            },
          ],
          currentDate,
        )),
      );
    }

    // if investor didn't specify when he is available, we propose the day after tomorrow 09:00 AM
    if (suitableTimes.length === 0) {
      const date = TimezoneUtils.getCurrentCityDate(location.city, location.timeZone);
      date.setDate(date.getDate() + 2);
      date.setUTCHours(9, 0, 0, 0);

      suitableTimes.push({
        startTime: date,
        endTime: addMinutes(date, 15),
      });
    }

    return suitableTimes;
  }

  getSuitableTimeFromOwnerInitiatedRescheduleRequest(showing: Showing, location: PropertyLocation): TimeRange {
    const showingStartTime = showing.startTime;
    const showingEndTime = showing.endTime;

    return {
      startTime: TimezoneUtils.convertUtcDateToCityTimeWithMissingTz(
        showingStartTime,
        location.city,
        location.timeZone,
      ),
      endTime: TimezoneUtils.convertUtcDateToCityTimeWithMissingTz(showingEndTime, location.city, location.timeZone),
    };
  }

  private async findSuitableAvailabilitySlotsInTimeRange(
    availabilitySlots: AvailabilitySlot[],
    renterDesiredShowingTimes: TimeRange[],
    currentDateUtc: Date,
  ): Promise<TimeRange[]> {
    const suitableTimes: TimeRange[] = [];

    if (!availabilitySlots) {
      return renterDesiredShowingTimes;
    }

    // if renter specified when he is available, we find the time slots that match both investor and renter availability
    for (const item of renterDesiredShowingTimes) {
      const matchedTimeSlotsInUtcWithoutTz = await this.availabilityService.findAvailabilitySlotsInRange(
        new Date(item.startTime),
        new Date(item.endTime),
        availabilitySlots,
        currentDateUtc,
      );

      if (matchedTimeSlotsInUtcWithoutTz.length > 0) {
        suitableTimes.push(...matchedTimeSlotsInUtcWithoutTz);
      }
    }

    return suitableTimes;
  }

  async getExistingCalendarEvents(
    property: Property,
    renterDesiredShowingTimes: TimeRange[],
    propertyLocation: PropertyLocation,
  ): Promise<CalendarTimeRange[]> {
    try {
      const investor = await property.owner;
      const investorUser = await investor.user;

      const calendars = await investorUser.calendars;

      // filter only active calendars
      const activeCalendars = calendars.filter((calendar) => calendar.isActive);

      if (activeCalendars.length === 0) {
        console.log(`[Calendar Integration] No active calendars found for investor ${investorUser.id}`);
        return [];
      }

      const existingCalendarEvents: CalendarTimeRange[] = [];

      for (const timeRange of renterDesiredShowingTimes) {
        console.log(`[Calendar Integration] Checking time range: ${timeRange.startTime} to ${timeRange.endTime}`);

        try {
          const calendarEvents = await this.calendarService.getCalendarEvents(
            investorUser.id,
            new Date(timeRange.startTime),
            new Date(timeRange.endTime),
          );

          console.log(`[Calendar Integration] Found ${calendarEvents.length} calendar events for time range:`, {
            startTime: timeRange.startTime,
            endTime: timeRange.endTime,
            events: calendarEvents,
          });

          calendarEvents.forEach((calendarEvent) => {
            existingCalendarEvents.push({
              eventName: calendarEvent.title,
              type: calendarEvent.title.includes(propertyLocation.address) ? 'Showing' : 'Other',
              startTime: DateUtils.toAiReadableFormat(
                TimezoneUtils.convertUtcDateToCityTimeWithMissingTz(
                  new Date(calendarEvent.startTime),
                  propertyLocation.city,
                  propertyLocation.timeZone,
                ),
              ),
              endTime: DateUtils.toAiReadableFormat(
                TimezoneUtils.convertUtcDateToCityTimeWithMissingTz(
                  new Date(calendarEvent.endTime),
                  propertyLocation.city,
                  propertyLocation.timeZone,
                ),
              ),
            });
          });
        } catch (calendarError) {
          if (calendarError.message.includes('does not have calendar connected')) {
            console.log(`[Calendar Integration] User ${investorUser.id} does not have calendar connected`);
          } else {
            console.error(
              `[Calendar Integration] Error fetching calendar events for investor ${investorUser.id}:`,
              calendarError.message,
            );
          }
        }
      }

      return existingCalendarEvents;
    } catch (error) {
      console.error('[Calendar Integration] Unexpected error during calendar conflict check:', error);
      return [];
    }
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { CalendarService } from '../../../../../../shared/calendar/calendar.service';
import { SchedulingStrategiesSharedService } from './scheduling-strategies-shared.service';
import { AiService } from '../../../../../../ai/ai.service';
import { ShowingService } from '../../../../../../investor/showing/showing.service';
import { AvailabilitySlotsService } from '../../../../../../investor/availability/availability-slot.service';
import { PropertyAvailabilityService } from '../../../../../../investor/property/availability/property-availability.service';
import { TimeRange } from './interfaces/time-range';
import {
  CalendarEventResponseDto,
  CalendarEventStatus,
} from '../../../../../../shared/calendar/models/calendar-event.dto';

describe('SchedulingStrategiesSharedService - Comprehensive Tests', () => {
  let service: SchedulingStrategiesSharedService;
  let calendarService: CalendarService;
  let aiService: AiService;
  let showingService: ShowingService;
  let availabilitySlotsService: AvailabilitySlotsService;
  let propertyAvailabilityService: PropertyAvailabilityService;

  // Mock data setup
  const mockUser = {
    id: 'user-123',
    name: 'Test Renter',
    email: '<EMAIL>',
    phoneNumber: '+1234567890',
  };

  const mockRenter = {
    id: 'renter-123',
    userId: 'user-123',
    user: Promise.resolve(mockUser),
  };

  const mockInvestor = {
    id: 'investor-123',
    user: Promise.resolve({
      id: 'investor-user-123',
      name: 'Test Investor',
      email: '<EMAIL>',
      calendars: Promise.resolve([
        {
          id: 'calendar-1',
          isActive: true,
          calendarType: 'GOOGLE',
        },
      ]),
    }),
  };

  const mockLocation = {
    id: 'location-1',
    city: 'New York',
    timeZone: 'America/New_York',
    address: '123 Test St, New York, NY',
    state: 'NY',
    apartmentNumber: null,
    isAddressHidden: false,
    zip: 10001,
    latitude: 40.7128,
    longitude: -74.006,
    isDeleted: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    property: Promise.resolve({} as any),
    nearbyPlaces: Promise.resolve([]),
  } as any;

  const mockProperty = {
    id: 'property-123',
    location: Promise.resolve(mockLocation),
    owner: Promise.resolve(mockInvestor),
  };

  const mockConversation = {
    id: 'conversation-123',
    renter: mockRenter,
    property: mockProperty,
  };

  const mockDesiredTimes: TimeRange[] = [
    {
      startTime: new Date('2024-01-15T14:00:00Z'),
      endTime: new Date('2024-01-15T16:00:00Z'),
    },
    {
      startTime: new Date('2024-01-16T10:00:00Z'),
      endTime: new Date('2024-01-16T12:00:00Z'),
    },
  ];

  const mockAvailabilitySlots = [
    {
      id: 'slot-1',
      weekday: 1, // Monday
      startTime: '14:00:00+00',
      endTime: '18:00:00+00',
      investorAvailability: Promise.resolve({
        id: 'availability-1',
        showingDurationInMinutes: 60,
      }),
    },
    {
      id: 'slot-2',
      weekday: 2, // Tuesday
      startTime: '09:00:00+00',
      endTime: '17:00:00+00',
      investorAvailability: Promise.resolve({
        id: 'availability-1',
        showingDurationInMinutes: 60,
      }),
    },
  ];

  const mockPropertyAvailability = {
    id: 'availability-1',
    showingDurationInMinutes: 60,
    availabilitySlots: Promise.resolve(mockAvailabilitySlots),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SchedulingStrategiesSharedService,
        {
          provide: AiService,
          useValue: {
            getResponseWithChatMemory: jest.fn(),
          },
        },
        {
          provide: ShowingService,
          useValue: {
            findShowingsOverlappingWithTimeRange: jest.fn(),
          },
        },
        {
          provide: AvailabilitySlotsService,
          useValue: {
            findAvailabilitySlotsInRange: jest.fn(),
          },
        },
        {
          provide: PropertyAvailabilityService,
          useValue: {
            findByProperty: jest.fn(),
          },
        },
        {
          provide: CalendarService,
          useValue: {
            getCalendarEvents: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SchedulingStrategiesSharedService>(SchedulingStrategiesSharedService);
    calendarService = module.get<CalendarService>(CalendarService);
    aiService = module.get<AiService>(AiService);
    showingService = module.get<ShowingService>(ShowingService);
    availabilitySlotsService = module.get<AvailabilitySlotsService>(AvailabilitySlotsService);
    propertyAvailabilityService = module.get<PropertyAvailabilityService>(PropertyAvailabilityService);

    // Clear mocks and set up default behavior for each test
    jest.clearAllMocks();

    // Set up default mock behaviors that tests can rely on
    (aiService.getResponseWithChatMemory as jest.Mock).mockResolvedValue([
      { startTime: '2024-01-15T14:00:00Z', endTime: '2024-01-15T16:00:00Z' },
      { startTime: '2024-01-16T10:00:00Z', endTime: '2024-01-16T12:00:00Z' },
    ]);

    (showingService.findShowingsOverlappingWithTimeRange as jest.Mock).mockResolvedValue([]);
    (availabilitySlotsService.findAvailabilitySlotsInRange as jest.Mock).mockResolvedValue(mockDesiredTimes);
    (propertyAvailabilityService.findByProperty as jest.Mock).mockResolvedValue(mockPropertyAvailability);
    (calendarService.getCalendarEvents as jest.Mock).mockResolvedValue([]);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('parseRenterDesiredShowingTimes', () => {
    it('should parse valid time ranges from renter message', async () => {
      const mockParsedTimes = [
        { startTime: '2024-01-15T14:00:00Z', endTime: '2024-01-15T16:00:00Z' },
        { startTime: '2024-01-16T10:00:00Z', endTime: '2024-01-16T12:00:00Z' },
      ];

      // Override default behavior for this specific test
      (aiService.getResponseWithChatMemory as jest.Mock).mockResolvedValueOnce(mockParsedTimes);

      const result = await service.parseRenterDesiredShowingTimes(
        'I would like to schedule a showing for Monday 2-4pm or Tuesday 10am-12pm',
        mockConversation.id,
        mockLocation,
      );

      expect(result).toEqual(mockParsedTimes);
      expect(aiService.getResponseWithChatMemory).toHaveBeenCalledWith(
        expect.objectContaining({
          input: 'I would like to schedule a showing for Monday 2-4pm or Tuesday 10am-12pm',
          currentDate: expect.any(String),
        }),
        mockConversation.id,
        expect.any(String), // template string
        6,
        'CLAUDE_4_SONNET', // language model
        expect.any(Object), // parser
      );
    });

    it('should handle AI service errors gracefully', async () => {
      (aiService.getResponseWithChatMemory as jest.Mock).mockRejectedValue(new Error('AI service unavailable'));

      await expect(
        service.parseRenterDesiredShowingTimes('Invalid message', mockConversation.id, mockLocation),
      ).rejects.toThrow('AI service unavailable');
    });
  });

  describe('service methods', () => {
    it('should have getExistingCalendarEvents method', () => {
      expect(typeof service.getExistingCalendarEvents).toBe('function');
    });
  });

  describe('determineSuitableShowingTime', () => {
    it('should determine suitable times based on investor availability', async () => {
      const currentDate = new Date('2024-01-15T12:00:00Z');

      const result = await service.determineSuitableShowingTime(
        mockLocation,
        mockProperty as any,
        mockDesiredTimes,
        currentDate,
      );

      expect(result).toBeDefined();
      expect(propertyAvailabilityService.findByProperty).toHaveBeenCalledWith(mockProperty.id);
      expect(availabilitySlotsService.findAvailabilitySlotsInRange).toHaveBeenCalled();
    });

    it('should check renter calendar conflicts with real calendar integration', async () => {
      const mockCalendarEvents: CalendarEventResponseDto[] = [
        {
          id: 'event-1',
          title: 'Existing Meeting',
          startTime: '2024-01-15T14:30:00Z',
          endTime: '2024-01-15T15:30:00Z',
          status: CalendarEventStatus.CONFIRMED,
          description: 'Important meeting',
          location: 'Office',
        },
      ];

      // Override default behavior for this specific test
      (calendarService.getCalendarEvents as jest.Mock).mockResolvedValueOnce(mockCalendarEvents);

      // Spy on console.log to verify calendar data logging
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const result = await service.getExistingCalendarEvents(mockProperty as any, mockDesiredTimes, mockLocation);

      // Verify calendar service was called with correct parameters (investor user ID, not renter user ID)
      expect(calendarService.getCalendarEvents).toHaveBeenCalledWith(
        'investor-user-123', // The investor's user ID, not the renter's
        mockDesiredTimes[0].startTime,
        mockDesiredTimes[0].endTime,
      );

      // Verify calendar data was logged for verification - the actual log format includes "for time range:"
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[Calendar Integration] Found 1 calendar events for time range:'),
        expect.any(Object),
      );

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);

      consoleSpy.mockRestore();
    });

    it('should handle calendar not connected error gracefully', async () => {
      const calendarError = new Error('User does not have calendar connected');
      (calendarError as any).response = { code: 'CALENDAR_NOT_CONNECTED' };

      // Override default behavior for this specific test
      (calendarService.getCalendarEvents as jest.Mock).mockRejectedValueOnce(calendarError);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Should not throw error, but handle gracefully
      const result = await service.getExistingCalendarEvents(mockProperty as any, mockDesiredTimes, mockLocation);

      expect(result).toEqual([]);

      // Verify appropriate log message
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('does not have calendar connected'));

      consoleSpy.mockRestore();
    });
  });

  describe('generateResponseMessage', () => {
    it('should generate AI response with available times', async () => {
      const mockResponse = {
        response: 'I can schedule your showing for Monday at 2:00 PM. Does this work for you?',
        proposedTime: new Date('2024-01-15T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
      };

      // Override default behavior for this specific test
      (aiService.getResponseWithChatMemory as jest.Mock).mockResolvedValueOnce(JSON.stringify(mockResponse));

      const result = await service.generateResponseMessage(
        'Yes, Monday at 2pm works for me',
        mockDesiredTimes,
        [],
        mockLocation,
        mockConversation as any,
        mockInvestor as any,
      );

      expect(result.response).toContain('schedule your showing');
      expect(result.wasProposedTimeAgreedOn).toBe(true);
      expect(aiService.getResponseWithChatMemory).toHaveBeenCalled();
    });

    it('should handle no available times scenario', async () => {
      const mockResponse = {
        response: "Unfortunately, I don't have any available times that match your request.",
        proposedTime: null,
        wasProposedTimeAgreedOn: false,
      };

      // Override default behavior for this specific test
      (aiService.getResponseWithChatMemory as jest.Mock).mockResolvedValueOnce(JSON.stringify(mockResponse));

      const result = await service.generateResponseMessage(
        'I need a showing this week',
        [], // No available times
        [],
        mockLocation,
        mockConversation as any,
        mockInvestor as any,
      );

      expect(result.wasProposedTimeAgreedOn).toBe(false);
      expect(result.response).toContain("don't have any available times");
    });
  });

  describe('Real Calendar Integration Tests', () => {
    it('should make actual calendar API calls for conflict detection', async () => {
      // This test verifies that we're making real calendar API calls, not using mocks
      const mockEvents: CalendarEventResponseDto[] = [
        {
          id: 'real-event-1',
          title: 'Doctor Appointment',
          startTime: '2024-01-15T15:00:00Z',
          endTime: '2024-01-15T16:00:00Z',
          status: CalendarEventStatus.CONFIRMED,
          description: 'Annual checkup',
          location: 'Medical Center',
        },
      ];

      // Override default behavior for this specific test
      (calendarService.getCalendarEvents as jest.Mock).mockResolvedValue(mockEvents);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await service.getExistingCalendarEvents(mockProperty as any, mockDesiredTimes, mockLocation);

      // Verify the calendar service was called for each desired time range
      expect(calendarService.getCalendarEvents).toHaveBeenCalledTimes(2);

      // Verify detailed calendar event logging
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[Calendar Integration] Found 1 calendar events for time range:'),
        expect.any(Object),
      );

      consoleSpy.mockRestore();
    });

    it('should log calendar data for verification during development', async () => {
      const detailedEvents: CalendarEventResponseDto[] = [
        {
          id: 'detailed-event-1',
          title: 'Team Meeting',
          startTime: '2024-01-16T11:00:00Z',
          endTime: '2024-01-16T12:00:00Z',
          status: CalendarEventStatus.CONFIRMED,
          description: 'Weekly team sync',
          location: 'Conference Room A',
        },
      ];

      // Override default behavior for this specific test
      (calendarService.getCalendarEvents as jest.Mock).mockResolvedValue(detailedEvents);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await service.getExistingCalendarEvents(mockProperty as any, mockDesiredTimes, mockLocation);

      // Verify comprehensive logging for development verification
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[Calendar Integration] Found 1 calendar events for time range:'),
        expect.objectContaining({
          startTime: mockDesiredTimes[1].startTime,
          endTime: mockDesiredTimes[1].endTime,
          events: expect.arrayContaining([
            expect.objectContaining({
              id: 'detailed-event-1',
              title: 'Team Meeting',
              startTime: '2024-01-16T11:00:00Z',
              endTime: '2024-01-16T12:00:00Z',
              status: CalendarEventStatus.CONFIRMED,
            }),
          ]),
        }),
      );

      consoleSpy.mockRestore();
    });
  });
});

import { Test, TestingModule } from '@nestjs/testing';
import { RescheduleShowingStrategy } from './reschedule-showing.strategy';
import { ShowingRequestService } from '../../../../../../investor/showing-request/showing-request.service';
import { SchedulingStrategiesSharedService } from '../shared/scheduling-strategies-shared.service';
import { PropertyInquiryService } from '../../../../../../investor/property-inquiry/property-inquiry.service';
import { RescheduleService } from '../../../../../../investor/reschedule/reschedule.service';
import { AiService } from '../../../../../../ai/ai.service';
import { ShowingCalendarService } from '../../../../../../investor/showing/showing-calendar.service';
import { RentStageEnum } from '../../../../../../shared/communication/conversation/enums/rent-stage.enum';
import { RescheduleRequestStatus } from '../../../../../../investor/reschedule/reschedule-request.entity';
import { ShowingRequestStatus } from '../../../../../../investor/showing-request/enums/showing-request-status.enum';
import { TimeRange } from '../shared/interfaces/time-range';
import { ShowingService } from '../../../../../../investor/showing/showing.service';
import { FollowUpService } from '../../../../../../shared/communication/follow-up/follow-up.service';
import { SlackCommunicationService } from '../../../../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { TourType } from '../../../../../../investor/showing/enums/tour-type.enum';
import { ShowingAgentService } from '../../../../../../investor/showing-agent/showing-agent.service';

describe('RescheduleShowingStrategy - Comprehensive Tests', () => {
  let strategy: RescheduleShowingStrategy;
  let showingRequestService: ShowingRequestService;
  let schedulingStrategiesSharedService: SchedulingStrategiesSharedService;
  let propertyInquiryService: PropertyInquiryService;
  let rescheduleService: RescheduleService;
  let aiService: AiService;

  // Mock data setup
  const mockUser = {
    id: 'user-123',
    name: 'Test Renter',
    email: '<EMAIL>',
    phoneNumber: '+1234567890',
  };

  const mockRenter = {
    id: 'renter-123',
    userId: 'user-123',
    user: Promise.resolve(mockUser),
  };

  const mockInvestor = {
    id: 'investor-123',
    user: Promise.resolve({
      id: 'investor-user-123',
      name: 'Test Investor',
      email: '<EMAIL>',
    }),
  };

  const mockLocation = {
    city: 'New York',
    timeZone: 'America/New_York',
    address: '123 Test St, New York, NY',
  };

  const mockProperty = {
    id: 'property-123',
    location: Promise.resolve(mockLocation),
    owner: Promise.resolve(mockInvestor),
  };

  const mockConversation = {
    id: 'conversation-123',
    renter: mockRenter,
    property: mockProperty,
  };

  const mockShowing = {
    id: 'showing-123',
    startTime: new Date('2024-01-15T14:00:00Z'),
    endTime: new Date('2024-01-15T15:00:00Z'),
    tourType: TourType.IN_PERSON,
    property: mockProperty,
    showingRequests: Promise.resolve([
      {
        id: 'showing-request-123',
        status: ShowingRequestStatus.ACCEPTED,
        renter: mockRenter,
        property: mockProperty,
      },
    ]),
  };

  const mockShowingRequest = {
    id: 'showing-request-123',
    status: ShowingRequestStatus.ACCEPTED,
    renter: mockRenter,
    property: mockProperty,
    showing: Promise.resolve(mockShowing),
  };

  const mockPropertyInquiry = {
    id: 'inquiry-123',
    renter: mockRenter,
    property: mockProperty,
    stage: RentStageEnum.SHOWING_CONFIRMED,
  };

  const mockRescheduleShowing = {
    id: 'reschedule-showing-123',
    startTime: new Date('2024-01-17T10:00:00Z'), // Different time for reschedule
    endTime: new Date('2024-01-17T11:00:00Z'),
    tourType: TourType.IN_PERSON,
    property: mockProperty,
    showingRequests: Promise.resolve([
      {
        id: 'reschedule-showing-request-123',
        status: ShowingRequestStatus.ACCEPTED,
        renter: mockRenter,
        property: mockProperty,
      },
    ]),
  };

  const mockRescheduleRequest = {
    id: 'reschedule-123',
    status: RescheduleRequestStatus.PENDING,
    renter: mockRenter,
    showing: Promise.resolve(mockRescheduleShowing),
  };

  const mockDesiredTimes: TimeRange[] = [
    {
      startTime: new Date('2024-01-16T14:00:00Z'),
      endTime: new Date('2024-01-16T16:00:00Z'),
    },
  ];

  const mockSuitableTimes: TimeRange[] = [
    {
      startTime: new Date('2024-01-16T14:00:00Z'),
      endTime: new Date('2024-01-16T15:00:00Z'),
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RescheduleShowingStrategy,
        {
          provide: ShowingRequestService,
          useValue: {
            findByRenterAndProperty: jest.fn(),
            rescheduleShowingRequest: jest.fn(),
            sendNewRequestToPropertyOwner: jest.fn(),
            confirmRescheduleRequest: jest.fn(),
            declinedRescheduleRequest: jest.fn(),
          },
        },
        {
          provide: SchedulingStrategiesSharedService,
          useValue: {
            parseRenterDesiredShowingTimes: jest.fn(),
            determineSuitableShowingTime: jest.fn(),
            generateResponseMessage: jest.fn(),
            getSuitableTimeFromOwnerInitiatedRescheduleRequest: jest.fn(),
            getExistingCalendarEvents: jest.fn(),
          },
        },
        {
          provide: PropertyInquiryService,
          useValue: {
            updateStageAndAddCorrespondingEvent: jest.fn(),
            updateRentStageAndAddEvent: jest.fn(),
          },
        },
        {
          provide: RescheduleService,
          useValue: {
            findOneBy: jest.fn(),
            acceptRescheduleRequest: jest.fn(),
          },
        },
        {
          provide: AiService,
          useValue: {
            getResponseWithChatMemory: jest.fn(),
          },
        },
        {
          provide: ShowingService,
          useValue: {
            rescheduleShowing: jest.fn(),
            updateStatusBasedOnRequests: jest.fn(),
            findById: jest.fn(),
          },
        },
        {
          provide: FollowUpService,
          useValue: {
            deleteRenterFollowUps: jest.fn(),
            deleteRenterFollowUpsByStatus: jest.fn(),
          },
        },
        {
          provide: SlackCommunicationService,
          useValue: {
            sendMessageToConvosChannel: jest.fn(),
          },
        },
        {
          provide: ShowingAgentService,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: ShowingCalendarService,
          useValue: {
            updateEvent: jest.fn(),
            deleteEvent: jest.fn(),
          },
        },
      ],
    }).compile();

    strategy = module.get<RescheduleShowingStrategy>(RescheduleShowingStrategy);
    showingRequestService = module.get<ShowingRequestService>(ShowingRequestService);
    schedulingStrategiesSharedService = module.get<SchedulingStrategiesSharedService>(
      SchedulingStrategiesSharedService,
    );
    propertyInquiryService = module.get<PropertyInquiryService>(PropertyInquiryService);
    rescheduleService = module.get<RescheduleService>(RescheduleService);
    aiService = module.get<AiService>(AiService);

    // Clear mocks and set up default behavior for each test
    jest.clearAllMocks();

    // Set up default mock behaviors that tests can rely on
    (showingRequestService.findByRenterAndProperty as jest.Mock).mockResolvedValue(mockShowingRequest);
    (schedulingStrategiesSharedService.parseRenterDesiredShowingTimes as jest.Mock).mockResolvedValue(mockDesiredTimes);
    (schedulingStrategiesSharedService.determineSuitableShowingTime as jest.Mock).mockResolvedValue(mockSuitableTimes);
    (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValue({
      response: 'Default response',
      proposedTime: new Date('2024-01-16T14:00:00Z'),
      wasProposedTimeAgreedOn: false,
      tourType: TourType.IN_PERSON,
    });
    (schedulingStrategiesSharedService.getExistingCalendarEvents as jest.Mock).mockResolvedValue([]);
    (rescheduleService.findOneBy as jest.Mock).mockResolvedValue(mockRescheduleRequest);
    (aiService.getResponseWithChatMemory as jest.Mock).mockResolvedValue(
      JSON.stringify({ isOwnerProposedTimeChosen: false }),
    );
    (showingRequestService.sendNewRequestToPropertyOwner as jest.Mock).mockResolvedValue({
      newShowingId: 'new-showing-id',
    });
    (showingRequestService.confirmRescheduleRequest as jest.Mock).mockResolvedValue(undefined);
    (propertyInquiryService.updateStageAndAddCorrespondingEvent as jest.Mock).mockResolvedValue(undefined);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('execute - Renter-Initiated Reschedule', () => {
    it('should handle renter-initiated reschedule workflow', async () => {
      const mockInquiry = {
        ...mockPropertyInquiry,
        stage: RentStageEnum.SHOWING_CONFIRMED, // Not owner-initiated reschedule
      };

      const mockResponse = {
        response: 'I can reschedule your showing to Tuesday at 2:00 PM.',
        proposedTime: new Date('2024-01-16T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      const result = await strategy.execute(
        'Can we reschedule to Tuesday at 2pm?',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify the workflow
      expect(showingRequestService.findByRenterAndProperty).toHaveBeenCalledWith(mockRenter.id, mockProperty.id);

      expect(schedulingStrategiesSharedService.parseRenterDesiredShowingTimes).toHaveBeenCalledWith(
        'Can we reschedule to Tuesday at 2pm?',
        mockConversation.id,
        mockLocation,
      );

      expect(schedulingStrategiesSharedService.determineSuitableShowingTime).toHaveBeenCalledWith(
        mockLocation,
        mockProperty,
        mockDesiredTimes,
        expect.any(Date),
      );

      expect(schedulingStrategiesSharedService.generateResponseMessage).toHaveBeenCalledWith(
        'Can we reschedule to Tuesday at 2pm?',
        mockSuitableTimes,
        expect.any(Array), // existingCalendarEvents from calendar integration
        mockLocation,
        mockConversation,
        mockProperty,
        TourType.IN_PERSON, // initiallySelectedTourType from current showing
      );

      expect(result).toBe('Showing time: I can reschedule your showing to Tuesday at 2:00 PM.');
    });

    it('should handle reschedule when time is agreed upon', async () => {
      const mockInquiry = {
        ...mockPropertyInquiry,
        stage: RentStageEnum.SHOWING_CONFIRMED,
      };

      const mockResponse = {
        response: 'Perfect! Your showing has been rescheduled.',
        proposedTime: new Date('2024-01-16T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      await strategy.execute(
        'Yes, Tuesday at 2pm works',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      expect(showingRequestService.sendNewRequestToPropertyOwner).toHaveBeenCalledWith(
        mockProperty,
        mockRenter,
        mockShowingRequest,
        expect.any(Date), // UTC converted time
        mockInquiry,
        TourType.IN_PERSON, // tourType from response
      );
    });

    it('should not reschedule when time is not agreed upon', async () => {
      const mockInquiry = {
        ...mockPropertyInquiry,
        stage: RentStageEnum.SHOWING_CONFIRMED,
      };

      const mockResponse = {
        response: 'Let me check other available times for you.',
        proposedTime: null,
        wasProposedTimeAgreedOn: false,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      await strategy.execute(
        'I need to reschedule but not sure when',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      expect(showingRequestService.sendNewRequestToPropertyOwner).not.toHaveBeenCalled();
    });
  });

  describe('execute - Owner-Initiated Reschedule', () => {
    it('should handle owner-initiated reschedule workflow', async () => {
      const mockInquiry = {
        ...mockPropertyInquiry,
        stage: RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER,
      };

      const mockOwnerSuitableTime = {
        startTime: new Date('2024-01-17T10:00:00Z'),
        endTime: new Date('2024-01-17T11:00:00Z'),
      };

      (
        schedulingStrategiesSharedService.getSuitableTimeFromOwnerInitiatedRescheduleRequest as jest.Mock
      ).mockReturnValue(mockOwnerSuitableTime);

      const mockResponse = {
        response: "I can offer you the owner's suggested time or Tuesday at 2pm.",
        proposedTime: new Date('2024-01-16T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      await strategy.execute(
        'Yes, Tuesday works for me',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify owner's reschedule request was found
      expect(rescheduleService.findOneBy).toHaveBeenCalledWith(
        mockRenter.id,
        mockProperty.id,
        RescheduleRequestStatus.PENDING,
      );

      // Verify owner's suggested time was added to suitable times
      expect(schedulingStrategiesSharedService.getSuitableTimeFromOwnerInitiatedRescheduleRequest).toHaveBeenCalledWith(
        mockRescheduleShowing,
        mockLocation,
      );

      // Verify suitable times included both renter preferences and owner suggestion
      const generateResponseCall = (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mock
        .calls[0];
      const suitableTimesPassedToAI = generateResponseCall[1];
      expect(suitableTimesPassedToAI).toContain(mockOwnerSuitableTime);
    });

    it('should accept owner reschedule request when owner time is chosen', async () => {
      const mockInquiry = {
        ...mockPropertyInquiry,
        stage: RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER,
      };

      // Mock the owner's suggested time to match the reschedule showing start time
      const ownerProposedTime = new Date('2024-01-17T10:00:00Z'); // Same as mockRescheduleShowing.startTime
      const mockResponse = {
        response: "Great! I've scheduled you for the owner's suggested time.",
        proposedTime: ownerProposedTime,
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      const result = await strategy.execute(
        "Yes, the owner's suggested time works",
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // The strategy should return the response from generateResponseMessage
      expect(result).toBe("Showing time: Great! I've scheduled you for the owner's suggested time.");

      // Verify that the owner's reschedule request was found
      expect(rescheduleService.findOneBy).toHaveBeenCalledWith(
        mockRenter.id,
        mockProperty.id,
        RescheduleRequestStatus.PENDING,
      );
    });

    it('should create new reschedule request when renter chooses different time', async () => {
      const mockInquiry = {
        ...mockPropertyInquiry,
        stage: RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER,
      };

      const renterProposedTime = new Date('2024-01-16T14:00:00Z');
      const mockResponse = {
        response: 'I can reschedule you for Tuesday at 2pm instead.',
        proposedTime: renterProposedTime,
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      const result = await strategy.execute(
        'Actually, can we do Tuesday at 2pm instead?',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // The strategy should return the response
      expect(result).toBe('Showing time: I can reschedule you for Tuesday at 2pm instead.');

      // Verify that the owner's reschedule request was found
      expect(rescheduleService.findOneBy).toHaveBeenCalledWith(
        mockRenter.id,
        mockProperty.id,
        RescheduleRequestStatus.PENDING,
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle missing showing request gracefully', async () => {
      // Override default behavior for this specific test
      (showingRequestService.findByRenterAndProperty as jest.Mock).mockResolvedValueOnce(null);

      // The strategy should handle null showing request by throwing an error
      await expect(
        strategy.execute(
          'Reschedule please',
          mockPropertyInquiry as any,
          mockRenter as any,
          mockProperty as any,
          mockConversation as any,
        ),
      ).rejects.toThrow();
    });

    it('should handle AI parsing errors', async () => {
      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.parseRenterDesiredShowingTimes as jest.Mock).mockRejectedValueOnce(
        new Error('AI parsing failed'),
      );

      await expect(
        strategy.execute(
          'Invalid message',
          mockPropertyInquiry as any,
          mockRenter as any,
          mockProperty as any,
          mockConversation as any,
        ),
      ).rejects.toThrow('AI parsing failed');
    });

    it('should handle missing reschedule request for owner-initiated reschedule', async () => {
      const mockInquiry = {
        ...mockPropertyInquiry,
        stage: RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER,
      };

      // Override default behavior for this specific test
      (rescheduleService.findOneBy as jest.Mock).mockResolvedValueOnce(null);

      // The strategy will throw an error when trying to access null.showing
      await expect(
        strategy.execute(
          'Reschedule please',
          mockInquiry as any,
          mockRenter as any,
          mockProperty as any,
          mockConversation as any,
        ),
      ).rejects.toThrow("Cannot read properties of null (reading 'showing')");

      expect(
        schedulingStrategiesSharedService.getSuitableTimeFromOwnerInitiatedRescheduleRequest,
      ).not.toHaveBeenCalled();
    });
  });

  describe('Timezone Handling', () => {
    it('should handle timezone conversion for reschedule times', async () => {
      const mockInquiry = {
        ...mockPropertyInquiry,
        stage: RentStageEnum.SHOWING_CONFIRMED,
      };

      const mockResponse = {
        response: 'Rescheduled successfully!',
        proposedTime: new Date('2024-01-16T19:00:00'), // Local time without timezone
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      await strategy.execute(
        'Yes, 7pm works',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify timezone conversion was applied
      expect(showingRequestService.sendNewRequestToPropertyOwner).toHaveBeenCalledWith(
        mockProperty,
        mockRenter,
        mockShowingRequest,
        expect.any(Date), // Should be UTC converted
        mockInquiry,
        TourType.IN_PERSON, // tourType from response
      );
    });
  });
});

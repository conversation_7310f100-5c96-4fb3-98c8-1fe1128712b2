import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../../ai/prompts/ai-instructions';
import { PromptVariable } from '../../../../../../../ai/enums/prompt-variable.enum';

export const thankRenterForAgreeingToRescheduleTemplate = `
<task>
Imagine you are a property manager, and you are talking to a potential renter.
The property owner has rescheduled the property showing time and renter agreed to come at that time.
You need to create a message for the renter to thank him/her and say that everything is ready.

Share all the necessary details like address, time, date, and info of a person who will do the showing.
You will be given today date and the showing date, feel free to use words like "today", "tomorrow", etc.
Please use full month names and days of the week.
Please share the information of the showing agent if provided.
</task>

<instructions>
${generalAiInstructionsTemplate}
${AiInstruction.PlainTextOutput}
${AiInstruction.DontSayWe}
- Be concise, using up to 4 sentences
- Do not mention the property owner
- Do not say that you will be there to meet the renter, use general phrase like "Looking forward to it" when needed
- Ensure that relative terms like "today" and "tomorrow" are used accurately based on the current date and the showing date
- Share the name and phone of a person who will be doing the showing (showing agent)
- If showing agent info is empty don't mention it
</instructions>

<examples>
<example>
Try to be as close as possible to the following example:
- Great news! We're confirmed for Tuesday, June 17th at 6:00 PM at 1112 Flowers Street. The showing will be held by Andrew, you can reach him at ************.
I'll send you a text before the showing to confirm that the showing time still works for you. Looking forward to it.
</example>
<example>
Try to be as close as possible to the following example:
- Great news! We're confirmed for Tuesday, June 17th at 6:00 PM at 1112 Flowers Street.
I'll send you a text before the showing to confirm that the showing time still works for you. Looking forward to it.
</example>
</examples>

<showing_agent_info>{showingAgent}</showing_agent_info>

${PromptVariable.PropertyAddress}
${PromptVariable.ShowingTime}
${PromptVariable.CurrentDate}

${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

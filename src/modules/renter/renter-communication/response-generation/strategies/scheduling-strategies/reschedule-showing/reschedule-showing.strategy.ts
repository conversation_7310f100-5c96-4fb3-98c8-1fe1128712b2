import { Injectable } from '@nestjs/common';
import { TimezoneUtils } from '../../../../../../../utils/timezone.utils';
import { AiService } from '../../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../../ai/enums/language-models.enum';
import { PropertyInquiryEventTypeEnum } from '../../../../../../investor/property-inquiry/events/property-inquiry-event-type.enum';
import { PropertyInquiry } from '../../../../../../investor/property-inquiry/property-inquiry.entity';
import { PropertyInquiryService } from '../../../../../../investor/property-inquiry/property-inquiry.service';
import { Property } from '../../../../../../investor/property/property/entities/property.entity';
import { PropertyLocation } from '../../../../../../investor/property/property/property-details/location/property-location.entity';
import { RescheduleRequestStatus } from '../../../../../../investor/reschedule/reschedule-request.entity';
import { RescheduleService } from '../../../../../../investor/reschedule/reschedule.service';
import { ShowingRequest } from '../../../../../../investor/showing-request/showing-request.entity';
import { ShowingRequestService } from '../../../../../../investor/showing-request/showing-request.service';
import { ShowingService } from '../../../../../../investor/showing/showing.service';
import { Conversation } from '../../../../../../shared/communication/conversation/entities/conversation.entity';
import { RentStageEnum } from '../../../../../../shared/communication/conversation/enums/rent-stage.enum';
import { SlackCommunicationService } from '../../../../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { Renter } from '../../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../../interfaces/handle-renter-message.strategy';
import { AiStrategyExecutionCode } from '../../../enums/ai-strategy-execution-code';
import { SchedulingStrategiesSharedService } from '../shared/scheduling-strategies-shared.service';
import { thankRenterForAgreeingToRescheduleTemplate } from './prompts/thank-renter-for-agreeing-to-reschedule.template';
import { ShowingRequestStatus } from '../../../../../../investor/showing-request/enums/showing-request-status.enum';
import { FollowUpService } from '../../../../../../shared/communication/follow-up/follow-up.service';
import { ShowingAgentService } from '../../../../../../investor/showing-agent/showing-agent.service';
import { ShowingCalendarService } from '../../../../../../investor/showing/showing-calendar.service';
import { TourType } from '../../../../../../investor/showing/enums/tour-type.enum';

@Injectable()
export class RescheduleShowingStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly showingRequestService: ShowingRequestService,
    private readonly showingService: ShowingService,
    private readonly followUpService: FollowUpService,
    private readonly rescheduleService: RescheduleService,
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly schedulingStrategiesSharedService: SchedulingStrategiesSharedService,
    private readonly slackService: SlackCommunicationService,
    private readonly showingAgentService: ShowingAgentService,
    private readonly showingCalendarService: ShowingCalendarService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const location = await property.location;
    const currentDate = TimezoneUtils.getCurrentCityDateInAiReadableFormat(location.city, location.timeZone);
    const locationDate = TimezoneUtils.getCurrentCityDate(location.city, location.timeZone);
    const showingRequest = await this.showingRequestService.findByRenterAndProperty(renter.id, property.id);
    const isShowingRequestRescheduledByOwner = inquiry.stage === RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER;

    const renterDesiredShowingTimes = await this.schedulingStrategiesSharedService.parseRenterDesiredShowingTimes(
      renterMessage,
      conversation.id,
      location,
    );

    const suitableTimes = await this.schedulingStrategiesSharedService.determineSuitableShowingTime(
      location,
      property,
      renterDesiredShowingTimes,
      locationDate,
    );

    if (isShowingRequestRescheduledByOwner) {
      const pendingRescheduleRequest = await this.rescheduleService.findOneBy(
        renter.id,
        property.id,
        RescheduleRequestStatus.PENDING,
      );

      suitableTimes.push(
        this.schedulingStrategiesSharedService.getSuitableTimeFromOwnerInitiatedRescheduleRequest(
          await pendingRescheduleRequest.showing,
          location,
        ),
      );
    }

    const existingCalendarEvents = await this.schedulingStrategiesSharedService.getExistingCalendarEvents(
      property,
      renterDesiredShowingTimes,
      location,
    );

    // Get the current showing's tour type to maintain consistency during rescheduling
    const currentShowing = await showingRequest.showing;
    const previousTourType = currentShowing.tourType;

    const response = await this.schedulingStrategiesSharedService.generateResponseMessage(
      renterMessage,
      suitableTimes,
      existingCalendarEvents,
      location,
      conversation,
      property,
      previousTourType,
    );

    if (response.wasProposedTimeAgreedOn) {
      return this.handleProposedTimeAgreement(
        response.response,
        response.proposedTime.toString(),
        response.tourType,
        isShowingRequestRescheduledByOwner,
        renter,
        property,
        showingRequest,
        conversation,
        location,
        currentDate,
        renterMessage,
        inquiry,
      );
    }

    return `Showing time: ${response.response}`;
  }

  private async handleProposedTimeAgreement(
    aiTimeNegotiationResponse: string,
    proposedTime: string,
    tourType: TourType,
    isShowingRequestRescheduledByOwner: boolean,
    renter: Renter,
    property: Property,
    showingRequest: ShowingRequest,
    conversation: Conversation,
    location: PropertyLocation,
    currentDate: string,
    renterMessage: string,
    inquiry: PropertyInquiry,
  ): Promise<string> {
    const proposedStartTimeUtc = TimezoneUtils.convertDateWithMissingTzToUtcDate(
      new Date(proposedTime),
      location.city,
      location.timeZone,
    );

    if (isShowingRequestRescheduledByOwner) {
      return this.handleOwnerRescheduledRequest(
        aiTimeNegotiationResponse,
        proposedStartTimeUtc,
        tourType,
        renter,
        property,
        showingRequest,
        conversation,
        location,
        currentDate,
        renterMessage,
        inquiry,
      );
    } else {
      return this.handleRenterRescheduleRequest(
        aiTimeNegotiationResponse,
        proposedStartTimeUtc,
        tourType,
        property,
        renter,
        showingRequest,
        inquiry,
        conversation,
      );
    }
  }

  private async handleOwnerRescheduledRequest(
    aiTimeNegotiationResponse: string,
    proposedStartTimeUtc: Date,
    tourType: TourType,
    renter: Renter,
    property: Property,
    showingRequest: ShowingRequest,
    conversation: Conversation,
    location: PropertyLocation,
    currentDate: string,
    renterMessage: string,
    inquiry: PropertyInquiry,
  ): Promise<string> {
    const pendingRescheduleRequest = await this.rescheduleService.findOneBy(
      renter.id,
      property.id,
      RescheduleRequestStatus.PENDING,
    );

    const proposedTimeCityTz = TimezoneUtils.convertDateToStringInCityTz(
      proposedStartTimeUtc,
      location.city,
      location.timeZone,
    );

    const showingRelatedToRescheduleRequest = await pendingRescheduleRequest.showing;

    try {
      console.log(
        '[Owner reschedules data] [ai strategy]',
        JSON.stringify({
          relatedShowingId: showingRelatedToRescheduleRequest?.id || 'nothing',
          relatedShowingStartTime: showingRelatedToRescheduleRequest?.startTime?.toISOString() || 'nothing',
          relatedShowingGetTime: showingRelatedToRescheduleRequest?.startTime?.getTime() || 'nothing',
          proposedStartTime: proposedStartTimeUtc?.toISOString() || 'nothing',
          proposedStartTimeGetTime: proposedStartTimeUtc?.getTime() || 'nothing',
        }),
      );
    } catch (error) {
      console.log('Error logging reschedule data', error);
    }

    if (showingRelatedToRescheduleRequest.startTime.getTime() === proposedStartTimeUtc.getTime()) {
      this.followUpService.deleteRenterFollowUpsByStatus(conversation.id);
      await this.showingRequestService.confirmRescheduleRequest(
        property,
        renter,
        showingRequest,
        proposedStartTimeUtc,
        pendingRescheduleRequest,
        inquiry,
      );

      await this.propertyInquiryService.updateRentStageAndAddEvent(
        inquiry.id,
        RentStageEnum.SHOWING_CONFIRMED,
        PropertyInquiryEventTypeEnum.RENTER_AGREED_TO_RESCHEDULE,
      );

      this.slackService.sendMessageToConvosChannel('🎉 Renter accepted the reschedule request.', conversation);

      return this.generateThankfulMessageForTheRenter(
        proposedTimeCityTz,
        renterMessage,
        conversation,
        location,
        currentDate,
        showingRelatedToRescheduleRequest.id,
      );
    } else {
      this.followUpService.deleteRenterFollowUpsByStatus(conversation.id);
      await this.propertyInquiryService.updateRentStageAndAddEvent(
        inquiry.id,
        RentStageEnum.SHOWING_REQUESTED,
        PropertyInquiryEventTypeEnum.RENTER_AGREED_TO_SHOWING_TIME_DIFFERENT_FROM_RESCHEDULE_REQUEST,
      );

      await this.showingRequestService.declinedRescheduleRequest(
        property,
        renter,
        showingRequest,
        proposedStartTimeUtc,
        pendingRescheduleRequest,
        inquiry,
        tourType,
      );

      this.slackService.sendMessageToConvosChannel(
        "🫠 Renter declined the owner's proposed time and suggested an alternative (new showing request is created)",
        conversation,
      );
    }

    return `Showing time: ${aiTimeNegotiationResponse}`;
  }

  private async handleRenterRescheduleRequest(
    aiTimeNegotiationResponse: string,
    proposedStartTimeUtc: Date,
    tourType: TourType,
    property: Property,
    renter: Renter,
    showingRequest: ShowingRequest,
    inquiry: PropertyInquiry,
    conversation: Conversation,
  ): Promise<string> {
    const showing = await showingRequest.showing;
    const isShowingAlreadyScheduledAtProposedTime = proposedStartTimeUtc.getTime() === showing.startTime.getTime();
    const showingRequestIsActive = [ShowingRequestStatus.PENDING, ShowingRequestStatus.ACCEPTED].includes(
      showingRequest.status,
    );

    const showingActiveShowingRequests = (await showing.showingRequests).filter(
      (showingRequest) => showingRequest.status === ShowingRequestStatus.ACCEPTED,
    );

    // if the showing only has one renter that is about to be rescheduled, cancel meeting. If there is someone else, keep it
    if (showingActiveShowingRequests.length === 1) {
      await this.showingCalendarService.deleteEvent(showing);
    } else {
      await this.showingCalendarService.updateEvent(showing);
    }

    if (isShowingAlreadyScheduledAtProposedTime && showingRequestIsActive) {
      return AiStrategyExecutionCode.DO_NOTHING;
    }

    await this.showingRequestService.sendNewRequestToPropertyOwner(
      property,
      renter,
      showingRequest,
      proposedStartTimeUtc,
      inquiry,
      tourType,
    );

    await this.showingService.updateStatusBasedOnRequests(showing);

    await this.propertyInquiryService.updateStageAndAddCorrespondingEvent(
      inquiry.id,
      RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_RENTER,
    );

    this.slackService.sendMessageToConvosChannel('📅 Renter reschedules the showing', conversation);

    return `Showing time: ${aiTimeNegotiationResponse}`;
  }

  private async generateThankfulMessageForTheRenter(
    showingTime: string,
    renterMessage: string,
    conversation: Conversation,
    location: PropertyLocation,
    currentDate: string,
    showingId: string,
  ): Promise<string> {
    // Get the showing using the inquiry and showingId
    const showing = await this.showingService.findById(showingId);

    // Get showing agent details if available
    const showingAgent = showing.showingAgentId ? await this.showingAgentService.findOne(showing.showingAgentId) : null;

    return <any>await this.aiService.getResponseWithChatMemory(
      {
        showingTime,
        input: renterMessage,
        propertyAddress: location.address,
        currentDate,
        showingAgent: showingAgent
          ? {
              name: showingAgent.firstName,
              phone: showingAgent.phone,
            }
          : null,
      },
      conversation.id,
      thankRenterForAgreeingToRescheduleTemplate,
      3,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );
  }
}

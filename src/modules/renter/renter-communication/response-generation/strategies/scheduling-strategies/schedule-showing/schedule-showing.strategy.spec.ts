jest.mock('../../../../../../investor/showing-request/showing-request.service', () => ({
  ShowingRequestService: jest.fn().mockImplementation(),
}));

import { Test, TestingModule } from '@nestjs/testing';
import { PropertyInquiryService } from '../../../../../../investor/property-inquiry/property-inquiry.service';
import { ShowingRequestStatus } from '../../../../../../investor/showing-request/enums/showing-request-status.enum';
import { ShowingRequestService } from '../../../../../../investor/showing-request/showing-request.service';
import { TourType } from '../../../../../../investor/showing/enums/tour-type.enum';
import { RentStageEnum } from '../../../../../../shared/communication/conversation/enums/rent-stage.enum';
import { FollowUpService } from '../../../../../../shared/communication/follow-up/follow-up.service';
import { SlackCommunicationService } from '../../../../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { AiObjectiveType } from '../../../../ai-objective/ai-objective-type.enum';
import { AiObjectiveService } from '../../../../ai-objective/ai-objective.service';
import { TimeRange } from '../shared/interfaces/time-range';
import { SchedulingStrategiesSharedService } from '../shared/scheduling-strategies-shared.service';
import { buildShowingRequestSentSlackMessage, ScheduleShowingStrategy } from './schedule-showing.strategy';

describe('ScheduleShowingStrategy - Comprehensive Tests', () => {
  let strategy: ScheduleShowingStrategy;
  let showingRequestService: ShowingRequestService;
  let followUpService: FollowUpService;
  let schedulingStrategiesSharedService: SchedulingStrategiesSharedService;
  let propertyInquiryService: PropertyInquiryService;
  let slackService: SlackCommunicationService;
  let aiObjectiveService: AiObjectiveService;

  // Mock data setup
  const mockUser = {
    id: 'user-123',
    name: 'Test Renter',
    email: '<EMAIL>',
    phoneNumber: '+1234567890',
  };

  const mockRenterScreening = {
    creditScore: 750,
    monthlyIncome: 5000,
    desiredMoveInDate: '2024-02-01',
    convoHighlights: ['Great communication', 'Stable employment'],
  };

  const mockRenter = {
    id: 'renter-123',
    userId: 'user-123',
    user: mockUser, // Direct reference, not a Promise
    renterScreening: Promise.resolve(mockRenterScreening),
  };

  const mockInvestor = {
    id: 'investor-123',
    user: Promise.resolve({
      id: 'investor-user-123',
      name: 'Test Investor',
      email: '<EMAIL>',
    }),
  };

  const mockLocation = {
    city: 'New York',
    timeZone: 'America/New_York',
    address: '123 Test St, New York, NY',
  };

  const mockProperty = {
    id: 'property-123',
    location: Promise.resolve(mockLocation),
    owner: Promise.resolve(mockInvestor),
  };

  const mockConversation = {
    id: 'conversation-123',
    renter: mockRenter,
    property: mockProperty,
  };

  const mockAiObjective = {
    id: 'objective-123',
    type: AiObjectiveType.SCHEDULE_SHOWING,
    completed: false,
  };

  const mockPropertyInquiry = {
    id: 'inquiry-123',
    renter: mockRenter,
    property: mockProperty,
    stage: RentStageEnum.INITIAL_CONTACT,
    aiObjectives: Promise.resolve([mockAiObjective]),
  };

  const mockShowingRequest = {
    id: 'showing-request-123',
    status: ShowingRequestStatus.PENDING,
    renter: mockRenter,
    property: mockProperty,
  };

  const mockDesiredTimes: TimeRange[] = [
    {
      startTime: new Date('2024-01-15T14:00:00Z'),
      endTime: new Date('2024-01-15T16:00:00Z'),
    },
  ];

  const mockSuitableTimes: TimeRange[] = [
    {
      startTime: new Date('2024-01-15T14:00:00Z'),
      endTime: new Date('2024-01-15T15:00:00Z'),
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduleShowingStrategy,
        {
          provide: ShowingRequestService,
          useValue: {
            create: jest.fn(),
            sendNewRequestToPropertyOwner: jest.fn(),
          },
        },
        {
          provide: FollowUpService,
          useValue: {
            deleteRenterFollowUps: jest.fn(),
            deleteRenterFollowUpsByStatus: jest.fn(),
            deleteRenterFollowUpsByStatusAndType: jest.fn(),
          },
        },
        {
          provide: SchedulingStrategiesSharedService,
          useValue: {
            parseRenterDesiredShowingTimes: jest.fn(),
            determineSuitableShowingTime: jest.fn(),
            generateResponseMessage: jest.fn(),
            getExistingCalendarEvents: jest.fn(),
          },
        },
        {
          provide: PropertyInquiryService,
          useValue: {
            findByRenterAndProperty: jest.fn(),
            updateStageAndAddCorrespondingEvent: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: SlackCommunicationService,
          useValue: {
            sendMessageToConvosChannel: jest.fn(),
          },
        },
        {
          provide: AiObjectiveService,
          useValue: {
            updateObjectiveAndSyncItInItsInquiry: jest.fn(),
          },
        },
      ],
    }).compile();

    strategy = module.get<ScheduleShowingStrategy>(ScheduleShowingStrategy);
    showingRequestService = module.get<ShowingRequestService>(ShowingRequestService);
    followUpService = module.get<FollowUpService>(FollowUpService);
    schedulingStrategiesSharedService = module.get<SchedulingStrategiesSharedService>(
      SchedulingStrategiesSharedService,
    );
    propertyInquiryService = module.get<PropertyInquiryService>(PropertyInquiryService);
    slackService = module.get<SlackCommunicationService>(SlackCommunicationService);
    aiObjectiveService = module.get<AiObjectiveService>(AiObjectiveService);

    // Clear mocks and set up default behavior for each test
    jest.clearAllMocks();

    // Set up default mock behaviors that tests can rely on
    (showingRequestService.create as jest.Mock).mockResolvedValue(mockShowingRequest);
    (showingRequestService.sendNewRequestToPropertyOwner as jest.Mock).mockResolvedValue(undefined);
    (followUpService.deleteRenterFollowUpsByStatusAndType as jest.Mock).mockResolvedValue(undefined);
    (schedulingStrategiesSharedService.parseRenterDesiredShowingTimes as jest.Mock).mockResolvedValue(mockDesiredTimes);
    (schedulingStrategiesSharedService.determineSuitableShowingTime as jest.Mock).mockResolvedValue(mockSuitableTimes);
    (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValue({
      response: 'Great! I can schedule your showing for Monday at 2:00 PM.',
      proposedTime: new Date('2024-01-15T14:00:00Z'),
      wasProposedTimeAgreedOn: true,
      tourType: TourType.IN_PERSON,
    });
    (schedulingStrategiesSharedService.getExistingCalendarEvents as jest.Mock).mockResolvedValue([]);
    (propertyInquiryService.findByRenterAndProperty as jest.Mock).mockResolvedValue(mockPropertyInquiry);
    (propertyInquiryService.updateStageAndAddCorrespondingEvent as jest.Mock).mockResolvedValue(undefined);
    (propertyInquiryService.update as jest.Mock).mockResolvedValue(undefined);
    (slackService.sendMessageToConvosChannel as jest.Mock).mockResolvedValue(undefined);
    (aiObjectiveService.updateObjectiveAndSyncItInItsInquiry as jest.Mock).mockResolvedValue(undefined);
  });

  describe('execute - Complete Scheduling Workflow', () => {
    it('should complete full scheduling workflow when time is agreed upon', async () => {
      const mockResponse = {
        response: 'Great! I can schedule your showing for Monday at 2:00 PM.',
        proposedTime: new Date('2024-01-15T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      // Mock static method
      jest
        .spyOn(
          // eslint-disable-next-line @typescript-eslint/no-require-imports
          require('../../../../../../investor/property-inquiry/property-inquiry.entity').PropertyInquiry,
          'getPendingObjectiveByType',
        )
        .mockResolvedValue(mockAiObjective);

      const result = await strategy.execute(
        'Yes, Monday at 2pm works perfectly for me!',
        mockPropertyInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify the complete workflow
      expect(schedulingStrategiesSharedService.parseRenterDesiredShowingTimes).toHaveBeenCalledWith(
        'Yes, Monday at 2pm works perfectly for me!',
        mockConversation.id,
        mockLocation,
      );

      expect(schedulingStrategiesSharedService.determineSuitableShowingTime).toHaveBeenCalledWith(
        mockLocation,
        mockProperty,
        mockDesiredTimes,
        expect.any(Date),
      );

      expect(showingRequestService.create).toHaveBeenCalledWith({
        status: ShowingRequestStatus.PENDING,
        renter: mockRenter,
        property: mockProperty,
      });

      expect(propertyInquiryService.updateStageAndAddCorrespondingEvent).toHaveBeenCalledWith(
        mockPropertyInquiry.id,
        RentStageEnum.SHOWING_REQUESTED,
      );

      expect(showingRequestService.sendNewRequestToPropertyOwner).toHaveBeenCalledWith(
        mockProperty,
        mockRenter,
        mockShowingRequest,
        expect.any(Date), // UTC converted time
        mockPropertyInquiry,
        TourType.IN_PERSON, // tourType from response
      );

      expect(propertyInquiryService.update).toHaveBeenCalledWith(mockPropertyInquiry.id, {
        showingRequest: mockShowingRequest,
      });

      expect(followUpService.deleteRenterFollowUpsByStatusAndType).toHaveBeenCalledWith(
        mockConversation.id,
        expect.anything(),
      );

      expect(aiObjectiveService.updateObjectiveAndSyncItInItsInquiry).toHaveBeenCalledWith(
        mockRenter,
        mockAiObjective,
        { completed: true },
      );

      expect(result).toBe('Schedule showing: Great! I can schedule your showing for Monday at 2:00 PM.');
    });

    it('should not create showing request when time is not agreed upon', async () => {
      const mockResponse = {
        response: "I understand you're looking for a showing. Let me check available times.",
        proposedTime: null,
        wasProposedTimeAgreedOn: false,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      const result = await strategy.execute(
        "I need to see the property but I'm not sure about timing",
        mockPropertyInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify no showing request was created
      expect(showingRequestService.create).not.toHaveBeenCalled();
      expect(propertyInquiryService.updateStageAndAddCorrespondingEvent).not.toHaveBeenCalled();
      expect(showingRequestService.sendNewRequestToPropertyOwner).not.toHaveBeenCalled();
      expect(followUpService.deleteRenterFollowUpsByStatus).not.toHaveBeenCalled();

      expect(result).toBe("Schedule showing: I understand you're looking for a showing. Let me check available times.");
    });

    it('should handle timezone conversion correctly', async () => {
      const mockResponse = {
        response: 'Perfect! Your showing is scheduled.',
        proposedTime: new Date('2024-01-15T19:00:00'), // 7 PM local time (no timezone)
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      await strategy.execute(
        'Yes, 7pm works',
        mockPropertyInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify timezone conversion was applied
      expect(showingRequestService.sendNewRequestToPropertyOwner).toHaveBeenCalledWith(
        mockProperty,
        mockRenter,
        mockShowingRequest,
        expect.any(Date), // Should be UTC converted
        mockPropertyInquiry,
        TourType.IN_PERSON, // tourType from response
      );

      // Get the actual UTC time passed to the service
      const callArgs = (showingRequestService.sendNewRequestToPropertyOwner as jest.Mock).mock.calls[0];
      const utcTime = callArgs[3];

      // Verify it's a valid Date object
      expect(utcTime).toBeInstanceOf(Date);
    });

    it('should handle AI objective completion when objective exists', async () => {
      const mockResponse = {
        response: 'Showing scheduled successfully!',
        proposedTime: new Date('2024-01-15T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      // Mock static method to return an objective
      jest
        .spyOn(
          // eslint-disable-next-line @typescript-eslint/no-require-imports
          require('../../../../../../investor/property-inquiry/property-inquiry.entity').PropertyInquiry,
          'getPendingObjectiveByType',
        )
        .mockResolvedValue(mockAiObjective);

      await strategy.execute(
        'Yes, that time works',
        mockPropertyInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      expect(aiObjectiveService.updateObjectiveAndSyncItInItsInquiry).toHaveBeenCalledWith(
        mockRenter,
        mockAiObjective,
        { completed: true },
      );
    });

    it('should handle case when no AI objective exists', async () => {
      const mockResponse = {
        response: 'Showing scheduled successfully!',
        proposedTime: new Date('2024-01-15T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      // Mock static method to return null (no objective)
      jest
        .spyOn(
          // eslint-disable-next-line @typescript-eslint/no-require-imports
          require('../../../../../../investor/property-inquiry/property-inquiry.entity').PropertyInquiry,
          'getPendingObjectiveByType',
        )
        .mockResolvedValue(null);

      await strategy.execute(
        'Yes, that time works',
        mockPropertyInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Should not call objective update when no objective exists
      expect(aiObjectiveService.updateObjectiveAndSyncItInItsInquiry).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.parseRenterDesiredShowingTimes as jest.Mock).mockRejectedValueOnce(
        new Error('AI parsing failed'),
      );

      await expect(
        strategy.execute(
          'Invalid message',
          mockPropertyInquiry as any,
          mockRenter as any,
          mockProperty as any,
          mockConversation as any,
        ),
      ).rejects.toThrow('AI parsing failed');

      // Verify no side effects occurred
      expect(showingRequestService.create).not.toHaveBeenCalled();
      expect(propertyInquiryService.updateStageAndAddCorrespondingEvent).not.toHaveBeenCalled();
    });
  });

  describe('Slack Integration', () => {
    it('should send confirmation to Slack when showing is scheduled', async () => {
      const mockResponse = {
        response: 'Showing scheduled!',
        proposedTime: new Date('2024-01-15T14:00:00Z'),
        wasProposedTimeAgreedOn: true,
        tourType: TourType.IN_PERSON,
      };

      // Override default behavior for this specific test
      (schedulingStrategiesSharedService.generateResponseMessage as jest.Mock).mockResolvedValueOnce(mockResponse);

      await strategy.execute(
        'Yes, schedule it',
        mockPropertyInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify Slack notification was sent
      expect(slackService.sendMessageToConvosChannel).toHaveBeenCalledWith(
        expect.stringContaining(buildShowingRequestSentSlackMessage(TourType.IN_PERSON)),
        mockConversation,
      );
    });
  });
});

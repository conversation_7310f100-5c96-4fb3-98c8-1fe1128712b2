import { Injectable } from '@nestjs/common';

import { TimezoneUtils } from '../../../../../../../utils/timezone.utils';
import { PropertyInquiry } from '../../../../../../investor/property-inquiry/property-inquiry.entity';
import { PropertyInquiryService } from '../../../../../../investor/property-inquiry/property-inquiry.service';
import { Property } from '../../../../../../investor/property/property/entities/property.entity';
import { ShowingRequestStatus } from '../../../../../../investor/showing-request/enums/showing-request-status.enum';
import { ShowingRequestService } from '../../../../../../investor/showing-request/showing-request.service';
import { Conversation } from '../../../../../../shared/communication/conversation/entities/conversation.entity';
import { RentStageEnum } from '../../../../../../shared/communication/conversation/enums/rent-stage.enum';
import { FollowUpService } from '../../../../../../shared/communication/follow-up/follow-up.service';
import { SlackCommunicationService } from '../../../../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../../../../../shared/communication/outbound-communication/slack/slack-convo-message-builder.class';
import { Renter } from '../../../../../renter/renter.entity';
import { AiObjectiveType } from '../../../../ai-objective/ai-objective-type.enum';
import { AiObjectiveService } from '../../../../ai-objective/ai-objective.service';
import { HandleRenterMessageStrategy } from '../../../interfaces/handle-renter-message.strategy';
import { SchedulingStrategiesSharedService } from '../shared/scheduling-strategies-shared.service';
import { TourType } from '../../../../../../investor/showing/enums/tour-type.enum';
import { FollowUpTypeEnum } from '../../../../../../shared/communication/follow-up/enums/follow-up-type.enum';

export const buildShowingRequestSentSlackMessage = (tourType: TourType): string => {
  return `⛳️ Showing request has been sent to the owner ("${tourType}" tour)`;
};

@Injectable()
export class ScheduleShowingStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly showingRequestService: ShowingRequestService,
    private readonly followUpService: FollowUpService,
    private readonly schedulingStrategiesSharedService: SchedulingStrategiesSharedService,
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly slackService: SlackCommunicationService,
    private readonly aiObjectiveService: AiObjectiveService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const location = await property.location;
    const locationDate = TimezoneUtils.getCurrentCityDate(location.city, location.timeZone);

    // as the first step we parse the renter's desired showing times from the message
    const renterDesiredShowingTimes = await this.schedulingStrategiesSharedService.parseRenterDesiredShowingTimes(
      renterMessage,
      conversation.id,
      location,
    );

    // then we find the suitable showing times for the renter
    const suitableTimes = await this.schedulingStrategiesSharedService.determineSuitableShowingTime(
      location,
      property,
      renterDesiredShowingTimes,
      locationDate,
    );

    // const existingCalendarEvents = await this.schedulingStrategiesSharedService.getExistingCalendarEvents(
    //   property,
    //   renterDesiredShowingTimes,
    //   location,
    // );
    const existingCalendarEvents = [];

    // then we generate the response message to the renter
    const response = await this.schedulingStrategiesSharedService.generateResponseMessage(
      renterMessage,
      suitableTimes,
      existingCalendarEvents,
      location,
      conversation,
      property,
    );

    // then we validate the response and send the showing request to the property owner if needed
    if (response.wasProposedTimeAgreedOn) {
      const timeUtc = TimezoneUtils.convertDateWithMissingTzToUtcDate(
        new Date(response.proposedTime),
        location.city,
        location.timeZone,
      );
      const showingRequest = await this.showingRequestService.create({
        status: ShowingRequestStatus.PENDING,
        renter,
        property,
      });

      const inquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, property.id);
      await this.propertyInquiryService.updateStageAndAddCorrespondingEvent(
        inquiry.id,
        RentStageEnum.SHOWING_REQUESTED,
      );
      await this.showingRequestService.sendNewRequestToPropertyOwner(
        property,
        renter,
        showingRequest,
        timeUtc,
        inquiry,
        response.tourType,
      );
      await this.propertyInquiryService.update(inquiry.id, { showingRequest });
      await this.followUpService.deleteRenterFollowUpsByStatusAndType(
        conversation.id,
        FollowUpTypeEnum.RENTER_STOPPED_RESPONDING,
      );
      await this.sendConfirmationToSlack(conversation, response.tourType);

      await this.completeSchedulingObjective(inquiry, renter);
    }

    return `Schedule showing: ${response.response}`;
  }

  private async completeSchedulingObjective(inquiry: PropertyInquiry, renter: Renter): Promise<void> {
    const schedulingObjective = await PropertyInquiry.getPendingObjectiveByType(
      inquiry,
      AiObjectiveType.SCHEDULE_SHOWING,
    );

    if (schedulingObjective && !schedulingObjective.completed) {
      await this.aiObjectiveService.updateObjectiveAndSyncItInItsInquiry(renter, schedulingObjective, {
        completed: true,
      });
    }
  }

  private async sendConfirmationToSlack(conversation: Conversation, tourType: TourType): Promise<void> {
    const slackMessage = new SlackConvoMessageBuilder().appendTextLine(buildShowingRequestSentSlackMessage(tourType));

    await this.slackService.sendMessageToConvosChannel(slackMessage.build(), conversation);
  }
}

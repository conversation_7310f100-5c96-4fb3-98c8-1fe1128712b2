import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';
import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';

export const ensureRenterShowingIsConfirmedTemplate = `
<task>
<PERSON><PERSON> is asking about the current status of the showing request.
The showing is confirmed, and you need to ensure the renter that the showing is still happening.
</task>

<instructions>
${generalAiInstructionsTemplate}
${AiInstruction.DontSayWe}
${AiInstruction.UseOnlyPromptData}
- You will be given today date and the showing date, feel free to use words like "today", "tomorrow", etc. Please use full month names and days of the week.
- If renter is wrong about the showing time, correct him when makes sense
</instructions>

${PromptVariable.PropertyAddress}
${PromptVariable.ShowingTime}
${PromptVariable.CurrentDate}
${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

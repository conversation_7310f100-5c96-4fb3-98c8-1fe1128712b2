import { Injectable } from '@nestjs/common';

import { TimezoneUtils } from '../../../../../../utils/timezone.utils';
import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { ShowingRequest } from '../../../../../investor/showing-request/showing-request.entity';
import { ShowingRequestService } from '../../../../../investor/showing-request/showing-request.service';
import { Showing } from '../../../../../investor/showing/showing.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { ensureRenterShowingIsConfirmedTemplate } from './prompts/ensure-renter-showing-is-confirmed.template';
import { PropertyInquiryService } from '../../../../../investor/property-inquiry/property-inquiry.service';
import { PropertyInquiryEventTypeEnum } from '../../../../../investor/property-inquiry/events/property-inquiry-event-type.enum';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';

@Injectable()
export class RenterAsksIfShowingIsStillConfirmedStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly showingRequestService: ShowingRequestService,
    private readonly propertyInquiryService: PropertyInquiryService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const showingRequest: ShowingRequest = await this.showingRequestService.findByRenterAndProperty(
      renter.id,
      property.id,
    );

    const showing: Showing = await showingRequest.showing;
    const location = await property.location;

    // If the showing is within 12 hours, we consider it confirmed
    const timeBeforeShowing = showing.startTime.getTime() - Date.now();
    if (timeBeforeShowing < 12 * 60 * 60 * 1000) {
      const inquiry = await this.propertyInquiryService.findByRenterAndProperty(renter.id, property.id);
      this.propertyInquiryService.addEvent(inquiry.id, PropertyInquiryEventTypeEnum.SHOWING_REMINDER_SENT).then();
    }

    const response = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        propertyAddress: location.address,
        showingTime: TimezoneUtils.convertDateToStringInCityTz(showing.startTime, location.city, location.timeZone),
        currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(location.city, location.timeZone),
      },
      conversation.id,
      ensureRenterShowingIsConfirmedTemplate,
      5,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    return `Showing status question: ${response}`;
  }
}

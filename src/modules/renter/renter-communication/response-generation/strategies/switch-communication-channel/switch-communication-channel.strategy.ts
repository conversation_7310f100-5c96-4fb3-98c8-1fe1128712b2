import { Injectable } from '@nestjs/common';

import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { switchCommunicationChannelTemplate } from './prompts/switch-communication-channel.template';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { Renter } from '../../../../renter/renter.entity';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { AiService } from '../../../../../ai/ai.service';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { z } from 'zod';
import { CommunicationChannel } from '../../../../../shared/communication/conversation/enums/preferred-communication-channel.enum';
import { UserService } from '../../../../../shared/user/user.service';
import { AiStrategyExecutionCode } from '../../enums/ai-strategy-execution-code';

@Injectable()
export class SwitchCommunicationChannelStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly userService: UserService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    _property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const parser = StructuredOutputParser.fromZodSchema(
      z.object({
        wasCommunicationChannelSwitched: z
          .boolean()
          .describe(
            `Flag if channel was changed. It's considered changed if renter's preferred communication channel is different from the current one.`,
          ),
        newCommunicationChannelBelongsToRenter: z
          .boolean()
          .describe(
            'Flag if the new communication channel belongs to the renter. Return false if it belongs to someone else.',
          ),
        communicationChannel: z
          .enum([CommunicationChannel.EMAIL, CommunicationChannel.SMS])
          .describe('Communication channel to be used to communicate with the renter'),
      }),
    );

    const response = <any>await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        currentCommunicationChannel: renter.user.preferredCommunicationChannel,
      },
      conversation.id,
      switchCommunicationChannelTemplate,
      4,
      LanguageModelsEnum.GPT_4,
      parser,
    );

    if (!response.newCommunicationChannelBelongsToRenter) {
      return AiStrategyExecutionCode.DO_NOTHING;
    }

    if (response.wasCommunicationChannelSwitched) {
      await this.userService.updateUser(renter.user.id, {
        preferredCommunicationChannel: response.communicationChannel,
      });

      renter.user.preferredCommunicationChannel = response.communicationChannel;

      let responseText: string;

      if (response.communicationChannel === CommunicationChannel.EMAIL) {
        responseText = `Hi, it's Emily again. You mentioned email worked better so let's talk here`;
      } else if (response.communicationChannel === CommunicationChannel.SMS) {
        responseText = `Hi, it's Emily again. You said it's better to talk by text, so let's continue here`;
      } else {
        throw new Error('Trying to switch to invalid communication channel');
      }

      return (
        `[AI note: this message will be sent by new ${response.communicationChannel}. This means that the final answer you build will be sent by the new channel. Acknowledge this in your final answer.] Switch communication channel: ` +
        responseText
      );
    } else {
      return `Communication channel request: We are already talking by ${response.communicationChannel}`;
    }
  }
}

import { Injectable } from '@nestjs/common';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { AiService } from '../../../../../ai/ai.service';
import { welcomeRenterTemplate } from './prompts/welcome-renter.template';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';

@Injectable()
export class WelcomeRenterStrategy implements HandleRenterMessageStrategy {
  constructor(private readonly aiService: AiService) {}

  async execute(
    _renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const location = await property.location;
    const specifications = await property.specifications;

    const response = await this.aiService.getResponseWithChatMemory(
      {
        renterName: renter.user.name.split(' ')[0],
        address: location.address,
        propertyType: specifications.propertyType,
      },
      conversation.id,
      welcomeRenterTemplate,
      6,
      LanguageModelsEnum.GPT_4,
    );

    return 'Welcome message: ' + response;
  }
}

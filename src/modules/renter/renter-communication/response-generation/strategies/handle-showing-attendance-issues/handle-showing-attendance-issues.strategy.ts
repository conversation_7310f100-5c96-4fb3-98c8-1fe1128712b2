import { Injectable } from '@nestjs/common';

import { TimezoneUtils } from '../../../../../../utils/timezone.utils';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { IntelligentEscalationService } from '../../../../../investor/property-question/intelligent-escalation.service';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { ShowingRequest } from '../../../../../investor/showing-request/showing-request.entity';
import { ShowingRequestService } from '../../../../../investor/showing-request/showing-request.service';
import { Showing } from '../../../../../investor/showing/showing.entity';
import { Renter } from '../../../../renter/renter.entity';
import { AiStrategyExecutionCode } from '../../enums/ai-strategy-execution-code';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { AiService } from '../../../../../ai/ai.service';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { handleShowingAttendanceIssuesTemplate } from './prompts/handle-showing-attendance-issues.template';

@Injectable()
export class HandleShowingAttendanceIssuesStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly showingRequestService: ShowingRequestService,
    private readonly propertyQuestionService: IntelligentEscalationService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const { city, timeZone } = await property.location;
    const showingRequest: ShowingRequest = await this.showingRequestService.findByRenterAndProperty(
      renter.id,
      property.id,
    );

    const showing: Showing = await showingRequest.showing;
    const showingTime = TimezoneUtils.convertDateToStringInCityTz(showing.startTime, city, timeZone);
    const showingAgent = await showing.showingAgent;

    if (showingAgent) {
      return this.aiService.getResponseWithChatMemory(
        {
          input: renterMessage,
          showingTime,
          renterName: renter.user.name,
          showingAgent: {
            name: showingAgent.firstName,
            phone: showingAgent.phone,
          },
        },
        conversation.id,
        handleShowingAttendanceIssuesTemplate,
        5,
        LanguageModelsEnum.GPT_5_MINI,
      );
    } else {
      // eslint-disable-next-line max-len
      const questionToOwner = `The showing was scheduled for ${showingTime}. The renter left this message: "${renterMessage}"`;

      await this.propertyQuestionService.escalateQuestion(
        property,
        renter,
        questionToOwner,
        `${renter.user.name} is late for the showing`,
      );

      return AiStrategyExecutionCode.ESCALATED_WITHOUT_ANSWER;
    }
  }
}

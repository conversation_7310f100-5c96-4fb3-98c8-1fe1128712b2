import { Injectable } from '@nestjs/common';

import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { doYouHaveOtherPropertiesTemplate } from './prompts/do-you-have-other-properties.template';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';

interface DoYouHaveOtherPropertyDiscussionAiOutput {
  stopFollowups: boolean;
  response: string;
}

@Injectable()
export class DoYouHaveOtherPropertiesStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly followUpService: FollowUpService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    _renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    let aiOutput: DoYouHaveOtherPropertyDiscussionAiOutput | undefined;
    let aiResponse = 'Currently, there are no other properties available.';

    try {
      aiOutput = JSON.parse(
        await this.aiService.getResponseWithChatMemory(
          {
            input: renterMessage,
            address: (await property.location).address,
          },
          conversation.id,
          doYouHaveOtherPropertiesTemplate,
          6,
          LanguageModelsEnum.GPT_4_MINI,
        ),
      );

      aiResponse = aiOutput?.response;

      if (aiOutput?.stopFollowups) {
        await this.followUpService.deleteRenterFollowUpsByStatus(conversation.id);
      }
    } catch (error) {
      console.error('Error occurred while discussing other available properties:', error);
    }

    return `Discuss other available properties: ${aiResponse}`;
  }
}

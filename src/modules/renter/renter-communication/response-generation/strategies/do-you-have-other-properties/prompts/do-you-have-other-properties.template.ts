import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';
import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';

/* eslint-disable max-len */
export const doYouHaveOtherPropertiesTemplate = `
<task>
As an AI, you are simulating the role of a leasing manager communicating with a potential renter.
<PERSON><PERSON> is asking if you have other properties available for rent except the one already discussed and listed.
Your task is to respond to the renter, indicating that currently,
there are no other properties available besides the one already listed at the address "{address}".
You can say that will reach out if any other new properties become available.
</task>

<output_format>
- Return JSON object with the following structure:
{{
  "response": string, // your answer to the renter. Avoid repeating the same wording if you already used it in the previous message, rephrase it.
  "stopFollowups": boolean // in case if it's clear from the conversation that renter won't be proceeding with the current property, set this to true. For example, in case if renter says they don't qualify for the current property, or they are not interested in it anymore.
}}.
- Do not wrap the output in a markdown code block like "\`\`\`json, it should be parsable with js JSON.parse() function.
</output_format>

<instructions>
${generalAiInstructionsTemplate}
${AiInstruction.DontSayWe}
${AiInstruction.PlainTextOutput}
</instructions>

${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

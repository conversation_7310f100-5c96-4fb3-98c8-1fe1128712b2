import { Injectable } from '@nestjs/common';

import { TimezoneUtils } from '../../../../../../utils/timezone.utils';
import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { renterConfirmsShowingTemplate } from './prompts/renter-confirms-showing.template';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';
import { FollowUpTypeEnum } from '../../../../../shared/communication/follow-up/enums/follow-up-type.enum';
import { ShowingService } from '../../../../../investor/showing/showing.service';
import { OutboundCommunicationService } from '../../../../../shared/communication/outbound-communication/outbound-communication.service';

@Injectable()
export class RenterConfirmsShowingStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly followUpService: FollowUpService,
    private readonly showingService: ShowingService,
    private readonly communicationService: OutboundCommunicationService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const showingRequest = await inquiry.showingRequest;
    const showing = await showingRequest.showing;
    const location = await property.location;

    const response = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(location.city, location.timeZone),
      },
      conversation.id,
      renterConfirmsShowingTemplate,
      5,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    // Check if showing is less than 4 hours away
    const currentTime = new Date();
    const timeBeforeShowing = showing.startTime.getTime() - currentTime.getTime();
    const fourHoursInMilliseconds = 4 * 60 * 60 * 1000; // 4 hours in milliseconds

    if (timeBeforeShowing < fourHoursInMilliseconds) {
      // Only update showing and delete follow-ups if showing is less than 4 hours away
      await this.showingService.update(showing.id, {
        renterConfirmedAttendance: true,
      });

      await this.followUpService.deleteRenterFollowUpsByType(
        conversation.id,
        FollowUpTypeEnum.SHOWING_REMINDER_FOLLOW_UP,
      );

      await this.followUpService.deleteRenterFollowUpsByType(
        conversation.id,
        FollowUpTypeEnum.SHOWING_CANCELLATION_WARNING_FOLLOW_UP,
      );

      const showingAgent = await showing.showingAgent;
      const showingDate = TimezoneUtils.convertDateToStringInCityTz(
        showing.startTime,
        location.city,
        location.timeZone,
      );

      if (showingAgent && showingAgent.phone) {
        await this.communicationService.sendRenterConfirmedShowingNotification(
          showingAgent.phone,
          showingAgent.firstName,
          renter.user.name,
          showingDate,
          location.address,
        );
      }
    }

    return `Renter confirmed showing: ${response}`;
  }
}

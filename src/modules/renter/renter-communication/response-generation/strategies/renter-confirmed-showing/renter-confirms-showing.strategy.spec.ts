import { Test, TestingModule } from '@nestjs/testing';
import { RenterConfirmsShowingStrategy } from './renter-confirms-showing.strategy';
import { AiService } from '../../../../../ai/ai.service';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';
import { ShowingService } from '../../../../../investor/showing/showing.service';
import { FollowUpTypeEnum } from '../../../../../shared/communication/follow-up/enums/follow-up-type.enum';
import { TimezoneUtils } from '../../../../../../utils/timezone.utils';
import { OutboundCommunicationService } from '../../../../../shared/communication/outbound-communication/outbound-communication.service';

jest.mock('../../../../../investor/property/property/property.service', () => ({
  PropertyService: jest.fn().mockImplementation(),
}));

describe('RenterConfirmsShowingStrategy', () => {
  let strategy: RenterConfirmsShowingStrategy;
  let aiService: AiService;
  let followUpService: FollowUpService;
  let showingService: ShowingService;
  let communicationService: OutboundCommunicationService;

  // Store original Date constructor
  const OriginalDate = global.Date;

  beforeEach(async () => {
    // Mock TimezoneUtils
    jest.spyOn(TimezoneUtils, 'getCurrentCityDateInAiReadableFormat').mockReturnValue('January 1, 2023, 10:00 AM');

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RenterConfirmsShowingStrategy,
        {
          provide: AiService,
          useValue: {
            getResponseWithChatMemory: jest.fn().mockResolvedValue('AI generated response'),
          },
        },
        {
          provide: FollowUpService,
          useValue: {
            deleteRenterFollowUpsByType: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: ShowingService,
          useValue: {
            update: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: OutboundCommunicationService,
          useValue: {
            sendRenterConfirmedShowingNotification: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    strategy = module.get<RenterConfirmsShowingStrategy>(RenterConfirmsShowingStrategy);
    aiService = module.get<AiService>(AiService);
    followUpService = module.get<FollowUpService>(FollowUpService);
    showingService = module.get<ShowingService>(ShowingService);
    communicationService = module.get<OutboundCommunicationService>(OutboundCommunicationService);
  });

  afterEach(() => {
    // Restore original Date constructor
    global.Date = OriginalDate;
    jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('execute', () => {
    it('should update showing and delete follow-ups when showing is less than 4 hours away', async () => {
      // Mock Date constructor to return a fixed time
      const mockDate = new Date('2023-01-01T10:00:00Z');
      global.Date = jest.fn(() => mockDate) as any;
      global.Date.now = jest.fn(() => mockDate.getTime());
      // Preserve original Date functionality for specific dates
      global.Date.UTC = OriginalDate.UTC;
      global.Date.parse = OriginalDate.parse;

      // Mock data
      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
      };

      // Create a showing time that's 3.5 hours in the future
      // We need to use the original Date constructor to create a date that won't be affected by our mock
      const mockShowing = {
        id: 'showing-id',
        startTime: new OriginalDate('2023-01-01T13:30:00Z'), // 3.5 hours from current time
      };

      const mockShowingRequest = {
        showing: Promise.resolve(mockShowing),
      };

      const mockInquiry = {
        showingRequest: Promise.resolve(mockShowingRequest),
      };

      const mockOwnerUser = {
        id: 'owner-user-id',
        name: 'Property Owner',
        phoneNumber: '+0987654321',
      };

      const mockPropertyOwner = {
        id: 'owner-id',
        user: Promise.resolve(mockOwnerUser),
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
        owner: Promise.resolve(mockPropertyOwner),
      };

      const mockConversation = {
        id: 'conversation-id',
      };

      const mockRenter = {
        id: 'renter-id',
        user: {
          name: 'Jane Doe',
        },
      };

      // Execute the strategy
      await strategy.execute(
        'I will be at the showing',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify services were called
      expect(aiService.getResponseWithChatMemory).toHaveBeenCalled();
      expect(showingService.update).toHaveBeenCalledWith(mockShowing.id, {
        renterConfirmedAttendance: true,
      });
      expect(followUpService.deleteRenterFollowUpsByType).toHaveBeenCalledWith(
        mockConversation.id,
        FollowUpTypeEnum.SHOWING_REMINDER_FOLLOW_UP,
      );
      expect(followUpService.deleteRenterFollowUpsByType).toHaveBeenCalledWith(
        mockConversation.id,
        FollowUpTypeEnum.SHOWING_CANCELLATION_WARNING_FOLLOW_UP,
      );
    });

    it('should not update showing or delete follow-ups when showing is more than 4 hours away', async () => {
      // Mock Date constructor to return a fixed time
      const mockDate = new Date('2023-01-01T10:00:00Z');
      global.Date = jest.fn(() => mockDate) as any;
      global.Date.now = jest.fn(() => mockDate.getTime());
      // Preserve original Date functionality for specific dates
      global.Date.UTC = OriginalDate.UTC;
      global.Date.parse = OriginalDate.parse;

      // Mock data
      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
      };

      // Create a showing time that's 5 hours in the future
      // We need to use the original Date constructor to create a date that won't be affected by our mock
      const mockShowing = {
        id: 'showing-id',
        startTime: new OriginalDate('2023-01-01T15:00:00Z'), // 5 hours from current time
      };

      const mockShowingRequest = {
        showing: Promise.resolve(mockShowing),
      };

      const mockInquiry = {
        showingRequest: Promise.resolve(mockShowingRequest),
      };

      const mockOwnerUser = {
        id: 'owner-user-id',
        name: 'Property Owner',
        phoneNumber: '+0987654321',
      };

      const mockPropertyOwner = {
        id: 'owner-id',
        user: Promise.resolve(mockOwnerUser),
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
        owner: Promise.resolve(mockPropertyOwner),
      };

      const mockConversation = {
        id: 'conversation-id',
      };

      const mockRenter = {
        id: 'renter-id',
        user: {
          name: 'Jane Doe',
        },
      };

      // Execute the strategy
      await strategy.execute(
        'I will be at the showing',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify AI service was called but not the other services
      expect(aiService.getResponseWithChatMemory).toHaveBeenCalled();
      expect(showingService.update).not.toHaveBeenCalled();
      expect(followUpService.deleteRenterFollowUpsByType).not.toHaveBeenCalled();
    });

    it('should send notification to showing agent', async () => {
      // Mock Date constructor to return a fixed time
      const mockDate = new Date('2023-01-01T10:00:00Z');
      global.Date = jest.fn(() => mockDate) as any;
      global.Date.now = jest.fn(() => mockDate.getTime());
      // Preserve original Date functionality for specific dates
      global.Date.UTC = OriginalDate.UTC;
      global.Date.parse = OriginalDate.parse;

      // Mock TimezoneUtils
      jest.spyOn(TimezoneUtils, 'convertDateToStringInCityTz').mockReturnValue('January 1, 2023, 1:30 PM');

      // Mock data
      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
        address: '123 Test Street',
      };

      const mockShowingAgent = {
        id: 'agent-id',
        firstName: 'John',
        phone: '+1234567890',
      };

      const mockOwnerUser = {
        id: 'owner-user-id',
        name: 'Property Owner',
        phoneNumber: '+0987654321', // Different phone number
      };

      const mockPropertyOwner = {
        id: 'owner-id',
        user: Promise.resolve(mockOwnerUser),
      };

      // Create a showing time that's 3.5 hours in the future
      const mockShowing = {
        id: 'showing-id',
        startTime: new OriginalDate('2023-01-01T13:30:00Z'), // 3.5 hours from current time
        showingAgent: Promise.resolve(mockShowingAgent),
      };

      const mockShowingRequest = {
        showing: Promise.resolve(mockShowing),
      };

      const mockInquiry = {
        showingRequest: Promise.resolve(mockShowingRequest),
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
        owner: Promise.resolve(mockPropertyOwner),
      };

      const mockConversation = {
        id: 'conversation-id',
      };

      const mockRenter = {
        id: 'renter-id',
        user: {
          name: 'Jane Doe',
        },
      };

      // Execute the strategy
      await strategy.execute(
        'I will be at the showing',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify notification was sent to the showing agent only
      expect(communicationService.sendRenterConfirmedShowingNotification).toHaveBeenCalledTimes(1);
      expect(communicationService.sendRenterConfirmedShowingNotification).toHaveBeenCalledWith(
        mockShowingAgent.phone,
        mockShowingAgent.firstName,
        mockRenter.user.name,
        'January 1, 2023, 1:30 PM',
        mockLocation.address,
      );
    });

    it('should send only one notification when agent and owner have the same phone number', async () => {
      // Mock Date constructor to return a fixed time
      const mockDate = new Date('2023-01-01T10:00:00Z');
      global.Date = jest.fn(() => mockDate) as any;
      global.Date.now = jest.fn(() => mockDate.getTime());
      // Preserve original Date functionality for specific dates
      global.Date.UTC = OriginalDate.UTC;
      global.Date.parse = OriginalDate.parse;

      // Mock TimezoneUtils
      jest.spyOn(TimezoneUtils, 'convertDateToStringInCityTz').mockReturnValue('January 1, 2023, 1:30 PM');

      // Mock data
      const mockLocation = {
        city: 'Test City',
        timeZone: 'America/New_York',
        address: '123 Test Street',
      };

      const samePhoneNumber = '+1234567890';
      const mockShowingAgent = {
        id: 'agent-id',
        firstName: 'John',
        phone: samePhoneNumber,
      };

      const mockOwnerUser = {
        id: 'owner-user-id',
        name: 'John Smith',
        phoneNumber: samePhoneNumber, // Same phone number
      };

      const mockPropertyOwner = {
        id: 'owner-id',
        user: Promise.resolve(mockOwnerUser),
      };

      // Create a showing time that's 3.5 hours in the future
      const mockShowing = {
        id: 'showing-id',
        startTime: new OriginalDate('2023-01-01T13:30:00Z'), // 3.5 hours from current time
        showingAgent: Promise.resolve(mockShowingAgent),
      };

      const mockShowingRequest = {
        showing: Promise.resolve(mockShowing),
      };

      const mockInquiry = {
        showingRequest: Promise.resolve(mockShowingRequest),
      };

      const mockProperty = {
        id: 'property-id',
        location: Promise.resolve(mockLocation),
        owner: Promise.resolve(mockPropertyOwner),
      };

      const mockConversation = {
        id: 'conversation-id',
      };

      const mockRenter = {
        id: 'renter-id',
        user: {
          name: 'Jane Doe',
        },
      };

      // Execute the strategy
      await strategy.execute(
        'I will be at the showing',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify only one notification was sent (to the agent/owner)
      expect(communicationService.sendRenterConfirmedShowingNotification).toHaveBeenCalledTimes(1);
      expect(communicationService.sendRenterConfirmedShowingNotification).toHaveBeenCalledWith(
        mockShowingAgent.phone,
        mockShowingAgent.firstName,
        mockRenter.user.name,
        'January 1, 2023, 1:30 PM',
        mockLocation.address,
      );
    });
  });
});

import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';
import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';

export const renterConfirmsShowingTemplate = `
<task>
Ren<PERSON> confirms they will attend the showing. Acknowledge their confirmation in a friendly way.
Do not imply that you or anyone else will be meeting them at the showing.
Keep the response simple and professional.
</task>

<instructions>
${generalAiInstructionsTemplate}
${AiInstruction.DontSayWe}
${AiInstruction.UseOnlyPromptData}
- Avoid phrases like "See you at the showing" or "looking forward to meeting you"
</instructions>

${PromptVariable.CurrentDate}
${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

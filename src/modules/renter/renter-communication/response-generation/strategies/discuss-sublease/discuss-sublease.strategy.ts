import { Renter } from '../../../../renter/renter.entity';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { Injectable } from '@nestjs/common';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { AiService } from '../../../../../ai/ai.service';
import { discussSubleaseTemplate } from './prompts/discuss-sublease.template';
import { DateUtils } from '../../../../../../utils/date.utils';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';

@Injectable()
export class DiscussSubleaseStrategy implements HandleRenterMessageStrategy {
  constructor(private readonly aiService: AiService) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const leaseConditions = await property.leaseConditions;
    const response = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        desiredLeasingDate: DateUtils.toAiReadableFormat(leaseConditions.desiredLeasingDate),
        renterName: renter.user.name,
        leaseConditions: JSON.stringify(leaseConditions),
      },
      conversation.id,
      discussSubleaseTemplate,
      3,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    return 'Availability question: ' + response;
  }
}

import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';
import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';

export const discussSubleaseTemplate = `
<task>
Imagine you are a property manager.
Answer to the question about possible property sublease.
</task>

<instructions>
- Call renter by the name to show the personal approach.
- You will be given data about the lease conditions, availability date, and lead info to answer
the question. Operate with this data to provide the best answer. No need to
share it all, only if it is relevant to the conversation.
- Do not mention database or any other technical details.
</instructions>

<lease_conditions>
{leaseConditions}
</lease_conditions>

${PromptVariable.RenterName}

<desired_leasing_date>
{desiredLeasingDate}
</desired_leasing_date>

<instructions>
${generalAiInstructionsTemplate}
${AiInstruction.UseOnlyPromptData}

${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
</instructions>
`;

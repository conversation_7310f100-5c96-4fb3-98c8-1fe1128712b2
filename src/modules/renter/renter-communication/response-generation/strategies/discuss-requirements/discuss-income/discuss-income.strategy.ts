import { Injectable } from '@nestjs/common';
import { StructuredOutputParser } from '@langchain/core/output_parsers';

import { AiService } from '../../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../../ai/enums/language-models.enum';
import { PropertyInquiry } from '../../../../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../../interfaces/handle-renter-message.strategy';
import { AiStrategyExecutionCode } from '../../../enums/ai-strategy-execution-code';
import { AiObjectiveService } from '../../../../ai-objective/ai-objective.service';
import { AiObjectiveType } from '../../../../ai-objective/ai-objective-type.enum';
import { discussIncomeTemplate } from './discuss-income.template';
import { z } from 'zod';
import { calculateRequiredMonthlyRent } from '../../../../../../investor/property/property/property-details/renter-requirements/calculate-required-monthly-rent.util';
import { RenterScreeningService } from '../../../../../renter-screening/renter-screening.service';

export const incomeDiscussionAiOutputSchema = z.object({
  completed: z.boolean().describe('indicates if conversation is completed'),
  value: z
    .string()
    .nullable()
    .describe(
      'renter\'s monthly income (e.g. "3000"). Refer to "value_preffered_formats" to see how to format the value',
    ),
  highlight: z
    .string()
    .nullable()
    .describe(
      'highlight for the owner, factors that might help owner to make a decision. Refer to \"highlight_examples\" for examples',
    ),
  response: z.string().describe('your answer to the renter'),
});

type IncomeDiscussionAiOutput = z.infer<typeof incomeDiscussionAiOutputSchema>;

@Injectable()
export class DiscussIncomeStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly aiObjectiveService: AiObjectiveService,
    private readonly renterScreeningService: RenterScreeningService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const renterRequirements = await property.renterRequirements;
    const leaseConditions = await property.leaseConditions;
    const petRequirements = await property.petPolicy;
    const objectives = await inquiry.aiObjectives;

    const evaluateIncomeObjective = objectives.find((objective) => objective.type === AiObjectiveType.EVALUATE_INCOME);

    let aiOutput: IncomeDiscussionAiOutput | undefined;

    try {
      const outputParser = StructuredOutputParser.fromZodSchema(incomeDiscussionAiOutputSchema);

      aiOutput = (await this.aiService.getResponseWithChatMemory(
        {
          input: renterMessage,
          minimumCreditScore: renterRequirements?.minimumCreditScore || 'No minimum credit score requirement',
          securityDeposit: leaseConditions.securityDeposit,
          rent: leaseConditions.rent,
          monthlyIncomeRequirement: renterRequirements.minimumIncome || 'No minimum income requirement',
          monthlyIncomeRequirementAmount: calculateRequiredMonthlyRent(
            leaseConditions.rent,
            renterRequirements.minimumIncome,
          ),
          applicationFee: leaseConditions.applicationFee,
          petRent: petRequirements.petRent,
          petDeposit: petRequirements.petDeposit,
          currentIncomeValue: evaluateIncomeObjective?.value || '-',
          requirementCompensatingFactors: evaluateIncomeObjective?.requirementCompensatingFactors.length
            ? evaluateIncomeObjective.requirementCompensatingFactors.join(', ')
            : '-',
        },
        conversation.id,
        discussIncomeTemplate,
        6,
        LanguageModelsEnum.CLAUDE_4_SONNET,
        outputParser,
      )) as IncomeDiscussionAiOutput;
    } catch (error) {
      console.error('Error discussing income:', error);

      return AiStrategyExecutionCode.DO_NOTHING;
    }

    await this.aiObjectiveService.updateObjectiveAndSyncItInItsInquiry(renter, evaluateIncomeObjective, {
      completed: evaluateIncomeObjective.completed ? true : aiOutput.completed, // if it was already completed, keep it completed
      value: aiOutput.value,
      requirementCompensatingFactors: [
        ...new Set([
          ...(evaluateIncomeObjective.requirementCompensatingFactors || []),
          ...(aiOutput.highlight ? [aiOutput.highlight] : []),
        ]),
      ],
    });

    await this.renterScreeningService.requalifyRenterForProperty(conversation, inquiry, property, renter);

    return 'Discuss Renter Income: ' + aiOutput.response;
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { DiscussIncomeStrategy } from './discuss-income.strategy';
import { AiService } from '../../../../../../ai/ai.service';
import { AiObjectiveService } from '../../../../ai-objective/ai-objective.service';
import { AiObjectiveType } from '../../../../ai-objective/ai-objective-type.enum';
import { AiStrategyExecutionCode } from '../../../enums/ai-strategy-execution-code';
import {
  RenterScreeningService,
  skipQualificationOutput,
} from '../../../../../renter-screening/renter-screening.service';

describe('DiscussIncomeStrategy', () => {
  let strategy: DiscussIncomeStrategy;
  let aiService: AiService;
  let aiObjectiveService: AiObjectiveService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DiscussIncomeStrategy,
        {
          provide: AiService,
          useValue: {
            getResponseWithChatMemory: jest.fn(),
          },
        },
        {
          provide: RenterScreeningService,
          useValue: {
            requalifyRenterForProperty: jest.fn().mockReturnValue(skipQualificationOutput),
          },
        },
        {
          provide: AiObjectiveService,
          useValue: {
            updateObjectiveAndSyncItInItsInquiry: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    strategy = module.get<DiscussIncomeStrategy>(DiscussIncomeStrategy);
    aiService = module.get<AiService>(AiService);
    aiObjectiveService = module.get<AiObjectiveService>(AiObjectiveService);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('execute', () => {
    it('should process AI output with Zod schema validation', async () => {
      // Mock AI response that matches our Zod schema
      const mockAiOutput = {
        completed: true,
        value: '5000',
        highlight: 'Combined income with spouse is $7000',
        response: 'Great! Your income meets our requirements.',
      };

      (aiService.getResponseWithChatMemory as jest.Mock).mockResolvedValue(mockAiOutput);

      // Mock data
      const mockObjective = {
        type: AiObjectiveType.EVALUATE_INCOME,
        completed: false,
        value: null,
        requirementCompensatingFactors: [],
      };

      const mockInquiry = {
        aiObjectives: Promise.resolve([mockObjective]),
      };

      const mockRenterRequirements = {
        minimumCreditScore: 650,
        minimumIncome: 4000,
      };

      const mockLeaseConditions = {
        rent: 2000,
        securityDeposit: 2000,
        applicationFee: 50,
      };

      const mockPetRequirements = {
        petRent: 50,
        petDeposit: 300,
      };

      const mockProperty = {
        renterRequirements: Promise.resolve(mockRenterRequirements),
        leaseConditions: Promise.resolve(mockLeaseConditions),
        petPolicy: Promise.resolve(mockPetRequirements),
      };

      const mockConversation = {
        id: 'conversation-id',
      };

      const mockRenter = {
        id: 'renter-id',
      };

      // Execute the strategy
      const result = await strategy.execute(
        'My monthly income is $5000',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Verify AI service was called with StructuredOutputParser
      expect(aiService.getResponseWithChatMemory).toHaveBeenCalledWith(
        expect.objectContaining({
          input: 'My monthly income is $5000',
          monthlyIncomeRequirement: 4000,
          rent: 2000,
        }),
        'conversation-id',
        expect.any(String), // template
        6,
        expect.any(String), // language model
        expect.any(Object), // StructuredOutputParser
      );

      // Verify objective service was called with correct data
      expect(aiObjectiveService.updateObjectiveAndSyncItInItsInquiry).toHaveBeenCalledWith(mockRenter, mockObjective, {
        completed: true,
        value: '5000',
        requirementCompensatingFactors: ['Combined income with spouse is $7000'],
      });

      // Verify return value
      expect(result).toBe('Discuss Renter Income: Great! Your income meets our requirements.');
    });

    it('should handle AI service errors gracefully', async () => {
      // Mock AI service to throw an error
      (aiService.getResponseWithChatMemory as jest.Mock).mockRejectedValue(new Error('AI service error'));

      // Mock minimal data
      const mockInquiry = {
        aiObjectives: Promise.resolve([]),
      };

      const mockProperty = {
        renterRequirements: Promise.resolve({}),
        leaseConditions: Promise.resolve({}),
        petPolicy: Promise.resolve({}),
      };

      const mockConversation = { id: 'conversation-id' };
      const mockRenter = { id: 'renter-id' };

      // Execute the strategy
      const result = await strategy.execute(
        'My income is $5000',
        mockInquiry as any,
        mockRenter as any,
        mockProperty as any,
        mockConversation as any,
      );

      // Should return DO_NOTHING code on error
      expect(result).toBe(AiStrategyExecutionCode.DO_NOTHING);
      expect(aiObjectiveService.updateObjectiveAndSyncItInItsInquiry).not.toHaveBeenCalled();
    });
  });
});

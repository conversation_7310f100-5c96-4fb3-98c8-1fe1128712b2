import { Injectable } from '@nestjs/common';

import { TimezoneUtils } from '../../../../../../../utils/timezone.utils';
import { AiService } from '../../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../../ai/enums/language-models.enum';
import { PropertyInquiry } from '../../../../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../../renter/renter.entity';
import { AiObjectiveType } from '../../../../ai-objective/ai-objective-type.enum';
import { AiObjectiveService } from '../../../../ai-objective/ai-objective.service';
import { AiStrategyExecutionCode } from '../../../enums/ai-strategy-execution-code';
import { HandleRenterMessageStrategy } from '../../../interfaces/handle-renter-message.strategy';
import { getAvailabilityPromptInstruction } from '../../../utils/get-availability-prompt-instruction.function';
import { discussMoveInDateTemplate } from './discuss-move-in-date.template';
import { answerAvailabilityQuestionTemplate } from './answer-availability-question.template';
import { FollowUpService } from '../../../../../../shared/communication/follow-up/follow-up.service';
import { RenterScreeningService } from '../../../../../renter-screening/renter-screening.service';

interface MoveInDateDiscussionAiOutput {
  completed: boolean;
  highlight: string;
  value: string;
  response: string;
}

@Injectable()
export class DiscussMoveInDateStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly aiObjectiveService: AiObjectiveService,
    private readonly followUpService: FollowUpService,
    private readonly renterScreeningService: RenterScreeningService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const objectives = await inquiry.aiObjectives;
    const { city, timeZone } = await property.location;
    const leaseConditions = await property.leaseConditions;
    const availabilityStatus = getAvailabilityPromptInstruction(
      TimezoneUtils.getCurrentCityDate(city, timeZone),
      leaseConditions.desiredLeasingDate,
    );

    const askAboutMoveInDateObjective = objectives.find(
      (objective) => objective.type === AiObjectiveType.ASK_ABOUT_MOVE_IN_DATE,
    );

    if (!askAboutMoveInDateObjective) {
      return this.aiService.getResponseWithChatMemory(
        {
          availabilityStatus,
          input: renterMessage,
          renterName: renter.user.name,
          currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone),
        },
        conversation.id,
        answerAvailabilityQuestionTemplate,
        6,
        LanguageModelsEnum.GPT_4_MINI,
      );
    }

    let aiOutput: MoveInDateDiscussionAiOutput | undefined;

    try {
      aiOutput = JSON.parse(
        await this.aiService.getResponseWithChatMemory(
          {
            input: renterMessage,
            availabilityStatus,
            currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(city, timeZone),
            currentMoveInDate: askAboutMoveInDateObjective?.value || '-',
            requirementCompensatingFactors: askAboutMoveInDateObjective?.requirementCompensatingFactors.length
              ? askAboutMoveInDateObjective.requirementCompensatingFactors.join(', ')
              : '-',
          },
          conversation.id,
          discussMoveInDateTemplate,
          12,
          LanguageModelsEnum.CLAUDE_4_SONNET_REASONER,
        ),
      );
    } catch (error) {
      console.error('Error discussing move in date:', error);

      return AiStrategyExecutionCode.DO_NOTHING;
    }

    await this.aiObjectiveService.updateObjectiveAndSyncItInItsInquiry(renter, askAboutMoveInDateObjective, {
      completed: askAboutMoveInDateObjective.completed ? true : aiOutput.completed, // if it was already completed, keep it completed
      value: aiOutput.value,
      requirementCompensatingFactors: [
        ...new Set([
          ...(askAboutMoveInDateObjective.requirementCompensatingFactors || []),
          ...(aiOutput.highlight ? [aiOutput.highlight] : []),
        ]),
      ],
    });

    await this.renterScreeningService.requalifyRenterForProperty(conversation, inquiry, property, renter);

    return 'Discuss Move In Date: ' + aiOutput.response;
  }
}

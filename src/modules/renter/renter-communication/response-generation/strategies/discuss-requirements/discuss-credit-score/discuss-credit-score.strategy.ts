import { Injectable } from '@nestjs/common';

import { AiService } from '../../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../../ai/enums/language-models.enum';
import { PropertyInquiry } from '../../../../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../../interfaces/handle-renter-message.strategy';
import { buildCreditScoreTemplate } from './discuss-credit-score.template';
import { AiStrategyExecutionCode } from '../../../enums/ai-strategy-execution-code';
import { AiObjectiveService } from '../../../../ai-objective/ai-objective.service';
import { AiObjectiveType } from '../../../../ai-objective/ai-objective-type.enum';
import { RenterScreeningService } from '../../../../../renter-screening/renter-screening.service';

interface CreditScoreDiscussionAiOutput {
  completed: boolean;
  value: string;
  note: string;
  highlight: string;
  response: string;
}

@Injectable()
export class DiscussCreditScoreStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly aiObjectiveService: AiObjectiveService,
    private readonly renterScreeningService: RenterScreeningService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const renterRequirements = await property.renterRequirements;
    const leaseConditions = await property.leaseConditions;
    const petRequirements = await property.petPolicy;
    const objectives = await inquiry.aiObjectives;

    const evaluateCreditObjective = objectives.find(
      (objective) => objective.type === AiObjectiveType.EVALUATE_CREDIT_SCORE,
    );

    const dynamicTemplate = buildCreditScoreTemplate(renterRequirements?.acceptsCosigners);

    let aiOutput: CreditScoreDiscussionAiOutput | undefined;

    try {
      aiOutput = JSON.parse(
        await this.aiService.getResponseWithChatMemory(
          {
            input: renterMessage,
            executionCode: AiStrategyExecutionCode.DO_NOTHING,
            acceptsCosigners: Boolean(renterRequirements?.acceptsCosigners),
            minimumCreditScore: renterRequirements?.minimumCreditScore || 'No minimum credit score requirement',
            securityDeposit: leaseConditions.securityDeposit,
            rent: leaseConditions.rent,
            monthlyIncome: renterRequirements.minimumIncome || 'No minimum income requirement',
            applicationFee: leaseConditions.applicationFee,
            petRent: petRequirements.petRent,
            petDeposit: petRequirements.petDeposit,
            currentCreditScoreValue: evaluateCreditObjective?.value || '-',
            notes: evaluateCreditObjective?.notes.length ? evaluateCreditObjective.notes.join(', ') : '-',
            requirementCompensatingFactors: evaluateCreditObjective?.requirementCompensatingFactors.length
              ? evaluateCreditObjective.requirementCompensatingFactors.join(', ')
              : '-',
          },
          conversation.id,
          dynamicTemplate,
          12,
          LanguageModelsEnum.CLAUDE_4_SONNET_REASONER,
        ),
      );
    } catch (error) {
      console.error('Error discussing credit score:', error);

      return AiStrategyExecutionCode.DO_NOTHING;
    }

    if (aiOutput.response === AiStrategyExecutionCode.DO_NOTHING) {
      return AiStrategyExecutionCode.DO_NOTHING;
    }

    await this.aiObjectiveService.updateObjectiveAndSyncItInItsInquiry(renter, evaluateCreditObjective, {
      completed: evaluateCreditObjective.completed ? true : aiOutput.completed, // if it was already completed, keep it completed
      value: aiOutput.value,
      notes: [...new Set([...(evaluateCreditObjective.notes || []), ...(aiOutput.note ? [aiOutput.note] : [])])],
      requirementCompensatingFactors: [
        ...new Set([
          ...(evaluateCreditObjective.requirementCompensatingFactors || []),
          ...(aiOutput.highlight ? [aiOutput.highlight] : []),
        ]),
      ],
    });

    await this.renterScreeningService.requalifyRenterForProperty(conversation, inquiry, property, renter);

    return 'Discuss Renter Credit Score: ' + aiOutput.response;
  }
}

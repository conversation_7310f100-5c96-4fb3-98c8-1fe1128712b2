import { Injectable } from '@nestjs/common';
import { PropertyInquiryEventTypeEnum } from '../../../../../../../investor/property-inquiry/events/property-inquiry-event-type.enum';
import { PropertyInquiry } from '../../../../../../../investor/property-inquiry/property-inquiry.entity';
import { PropertyInquiryService } from '../../../../../../../investor/property-inquiry/property-inquiry.service';
import { HandleRenterMessageStrategy } from '../../../../interfaces/handle-renter-message.strategy';
import { AiObjectiveService } from '../../../../../ai-objective/ai-objective.service';
import { AiObjectiveType } from '../../../../../ai-objective/ai-objective-type.enum';
import { Renter } from '../../../../../../renter/renter.entity';

@Injectable()
export class AskIfRenterHasRequirementsQuestionsStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly aiObjectiveService: AiObjectiveService,
  ) {}

  async execute(_renterMessage: string, inquiry: PropertyInquiry, renter: Renter): Promise<string> {
    const askingAboutRequirementsObjective = await PropertyInquiry.getPendingObjectiveByType(
      inquiry,
      AiObjectiveType.ASK_IF_RENTER_HAS_REQUIREMENTS_QUESTION,
    );

    if (askingAboutRequirementsObjective && !askingAboutRequirementsObjective.completed) {
      await this.aiObjectiveService.updateObjectiveAndSyncItInItsInquiry(renter, askingAboutRequirementsObjective, {
        completed: true,
      });
    }

    await this.propertyInquiryService.addEvent(
      inquiry.id,
      PropertyInquiryEventTypeEnum.WAS_ASKED_IF_HAS_REQUIREMENTS_QUESTIONS,
    );

    return `Requirements questions: Before we proceed, do you have any questions about the requirements?
    (AI note: we don't know if this date is actually available, so don\'t confirm showings if renter suggest it)`;
  }
}

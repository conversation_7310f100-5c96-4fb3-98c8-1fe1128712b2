import { Injectable } from '@nestjs/common';
import { AiService } from '../../../../../../../ai/ai.service';
import { HandleRenterMessageStrategy } from '../../../../interfaces/handle-renter-message.strategy';
import { Renter } from '../../../../../../renter/renter.entity';
import { Property } from '../../../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../../../shared/communication/conversation/entities/conversation.entity';
import { LanguageModelsEnum } from '../../../../../../../ai/enums/language-models.enum';
import { shareRequirementsTemplate } from './prompts/share-requirements.template';
import { PropertyInquiryService } from '../../../../../../../investor/property-inquiry/property-inquiry.service';
import { PropertyInquiry } from '../../../../../../../investor/property-inquiry/property-inquiry.entity';
import { PropertyInquiryEventTypeEnum } from '../../../../../../../investor/property-inquiry/events/property-inquiry-event-type.enum';
import { TimezoneUtils } from '../../../../../../../../utils/timezone.utils';
import { getAvailabilityPromptInstruction } from '../../../../utils/get-availability-prompt-instruction.function';
import { AiObjectiveService } from '../../../../../ai-objective/ai-objective.service';
import { AiObjectiveType } from '../../../../../ai-objective/ai-objective-type.enum';

@Injectable()
export class ShareRequirementsStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly aiObjectiveService: AiObjectiveService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const leaseConditions = await property.leaseConditions;
    const renterRequirements = await property.renterRequirements;
    const { city, timeZone } = await property.location;

    if (!inquiry) {
      throw new Error('Showing request not found');
    }

    const availabilityInstruction = getAvailabilityPromptInstruction(
      TimezoneUtils.getCurrentCityDate(city, timeZone),
      leaseConditions.desiredLeasingDate,
    );

    if (!(await PropertyInquiry.didEventHappen(inquiry, PropertyInquiryEventTypeEnum.WAS_SHARED_REQUIREMENTS))) {
      await this.propertyInquiryService.addEvent(inquiry.id, PropertyInquiryEventTypeEnum.WAS_SHARED_REQUIREMENTS);
    }

    const askingAboutRequirementsObjective = await PropertyInquiry.getPendingObjectiveByType(
      inquiry,
      AiObjectiveType.ASK_IF_RENTER_HAS_REQUIREMENTS_QUESTION,
    );

    if (askingAboutRequirementsObjective) {
      await this.aiObjectiveService.updateObjectiveAndSyncItInItsInquiry(renter, askingAboutRequirementsObjective, {
        completed: true,
      });
    }

    const location = await property.location;

    const response = await this.aiService.getResponseWithChatMemory(
      {
        acceptanceCriteria: JSON.stringify({
          minimumCreditScore: renterRequirements.minimumCreditScore,
          minimumIncome: renterRequirements.minimumIncome,
          acceptsCosigners: renterRequirements.acceptsCosigners,
        }),
        moveInCost: JSON.stringify({
          rent: leaseConditions.rent,
          applicationFee: leaseConditions.applicationFee,
          lastMonthRent: leaseConditions.lastMonthRent,
          securityDeposit: leaseConditions.securityDeposit,
          possibleLeaseTerms: leaseConditions.possibleLeaseTerms,
          isSubleaseAllowed: leaseConditions.isSubleaseAllowed,
          availability: availabilityInstruction,
        }),
        input: renterMessage,
        currentDate: TimezoneUtils.getCurrentCityDateInAiReadableFormat(location.city, location.timeZone),
      },
      conversation.id,
      shareRequirementsTemplate,
      5,
      LanguageModelsEnum.GPT_4,
    );

    return 'Move in requirements: ' + response;
  }
}

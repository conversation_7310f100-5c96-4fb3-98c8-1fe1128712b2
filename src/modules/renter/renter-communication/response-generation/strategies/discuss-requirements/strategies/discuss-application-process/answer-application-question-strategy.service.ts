import { Injectable } from '@nestjs/common';

import { ConfigService } from '@nestjs/config';
import { AiService } from '../../../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../../../ai/enums/language-models.enum';
import { PropertyInquiry } from '../../../../../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../../../interfaces/handle-renter-message.strategy';
import { answerApplicationQuestionTemplate } from './prompts/answer-application-question.template';
import { talloProductPricesMap } from '../../../../../../../shared/checkout/tallo-product-prices.map';
import { TalloProductsEnum } from '../../../../../../../shared/checkout/tallo-products.enum';

@Injectable()
export class AnswerApplicationQuestionStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly config: ConfigService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    _renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const renterRequirements = await property.renterRequirements;
    const acceptsCoSigners = renterRequirements.acceptsCosigners;
    const response = await this.aiService.getResponseWithChatMemory(
      {
        applicationWebsite: this.config.get('RENTER_APP_URL'),
        applicationFee: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_FULL_PACKAGE),
        acceptsCoSigners: acceptsCoSigners ? 'accepted' : 'not accepted',
        coSignerFee: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_BASE_PACKAGE),
        input: renterMessage,
      },
      conversation.id,
      answerApplicationQuestionTemplate,
      5,
      LanguageModelsEnum.GPT_4,
    );

    return `Application process : ${response}`;
  }
}

import { Injectable } from '@nestjs/common';

import { AiService } from '../../../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../../../ai/enums/language-models.enum';
import { Property } from '../../../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../../../interfaces/handle-renter-message.strategy';
import { discussSectionEightTemplate } from './prompts/discuss-section-eight.template';
import { PropertyInquiry } from '../../../../../../../investor/property-inquiry/property-inquiry.entity';

@Injectable()
export class SectionEightStrategy implements HandleRenterMessageStrategy {
  constructor(private readonly aiService: AiService) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    _renter: Renter,
    _property: Property,
    conversation: Conversation,
  ): Promise<string> {
    return await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
      },
      conversation.id,
      discussSectionEightTemplate,
      5,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );
  }
}

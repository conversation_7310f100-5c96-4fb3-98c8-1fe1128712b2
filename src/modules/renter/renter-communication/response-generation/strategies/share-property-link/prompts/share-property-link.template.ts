import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';
import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';

export const sharePropertyLinkTemplate = `
<task>
You are asked to share a link to the property.
Share it with the renter.

If renter asks for the link on a specific platform, like zillow,
explain that you don't have a link on that platform.

Share the one you have instead if appropriate.
</task>

<property_link>{propertyLink}</property_link>

<instructions>
${generalAiInstructionsTemplate}
${AiInstruction.DontSayWe}
${AiInstruction.PlainTextOutput}
</instructions>

${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

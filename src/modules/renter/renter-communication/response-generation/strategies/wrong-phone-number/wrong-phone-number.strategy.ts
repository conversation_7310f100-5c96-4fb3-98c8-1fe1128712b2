import { Injectable } from '@nestjs/common';

import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { CommunicationChannel } from '../../../../../shared/communication/conversation/enums/preferred-communication-channel.enum';
import { SlackCommunicationService } from '../../../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { UserService } from '../../../../../shared/user/user.service';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { AiStrategyExecutionCode } from '../../enums/ai-strategy-execution-code';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';

@Injectable()
export class WrongPhoneNumberStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly userService: UserService,
    private readonly slackService: SlackCommunicationService,
  ) {}

  async execute(
    _renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    _property: Property,
    conversation: Conversation,
  ): Promise<string> {
    if (renter.user.phoneNumber && renter.user.preferredCommunicationChannel === CommunicationChannel.SMS) {
      await this.userService.updateUser(renter.user.id, {
        phoneNumber: null,
        preferredCommunicationChannel: CommunicationChannel.EMAIL,
      });

      this.slackService.sendMessageToConvosChannel(
        '📧 Switching back to emails. The incorrect phone number has been removed.',
        conversation,
      );

      return AiStrategyExecutionCode.EXECUTED_WITHOUT_ANSWER;
    }

    return AiStrategyExecutionCode.DO_NOTHING;
  }
}

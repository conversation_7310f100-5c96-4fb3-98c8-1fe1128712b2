import { Injectable } from '@nestjs/common';

import { AiStrategyExecutionCode } from '../../enums/ai-strategy-execution-code';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';

@Injectable()
export class AnswerSobStoryQuestionStrategy implements HandleRenterMessageStrategy {
  async execute(): Promise<string> {
    return AiStrategyExecutionCode.STOP_AI_DOES_NOT_KNOW_WHAT_TO_DO;
  }
}

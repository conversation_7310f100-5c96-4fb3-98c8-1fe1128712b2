import { Injectable } from '@nestjs/common';
import { AiService } from '../../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../../ai/enums/language-models.enum';
import { PropertyInquiry } from '../../../../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../../renter/renter.entity';
import { AiStrategyExecutionCode } from '../../../enums/ai-strategy-execution-code';
import { HandleRenterMessageStrategy } from '../../../interfaces/handle-renter-message.strategy';
import { handleUnqualifiedRenterTemplate } from './handle-unqualified-renter.template';

@Injectable()
export class HandleUnqualifiedRenterStrategy implements HandleRenterMessageStrategy {
  constructor(private readonly aiService: AiService) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    _property: Property,
    conversation: Conversation,
  ): Promise<string> {
    return this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        renterName: renter.user.name,
        outputCode: AiStrategyExecutionCode.DO_NOTHING,
      },
      conversation.id,
      handleUnqualifiedRenterTemplate,
      8,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );
  }
}

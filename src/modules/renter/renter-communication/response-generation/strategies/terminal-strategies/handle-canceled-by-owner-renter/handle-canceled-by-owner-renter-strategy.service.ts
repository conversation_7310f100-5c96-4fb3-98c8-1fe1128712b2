import { Injectable } from '@nestjs/common';
import { HandleRenterMessageStrategy } from '../../../interfaces/handle-renter-message.strategy';
import { AiService } from '../../../../../../ai/ai.service';
import { Renter } from '../../../../../renter/renter.entity';
import { Property } from '../../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../../shared/communication/conversation/entities/conversation.entity';
import { LanguageModelsEnum } from '../../../../../../ai/enums/language-models.enum';
import { handleCanceledByOwnerRenterTemplate } from './prompt/handle-canceled-by-owner-renter.template';
import { ShowingRequestService } from '../../../../../../investor/showing-request/showing-request.service';
import { PropertyInquiry } from '../../../../../../investor/property-inquiry/property-inquiry.entity';
import { AiStrategyExecutionCode } from '../../../enums/ai-strategy-execution-code';

@Injectable()
export class HandleCanceledByOwnerRenterStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly showingRequestService: ShowingRequestService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const showingRequest = await this.showingRequestService.findByRenterAndProperty(renter.id, property.id);

    return this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        renterName: renter.user.name,
        cancellationReason: showingRequest.cancelReason || 'Not specified',
        outputCode: AiStrategyExecutionCode.DO_NOTHING,
      },
      conversation.id,
      handleCanceledByOwnerRenterTemplate,
      5,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );
  }
}

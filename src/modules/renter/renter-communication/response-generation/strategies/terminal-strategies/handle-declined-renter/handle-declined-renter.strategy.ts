import { Injectable } from '@nestjs/common';
import { HandleRenterMessageStrategy } from '../../../interfaces/handle-renter-message.strategy';
import { AiService } from '../../../../../../ai/ai.service';
import { Renter } from '../../../../../renter/renter.entity';
import { Property } from '../../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../../shared/communication/conversation/entities/conversation.entity';
import { LanguageModelsEnum } from '../../../../../../ai/enums/language-models.enum';
import { handleDeclinedRenterTemplate } from './prompt/handle-declined-renter.template';
import { AiStrategyExecutionCode } from '../../../enums/ai-strategy-execution-code';
import { PropertyInquiry } from '../../../../../../investor/property-inquiry/property-inquiry.entity';

@Injectable()
export class HandleDeclinedRenterStrategy implements HandleRenterMessageStrategy {
  constructor(private readonly aiService: AiService) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    _property: Property,
    conversation: Conversation,
  ): Promise<string> {
    return this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        renterName: renter.user.name,
        outputCode: AiStrategyExecutionCode.DO_NOTHING,
      },
      conversation.id,
      handleDeclinedRenterTemplate,
      5,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );
  }
}

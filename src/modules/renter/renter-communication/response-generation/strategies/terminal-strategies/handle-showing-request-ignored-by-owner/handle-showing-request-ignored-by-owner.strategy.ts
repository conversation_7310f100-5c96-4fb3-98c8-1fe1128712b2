import { Injectable } from '@nestjs/common';
import { HandleRenterMessageStrategy } from '../../../interfaces/handle-renter-message.strategy';
import { AiService } from '../../../../../../ai/ai.service';
import { Renter } from '../../../../../renter/renter.entity';
import { Property } from '../../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../../shared/communication/conversation/entities/conversation.entity';
import { LanguageModelsEnum } from '../../../../../../ai/enums/language-models.enum';
import { handleShowingRequestIgnoredByOwnerTemplate } from './prompt/handle-showing-request-ignored-by-owner.template';
import { ShowingRequestService } from '../../../../../../investor/showing-request/showing-request.service';
import { PropertyInquiry } from '../../../../../../investor/property-inquiry/property-inquiry.entity';
import { AiStrategyExecutionCode } from '../../../enums/ai-strategy-execution-code';
import { TimezoneUtils } from '../../../../../../../utils/timezone.utils';

@Injectable()
export class HandleShowingRequestIgnoredByOwnerStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly showingRequestService: ShowingRequestService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const showingRequest = await this.showingRequestService.findByRenterAndProperty(renter.id, property.id);
    const showing = await showingRequest.showing;
    const location = await property.location;

    const showingTime = TimezoneUtils.convertDateToStringInCityTz(showing.startTime, location.city, location.timeZone);

    return this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        renterName: renter.user.name,
        propertyAddress: location.address,
        showingTime,
        outputCode: AiStrategyExecutionCode.DO_NOTHING,
      },
      conversation.id,
      handleShowingRequestIgnoredByOwnerTemplate,
      8,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );
  }
}

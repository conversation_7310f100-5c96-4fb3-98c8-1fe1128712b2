import { Injectable } from '@nestjs/common';

import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { answerTalloQuestionTemplate } from './answer-tallo-question.template';
import { ConfigService } from '@nestjs/config';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';

@Injectable()
export class AnswerTalloQuestionStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly config: ConfigService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    _renter: Renter,
    _property: Property,
    conversation: Conversation,
  ): Promise<string> {
    return this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        applicationWebsite: this.config.get('RENTER_APP_URL'),
      },
      conversation.id,
      answerTalloQuestionTemplate,
      6,
      LanguageModelsEnum.GPT_4,
    );
  }
}

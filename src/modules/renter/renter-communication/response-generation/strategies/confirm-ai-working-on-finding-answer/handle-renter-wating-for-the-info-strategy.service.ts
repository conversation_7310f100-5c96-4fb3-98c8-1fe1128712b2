import { Injectable } from '@nestjs/common';
import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { AiStrategyExecutionCode } from '../../enums/ai-strategy-execution-code';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { z } from 'zod';
import { renterWaitingForTheInfoTemplate } from './prompt/renter-waiting-for-the-info.template';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';

@Injectable()
export class HandleRenterWaitingForTheInfoStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly followUpService: FollowUpService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    _renter: Renter,
    _property: Property,
    conversation: Conversation,
  ): Promise<string> {
    await this.followUpService.deleteRenterFollowUpsByStatus(conversation.id);
    try {
      const outputParser = StructuredOutputParser.fromZodSchema(
        z.object({
          isStatusUpdate: z
            .boolean()
            .describe('True if the renter is asking for a status update on a previous question'),
          response: z.string().describe('The response to send to the renter'),
        }),
      );

      const aiOutput = <any>await this.aiService.getResponseWithChatMemory(
        {
          input: renterMessage,
        },
        conversation.id,
        renterWaitingForTheInfoTemplate,
        6,
        LanguageModelsEnum.GPT_4,
        outputParser,
      );

      if (!aiOutput.isStatusUpdate) {
        return AiStrategyExecutionCode.DO_NOTHING;
      }

      return aiOutput.response;
    } catch (error) {
      console.error('Error in HandleRenterWaitingForTheInfoStrategy:', error);
    }
  }
}

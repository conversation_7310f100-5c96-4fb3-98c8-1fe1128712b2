import { Injectable } from '@nestjs/common';

import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { IntelligentEscalationService } from '../../../../../investor/property-question/intelligent-escalation.service';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { SlackCommunicationService } from '../../../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { AiStrategyExecutionCode } from '../../enums/ai-strategy-execution-code';
import { renterHasSeenPropertyTemplate } from './renter-has-seen-property.template';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';

@Injectable()
export class RenterHasSeenPropertyStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly slackService: SlackCommunicationService,
    private readonly propertyQuestionService: IntelligentEscalationService,
    private readonly aiService: AiService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const { city, address, state } = await property.location;
    const { bedrooms, propertyType } = await property.specifications;

    const output = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        propertyInfo: JSON.stringify({
          address,
          city,
          state,
          propertyType,
          numberOfBedrooms: bedrooms,
        }),
      },
      conversation.id,
      renterHasSeenPropertyTemplate,
      6,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    let parsedOutput: { response: string; propertyAddressConfirmed: boolean };

    try {
      parsedOutput = JSON.parse(output);
    } catch (error) {
      console.error('Failed to parse AI response in the "renter has seen property" strategy', error);

      return AiStrategyExecutionCode.DO_NOTHING;
    }

    if (parsedOutput.propertyAddressConfirmed) {
      await this.propertyQuestionService.escalateQuestion(
        property,
        renter,
        `${renter.user.name} reached out and mentioned that they have already seen the property and would like to know the next steps. Please inform them on how to proceed.`,
        'Application process',
      );

      this.slackService.sendMessageToConvosChannel('Question has been escalated to the owner 🙋', conversation);
    }

    return parsedOutput.response;
  }
}

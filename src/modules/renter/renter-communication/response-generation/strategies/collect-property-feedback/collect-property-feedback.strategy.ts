import { Injectable } from '@nestjs/common';

import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { collectPropertyFeedbackTemplate } from './prompt/collect-property-feedback.template';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { z } from 'zod';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';
import { FollowUpTypeEnum } from '../../../../../shared/communication/follow-up/enums/follow-up-type.enum';
import { OutboundCommunicationService } from '../../../../../shared/communication/outbound-communication/outbound-communication.service';
import { talloProductPricesMap } from '../../../../../shared/checkout/tallo-product-prices.map';
import { TalloProductsEnum } from '../../../../../shared/checkout/tallo-products.enum';

@Injectable()
export class CollectPostShowingFeedbackStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly followUpService: FollowUpService,
    private readonly outboundCommunicationService: OutboundCommunicationService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const owner = await property.owner;
    const parser = StructuredOutputParser.fromZodSchema(
      z.object({
        wantsToApply: z
          .boolean()
          .describe(`Flag if renter expressed the desire to continue with the application process.`),
        doesNotWantToContinue: z
          .boolean()
          .describe(
            `Flag if renter says the property is a bad feet, he is not interested, does not want to rent it, etc...`,
          ),
        propertyFeedback: z.string().describe(`Feedback about the property, renter's impression, etc...`),
        response: z.string().describe(`AI response to the renter's message`),
      }),
    );
    // TODO add feature to save feedback and show to owner
    const response = <any>await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        renterName: renter.user.name,
        applicationFee: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_FULL_PACKAGE),
        coSignerFee: talloProductPricesMap.get(TalloProductsEnum.APPLICATION_BASE_PACKAGE),
      },
      conversation.id,
      collectPropertyFeedbackTemplate,
      8,
      LanguageModelsEnum.GPT_4,
      parser,
    );

    if (response.wantsToApply) {
      await this.followUpService.deleteInvestorFollowUpsByInquiry(
        inquiry,
        (await owner.user).id,
        FollowUpTypeEnum.POST_SHOWING_INVESTOR_FOLLOW_UP,
      );
      const location = await property.location;
      await this.outboundCommunicationService.sendRenterWantsToApplyNotification(
        property,
        location,
        await owner.user,
        renter,
      );
    } else if (response.doesNotWantToContinue) {
      await this.followUpService.deleteInvestorFollowUpsByInquiry(
        inquiry,
        (await owner.user).id,
        FollowUpTypeEnum.POST_SHOWING_INVESTOR_FOLLOW_UP,
      );
    }

    return 'Collect property feedback: ' + response.response;
  }
}

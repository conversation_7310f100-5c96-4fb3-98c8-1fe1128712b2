import { Injectable } from '@nestjs/common';

import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { shareMorePhotosTemplate } from './prompts/share-more-photos.template';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ShareMorePhotosStrategy implements HandleRenterMessageStrategy {
  private readonly baseUrl = this.config.get('RENTER_APP_URL') + '/property/';

  constructor(
    private readonly aiService: AiService,
    private readonly config: ConfigService,
  ) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    _renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const propertyLink = this.baseUrl + property.id;
    const response = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        propertyLink,
      },
      conversation.id,
      shareMorePhotosTemplate,
      4,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    return 'Share Photos: ' + response;
  }
}

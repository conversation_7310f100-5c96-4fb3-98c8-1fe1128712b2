import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';
import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';

export const shareMorePhotosTemplate = `
<task>
<PERSON><PERSON> asks you to share more photos of the property.
You don't have any more photos to share at the moment, but you can share the property link with currently available photos.
You can mention that in case if owner adds more photos in the future, they will be available on the property page, but you don't have any more photos to share right now.
Explain that to the renter. If it's appropriate, offer to show the property in person.
</task>

<property_link>{propertyLink}</property_link>

<instructions>
${generalAiInstructionsTemplate}
${AiInstruction.DontSayWe}
${AiInstruction.PlainTextOutput}
</instructions>

${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

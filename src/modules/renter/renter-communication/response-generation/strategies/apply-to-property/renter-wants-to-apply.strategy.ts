import { Injectable } from '@nestjs/common';
import { AiService } from '../../../../../ai/ai.service';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { PropertyInquiryEventTypeEnum } from '../../../../../investor/property-inquiry/events/property-inquiry-event-type.enum';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { proposeToDoShowingFirstTemplate } from './prompt/propose-to-do-showing-first.template';
import { agreeToApplyToPropertyTemplate } from './prompt/agree-to-apply-to-property.template';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';
import { FollowUpTypeEnum } from '../../../../../shared/communication/follow-up/enums/follow-up-type.enum';
import { OutboundCommunicationService } from '../../../../../shared/communication/outbound-communication/outbound-communication.service';
import { User } from '../../../../../shared/user/entities/user.entity';

@Injectable()
export class RenterWantsToApplyStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly followUpService: FollowUpService,
    private readonly outboundCommunicationService: OutboundCommunicationService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const inquiryEvents = await PropertyInquiry.getEvents(inquiry);

    if (inquiryEvents.includes(PropertyInquiryEventTypeEnum.APPLICATION_INVITE_SENT)) {
      return 'Apply to property: Application invite has already been sent.';
    }

    let response: string;

    if (this.isShowingHappened(inquiryEvents)) {
      response = await this.sendApplicationInvite(
        renterMessage,
        conversation,
        inquiry,
        inquiryEvents,
        renter,
        property,
      );
    } else {
      response = await this.proposeToDoShowingFirst(renterMessage, conversation);
    }

    return 'Apply to property: ' + response;
  }

  private async sendApplicationInvite(
    renterMessage: string,
    conversation: Conversation,
    inquiry: PropertyInquiry,
    events: PropertyInquiryEventTypeEnum[],
    renter: Renter,
    property: Property,
  ): Promise<string> {
    if (events.includes(PropertyInquiryEventTypeEnum.APPLICATION_INVITE_SENT)) {
      return 'Application invite has already been sent.';
    } else {
      const owner = await property.owner;
      await this.followUpService.deleteInvestorFollowUpsByInquiry(
        inquiry,
        (await owner.user).id,
        FollowUpTypeEnum.POST_SHOWING_INVESTOR_FOLLOW_UP,
      );

      await this.followUpService.deleteRenterFollowUpsByType(
        conversation.id,
        FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
      );

      this.sendRenterWantsToApplyMessage(property, await owner.user, renter);

      return await this.aiService.getResponseWithChatMemory(
        {
          input: renterMessage,
        },
        conversation.id,
        agreeToApplyToPropertyTemplate,
        5,
        LanguageModelsEnum.GPT_4_MINI,
      );
    }
  }

  private async sendRenterWantsToApplyMessage(property: Property, recipient: User, renter: Renter): Promise<void> {
    const location = await property.location;
    this.outboundCommunicationService.sendRenterWantsToApplyNotification(property, location, recipient, renter);
  }

  private async proposeToDoShowingFirst(renterMessage: string, conversation: Conversation): Promise<string> {
    return await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
      },
      conversation.id,
      proposeToDoShowingFirstTemplate,
      5,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );
  }

  private isShowingHappened(inquiryEvents: PropertyInquiryEventTypeEnum[]) {
    return inquiryEvents.includes(PropertyInquiryEventTypeEnum.SHOWING_COMPLETED);
  }
}

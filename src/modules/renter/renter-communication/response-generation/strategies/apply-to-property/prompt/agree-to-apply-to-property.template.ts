import { AiInstruction, generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';
import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';

export const agreeToApplyToPropertyTemplate = `
<task>
You are a property manager for a rental property communicating with a potential renter.
<PERSON><PERSON> wants to apply for the property.

Your task is to make a message saying you will let the property owner know that renter is interested and
will let him know when owner will send him a link to the application.
</task>

<instructions>
${generalAiInstructionsTemplate}
${AiInstruction.UseOnlyPromptData}
${AiInstruction.DontSayWe}
${AiInstruction.PlainTextOutput}
- Do not prompt the renter to ask additional questions.
- Only use one sentence
- Adjust your tone to align with the rest of the conversation
</instructions>

${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

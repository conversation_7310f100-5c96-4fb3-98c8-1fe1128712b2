import { AiService } from '../../../../../ai/ai.service';
import { Renter } from '../../../../renter/renter.entity';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { Injectable } from '@nestjs/common';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { AiStrategyExecutionCode } from '../../enums/ai-strategy-execution-code';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { handleDiscriminationConcernsTemplate } from './prompt/handle-discrimination-concerns.template';

@Injectable()
export class HandleDiscriminationConcernsStrategy implements HandleRenterMessageStrategy {
  constructor(private readonly aiService: AiService) {}

  async execute(
    renterMessage: string,
    _inquiry: PropertyInquiry,
    _renter: Renter,
    _property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const response = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        executionCode: AiStrategyExecutionCode.DO_NOTHING,
      },
      conversation.id,
      handleDiscriminationConcernsTemplate,
      5,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    if (response === AiStrategyExecutionCode.DO_NOTHING) {
      return AiStrategyExecutionCode.DO_NOTHING;
    } else {
      return 'Discrimination concerns: ' + response;
    }
  }
}

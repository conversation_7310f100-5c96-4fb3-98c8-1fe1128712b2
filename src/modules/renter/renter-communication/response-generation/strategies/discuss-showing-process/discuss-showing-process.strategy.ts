import { Injectable } from '@nestjs/common';

import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { discussShowingProcessTemplate } from './prompts/discuss-showing-process.template';
import { AiStrategyExecutionCode } from '../../enums/ai-strategy-execution-code';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { IntelligentEscalationService } from '../../../../../investor/property-question/intelligent-escalation.service';

interface DiscussShowingProcessAiOutput {
  escalateVirtualTourQuestion: boolean;
  response: string;
}

@Injectable()
export class DiscussShowingProcessStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly propertyQuestionService: IntelligentEscalationService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation: Conversation,
  ): Promise<string> {
    const showingRequest = await inquiry.showingRequest;
    const showing = await showingRequest?.showing;
    const showingAgent = await showing?.showingAgent;

    const allowsInPersonShowings = property.allowsInPersonTours;
    const allowsVirtualTours = property.allowsVirtualTours;

    let aiOutput: DiscussShowingProcessAiOutput | undefined;

    try {
      aiOutput = JSON.parse(
        await this.aiService.getResponseWithChatMemory(
          {
            allowsInPersonShowings,
            allowsVirtualTours,
            input: renterMessage,
            executionCode: AiStrategyExecutionCode.EXECUTED_WITHOUT_ANSWER,
            showingAgent: {
              firstName: showingAgent?.firstName,
              phone: showingAgent?.phone,
            },
          },
          conversation.id,
          discussShowingProcessTemplate,
          8,
          LanguageModelsEnum.GPT_4,
        ),
      );
    } catch (error) {
      console.error('Error discussing showing process:', error);

      return AiStrategyExecutionCode.DO_NOTHING;
    }

    if (aiOutput.response === AiStrategyExecutionCode.EXECUTED_WITHOUT_ANSWER) {
      return AiStrategyExecutionCode.EXECUTED_WITHOUT_ANSWER;
    }

    if (aiOutput.escalateVirtualTourQuestion) {
      const location = await property.location;
      await this.propertyQuestionService.escalatePropertyQuestion(
        property,
        `Can renters do virtual tours at ${location.address}?`,
        renter,
      );
    }

    return 'Showing process question: ' + aiOutput.response;
  }
}

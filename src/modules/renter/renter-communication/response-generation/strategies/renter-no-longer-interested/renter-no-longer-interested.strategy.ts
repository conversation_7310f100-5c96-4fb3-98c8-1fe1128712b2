import { Injectable } from '@nestjs/common';
import { AiService } from '../../../../../ai/ai.service';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { PropertyInquiryService } from '../../../../../investor/property-inquiry/property-inquiry.service';
import { Property } from '../../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../../shared/communication/conversation/entities/conversation.entity';
import { RentStageEnum } from '../../../../../shared/communication/conversation/enums/rent-stage.enum';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';
import { Renter } from '../../../../renter/renter.entity';
import { HandleRenterMessageStrategy } from '../../interfaces/handle-renter-message.strategy';
import { wishRenterGoodLuckTemplate } from './prompts/wish-renter-good-luck.template';

@Injectable()
export class RenterNoLongerInterestedStrategy implements HandleRenterMessageStrategy {
  constructor(
    private readonly aiService: AiService,
    private readonly followUpService: FollowUpService,
    private readonly propertyInquiryService: PropertyInquiryService,
  ) {}

  async execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    _property: Property,
    conversation: Conversation,
  ): Promise<string> {
    await this.followUpService.deleteRenterFollowUpsByStatus(conversation.id);

    const response = await this.aiService.getResponseWithChatMemory(
      {
        input: renterMessage,
        renterName: renter.user.name,
      },
      conversation.id,
      wishRenterGoodLuckTemplate,
      3,
      LanguageModelsEnum.CLAUDE_4_SONNET,
    );

    await this.propertyInquiryService.updateRentStage(inquiry.id, RentStageEnum.NO_LONGER_INTERESTED);

    return 'Wrap up conversation: ' + response;
  }
}

import { generalAiInstructionsTemplate } from '../../../../../../ai/prompts/ai-instructions';
import { PromptVariable } from '../../../../../../ai/enums/prompt-variable.enum';

export const wishRenterGoodLuckTemplate = `
<task>
Imagine you are a property manager and you are talking to a potential renter.
It seems like he is no longer interested in the property or property does not work for them.
Thank him and wish luck.
Try to do it in a way that will make him feel good about the conversation and the property.
Do it in one or two sentences.
</task>

${PromptVariable.RenterName}

<instructions>
${generalAiInstructionsTemplate}
</instructions>

${PromptVariable.ChatHistory}
${PromptVariable.RenterMessage}
`;

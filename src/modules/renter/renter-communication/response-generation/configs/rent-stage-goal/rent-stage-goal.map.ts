import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { RentStageEnum } from '../../../../../shared/communication/conversation/enums/rent-stage.enum';
import { ConversationGoal } from './conversation.goal';
import { getInitialContactGoal } from './get-initial-contact-goal';
import { getNoLongerInterestedGoal } from './get-no-longer-interested-goal';
import { getPropertyRentedOutGoal } from './get-property-rented-out-goal';
import { getRescheduleRequestedGoal } from './get-reschedule-requested-goal';
import { getShowingCanceledByOwnerGoal } from './get-showing-canceled-by-owner-goal';
import { getShowingCanceledByRenterGoal } from './get-showing-canceled-by-renter.goal';
import { getShowingCompletedGoal } from './get-showing-completed-goal';
import { getShowingConfirmedGoal } from './get-showing-confirmed-goal';
import { getShowingDeclinedGoal } from './get-showing-declined-goal';
import { getShowingRequestIgnoredGoal } from './get-showing-request-ignored-goal';
import { getShowingRequestedGoal } from './get-showing-requested-goal';
import { getApplicationSentGoal } from './get-application-sent.goal';
import { getRequirementsNotMetGoal } from './get-requirements-not-met-goal';

export const rentStageGoalMap = new Map<RentStageEnum, (inquiry: PropertyInquiry) => ConversationGoal>([
  [RentStageEnum.INITIAL_CONTACT, getInitialContactGoal],
  [RentStageEnum.SHOWING_REQUESTED, getShowingRequestedGoal],
  [RentStageEnum.SHOWING_DECLINED, getShowingDeclinedGoal],
  [RentStageEnum.REQUIREMENTS_NOT_MET, getRequirementsNotMetGoal],
  [RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER, getRescheduleRequestedGoal],
  [RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_RENTER, getRescheduleRequestedGoal],
  [RentStageEnum.SHOWING_CONFIRMED, getShowingConfirmedGoal],
  [RentStageEnum.SHOWING_COMPLETED, getShowingCompletedGoal],
  [RentStageEnum.SHOWING_CANCELLED_BY_RENTER, getShowingCanceledByRenterGoal],
  [RentStageEnum.SHOWING_CANCELLED_BY_OWNER, getShowingCanceledByOwnerGoal],
  [RentStageEnum.SHOWING_REQUEST_IGNORED_BY_OWNER, getShowingRequestIgnoredGoal],
  [RentStageEnum.STOPPED_PROPERTY_RENTED_OUT, getPropertyRentedOutGoal],
  [RentStageEnum.APPLICATION_INVITE_SENT, getApplicationSentGoal],
  [RentStageEnum.APPLICATION_IN_PROGRESS, getApplicationSentGoal],
  [RentStageEnum.APPLICATION_COMPLETED, getApplicationSentGoal],
  [RentStageEnum.APPLICATION_ACCEPTED, getApplicationSentGoal],
  [RentStageEnum.APPLICATION_REJECTED, getApplicationSentGoal],
  [RentStageEnum.NO_LONGER_INTERESTED, getNoLongerInterestedGoal],
]);

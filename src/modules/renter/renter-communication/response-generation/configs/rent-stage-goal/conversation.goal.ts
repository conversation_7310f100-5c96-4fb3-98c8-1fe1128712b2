export enum ConversationGoal {
  QUALIFY_RENTER_LEAD = 'Answer renter inquiries and collect qualification information',
  SCHEDULE_SHOWING = 'Guide potential renters towards scheduling a property tour',
  RESCHEDULE_SHOWING = 'Guide potential renters towards rescheduling a property tour',
  KEEP_RENTER_INFORMED = 'Keep the renter informed about the progress of their inquiry',
  MAKE_RENTER_MAKE_IT_TO_THE_SHOWING = 'Guide the renter towards making it to the showing',
  FIGURE_OUT_IF_RENTER_IS_INTERESTED = 'Figure out if the renter is interested in the property',
  SHUT_DOWN_CONVERSATION = 'Politely conclude the conversation',
  MAINTAIN_CONVERSATION_IF_NEEDED = 'Maintain the conversation if needed',
  GUIDE_RENTER_FINISH_APPLICATION = 'Guide the renter towards submitting an application',
  GUIDE_RENTER_THROUGH_APPLICATION = 'Guide the renter through the application process',
}

import { NextStep } from '../../enums/next-step';
import { defaultStrategies } from './default-strategies';

export function getShowingConfirmedStageStrategies(): NextStep[] {
  return [
    ...defaultStrategies,
    NextStep.SHARE_REQUIREMENTS,
    NextStep.RESCHEDULE_SHOWING,
    NextStep.CHECK_IF_SHOWING_IS_STILL_CONFIRMED,
    NextStep.HANDLE_SHOWING_ATTENDANCE_ISSUES,
    NextStep.DISCUSS_SHOWING_PROCESS,
    NextStep.CANCEL_SHOWING,
    NextStep.RENTER_HAS_SEEN_PROPERTY,
    NextStep.RENTER_CONFIRMS_SHOWING,
    NextStep.ANSWER_APPLICATION_PROCESS_QUESTION,
    NextStep.RENTER_WANTS_TO_APPLY_FOR_THE_PROPERTY,
  ];
}

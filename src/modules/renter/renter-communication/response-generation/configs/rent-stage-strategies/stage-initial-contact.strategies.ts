import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { CompanySettings } from '../../../../../shared/company/entities/company-settings.entity';
import { NextStep } from '../../enums/next-step';
import { addObjectiveRelatedNextSteps } from './add-objective-related-next-stepts.function';
import { defaultStrategies } from './default-strategies';

export function getInitialStageStrategies(inquiry: PropertyInquiry, companySettings: CompanySettings): NextStep[] {
  const strategies = [
    ...defaultStrategies,
    NextStep.SHARE_REQUIREMENTS,
    NextStep.WELCOME_USER,
    NextStep.SCHEDULE_SHOWING,
    NextStep.DISCUSS_SHOWING_PROCESS,
    NextStep.WISH_GOOD_LUCK,
    NextStep.RENTER_HAS_SEEN_PROPERTY,
    NextStep.SHARE_MORE_PHOTOS,
    NextStep.DISCUSS_MOVE_IN_DATE,
    NextStep.WRONG_PHONE_NUMBER,
    NextStep.RENTER_WANTS_TO_APPLY_FOR_THE_PROPERTY,
    NextStep.HANDLE_RENTER_WAITING_FOR_THE_INFO,
  ];

  if (companySettings.advancedRenterScreening) {
    addObjectiveRelatedNextSteps(strategies);
  }

  return strategies;
}

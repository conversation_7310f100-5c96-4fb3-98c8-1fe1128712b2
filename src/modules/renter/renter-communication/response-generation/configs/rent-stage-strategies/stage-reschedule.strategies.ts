import { NextStep } from '../../enums/next-step';
import { defaultStrategies } from './default-strategies';

export function getRescheduleStageStrategies(): NextStep[] {
  return [
    ...defaultStrategies,
    NextStep.SHARE_REQUIREMENTS,
    NextStep.CANCEL_SHOWING,
    NextStep.RESCHEDULE_SHOWING,
    NextStep.DISCUSS_SHOWING_PROCESS,
    NextStep.ANSWER_WHY_SHOWING_REQUEST_PENDING,
    NextStep.RENTER_HAS_SEEN_PROPERTY,
    NextStep.RENTER_WANTS_TO_APPLY_FOR_THE_PROPERTY,
  ];
}

import { PropertyInquiryEventTypeEnum } from '../../../../../investor/property-inquiry/events/property-inquiry-event-type.enum';
import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { CompanySettings } from '../../../../../shared/company/entities/company-settings.entity';
import { NextStep } from '../../enums/next-step';
import { addObjectiveRelatedNextSteps } from './add-objective-related-next-stepts.function';
import { defaultStrategies } from './default-strategies';

export async function getNoLongerInterestedStageStrategies(
  inquiry: PropertyInquiry,
  companySettings: CompanySettings,
): Promise<NextStep[]> {
  const steps = [
    ...defaultStrategies,
    NextStep.SHARE_REQUIREMENTS,
    NextStep.DISCUSS_SHOWING_PROCESS,
    NextStep.WISH_GOOD_LUCK,
    NextStep.SHARE_MORE_PHOTOS,
    NextStep.DISCUSS_MOVE_IN_DATE,
    NextStep.ANSWER_APPLICATION_PROCESS_QUESTION,
  ];

  if (await PropertyInquiry.didEventHappen(inquiry, PropertyInquiryEventTypeEnum.SHOWING_COMPLETED)) {
    return steps;
  }

  if (await PropertyInquiry.didEventHappen(inquiry, PropertyInquiryEventTypeEnum.REQUESTED_SHOWING)) {
    steps.push(NextStep.RESCHEDULE_SHOWING);
  } else {
    steps.push(NextStep.SCHEDULE_SHOWING);

    if (companySettings.advancedRenterScreening) {
      addObjectiveRelatedNextSteps(steps);
    }
  }

  return steps;
}

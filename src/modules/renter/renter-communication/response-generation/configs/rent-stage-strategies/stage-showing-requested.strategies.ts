import { NextStep } from '../../enums/next-step';
import { defaultStrategies } from './default-strategies';

export function getShowingRequestedStageStrategies(): NextStep[] {
  return [
    ...defaultStrategies,
    NextStep.SHARE_REQUIREMENTS,
    NextStep.ANSWER_WHY_SHOWING_REQUEST_PENDING,
    NextStep.RESCHEDULE_SHOWING,
    NextStep.DISCUSS_SHOWING_PROCESS,
    NextStep.CANCEL_SHOWING,
    NextStep.RENTER_HAS_SEEN_PROPERTY,
    NextStep.SHARE_PROPERTY_LINK,
    NextStep.RENTER_WANTS_TO_APPLY_FOR_THE_PROPERTY,
  ];
}

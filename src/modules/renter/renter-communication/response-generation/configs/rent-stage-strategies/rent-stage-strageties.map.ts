import { PropertyInquiry } from '../../../../../investor/property-inquiry/property-inquiry.entity';
import { RentStageEnum } from '../../../../../shared/communication/conversation/enums/rent-stage.enum';
import { CompanySettings } from '../../../../../shared/company/entities/company-settings.entity';
import { NextStep } from '../../enums/next-step';
import { getInitialStageStrategies } from './stage-initial-contact.strategies';
import { getNoLongerInterestedStageStrategies } from './stage-no-longer-interested.strategies';
import { getRescheduleStageStrategies } from './stage-reschedule.strategies';
import { getShowingCanceledByOwnerStageStrategies } from './stage-showing-canceled-by-owner.strategies';
import { getShowingCanceledByRenterStageStrategies } from './stage-showing-canceled-by-renter.strategies';
import { getShowingCompletedStageStrategies } from './stage-showing-completed.strategies';
import { getShowingConfirmedStageStrategies } from './stage-showing-confirmed.strategies';
import { getShowingDeclinedStageStrategies } from './stage-showing-declined.strategies';
import { getShowingIgnoredByOwnerStageStrategies } from './stage-showing-ignored-by-the-owner.strategies';
import { getShowingRequestedStageStrategies } from './stage-showing-requested.strategies';
import { getPropertyRentedOutStageStrategies } from './stage-stopped-rented-out.strategies';
import { getApplicationInviteSentStrategies } from './stage-application-invite-sent.strategies';
import { getApplicationInProgressStrategies } from './stage-application-in-progress.strategies';
import { getApplicationCompletedStrategies } from './stage-application-completed.strategies';
import { getApplicationAcceptedStrategies } from './stage-application-accepted.strategies';
import { getApplicationRejectedStrategies } from './stage-application-rejected.strategies';
import { getRequirementsNotMetStrategies } from './stage-requirements-not-met.strategies';

export const rentStageStrategiesMap = new Map<
  RentStageEnum,
  (inquiry: PropertyInquiry, companySettings: CompanySettings) => NextStep[] | Promise<NextStep[]>
>([
  [RentStageEnum.INITIAL_CONTACT, getInitialStageStrategies],
  [RentStageEnum.SHOWING_REQUESTED, getShowingRequestedStageStrategies],
  [RentStageEnum.SHOWING_DECLINED, getShowingDeclinedStageStrategies],
  [RentStageEnum.REQUIREMENTS_NOT_MET, getRequirementsNotMetStrategies],
  [RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_OWNER, getRescheduleStageStrategies],
  [RentStageEnum.SHOWING_RESCHEDULE_REQUESTED_BY_RENTER, getRescheduleStageStrategies],
  [RentStageEnum.SHOWING_CONFIRMED, getShowingConfirmedStageStrategies],
  [RentStageEnum.SHOWING_COMPLETED, getShowingCompletedStageStrategies],
  [RentStageEnum.SHOWING_CANCELLED_BY_RENTER, getShowingCanceledByRenterStageStrategies],
  [RentStageEnum.SHOWING_CANCELLED_BY_OWNER, getShowingCanceledByOwnerStageStrategies],
  [RentStageEnum.SHOWING_REQUEST_IGNORED_BY_OWNER, getShowingIgnoredByOwnerStageStrategies],
  [RentStageEnum.STOPPED_PROPERTY_RENTED_OUT, getPropertyRentedOutStageStrategies],
  [RentStageEnum.APPLICATION_INVITE_SENT, getApplicationInviteSentStrategies],
  [RentStageEnum.APPLICATION_IN_PROGRESS, getApplicationInProgressStrategies],
  [RentStageEnum.APPLICATION_COMPLETED, getApplicationCompletedStrategies],
  [RentStageEnum.APPLICATION_ACCEPTED, getApplicationAcceptedStrategies],
  [RentStageEnum.APPLICATION_REJECTED, getApplicationRejectedStrategies],
  [RentStageEnum.NO_LONGER_INTERESTED, getNoLongerInterestedStageStrategies],
]);

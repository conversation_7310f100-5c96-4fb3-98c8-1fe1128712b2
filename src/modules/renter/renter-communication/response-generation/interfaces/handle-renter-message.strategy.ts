import { Renter } from '../../../renter/renter.entity';
import { Property } from '../../../../investor/property/property/entities/property.entity';
import { Conversation } from '../../../../shared/communication/conversation/entities/conversation.entity';
import { PropertyInquiry } from '../../../../investor/property-inquiry/property-inquiry.entity';

export interface HandleRenterMessageStrategy {
  execute(
    renterMessage: string,
    inquiry: PropertyInquiry,
    renter: Renter,
    property: Property,
    conversation?: Conversation,
  ): Promise<string>;
}

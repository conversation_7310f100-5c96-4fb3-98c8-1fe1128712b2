import { DateUtils } from '../../../../../utils/date.utils';

export function getAvailabilityPromptInstruction(
  currentDateInCity: Date,
  desiredLeasingDate: Date | null | undefined,
): string {
  const isPropertyAvailableAtm = isPropertyAvailableForMoveIn(desiredLeasingDate, currentDateInCity);

  if (isPropertyAvailableAtm) {
    return 'Property is currently available for move-in.';
  }

  const availableSinceInAiReadableFormat = DateUtils.toAiReadableFormat(desiredLeasingDate, { skipTime: true });

  return `Property available for move-in starting: "${availableSinceInAiReadableFormat}".`;
}

function isPropertyAvailableForMoveIn(availableSince: Date, currentDateInCity: Date): boolean {
  if (!availableSince) {
    return true;
  }

  const currentDate = new Date(currentDateInCity);
  currentDate.setHours(0, 0, 0, 0);

  const availableDate = new Date(availableSince);
  availableDate.setHours(0, 0, 0, 0);

  return availableDate < currentDate || availableDate.getTime() === currentDate.getTime();
}

/**
 * NextStep Enum - AI Conversation Flow Management
 *
 * This enum defines all possible conversation scenarios that the AI can identify and respond to
 * when analyzing renter communications. The AI uses these steps to determine the appropriate
 * response strategy based on the current dialog context, conversation stage, and renter intent.
 *
 * Key Features:
 * - Conversation Classification: Each enum value represents a specific conversation scenario
 * - AI Decision Making: The AI selects the most appropriate next step based on message analysis
 * - Response Generation: Each step triggers tailored response templates and business logic
 * - Stage-Aware Processing: Steps consider the current conversation stage (initial inquiry,
 *   showing scheduled, post-showing, application phase, etc.)
 *
 * Common Workflow Categories:
 * - Property Information: Questions about property details, amenities, lease terms
 * - Showing Management: Scheduling, rescheduling, confirming, or canceling property viewings
 * - Application Process: Requirements, credit checks, qualification discussions
 * - Screening Questions: Income, credit score, move-in date collection
 * - Issue Resolution: Handling concerns, discrimination issues, technical problems
 * - Communication Management: Channel switching, human handoff requests
 * - Business Logic: Declined/unqualified renter handling, property availability updates
 *
 * Usage:
 * The AI analyzes incoming renter messages and selects the most appropriate NextStep scenario.
 * This drives the conversation flow, determines response content, and triggers any necessary
 * backend actions (showing booking, application creation, follow-up scheduling, etc.).
 */
export enum NextStep {
  ANSWER_PROPERTY_QUESTION = `Discuss property characteristics (question about property, its characteristics, lease terms and conditions, lease duration, pets, amenities, payments, price negotiation, rent adjustments, discounts, move-in specials, other renters touring/applying to the space, competition for the property, property management, who manages the property, property management company, landlord, homeowner management, etc...)`,
  ANSWER_LEGAL_QUESTION = 'Answer legal question (legislation related question)',
  SHARE_REQUIREMENTS = 'Share requirements (use when renter asks about requirements to rent the property, etc.)',
  ANSWER_SCAM_QUESTION = `Discuss if this is a scam question (use this when renter is suspicious about the property, company legitimacy, or asks if it's a scam, if property is real, "are you real?" in the context of authenticity/legitimacy, etc. Do NOT use for AI/bot detection questions)`,
  ANSWER_PERSONAL_QUESTION = 'Discuss personal matters (renter asks about your personal life, etc...)',
  WRONG_PHONE_NUMBER = 'AI reached out to the wrong phone number (renter indicates that we, the AI, contacted the wrong phone number)',
  ANSWER_WHY_SHOWING_REQUEST_PENDING = `Discuss showing request status (renter wants to know the current status of the showing request, if showing happening, says "let me know about showing" that was scheduled, etc...)`,
  ANSWER_APPLICATION_PROCESS_QUESTION = `Answer to application process question (renter is asking about the application process, asks if this is a hard or soft credit pull, if his credit will be impacted from application checks, what's co-signers role, etc...)`,
  RENTER_WANTS_TO_APPLY_FOR_THE_PROPERTY = `Renter wants to apply (renter says he would like to apply, wants to start the application, etc...)`,
  GENERAL_CONVERSATION = 'General conversation question (renter started to talk about topics not related to the property, just trying to talk to you)',
  CANCEL_SHOWING = 'Cancel showing (use when it is clear that the current scheduled showing will NOT happen: renter wants to cancel the showing, no longer interested, changed his mind, found another place, is unable to attend due to illness/circumstances, or when renter indicates they will contact later to reschedule - meaning the current showing must be canceled to prevent automated reminders. Select this whenever the current showing cannot proceed as scheduled, regardless of future rescheduling intentions)',
  DISCUSS_B2B_PARTNERSHIP = 'Discuss B2B partnership (renter is interested in a business partnership, etc...)',
  DISCUSS_SUBLEASE = 'Discuss sublease (renter is interested in subleasing the property, etc...)',
  CHECK_IF_SHOWING_IS_STILL_CONFIRMED = 'Check if showing is still confirmed (renter already booked a showing and wants general confirmation that the showing is still happening, asks "is the showing still on?" or "are we still meeting?", or sends Zillow auto-generated messages like "I am interested in this rental and would like to schedule a viewing. Please let me know when this would be possible" when a showing is already booked. Use this for simple status checks WITHOUT any specific logistical issues mentioned. NOT when there are specific issues like lockbox codes, agent problems, or timing concerns)',
  HANDLE_SHOWING_ATTENDANCE_ISSUES = 'Handle showing attendance issues (renter is late to the same-day showing, has arrived at the property saying "I\'m here", "we\'ve arrived", wants to come earlier by minutes/hours on the same day like "I can come 30 mins earlier if that\'s okay", OR needs assistance with same-day showing logistics like lockbox codes, access issues, showing agent problems that affect attendance. Also use when renter asks "should I still go?" due to specific logistical problems. Use this ONLY for same-day timing adjustments and logistics issues, NOT for requests to move to a different day)',
  RENTER_CONFIRMS_SHOWING = 'Confirm showing (renter was asked if he will attend a showing and renter confirms it, says "yes", "I will be there", etc...)',
  ANSWER_SECTION_8_QUESTION = 'Discuss Section 8 (renter asks if we accept section 8 vouchers, Housing Choice Voucher Program, etc)',
  WELCOME_USER = 'Welcome user (add this step when user just started the conversation)',
  SCHEDULE_SHOWING = `Prompt renter to schedule showing, schedule showing, discuss showing time (renter wants to see the property, schedule a showing, agrees to proposed showing time/date, finding a time to visit, asks about closest time to visit, continues scheduling, confirms attendance, agrees or declines showing time, responds positively to proposed time with phrases like "yes, thank you", etc...)`,
  RESCHEDULE_SHOWING = `Reschedule showing (renter or property owner want to reschedule a showing to a different day, renter suggests coming on a different day like "tomorrow" or "next week", renter confirms proposed showing time works for them, says "yes" to proposed showing time, negotiating time, conversation about rescheduling continues, etc. Use this for ANY request to move the showing to a different day, regardless of how close to the original showing time. ALSO use this when a renter previously cancelled a showing and now wants to schedule a new showing time, as this represents rescheduling their viewing of the property)`,
  RENTER_HAS_SEEN_PROPERTY = `Renter has seen the property (renter says he has seen the property and wants to proceed with the application, continues the conversation about the property he already seen, etc...)`,
  WISH_GOOD_LUCK = 'Wish good luck (no longer interested, changed his mind, already found a place, saying he does not want it, etc.)',
  ASK_IF_RENTER_HAS_REQUIREMENTS_QUESTION = 'Ask if renter wants to know the requirements',
  DO_NOTHING = `Do nothing (select this when the conversation has clearly concluded, when the renter indicates they will initiate the next contact themselves, or when no immediate response is needed. Examples: renter thanks you after the AI has promised to do something that will take time, renter reacts with an emoji like 'Loved "..."' or 'Liked "..." etc. Do NOT use for substantive messages that contain actual words like "love it", "loved the place", etc. that express genuine interest or intent. CRITICAL: Do NOT use when there is a confirmed showing that cannot happen and needs to be canceled - in such cases, use cancel showing instead)`,
  DO_YOU_HAVE_OTHER_PROPERTIES = 'Answer question about other properties (renter asks "do you have other properties (or units)?", "do you have similar properties", etc...)',
  UNDEFINED = 'Undefined (return when none of the steps above are applicable)',
  HANDLE_DECLINED_RENTER = 'Handle declined renter (renter was declined but continues writing)',
  HANDLE_UNQUALIFIED_RENTER = 'Handle unqualified renter (renter is not qualified but continues writing, expresses frustration or complaints about requirements like credit score, income, or other qualification criteria after being evaluated, asks rhetorical questions like "why is credit such a big deal", vents or provides sob stories about their situation)',
  HANDLE_CANCELED_RENTER = 'Renter had a meeting booked, but then it was canceled',
  NOTIFY_PROPERTY_RENTED_OUT = 'Answer lead (renter wants to see the property, continues the dialog)',
  DISCUSS_SHOWING_PROCESS = "Discuss showing process (renter asks about the showing process, what to expect, who will handle it, asks about available tour types like in-person, virtual, etc...). Don't use for scheduling a showing",
  SHARE_MORE_PHOTOS = 'Share more photos (renter asks for more photos of the property, etc...)',
  ANSWER_TALLO_QUESTION = 'Discuss our leasing agency (renter asks "who are you?", "give me your website", "do I have to pay you", "what is your company?", etc...)',
  DISCUSS_CREDIT_SCORE = 'Discuss credit score (renter answers the credit score question, continues the conversation about the credit score, responds to questions about applying with a co-signer if their credit score is low, discusses co-signer options, etc...)',
  DISCUSS_INCOME = 'Discuss income (renter answers the income question, continues the conversation about the income, etc...)',
  DISCUSS_MOVE_IN_DATE = 'Discuss move in date (renter wants to know if the property is available, when it will be available, renter answers to any question about the move in date, continues the conversation about the move in date, confirms or declines mentioned move in date, says what he thinks about mentioned move in date, etc...)',
  HANDLE_DISCRIMINATION_CONCERNS = 'Handle discrimination concerns (renter expresses concerns about discrimination, fair housing violation, etc...)',
  SHARE_PROPERTY_LINK = 'Share property link (renter asks for the property link, forgot which property it is, etc...)',
  SWITCH_COMMUNICATION_CHANNEL = 'Switch communication channel (renter asks to be contacted by email or SMS. Says you could write to him by some number, email, etc... If renter wants a call select "Speak to a human" step instead)',
  COLLECT_PROPERTY_FEEDBACK = 'Collect post showing feedback (renter gives feedback about the property, AI asked about the property after the showing, etc...)',
  SPEAK_TO_HUMAN = 'Speak to a human (renter explicitly asks to speak with a real person, human representative, wants to talk on the phone, asks to call him, expresses frustration with AI, or asks if you are AI/bot/automated system)',
  HANDLE_RENTER_WAITING_FOR_THE_INFO = 'Handler renter waiting for AI to find certain information (pick this if AI could not answer to renters question and promised to check it, and renter is waiting for the answer, saying ok to wait, or if renters asks about the status of the answer, etc)',
  HANDLE_SHOWING_REQUEST_IGNORED_BY_THE_OWNER = 'Handle showing request ignored by the owner (pick this if showing was not confirmed by the owner and renter is still writing, wanting to reschedule, know why that happened, etc...)',
}

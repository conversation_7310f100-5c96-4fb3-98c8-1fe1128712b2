import { Controller, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { RenterCommunicationService } from './renter-communication.service';
import { RolesGuard } from '../../shared/auth/guards/roles.guard';
import { BasicAuth } from '../../shared/auth/decorators/basic-auth.decorator';

@ApiTags('renter-communication')
@Controller('renter-communication')
@UseGuards(RolesGuard)
@ApiBearerAuth()
export class RenterCommunicationController {
  constructor(private readonly renterCommunicationService: RenterCommunicationService) {}

  @BasicAuth()
  @Put('answer/all')
  @ApiOkResponse({ description: 'Answered all pending messages' })
  async answerToRenter(): Promise<void> {
    await this.renterCommunicationService.answerToAllUnansweredMessages();
  }
}

import { Module } from '@nestjs/common';
import { ApplicationBundleModule } from '../../shared/application/application-bundle/application-bundle.module';
import { ApplicationModule } from '../../shared/application/application/application.module';
import { RenterApplicationController } from './application/renter-application.controller';
import { RenterApplicationService } from './application/renter-application.service';
import { PropertyInquiryModule } from '../../investor/property-inquiry/property-inquiry.module';
import { TransUnionModule } from '../../shared/background-check/trans-union/trans-union.module';
import { RenterModule } from '../renter/renter.module';
import { ApplicationInviteModule } from '../../shared/application/application-invite/application-invite.module';
import { RenterApplicationBundleController } from './application-bundle/renter-application-bundle-controller';
import { RenterApplicationInviteController } from './application-invite/renter-application-invite.controller';
import { RenterApplicationBundleService } from './application-bundle/renter-application-bundle.service';
import { RenterApplicationInvitesService } from './application-invite/renter-application-invite.service';
import { RenterApplicationTransUnionController } from './application-trans-union/renter-application-trans-union.controller';
import { RenterApplicationTransUnionService } from './application-trans-union/renter-application-trans-union.service';
import { OutboundCommunicationModule } from '../../shared/communication/outbound-communication/outbound-communication.module';
import { ConversationModule } from '../../shared/communication/conversation/conversetion.module';
import { UserModule } from '../../shared/user/user.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Application } from '../../shared/application/application/application.entity';
import { ApplicationBundle } from '../../shared/application/application-bundle/application-bundle.entity';
import { ApplicationInvite } from '../../shared/application/application-invite/application-invite.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Application, ApplicationInvite, ApplicationBundle]),
    ApplicationBundleModule,
    ApplicationModule,
    PropertyInquiryModule,
    TransUnionModule,
    RenterModule,
    ApplicationInviteModule,
    OutboundCommunicationModule,
    ConversationModule,
    UserModule,
  ],
  controllers: [
    RenterApplicationController,
    RenterApplicationBundleController,
    RenterApplicationInviteController,
    RenterApplicationTransUnionController,
  ],
  providers: [
    RenterApplicationService,
    RenterApplicationBundleService,
    RenterApplicationInvitesService,
    RenterApplicationTransUnionService,
  ],
  exports: [],
})
export class RenterApplicationModule {}

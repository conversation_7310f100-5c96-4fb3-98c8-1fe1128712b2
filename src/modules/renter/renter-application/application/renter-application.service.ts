import { Injectable } from '@nestjs/common';
import { File } from 'src/modules/shared/file/entities/file.entity';
import { ApplicationService } from '../../../shared/application/application/application.service';
import { ApplicationStatus } from '../../../shared/application/application/enums/application-status.enum';
import { ApplicationRenterDetails } from '../../../shared/application/application/renter-info/renter-details/application-renter-details.entity';
import { SaveApplicationRenterDetailsCommand } from '../../../shared/application/application/renter-info/renter-details/save-application-renter-details.command';
import { ApplicationRenterEmploymentInfo } from '../../../shared/application/application/renter-info/renter-employment-info/application-renter-employment-info.entity';
import { SaveApplicationRenterEmploymentInfoCommand } from '../../../shared/application/application/renter-info/renter-employment-info/save-application-renter-employment-info.command';
import { ApplicationRenterReference } from '../../../shared/application/application/renter-info/renter-reference/application-renter-reference.entity';
import { SaveApplicationRenterReferenceCommand } from '../../../shared/application/application/renter-info/renter-reference/save-application-renter-reference.command';
import { ApplicationRenterResidenceInfo } from '../../../shared/application/application/renter-info/renter-residence-info/application-renter-residence-info.entity';
import { SaveApplicationRenterResidenceInfoCommand } from '../../../shared/application/application/renter-info/renter-residence-info/save-application-renter-residence-info.command';
import { ApplicationBundleService } from '../../../shared/application/application-bundle/application-bundle.service';

@Injectable()
export class RenterApplicationService {
  constructor(
    private readonly applicationService: ApplicationService,
    private readonly bundleService: ApplicationBundleService,
  ) {}

  async savePhotoId(applicationId: string, file: Express.Multer.File): Promise<File> {
    return this.applicationService.savePhotoId(applicationId, file);
  }

  async deletePhotoId(applicationId: string, fileId: string): Promise<void> {
    return this.applicationService.deletePhotoId(applicationId, fileId);
  }

  async saveProofOfIncome(applicationId: string, file: Express.Multer.File): Promise<File> {
    return this.applicationService.saveProofOfIncome(applicationId, file);
  }

  async deleteProofOfIncome(applicationId: string, fileId: string): Promise<void> {
    return this.applicationService.deleteProofOfIncome(applicationId, fileId);
  }

  async saveAdditionalDocument(applicationId: string, file: Express.Multer.File): Promise<File> {
    return this.applicationService.saveAdditionalDocument(applicationId, file);
  }

  async deleteAdditionalDocument(applicationId: string, fileId: string): Promise<void> {
    return this.applicationService.deleteAdditionalDocument(applicationId, fileId);
  }

  async savePetPhoto(applicationId: string, file: Express.Multer.File): Promise<File> {
    return this.applicationService.savePetPhoto(applicationId, file);
  }

  async deletePetPhoto(applicationId: string, fileId: string): Promise<void> {
    return this.applicationService.deletePetPhoto(applicationId, fileId);
  }

  async createRenterDetails(
    applicationId: string,
    renterDetailsDto: SaveApplicationRenterDetailsCommand,
  ): Promise<ApplicationRenterDetails> {
    return this.applicationService.createRenterDetails(applicationId, renterDetailsDto);
  }

  async updateRenterDetails(
    renterDetailsId: string,
    renterDetails: Partial<SaveApplicationRenterDetailsCommand>,
  ): Promise<ApplicationRenterDetails> {
    return this.applicationService.updateRenterDetails(renterDetailsId, renterDetails);
  }

  async createRenterEmploymentInfo(
    applicationId: string,
    renterEmploymentInfo: Partial<SaveApplicationRenterEmploymentInfoCommand>,
  ): Promise<ApplicationRenterEmploymentInfo> {
    return this.applicationService.createRenterEmploymentInfo(applicationId, renterEmploymentInfo);
  }

  async updateRenterEmploymentInfo(
    renterEmploymentInfoId: string,
    renterEmploymentInfo: Partial<SaveApplicationRenterEmploymentInfoCommand>,
  ): Promise<ApplicationRenterEmploymentInfo> {
    return this.applicationService.updateRenterEmploymentInfo(renterEmploymentInfoId, renterEmploymentInfo);
  }

  async deleteRenterEmploymentInfo(renterEmploymentInfoId: string): Promise<void> {
    return this.applicationService.deleteRenterEmploymentInfo(renterEmploymentInfoId);
  }

  async createRenterReference(
    applicationId: string,
    renterReference: Partial<SaveApplicationRenterReferenceCommand>,
  ): Promise<ApplicationRenterReference> {
    return this.applicationService.createRenterReference(applicationId, renterReference);
  }

  async updateRenterReference(
    renterReferenceId: string,
    renterReference: Partial<SaveApplicationRenterReferenceCommand>,
  ): Promise<ApplicationRenterReference> {
    return this.applicationService.updateRenterReference(renterReferenceId, renterReference);
  }

  async deleteRenterReference(renterReferenceId: string): Promise<void> {
    return this.applicationService.deleteRenterReference(renterReferenceId);
  }

  async createRenterResidenceInfo(
    applicationId: string,
    renterResidenceInfo: Partial<SaveApplicationRenterResidenceInfoCommand>,
  ): Promise<ApplicationRenterResidenceInfo> {
    return this.applicationService.createRenterResidenceInfo(applicationId, renterResidenceInfo);
  }

  async updateRenterResidenceInfo(
    renterResidenceInfoId: string,
    renterResidenceInfo: Partial<SaveApplicationRenterResidenceInfoCommand>,
  ): Promise<ApplicationRenterResidenceInfo> {
    return this.applicationService.updateRenterResidenceInfo(renterResidenceInfoId, renterResidenceInfo);
  }

  async deleteRenterResidenceInfo(renterResidenceInfoId: string): Promise<void> {
    return this.applicationService.deleteRenterResidenceInfo(renterResidenceInfoId);
  }

  async completeApplication(applicationId: string): Promise<void> {
    const application = await this.applicationService.findApplicationById(applicationId);

    if (await this.applicationService.isApplicationReadyToBeCompleted(application)) {
      await this.applicationService.updateApplication(applicationId, { status: ApplicationStatus.COMPLETED });

      const bundle = await this.bundleService.findByApplicationId(applicationId);
      await this.bundleService.sendBundleIsReadyForSubmissionEmailIfAllApplicationsCompleted(bundle);
    } else {
      await this.applicationService.updateApplication(applicationId, { status: ApplicationStatus.WAITING_FOR_REPORT });
    }
  }
}

import { ApiBearerAuth, ApiBody, ApiConsumes, ApiCreatedResponse, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Delete,
  HttpStatus,
  Param,
  ParseFilePipeBuilder,
  Patch,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { HasRoles } from '../../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../../shared/auth/models/roles-enum';
import { RenterApplicationService } from './renter-application.service';
import { ApplicationRenterDetailsDto } from '../../../shared/application/application/renter-info/renter-details/application-renter-details.dto';
import { ApplicationRenterDetails } from '../../../shared/application/application/renter-info/renter-details/application-renter-details.entity';
import { ApplicationRenterEmploymentInfoDto } from '../../../shared/application/application/renter-info/renter-employment-info/application-renter-employment-info.dto';
import { ApplicationRenterEmploymentInfo } from '../../../shared/application/application/renter-info/renter-employment-info/application-renter-employment-info.entity';
import { ApplicationRenterReferenceDto } from '../../../shared/application/application/renter-info/renter-reference/application-renter-reference.dto';
import { ApplicationRenterReference } from '../../../shared/application/application/renter-info/renter-reference/application-renter-reference.entity';
import { ApplicationRenterResidenceInfo } from '../../../shared/application/application/renter-info/renter-residence-info/application-renter-residence-info.entity';
import { SaveApplicationRenterResidenceInfoCommand } from '../../../shared/application/application/renter-info/renter-residence-info/save-application-renter-residence-info.command';
import { SaveApplicationRenterDetailsCommand } from '../../../shared/application/application/renter-info/renter-details/save-application-renter-details.command';
import { SaveApplicationRenterEmploymentInfoCommand } from '../../../shared/application/application/renter-info/renter-employment-info/save-application-renter-employment-info.command';
import { SaveApplicationRenterReferenceCommand } from '../../../shared/application/application/renter-info/renter-reference/save-application-renter-reference.command';
import { FileInterceptor } from '@nestjs/platform-express';
import { File } from '../../../shared/file/entities/file.entity';
import { FileDto } from 'src/modules/shared/file/models/file.dto';
import { IsApplicationOwnerGuard } from '../../../../guards/renter/is-application-owner.guard';

@ApiTags('renter-application')
@Controller('renter-application')
@ApiBearerAuth()
@UseGuards(RolesGuard)
@HasRoles(Role.RENTER)
export class RenterApplicationController {
  constructor(private readonly renterApplicationService: RenterApplicationService) {}

  @Post(':applicationId/photo-id')
  @ApiCreatedResponse({ description: 'File uploaded', type: FileDto })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @UseGuards(IsApplicationOwnerGuard)
  async uploadPhotoId(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /image\/\w+/,
        })
        .addMaxSizeValidator({
          maxSize: 25 * 1024 * 1024,
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        }),
    )
    file: Express.Multer.File,
    @Param('applicationId') applicationId: string,
  ): Promise<FileDto> {
    const uploadedFile = await this.renterApplicationService.savePhotoId(applicationId, file);

    return File.convertToDto(uploadedFile);
  }

  @Delete(':applicationId/photo-id/:fileId')
  @UseGuards(IsApplicationOwnerGuard)
  async deletePhotoId(@Param('applicationId') applicationId: string, @Param('fileId') fileId: string): Promise<void> {
    await this.renterApplicationService.deletePhotoId(applicationId, fileId);
  }

  @Post(':applicationId/proof-of-income')
  @UseGuards(IsApplicationOwnerGuard)
  @ApiCreatedResponse({ description: 'File uploaded', type: FileDto })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  async uploadProofOfIncome(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /(image\/\w+|application\/pdf)/,
        })
        .addMaxSizeValidator({
          maxSize: 25 * 1024 * 1024,
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        }),
    )
    file: Express.Multer.File,
    @Param('applicationId') applicationId: string,
  ): Promise<FileDto> {
    const uploadedFile = await this.renterApplicationService.saveProofOfIncome(applicationId, file);

    return File.convertToDto(uploadedFile);
  }

  @Delete(':applicationId/proof-of-income/:fileId')
  @UseGuards(IsApplicationOwnerGuard)
  async deleteProofOfIncome(
    @Param('applicationId') applicationId: string,
    @Param('fileId') fileId: string,
  ): Promise<void> {
    await this.renterApplicationService.deleteProofOfIncome(applicationId, fileId);
  }

  @Post(':applicationId/additional-document')
  @ApiCreatedResponse({ description: 'File uploaded', type: FileDto })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @UseGuards(IsApplicationOwnerGuard)
  async uploadAdditionalDocument(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /(image\/\w+|application\/pdf)/,
        })
        .addMaxSizeValidator({
          maxSize: 25 * 1024 * 1024,
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        }),
    )
    file: Express.Multer.File,
    @Param('applicationId') applicationId: string,
  ): Promise<FileDto> {
    const uploadedFile = await this.renterApplicationService.saveAdditionalDocument(applicationId, file);

    return File.convertToDto(uploadedFile);
  }

  @Delete(':applicationId/additional-document/:fileId')
  @UseGuards(IsApplicationOwnerGuard)
  async deleteAdditionalDocument(
    @Param('applicationId') applicationId: string,
    @Param('fileId') fileId: string,
  ): Promise<void> {
    await this.renterApplicationService.deleteAdditionalDocument(applicationId, fileId);
  }

  @Post(':applicationId/renter-details')
  @ApiOkResponse({
    description: 'Create renter details',
    type: ApplicationRenterDetailsDto,
  })
  @UseGuards(IsApplicationOwnerGuard)
  async createApplicationRenterDetails(
    @Body() body: SaveApplicationRenterDetailsCommand,
    @Param('applicationId') applicationId: string,
  ): Promise<ApplicationRenterDetailsDto> {
    const renterDetails = await this.renterApplicationService.createRenterDetails(applicationId, body);

    return ApplicationRenterDetails.convertToDto(renterDetails);
  }

  @Patch('renter-details/:renterDetailsId')
  @ApiOkResponse({
    description: 'Update renter details',
    type: ApplicationRenterDetailsDto,
  })
  async updateApplicationRenterDetails(
    @Param('renterDetailsId') renterDetailsId: string,
    @Body() body: SaveApplicationRenterDetailsCommand,
  ): Promise<ApplicationRenterDetailsDto> {
    const renterDetails = await this.renterApplicationService.updateRenterDetails(renterDetailsId, body);

    return ApplicationRenterDetails.convertToDto(renterDetails);
  }

  @Post(':applicationId/complete')
  @ApiOkResponse({
    description: 'Complete application',
  })
  @UseGuards(IsApplicationOwnerGuard)
  async completeApplication(@Param('applicationId') applicationId: string): Promise<void> {
    await this.renterApplicationService.completeApplication(applicationId);
  }

  @Post('renter-details/:applicationId/pet-photo')
  @ApiCreatedResponse({ description: 'File uploaded', type: FileDto })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  async uploadPetPhoto(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /image\/\w+/,
        })
        .addMaxSizeValidator({
          maxSize: 25 * 1024 * 1024,
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        }),
    )
    file: Express.Multer.File,
    @Param('applicationId') applicationId: string,
  ): Promise<FileDto> {
    const uploadedFile = await this.renterApplicationService.savePetPhoto(applicationId, file);

    return File.convertToDto(uploadedFile);
  }

  @Delete(':applicationId/pet-photo/:fileId')
  async deletePetPhoto(@Param('applicationId') applicationId: string, @Param('fileId') fileId: string): Promise<void> {
    await this.renterApplicationService.deletePetPhoto(applicationId, fileId);
  }

  @Post(':applicationId/employment-info')
  @UseGuards(IsApplicationOwnerGuard)
  @ApiOkResponse({
    description: 'Create employment info',
    type: ApplicationRenterEmploymentInfoDto,
  })
  async createApplicationEmploymentInfo(
    @Param('applicationId') applicationId: string,
    @Body() body: SaveApplicationRenterEmploymentInfoCommand,
  ): Promise<ApplicationRenterEmploymentInfoDto> {
    const employmentInfo = await this.renterApplicationService.createRenterEmploymentInfo(applicationId, body);

    return ApplicationRenterEmploymentInfo.convertToDto(employmentInfo);
  }

  @Patch('employment-info/:employmentInfoId')
  @ApiOkResponse({
    description: 'Update employment info',
    type: ApplicationRenterEmploymentInfoDto,
  })
  async updateApplicationEmploymentInfo(
    @Param('employmentInfoId') employmentInfoId: string,
    @Body() body: SaveApplicationRenterEmploymentInfoCommand,
  ): Promise<ApplicationRenterEmploymentInfoDto> {
    const employmentInfo = await this.renterApplicationService.updateRenterEmploymentInfo(employmentInfoId, body);

    return ApplicationRenterEmploymentInfo.convertToDto(employmentInfo);
  }

  @Delete('employment-info/:employmentInfoId')
  @ApiOkResponse({
    description: 'Delete employment info',
  })
  async deleteApplicationEmploymentInfo(@Param('employmentInfoId') employmentInfoId: string): Promise<void> {
    await this.renterApplicationService.deleteRenterEmploymentInfo(employmentInfoId);
  }

  @Post(':applicationId/reference')
  @UseGuards(IsApplicationOwnerGuard)
  @ApiOkResponse({
    description: 'Create renter reference',
    type: ApplicationRenterReferenceDto,
  })
  async createApplicationReference(
    @Body() body: SaveApplicationRenterReferenceCommand,
    @Param('applicationId') applicationId: string,
  ): Promise<ApplicationRenterReferenceDto> {
    const propertySpecifications = await this.renterApplicationService.createRenterReference(applicationId, body);

    return ApplicationRenterReference.convertToDto(propertySpecifications);
  }

  @Patch('reference/:referenceId')
  @ApiOkResponse({
    description: 'Update renter reference',
    type: ApplicationRenterReferenceDto,
  })
  async updateApplicationReference(
    @Param('referenceId') referenceId: string,
    @Body() body: SaveApplicationRenterReferenceCommand,
  ): Promise<ApplicationRenterReferenceDto> {
    const propertySpecifications = await this.renterApplicationService.updateRenterReference(referenceId, body);

    return ApplicationRenterReference.convertToDto(propertySpecifications);
  }

  @Delete('reference/:referenceId')
  @ApiOkResponse({
    description: 'Delete renter reference',
  })
  async deleteApplicationReference(@Param('referenceId') referenceId: string): Promise<void> {
    await this.renterApplicationService.deleteRenterReference(referenceId);
  }

  @Post(':applicationId/residence-info')
  @ApiOkResponse({
    description: 'Create residence info',
    type: SaveApplicationRenterResidenceInfoCommand,
  })
  @UseGuards(IsApplicationOwnerGuard)
  async createApplicationResidenceInfo(
    @Body() body: SaveApplicationRenterResidenceInfoCommand,
    @Param('applicationId') applicationId: string,
  ): Promise<SaveApplicationRenterResidenceInfoCommand> {
    const propertySpecifications = await this.renterApplicationService.createRenterResidenceInfo(applicationId, body);

    return ApplicationRenterResidenceInfo.convertToDto(propertySpecifications);
  }

  @Patch('residence-info/:residenceInfoId')
  @ApiOkResponse({
    description: 'Update residence info',
    type: SaveApplicationRenterResidenceInfoCommand,
  })
  async updateApplicationResidenceInfo(
    @Param('residenceInfoId') residenceInfoId: string,
    @Body() body: SaveApplicationRenterResidenceInfoCommand,
  ): Promise<SaveApplicationRenterResidenceInfoCommand> {
    const propertySpecifications = await this.renterApplicationService.updateRenterResidenceInfo(residenceInfoId, body);

    return ApplicationRenterResidenceInfo.convertToDto(propertySpecifications);
  }

  @Delete('residence-info/:residenceInfoId')
  @ApiOkResponse({
    description: 'Delete residence info',
  })
  async deleteApplicationResidenceInfo(@Param('residenceInfoId') residenceInfoId: string): Promise<void> {
    await this.renterApplicationService.deleteRenterResidenceInfo(residenceInfoId);
  }
}

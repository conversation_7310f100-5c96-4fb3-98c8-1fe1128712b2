import { <PERSON>piB<PERSON>erAuth, ApiOkResponse, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Body, Controller, Get, Param, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { HasRoles } from '../../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../../shared/auth/models/roles-enum';
import { TransUnionRenterDto } from '../../../shared/application/application/model/trans-union-renter.dto';
import { Request } from 'express';
import { ExamDto } from '../../../shared/background-check/trans-union/models/exam.dto';
import { RenterApplicationTransUnionService } from './renter-application-trans-union.service';
import { TransUnionProductType } from '../../../shared/background-check/trans-union/enums/product-type.enum';
import { ValidationStatus } from '../../../shared/background-check/trans-union/models/is-validated.dto';
import { AnswerExamDto } from '../../../shared/background-check/trans-union/models/answer-exam.dto';

@ApiTags('renter-application/trans-union')
@Controller('renter-application/trans-union')
@ApiBearerAuth()
@UseGuards(RolesGuard)
@HasRoles(Role.RENTER)
export class RenterApplicationTransUnionController {
  constructor(private readonly applicationTransUnionService: RenterApplicationTransUnionService) {}

  @Post(':applicationId/initiate-background-check')
  @ApiOkResponse({
    description: 'TransUnion background check started',
  })
  async initiateBackgroundCheck(@Param('applicationId') applicationId: string): Promise<void> {
    await this.applicationTransUnionService.initiateBackgroundCheck(applicationId);
  }

  @Get(':applicationId/renter/is-validated')
  @ApiOkResponse({
    description: 'Renter validation status',
  })
  getValidationStatus(@Req() req: Request, @Param('applicationId') applicationId: string): Promise<ValidationStatus> {
    return this.applicationTransUnionService.getValidationStatus(applicationId, req.user.id);
  }

  @Get(':applicationId/report')
  @ApiOkResponse({
    description: 'Get report',
  })
  @ApiQuery({
    name: 'productType',
    enum: TransUnionProductType,
    description: 'Type of report to fetch',
    required: false,
  })
  getReport(
    @Req() req: Request,
    @Param('applicationId') applicationId: string,
    @Query('productType') productType: TransUnionProductType,
  ): Promise<string> {
    return this.applicationTransUnionService.getReport(applicationId, productType, req.user.id);
  }

  @Get(':applicationId/exam')
  @ApiOkResponse({
    description: 'Get exam',
    type: ExamDto,
  })
  getExam(@Req() req: Request, @Param('applicationId') applicationId: string): Promise<ExamDto> {
    return this.applicationTransUnionService.getExam(applicationId, req.user.id);
  }

  @Post(':applicationId/:examId/answer')
  @ApiOkResponse({
    description: 'Exam passed',
  })
  answerExam(
    @Req() req: Request,
    @Param('applicationId') applicationId: string,
    @Param('examId') examId: string,
    @Body() body: AnswerExamDto,
  ): Promise<ExamDto> {
    return this.applicationTransUnionService.answerExam(applicationId, examId, body, req.user.id);
  }

  @Get('renter')
  @ApiOkResponse({
    description: 'Get TransUnion renter',
    type: TransUnionRenterDto,
  })
  getTransUnionRenter(@Req() req: Request): Promise<TransUnionRenterDto | null> {
    return this.applicationTransUnionService.getTransUnionRenter(req.user.id);
  }

  @Put(':applicationId/renter')
  @ApiOkResponse({
    description: 'Create TransUnion renter',
    type: TransUnionRenterDto,
  })
  createTransUnionRenter(
    @Req() req: Request,
    @Body() body: TransUnionRenterDto,
    @Param('applicationId') applicationId: string,
  ): Promise<{ screeningRequestRenterId: string }> {
    return this.applicationTransUnionService.updateTransUnionRenter(body, req.user.id, applicationId);
  }
}

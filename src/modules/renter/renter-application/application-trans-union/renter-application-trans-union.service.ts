import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { ApplicationService } from '../../../shared/application/application/application.service';
import { TransUnionService } from '../../../shared/background-check/trans-union/trans-union.service';
import { TransUnionRenterDto } from '../../../shared/application/application/model/trans-union-renter.dto';
import { RenterService } from '../../renter/renter.service';
import { ApplicationInviteService } from '../../../shared/application/application-invite/application-invite.service';
import { ExamDto } from '../../../shared/background-check/trans-union/models/exam.dto';
import { TransUnionProductType } from '../../../shared/background-check/trans-union/enums/product-type.enum';
import { ValidationStatus } from '../../../shared/background-check/trans-union/models/is-validated.dto';
import { AnswerExamDto } from '../../../shared/background-check/trans-union/models/answer-exam.dto';
import { TransUnionRenter } from '../../../shared/background-check/trans-union/interfaces/renter.interface';
import { BackgroundCheckStatusEnum } from '../../../shared/application/application/enums/background-check-status.enum';

@Injectable()
export class RenterApplicationTransUnionService {
  constructor(
    private readonly transUnionService: TransUnionService,
    private readonly applicationService: ApplicationService,
    private readonly applicationInviteService: ApplicationInviteService,
    private readonly renterManagementService: RenterService,
  ) {}

  async getExam(applicationId: string, userId: string): Promise<ExamDto> {
    const application = await this.applicationService.findApplicationById(applicationId);

    if ((await application.renter).user.id !== userId) {
      throw new ForbiddenException('You are not authorized to get exam for this application');
    }

    return this.transUnionService.createScreeningRequestRenterExam(application.screeningRequestRenterId);
  }

  async getTransUnionRenter(userId: string): Promise<TransUnionRenterDto | null> {
    const renter = await this.renterManagementService.findByUserId(userId);

    if (!renter.transUnionRenterId) {
      return null;
    }

    const tuRenter = await this.transUnionService.getRenterDetails(renter.transUnionRenterId);

    return TransUnionRenter.convertToDto(tuRenter);
  }

  async getReport(applicationId: string, productType: TransUnionProductType, userId: string): Promise<string> {
    const application = await this.applicationService.findApplicationById(applicationId);

    if ((await application.renter).user.id !== userId) {
      throw new ForbiddenException('You are not authorized to view this application');
    }

    if (!application.screeningRequestRenterId) {
      throw new BadRequestException('Screening request has not been created');
    }

    const response = await this.transUnionService.getScreeningRequestRenterReport(
      application.screeningRequestRenterId,
      productType,
    );

    if (response.reportsExpireNumberOfDays < 0) {
      throw new BadRequestException('Report has expired');
    }

    return response.reportResponseModelDetails.find(
      (report) => report.providerName.toLowerCase() === productType.toLowerCase(),
    )?.reportData;
  }

  async getValidationStatus(applicationId: string, userId: string): Promise<ValidationStatus> {
    const application = await this.applicationService.findApplicationById(applicationId);
    const renter = await this.renterManagementService.findByUserId(userId);

    if (!application.screeningRequestRenterId || !renter.transUnionRenterId) {
      return {
        isValidated: false,
      };
    }

    if ((await application.renter).user.id !== userId) {
      throw new ForbiddenException('You are not authorized to view this application');
    }

    const validationStatus = await this.transUnionService.validateScreeningRequestRenter(
      application.screeningRequestRenterId,
      renter.transUnionRenterId,
    );

    return {
      isValidated: validationStatus.status === 'Verified',
    };
  }

  async answerExam(applicationId: string, examId: string, body: AnswerExamDto, userId: string): Promise<ExamDto> {
    const application = await this.applicationService.findApplicationById(applicationId);

    if ((await application.renter).user.id !== userId) {
      throw new ForbiddenException('You are not authorized to answer this exam');
    }

    if (!application.screeningRequestRenterId) {
      throw new BadRequestException('Screening request has not been created');
    }

    return await this.transUnionService.answerExam(application.screeningRequestRenterId, examId, body.answers);
  }

  async initiateBackgroundCheck(applicationId: string): Promise<void> {
    const application = await this.applicationService.findApplicationById(applicationId);

    if (!application.isPaid) {
      throw new ForbiddenException('Payment is required to initiate background check');
    }

    if (application.backgroundCheckStatus !== BackgroundCheckStatusEnum.NOT_STARTED) {
      throw new BadRequestException('Background check has already been initiated');
    }

    await this.transUnionService.triggerReportGeneration(application.screeningRequestRenterId);

    await this.applicationService.updateApplication(applicationId, {
      backgroundCheckStatus: BackgroundCheckStatusEnum.STARTED,
    });
  }

  async updateTransUnionRenter(
    payload: TransUnionRenterDto,
    userId: string,
    applicationId: string,
  ): Promise<{ screeningRequestRenterId: string }> {
    const application = await this.applicationService.findApplicationById(applicationId);
    const renter = await application.renter;

    if (renter.user.id !== userId) {
      throw new ForbiddenException('You are not authorized to perform this action');
    }

    const applicationInvite = await this.applicationInviteService.findByApplicationId(applicationId);

    // TODO: consider to create transUnionScreeningRequest for this case
    if (!applicationInvite.transUnionScreeningRequestId) {
      console.error(`ApplicationInvite ${applicationInvite.id} does not contain transUnionScreeningRequestId`);

      throw new BadRequestException('TransUnion screening request has not been created');
    }

    let transUnionRenterId = renter.transUnionRenterId;

    if (transUnionRenterId) {
      await this.transUnionService.updateRenter(transUnionRenterId, payload, renter);
    } else {
      transUnionRenterId = await this.transUnionService.createRenter(payload, renter);

      await this.renterManagementService.update(renter.id, { transUnionRenterId });
    }

    if (!application.screeningRequestRenterId) {
      const screeningRequestRenterId = await this.transUnionService.createScreeningRequestRenter(
        applicationInvite.transUnionScreeningRequestId,
        transUnionRenterId,
      );

      await this.applicationService.updateApplication(applicationId, { screeningRequestRenterId });

      return { screeningRequestRenterId };
    }

    return {
      screeningRequestRenterId: application.screeningRequestRenterId,
    };
  }
}

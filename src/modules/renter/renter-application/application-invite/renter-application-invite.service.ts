import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { ApplicationInviteService } from '../../../shared/application/application-invite/application-invite.service';
import { ApplicationBundleService } from '../../../shared/application/application-bundle/application-bundle.service';
import { ApplicationService } from '../../../shared/application/application/application.service';
import { RenterService } from '../../renter/renter.service';
import { ApplicationInvite } from '../../../shared/application/application-invite/application-invite.entity';
import { ApplicationInviteStatus } from '../../../shared/application/application-invite/application-invite-status.enum';
import { ApplicationBundleStatus } from '../../../shared/application/application-bundle/application-bundle-status.enum';
import { User } from '../../../shared/user/entities/user.entity';
import { ApplicantType } from '../../../shared/application/application/enums/applicant-type.enum';
import { SendApplicationInviteDto } from './send-application-invite.dto';
import { ConversationService } from '../../../shared/communication/conversation/conversation.service';
import { SlackCommunicationService } from '../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../../shared/communication/outbound-communication/slack/slack-convo-message-builder.class';
import { UserService } from '../../../shared/user/user.service';
import { TransUnionService } from '../../../shared/background-check/trans-union/trans-union.service';
import { PropertyInquiryService } from '../../../investor/property-inquiry/property-inquiry.service';
import { PropertyInquiryEventTypeEnum } from '../../../investor/property-inquiry/events/property-inquiry-event-type.enum';
import { RentStageEnum } from '../../../shared/communication/conversation/enums/rent-stage.enum';

@Injectable()
export class RenterApplicationInvitesService {
  constructor(
    private readonly applicationInviteService: ApplicationInviteService,
    private readonly applicationBundleService: ApplicationBundleService,
    private readonly transUnionService: TransUnionService,
    private readonly applicationService: ApplicationService,
    private readonly renterService: RenterService,
    private readonly slackCommsService: SlackCommunicationService,
    private readonly conversationService: ConversationService,
    private readonly userService: UserService,
    private readonly propertyInquiryService: PropertyInquiryService,
  ) {}

  async getById(applicationInviteId: string): Promise<ApplicationInvite> {
    return this.applicationInviteService.findById(applicationInviteId);
  }

  async getAllApplicationInvitesByRenter(renterUserId: string): Promise<ApplicationInvite[]> {
    const renter = await this.renterService.findByUserId(renterUserId);
    return this.applicationInviteService.findAllApplicationInvitesByRenter(renter.id, true);
  }

  async getApplicationBundleInvitesById(
    applicationBundleId: string,
    renterUserId: string,
  ): Promise<ApplicationInvite[]> {
    const renter = await this.renterService.findByUserId(renterUserId);
    const invites = await this.applicationInviteService.findAllApplicationInvitesByBundle(applicationBundleId, true);
    const renters = await Promise.all(invites.map(async (invite) => invite.renter));

    if (!renters.find((r) => r.id === renter.id)) {
      throw new ForbiddenException('You are not allowed to view this application bundle');
    }

    return invites;
  }

  async acceptApplicationInvite(applicationInviteId: string, userId: string): Promise<ApplicationInvite> {
    const currentRenter = await this.renterService.findByUserId(userId);
    const applicationInvite = await this.applicationInviteService.findById(applicationInviteId);

    if (currentRenter.id !== (await applicationInvite.renter).id) {
      throw new ForbiddenException('Invitation belongs to another renter');
    }

    if (applicationInvite.status !== ApplicationInviteStatus.PENDING) {
      throw new BadRequestException('Application invite is not pending');
    }

    const application = await this.applicationService.createApplication({
      renter: currentRenter,
      type: applicationInvite.type,
    });

    applicationInvite.status = ApplicationInviteStatus.ACCEPTED;
    applicationInvite.application = application;

    await this.applicationInviteService.save(applicationInvite);

    const applicationBundle = await applicationInvite.applicationBundle;
    applicationBundle.status = ApplicationBundleStatus.IN_PROGRESS;
    await this.applicationBundleService.update(applicationBundle.id, applicationBundle);

    const property = await applicationBundle.property;

    if (applicationInvite.type === ApplicantType.APPLICANT) {
      const inquiry = await this.propertyInquiryService.findByRenterAndProperty(currentRenter.id, property.id);

      this.propertyInquiryService
        .addEvent(inquiry.id, PropertyInquiryEventTypeEnum.APPLICATION_STARTED)
        .catch((err) => {
          console.error('Failed to add application started event', err);
        });

      this.propertyInquiryService
        .update(inquiry.id, {
          stage: RentStageEnum.APPLICATION_IN_PROGRESS,
        })
        .catch((err) => {
          console.error('Failed to update property inquiry stage', err);
        });
    }

    const conversation = await this.conversationService.findByPropertyUser(property.id, userId);
    this.slackCommsService.sendMessageToConvosChannel(
      new SlackConvoMessageBuilder()
        .appendTextLine(`👌 Application invite is accepted by ${currentRenter.user.name}`)
        .build(),
      conversation,
    );

    return applicationInvite;
  }

  async sendInvite(
    applicationBundleId: string,
    currentUser: User,
    payload: SendApplicationInviteDto,
  ): Promise<ApplicationInvite> {
    const applicationBundle = await this.applicationBundleService.findById(applicationBundleId);
    const property = await applicationBundle.property;
    const investor = await property.owner;

    if (!applicationBundle) {
      throw new NotFoundException('Application bundle not found');
    }

    if (await this.applicationInviteService.findBundleIdAndEmail(applicationBundleId, payload.email)) {
      throw new BadRequestException(`${payload.email} is already invited`);
    }

    const currentUserApplicationInvite = await this.applicationInviteService.findByBundleIdAndUserId(
      applicationBundleId,
      currentUser.id,
    );

    if (!currentUserApplicationInvite) {
      throw new BadRequestException('User is not associated with the application bundle');
    }

    const primaryApplicant = await applicationBundle.primaryApplicant;

    if (primaryApplicant.user.id !== currentUser.id) {
      switch (payload.applicantType) {
        case ApplicantType.CO_APPLICANT:
          throw new ForbiddenException('You can not invite other co-applicant');

        case ApplicantType.CO_SIGNER:
          if (currentUserApplicationInvite.type !== ApplicantType.CO_APPLICANT) {
            throw new ForbiddenException('You can not invite other co-signer');
          }
      }
    }

    let renter = await this.renterService.findByEmailAndAddRenterRoleIfNeeded(payload.email);

    if (!renter) {
      renter = await this.renterService.create(`${payload.firstName} ${payload.lastName}`, payload.email);
    }

    const invitedByUser = await this.userService.findById(currentUser.id);

    const transUnionScreeningRequestId = await this.transUnionService.createScreeningRequest(
      investor.transUnionLandlordId,
      property.transUnionPropertyId,
      payload.applicantType,
      undefined, // No attestation needed for additional applicant invites
    );

    const applicationInvite = await this.applicationInviteService.createApplicationInvite({
      applicationBundle,
      renter,
      type: payload.applicantType,
      invitedBy: invitedByUser,
      transUnionScreeningRequestId,
    });

    await this.applicationInviteService.sendApplicationInvite(applicationInvite, applicationBundle);

    const conversation = await this.conversationService.findByPropertyUser(property.id, currentUser.id);
    this.slackCommsService.sendMessageToConvosChannel(
      new SlackConvoMessageBuilder()
        .appendTextLine(`🤝 ${payload.firstName} ${payload.lastName} has been invited as a ${payload.applicantType}`)
        .build(),
      conversation,
    );

    return applicationInvite;
  }

  async delete(applicationInviteId: string, currentUserId: string): Promise<void> {
    const applicationInvite = await this.applicationInviteService.findById(applicationInviteId);
    const applicationInviteRenter = await applicationInvite.renter;

    if (applicationInviteRenter.user.id === currentUserId) {
      throw new ForbiddenException('You can not delete yourself');
    }

    const applicationBundle = await applicationInvite.applicationBundle;
    const currentUserApplicationInvite = await this.applicationInviteService.findByBundleIdAndUserId(
      applicationBundle.id,
      currentUserId,
    );

    if (!currentUserApplicationInvite) {
      throw new BadRequestException('User is not associated with the application bundle');
    }

    const applicationBundlePrimaryApplicant = await applicationBundle.primaryApplicant;

    if (applicationBundlePrimaryApplicant.user.id !== currentUserId) {
      const invitedBy = await applicationInvite.invitedBy;

      if (invitedBy.id !== currentUserId) {
        throw new ForbiddenException('You can not delete this person');
      }
    }

    await this.applicationInviteService.delete(applicationInviteId);
  }
}

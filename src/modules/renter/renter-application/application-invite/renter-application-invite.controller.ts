import { <PERSON>pi<PERSON><PERSON><PERSON><PERSON><PERSON>, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Body, Controller, Delete, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { HasRoles } from '../../../shared/auth/decorators/role-access.decorator';
import { Role } from '../../../shared/auth/models/roles-enum';
import { Request } from 'express';
import { ApplicationInvite } from '../../../shared/application/application-invite/application-invite.entity';
import { ApplicationInviteDto } from '../../../shared/application/application-invite/application-invite.dto';
import { RenterApplicationInvitesService } from './renter-application-invite.service';
import { SendApplicationInviteDto } from './send-application-invite.dto';
import { IsApplicationInviteInviteeGuard } from '../../../../guards/renter/is-application-invite-invitee.guard';
import { IsApplicationBundleParticipantGuard } from '../../../../guards/renter/is-application-bundle-participant.guard';
import { IsApplicationInviteInvitedByGuard } from '../../../../guards/renter/is-application-invited-by-user.guard';

@ApiTags('renter-application-invite')
@Controller('renter-application-invite')
@ApiBearerAuth()
@UseGuards(RolesGuard)
@HasRoles(Role.RENTER)
export class RenterApplicationInviteController {
  constructor(private readonly renterApplicationInvitesService: RenterApplicationInvitesService) {}

  @Get()
  @ApiOkResponse({
    description: 'Get all renter-application invites by renter',
    type: ApplicationInviteDto,
    isArray: true,
  })
  async getAllApplicationsByRenter(@Req() req: Request): Promise<ApplicationInviteDto[]> {
    const applicationInvites = await this.renterApplicationInvitesService.getAllApplicationInvitesByRenter(req.user.id);
    const dtoPromises = applicationInvites.map((bundle) => ApplicationInvite.convertToDto(bundle, true, false, true));

    return Promise.all(dtoPromises);
  }

  @Get(':applicationInviteId')
  @ApiOkResponse({
    description: 'Get application invite',
    type: ApplicationInviteDto,
  })
  @UseGuards(IsApplicationInviteInviteeGuard)
  async getByApplicationId(@Param('applicationInviteId') applicationInviteId: string): Promise<ApplicationInviteDto> {
    const applicationInvite = await this.renterApplicationInvitesService.getById(applicationInviteId);

    return ApplicationInvite.convertToDto(applicationInvite, true, false, true);
  }

  @Delete(':applicationInviteId')
  @ApiOkResponse({
    description: 'Delete application invite by id',
  })
  @UseGuards(IsApplicationInviteInvitedByGuard)
  async deleteByApplicationId(
    @Param('applicationInviteId') applicationInviteId: string,
    @Req() req: Request,
  ): Promise<void> {
    await this.renterApplicationInvitesService.delete(applicationInviteId, req.user.id);
  }

  @Get(':applicationBundleId/invites')
  @ApiOkResponse({
    description: 'Get renter-application bundle invites by id',
    type: ApplicationInviteDto,
    isArray: true,
  })
  @UseGuards(IsApplicationBundleParticipantGuard)
  async getApplicationBundleInvitesById(
    @Param('applicationBundleId') applicationBundleId: string,
    @Req() req: Request,
  ): Promise<ApplicationInviteDto[]> {
    const applicationInvites = await this.renterApplicationInvitesService.getApplicationBundleInvitesById(
      applicationBundleId,
      req.user.id,
    );

    const dtoPromises = applicationInvites.map((bundle) => ApplicationInvite.convertToDto(bundle, false, true, true));
    return Promise.all(dtoPromises);
  }

  @Post(':applicationBundleId/send-invite')
  @ApiOkResponse({
    description: 'Send application invite by application bundle id',
    type: ApplicationInviteDto,
  })
  @UseGuards(IsApplicationBundleParticipantGuard)
  async sendInvite(
    @Param('applicationBundleId') applicationBundleId: string,
    @Req() req: Request,
    @Body() body: SendApplicationInviteDto,
  ): Promise<ApplicationInviteDto> {
    const applicationInvite = await this.renterApplicationInvitesService.sendInvite(
      applicationBundleId,
      req.user,
      body,
    );

    return ApplicationInvite.convertToDto(applicationInvite, false, true, true);
  }

  @Post(':applicationInviteId/accept')
  @ApiOkResponse({
    description: 'Accept renter-application invite',
    type: ApplicationInviteDto,
  })
  @UseGuards(IsApplicationInviteInviteeGuard)
  async acceptApplicationInvite(
    @Param('applicationInviteId') applicationInviteId: string,
    @Req() req: Request,
  ): Promise<ApplicationInviteDto> {
    const applicationInvite = await this.renterApplicationInvitesService.acceptApplicationInvite(
      applicationInviteId,
      req.user.id,
    );

    return ApplicationInvite.convertToDto(applicationInvite, true, false, true);
  }
}

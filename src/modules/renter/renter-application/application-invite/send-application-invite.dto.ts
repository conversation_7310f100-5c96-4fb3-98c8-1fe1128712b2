import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsIn, IsString } from 'class-validator';
import { ApplicantType } from '../../../shared/application/application/enums/applicant-type.enum';

export class SendApplicationInviteDto {
  @ApiProperty()
  @IsString()
  firstName: string;

  @ApiProperty()
  @IsString()
  lastName: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty({ enum: [ApplicantType.CO_APPLICANT, ApplicantType.CO_SIGNER] })
  @IsIn([ApplicantType.CO_APPLICANT, ApplicantType.CO_SIGNER])
  applicantType: ApplicantType;
}

import { Body, Controller, Get, Param, Post, Put, Req, UseGuards } from '@nestjs/common';
import { ApiB<PERSON>erAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { AddApplicantDto } from '../../../shared/application/application-bundle/add-applicant.dto';
import { ApplicationBundleDto } from '../../../shared/application/application-bundle/application-bundle.dto';
import { ApplicationBundle } from '../../../shared/application/application-bundle/application-bundle.entity';
import { ApplicationInviteDto } from '../../../shared/application/application-invite/application-invite.dto';
import { ApplicationInvite } from '../../../shared/application/application-invite/application-invite.entity';
import { HasRoles } from '../../../shared/auth/decorators/role-access.decorator';
import { RolesGuard } from '../../../shared/auth/guards/roles.guard';
import { Role } from '../../../shared/auth/models/roles-enum';
import { RenterApplicationBundleService } from './renter-application-bundle.service';
import { UpdateApplicationBundleDto } from './update-application-bundle.dto';
import { IsApplicationBundleParticipantGuard } from '../../../../guards/renter/is-application-bundle-participant.guard';
import { IsApplicationBundlePrimaryApplicantGuard } from '../../../../guards/renter/is-application-bundle-primary-applicant.guard';

@ApiTags('renter-application-bundle')
@Controller('renter-application-bundle')
@ApiBearerAuth()
@UseGuards(RolesGuard)
@HasRoles(Role.RENTER)
export class RenterApplicationBundleController {
  constructor(private readonly renterApplicationBundleService: RenterApplicationBundleService) {}

  @Get(':applicationBundleId')
  @ApiOkResponse({
    description: 'Get application bundle by id',
    type: ApplicationBundleDto,
  })
  @UseGuards(IsApplicationBundleParticipantGuard)
  async getApplicationBundleById(
    @Param('applicationBundleId') applicationBundleId: string,
    @Req() req: Request,
  ): Promise<ApplicationBundleDto> {
    const applicationBundle = await this.renterApplicationBundleService.getApplicationBundleById(
      applicationBundleId,
      req.user.id,
    );

    return ApplicationBundle.convertToDto(applicationBundle, true);
  }

  @Put(':applicationBundleId')
  @ApiOkResponse({
    description: 'Update application bundle by id',
    type: ApplicationBundleDto,
  })
  @UseGuards(IsApplicationBundleParticipantGuard)
  async updateApplicationBundleById(
    @Param('applicationBundleId') applicationBundleId: string,
    @Body() body: UpdateApplicationBundleDto,
    @Req() req: Request,
  ): Promise<ApplicationBundleDto> {
    const applicationBundle = await this.renterApplicationBundleService.update(applicationBundleId, req.user.id, body);

    return ApplicationBundle.convertToDto(applicationBundle, true);
  }

  @Get(':applicationBundleId/invites')
  @ApiOkResponse({
    description: 'Get application bundle invites by id',
    type: ApplicationInviteDto,
    isArray: true,
  })
  @UseGuards(IsApplicationBundleParticipantGuard)
  async getApplicationBundleInvitesById(
    @Param('applicationBundleId') applicationBundleId: string,
    @Req() req: Request,
  ): Promise<ApplicationInviteDto[]> {
    const applicationInvites = await this.renterApplicationBundleService.getApplicationBundleInvitesByBundleId(
      applicationBundleId,
      req.user.id,
    );

    const dtoPromises = applicationInvites.map((bundle) => ApplicationInvite.convertToDto(bundle, false, true, true));
    return Promise.all(dtoPromises);
  }

  @Post(':applicationBundleId/add-applicant')
  @ApiOkResponse({
    description: 'Add applicant to the renter-application bundle',
  })
  @UseGuards(IsApplicationBundleParticipantGuard)
  async addApplicant(
    @Param('applicationBundleId') applicationBundleId: string,
    @Body() body: AddApplicantDto,
    @Req() req: Request,
  ): Promise<ApplicationBundleDto> {
    const applicationBundle = await this.renterApplicationBundleService.addApplicant(
      applicationBundleId,
      body,
      req.user.id,
    );

    return ApplicationBundle.convertToDto(applicationBundle, true);
  }

  @Post(':applicationBundleId/submit')
  @ApiOkResponse({
    description: 'Submit application bundle by id',
    type: ApplicationBundleDto,
  })
  @UseGuards(IsApplicationBundlePrimaryApplicantGuard)
  async submitApplicationBundle(
    @Param('applicationBundleId') applicationBundleId: string,
    @Req() req: Request,
  ): Promise<ApplicationBundleDto> {
    const applicationBundle = await this.renterApplicationBundleService.submitApplicationBundle(
      applicationBundleId,
      req.user.id,
    );

    return ApplicationBundle.convertToDto(applicationBundle, true);
  }
}

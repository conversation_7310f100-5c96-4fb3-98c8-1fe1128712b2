import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  PreconditionFailedException,
} from '@nestjs/common';
import { ApplicationBundle } from '../../../shared/application/application-bundle/application-bundle.entity';
import { AddApplicantDto } from '../../../shared/application/application-bundle/add-applicant.dto';
import { ApplicationBundleService } from '../../../shared/application/application-bundle/application-bundle.service';
import { RenterService } from '../../renter/renter.service';
import { ApplicantType } from '../../../shared/application/application/enums/applicant-type.enum';
import { ApplicationInviteService } from '../../../shared/application/application-invite/application-invite.service';
import { ApplicationInvite } from '../../../shared/application/application-invite/application-invite.entity';
import { UpdateApplicationBundleDto } from './update-application-bundle.dto';
import { ApplicationBundleStatus } from '../../../shared/application/application-bundle/application-bundle-status.enum';
import { ApplicationStatus } from '../../../shared/application/application/enums/application-status.enum';
import { OutboundCommunicationService } from '../../../shared/communication/outbound-communication/outbound-communication.service';
import { ConversationService } from '../../../shared/communication/conversation/conversation.service';
import { SlackCommunicationService } from '../../../shared/communication/outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../../shared/communication/outbound-communication/slack/slack-convo-message-builder.class';
import { PropertyInquiryService } from '../../../investor/property-inquiry/property-inquiry.service';
import { PropertyInquiryEventTypeEnum } from '../../../investor/property-inquiry/events/property-inquiry-event-type.enum';
import { RentStageEnum } from '../../../shared/communication/conversation/enums/rent-stage.enum';

@Injectable()
export class RenterApplicationBundleService {
  constructor(
    private readonly applicationInviteService: ApplicationInviteService,
    private readonly renterService: RenterService,
    private readonly applicationBundleService: ApplicationBundleService,
    private readonly notificationsService: OutboundCommunicationService,
    private readonly slackCommsService: SlackCommunicationService,
    private readonly conversationService: ConversationService,
    private readonly propertyInquiryService: PropertyInquiryService,
  ) {}

  async getApplicationBundleById(applicationBundleId: string, renterUserId: string): Promise<ApplicationBundle> {
    const renter = await this.renterService.findByUserId(renterUserId);
    const bundle = await this.applicationBundleService.findById(applicationBundleId, true);

    if (!bundle) {
      throw new BadRequestException('Application bundle not found');
    }

    // put every renter from each renter-application in the bundle to array
    const renters = await Promise.all(
      (await bundle.applicationInvites).map(async (invite) => {
        return invite.renter;
      }),
    );

    if (!renters.find((r) => r.id === renter.id)) {
      throw new ForbiddenException('You are not allowed to view this renter-application bundle');
    }

    return bundle;
  }

  async update(
    applicationBundleId: string,
    currentUserId: string,
    payload: Partial<UpdateApplicationBundleDto>,
  ): Promise<ApplicationBundle> {
    const applicationInvite = await this.applicationInviteService.findByBundleIdAndUserId(
      applicationBundleId,
      currentUserId,
    );

    if (!applicationInvite) {
      throw new NotFoundException('Application bundle not found');
    }

    if (applicationInvite.type !== ApplicantType.APPLICANT && applicationInvite.type !== ApplicantType.CO_APPLICANT) {
      throw new ForbiddenException('You do not have access for this operation');
    }

    return this.applicationBundleService.update(applicationBundleId, payload);
  }

  async submitApplicationBundle(applicationBundleId: string, renterUserId: string): Promise<ApplicationBundle> {
    const bundle = await this.applicationBundleService.findById(applicationBundleId);

    if (!bundle) {
      throw new NotFoundException('Application bundle not found');
    }

    if (bundle.status !== ApplicationBundleStatus.IN_PROGRESS) {
      throw new BadRequestException('Application bundle is not in progress');
    }

    const primaryApplicant = await bundle.primaryApplicant;

    if (primaryApplicant.user.id !== renterUserId) {
      throw new ForbiddenException('You are not allowed to submit this application bundle');
    }

    const invites = await bundle.applicationInvites;
    const applications = await Promise.all(invites.map(async (invite) => await invite.application));

    if (applications.some((application) => !application)) {
      throw new PreconditionFailedException('Not all applicants have accepted their application invitations');
    }

    if (!applications.every(({ status }) => status === ApplicationStatus.COMPLETED)) {
      throw new ConflictException('All applications must be filled out before submitting the application bundle');
    }

    const property = await bundle.property;
    const owner = await property.owner;

    await this.notificationsService.sendApplicationSubmittedNotification(
      await owner.user,
      primaryApplicant,
      property,
      await property.location,
    );

    const updatedBundle = await this.applicationBundleService.update(applicationBundleId, {
      status: ApplicationBundleStatus.SUBMITTED,
      submittedAt: new Date(),
    });

    const inquiry = await this.propertyInquiryService.findByRenterAndProperty(primaryApplicant.id, property.id);
    await this.propertyInquiryService.addEvent(inquiry.id, PropertyInquiryEventTypeEnum.APPLICATION_SUBMITTED);
    this.propertyInquiryService
      .update(inquiry.id, {
        stage: RentStageEnum.APPLICATION_COMPLETED,
      })
      .catch((e) => {
        console.error('Failed to update property inquiry stage', e);
      });

    const conversation = await this.conversationService.findByPropertyUser(property.id, renterUserId);
    this.slackCommsService.sendMessageToConvosChannel(
      new SlackConvoMessageBuilder().appendTextLine('📦 Application submitted. Pending owner review.').build(),
      conversation,
    );

    return updatedBundle;
  }

  async getApplicationBundleInvitesByBundleId(
    applicationBundleId: string,
    renterUserId: string,
  ): Promise<ApplicationInvite[]> {
    const renter = await this.renterService.findByUserId(renterUserId);

    const invites = await this.applicationInviteService.findAllApplicationInvitesByBundle(applicationBundleId, true);

    const renters = await Promise.all(
      invites.map(async (invite) => {
        return invite.renter;
      }),
    );

    if (!renters.find((r) => r.id === renter.id)) {
      throw new ForbiddenException('You are not allowed to view this renter-application bundle');
    }

    return invites;
  }

  async addApplicant(
    applicationBundleId: string,
    addApplicantDto: AddApplicantDto,
    currentApplicantUserId: string,
  ): Promise<ApplicationBundle> {
    const currentRenter = await this.renterService.findByUserId(currentApplicantUserId);
    let invitee = await this.renterService.findByEmailAndAddRenterRoleIfNeeded(addApplicantDto.email);

    const applicationBundle = await this.applicationBundleService.findById(applicationBundleId, true);

    const invites = await applicationBundle.applicationInvites;

    const applicationsWithRenters = await Promise.all(
      invites?.map(async (invite) => {
        const renter = await invite.renter;
        return { invite, renter };
      }),
    );

    const renterApplicationEntry = applicationsWithRenters.find(({ renter }) => renter.id === currentRenter.id);

    if (!renterApplicationEntry || renterApplicationEntry.invite.type !== ApplicantType.APPLICANT) {
      throw new BadRequestException('You are not authorized to add an applicant to this renter-application bundle.');
    }

    if (!invitee) {
      invitee = await this.renterService.create(
        `${addApplicantDto.firstName} ${addApplicantDto.lastName}`,
        addApplicantDto.email,
        null,
      );
    }

    const inviteeExists = applicationsWithRenters.some(({ renter }) => renter.user.email === invitee.user.email);

    if (inviteeExists) {
      throw new BadRequestException('The applicant already exists in the renter-application bundle.');
    }

    const newApplicationInvite = await this.applicationInviteService.createApplicationInvite({
      applicationBundle: applicationBundle,
      renter: invitee,
      type: addApplicantDto.type,
      invitedBy: currentRenter.user,
    });

    await this.applicationInviteService.sendApplicationInvite(newApplicationInvite, applicationBundle);

    return this.applicationBundleService.findById(applicationBundleId, true);
  }
}

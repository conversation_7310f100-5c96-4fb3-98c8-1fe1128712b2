import { Module } from '@nestjs/common';
import { ApplicationModule } from '../shared/application/application/application.module';
import { RenterModule } from './renter/renter.module';
import { RenterCommunicationModule } from './renter-communication/renter-communication.module';
import { RenterApplicationModule } from './renter-application/renter-application.module';

@Module({
  imports: [ApplicationModule, RenterModule, RenterCommunicationModule, RenterApplicationModule],
})
export class RenterCoreModule {}

import { MigrationInterface, QueryRunner } from 'typeorm';
/* eslint-disable max-len */

export class InitialMigration1722426119442 implements MigrationInterface {
  name = 'InitialMigration1722426119442';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const tables = await queryRunner.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
    `);

    if (tables.length === 1) {
      await this.initDb(queryRunner);
    }
  }

  private async initDb(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."user_preferredcommunicationchannel_enum" AS ENUM('Email', 'SMS', 'Phone')`,
    );
    await queryRunner.query(`CREATE TYPE "public"."user_signinmethods_enum" AS ENUM('credentials', 'google')`);
    await queryRunner.query(
      `CREATE TABLE "user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying(120), "avatar" text, "email" character varying(120), "phoneNumber" character varying(120), "preferredCommunicationChannel" "public"."user_preferredcommunicationchannel_enum" NOT NULL DEFAULT 'Email', "password" character varying(120), "isDeleted" boolean NOT NULL DEFAULT false, "isActivated" boolean NOT NULL DEFAULT false, "roles" text NOT NULL DEFAULT 'investor', "signInMethods" "public"."user_signinmethods_enum" array NOT NULL DEFAULT '{}', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "message" ("id" SERIAL NOT NULL, "content" text NOT NULL, "type" character varying(60) NOT NULL, "isSystem" boolean NOT NULL DEFAULT false, "isDistributed" boolean NOT NULL DEFAULT true, "isSeen" boolean NOT NULL DEFAULT false, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "conversationId" uuid NOT NULL, "userId" uuid, CONSTRAINT "PK_ba01f0a3e0123651915008bc578" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "email_metadata" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "subject" character varying NOT NULL, "previousText" text, "lastMessageId" character varying, "references" text array, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_cf3969bb4ffd29b2a8d97a21807" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."conversation_initiatedby_enum" AS ENUM('System', 'Investor', 'Renter', 'Admin')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."conversation_topic_enum" AS ENUM('New Property Renter Lead', 'Other')`,
    );
    await queryRunner.query(
      `CREATE TABLE "conversation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "conversationSummary" character varying, "initiatedBy" "public"."conversation_initiatedby_enum" NOT NULL, "topic" "public"."conversation_topic_enum" NOT NULL, "isStoppedByInvestor" boolean NOT NULL DEFAULT false, "slackLoggingThreadId" character varying, "isCompleted" boolean NOT NULL DEFAULT false, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "propertyId" uuid, "emailMetadataId" uuid, CONSTRAINT "REL_d0273c0e93140ea5879ce47dc1" UNIQUE ("emailMetadataId"), CONSTRAINT "PK_864528ec4274360a40f66c29845" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "investor" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "companyId" uuid NOT NULL, "userId" uuid NOT NULL, CONSTRAINT "REL_e476aa1a0b5f7d280542f99f64" UNIQUE ("userId"), CONSTRAINT "PK_c60a173349549955c39d3703551" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."company_numberofemployees_enum" AS ENUM('1-50', '51-200', '201-2000', '2000+')`,
    );
    await queryRunner.query(
      `CREATE TABLE "company" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying(120), "numberOfEmployees" "public"."company_numberofemployees_enum", "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_056f7854a7afdba7cbd6d45fc20" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "image" ("id" SERIAL NOT NULL, "url" character varying(512) NOT NULL, "thumbnailUrl" character varying(512), "order" integer NOT NULL DEFAULT '0', "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "propertyId" uuid, CONSTRAINT "PK_d6db1ab4ee9ad9dbe86c64e4cc3" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."property_question_status_enum" AS ENUM('Pending', 'Answered')`);
    await queryRunner.query(
      `CREATE TABLE "property_question" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying(240) NOT NULL, "questionText" text NOT NULL, "status" "public"."property_question_status_enum" NOT NULL DEFAULT 'Pending', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "propertyId" uuid NOT NULL, CONSTRAINT "PK_4cb97d6a5b61d00aa245f58a614" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."showing_request_status_enum" AS ENUM('Pending', 'Accepted', 'Declined', 'Cancelled by Renter', 'Canceled by Investor', 'Rescheduled', 'Expired')`,
    );
    await queryRunner.query(
      `CREATE TABLE "showing_request" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."showing_request_status_enum" NOT NULL DEFAULT 'Pending', "isDeleted" boolean NOT NULL DEFAULT false, "declineReason" text, "cancelReason" text, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "renterId" uuid, "propertyId" uuid NOT NULL, "showingId" uuid, CONSTRAINT "PK_24bcc70abd9c2e72fae2bac04cd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "accessibility" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "accessibleBathroom" boolean, "elevator" boolean, "ramp" boolean, "accessibilityNotes" text, "disabilityAccess" boolean, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_9729339e162bc7ec98a8815758c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "amenities" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "isFurnished" boolean, "canBeFurnished" boolean, "canBeUnfurnished" boolean, "hasBarbecueArea" boolean, "hasBasement" boolean, "hasBasketBallCourt" boolean, "hasBusinessCenter" boolean, "hasCableSatellite" boolean, "hasChildCare" boolean, "hasClubDiscount" boolean, "hasConcierge" boolean, "hasControlledAccess" boolean, "hasCourtyard" boolean, "isCourtyardShared" boolean, "isCourtyardFenced" boolean, "hasDeck" boolean, "hasDisabledAccess" boolean, "hasDock" boolean, "hasDoorman" boolean, "hasElevator" boolean, "hasFencedYard" boolean, "hasFitnessCenter" boolean, "hasGarden" boolean, "hasGatedEntry" boolean, "hasGreenHouse" boolean, "hasHotTubSpa" boolean, "hasHouseKeeping" boolean, "hasIntercom" boolean, "hasJettedBathTub" boolean, "hasLawn" boolean, "hasNightPatrol" boolean, "hasOnSiteMaintenance" boolean, "hasOnSiteManagement" boolean, "hasPackageReceiving" boolean, "hasPlayGround" boolean, "hasPong" boolean, "hasPorch" boolean, "hasRaquetBallCourt" boolean, "hasSauna" boolean, "hasSecuritySystem" boolean, "hasSkylight" boolean, "hasSportsCourt" boolean, "hasSprinklerSystem" boolean, "hasSunDeck" boolean, "hasTennisCourt" boolean, "hasTVLounge" boolean, "hasVolleyBallCourt" boolean, "hasWetBar" boolean, "hasDishwasher" boolean, "hasWasher" boolean, "hasDryer" boolean, "hasFreezer" boolean, "hasGarbageDisposal" boolean, "hasMicrowave" boolean, "hasRangeOven" boolean, "hasRefrigerator" boolean, "hasTrashCompactor" boolean, "hasCeilingFan" boolean, "hasDoublePaneWindows" boolean, "hasHandrails" boolean, "hasLargeClosets" boolean, "hasMotherInLawUnit" boolean, "hasPatio" boolean, "hasStorageSpace" boolean, "hasVaultedCeiling" boolean, "hasWindowCoverings" boolean, "hasBalcony" boolean, "hasFireplace" boolean, "hasPool" boolean, "hasDishWasher" boolean, "hasWasherDryer" boolean, "hasAirConditioning" boolean, "hasHeating" boolean, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_c0777308847b3556086f2fb233e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "included_utilities" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "isUtilitiesIncludedInRent" boolean, "water" boolean, "sewage" boolean, "garbage" boolean, "electricity" boolean, "gas" boolean, "internet" boolean, "cable" boolean, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_de3a367e73876a8c8df7c603a2a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."lease_conditions_lastmonthrent_enum" AS ENUM('0.5x monthly rent', '1x monthly rent', '2x monthly rent', '2.5x monthly rent', '3x monthly rent', '3.5x monthly rent', 'No requirement')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."lease_conditions_securitydeposit_enum" AS ENUM('0.5x monthly rent', '1x monthly rent', '2x monthly rent', '2.5x monthly rent', '3x monthly rent', '3.5x monthly rent', 'No requirement')`,
    );
    await queryRunner.query(
      `CREATE TABLE "lease_conditions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "possibleLeaseTerms" text array, "desiredLeasingDate" TIMESTAMP, "isSubleaseAllowed" boolean, "rent" double precision DEFAULT '0', "applicationFee" double precision DEFAULT '0', "lastMonthRent" "public"."lease_conditions_lastmonthrent_enum", "securityDeposit" "public"."lease_conditions_securitydeposit_enum", "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_37a21799e097aab7538b05af334" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "property_location_nearby_place" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying(100), "rating" double precision, "primaryType" character varying, "latitude" double precision, "longitude" double precision, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "propertyLocationId" uuid NOT NULL, CONSTRAINT "PK_eafeb1419a1badba6fffc4aa049" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "property_location" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "state" character varying(20) NOT NULL, "city" character varying(120) NOT NULL, "address" character varying(100) NOT NULL, "apartmentNumber" character varying(10), "isAddressHidden" boolean NOT NULL DEFAULT false, "zip" integer, "latitude" double precision, "longitude" double precision, "timeZone" character varying(100), "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_f51cb04e3d9aba7b38b40e100a1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b990f8250c81812e4b9bca11c3" ON "property_location" ("address") `);
    await queryRunner.query(
      `CREATE TYPE "public"."parking_parkingtype_enum" AS ENUM('Carport', 'Garage Attached', 'Garage Detached', 'Off Street', 'On Street', 'None')`,
    );
    await queryRunner.query(
      `CREATE TABLE "parking" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "hasParking" boolean, "parkingType" "public"."parking_parkingtype_enum", "parkingAvailableSpaces" integer, "parkingSpacesAreTandem" boolean, "parkingOtherDescription" text, "parkingMonthlyChargePerSpace" double precision, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_d611d86b1d39963d048b05976aa" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."pet_policy_smalldogspolicy_enum" AS ENUM('Not allowed', 'Allowed', 'Breed restrictions', 'Case by case', 'Not specified')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."pet_policy_largedogspolicy_enum" AS ENUM('Not allowed', 'Allowed', 'Breed restrictions', 'Case by case', 'Not specified')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."pet_policy_catspolicy_enum" AS ENUM('Not allowed', 'Allowed', 'Breed restrictions', 'Case by case', 'Not specified')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."pet_policy_otherpetspolicy_enum" AS ENUM('Not allowed', 'Allowed', 'Breed restrictions', 'Case by case', 'Not specified')`,
    );
    await queryRunner.query(
      `CREATE TABLE "pet_policy" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "allowsPets" boolean NOT NULL DEFAULT false, "smallDogsPolicy" "public"."pet_policy_smalldogspolicy_enum", "largeDogsPolicy" "public"."pet_policy_largedogspolicy_enum", "catsPolicy" "public"."pet_policy_catspolicy_enum", "otherPetsPolicy" "public"."pet_policy_otherpetspolicy_enum", "petRent" integer, "petDeposit" integer, "maxPets" integer, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_1fdc63e3ff698bafc796e0d443d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."renter_requirements_minimumincome_enum" AS ENUM('0.5x monthly rent', '1x monthly rent', '2x monthly rent', '2.5x monthly rent', '3x monthly rent', '3.5x monthly rent', 'No requirement')`,
    );
    await queryRunner.query(
      `CREATE TABLE "renter_requirements" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "minimumCreditScore" integer, "minimumIncome" "public"."renter_requirements_minimumincome_enum", "acceptsCosigners" boolean, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_f9cc871960c3ce4a6f521d75be7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_specifications_propertytype_enum" AS ENUM('Apartment', 'House', 'Condo', 'Townhouse', 'Duplex')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_specifications_heatingsystem_enum" AS ENUM('Baseboard', 'Forced Air', 'Heat Pump', 'Radiant', 'Stove', 'Wall', 'Other')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_specifications_coolingsystem_enum" AS ENUM('Central', 'Evaporative', 'Geothermal', 'Wall', 'Solar', 'Other', 'None')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_specifications_rooftype_enum" AS ENUM('Asphalt', 'Built Up', 'Composition', 'Metal', 'Shake Shingle', 'Slate', 'Tile', 'Other')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_specifications_viewtype_enum" AS ENUM('Water', 'Mountain', 'City', 'Territorial', 'Park', 'None')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_specifications_exteriortype_enum" AS ENUM('Brick', 'Cement Concrete', 'Composition', 'Metal', 'Shingle', 'Stone', 'Stucco', 'Vinyl', 'Wood', 'Wood Products', 'Other')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_specifications_floorcovering_enum" AS ENUM('Carpet', 'Concrete', 'Hardwood', 'Laminate', 'Slate', 'Softwood', 'Tile', 'Other')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_specifications_architecturestyle_enum" AS ENUM('Bungalow', 'Cape Cod', 'Colonial', 'Contemporary', 'Craftsman', 'French', 'Georgian', 'Loft', 'Modern', 'Queen Anne Victorian', 'Ranch/Rambler', 'Santa Fe/Pueblo Style', 'Spanish', 'Split-level', 'Tudor', 'Other')`,
    );
    await queryRunner.query(
      `CREATE TABLE "property_specifications" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "propertyType" "public"."property_specifications_propertytype_enum", "squareFeet" integer, "bedrooms" integer, "fullBathrooms" integer, "halfBathrooms" integer, "yearBuilt" integer, "numberOfFloors" integer, "apartmentFloor" integer, "floorPlanLink" character varying(2083), "breakfastNook" integer, "diningRoom" integer, "familyRoom" integer, "laundryRoom" integer, "library" integer, "masterBath" integer, "mudRoom" integer, "office" integer, "pantry" integer, "recreationRoom" integer, "workshop" integer, "solariumAtrium" integer, "sunRoom" integer, "walkInCloset" integer, "totalRooms" integer, "heatSource" character varying, "heatingSystem" "public"."property_specifications_heatingsystem_enum", "coolingSystem" "public"."property_specifications_coolingsystem_enum", "roofType" "public"."property_specifications_rooftype_enum", "viewType" "public"."property_specifications_viewtype_enum", "exteriorType" "public"."property_specifications_exteriortype_enum", "floorCovering" "public"."property_specifications_floorcovering_enum", "architectureStyle" "public"."property_specifications_architecturestyle_enum", "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_06d7ffc25e27f60a2ce5d60fdd0" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_event_type_enum" AS ENUM('Was asked if renter has requirements questions', 'Was shared requirements', 'Requested showing', 'Showing declined', 'Showing confirmed', 'Renter Agreed to Reschedule', 'Renter Agreed to Showing Time Different From Reschedule Request', 'Showing completed', 'Showing reschedule request by Owner', 'Showing reschedule request by Renter', 'Showing cancelled by Owner', 'Showing canceled by Renter', 'Property rented out by Owner', 'Showing Request Ignored by Owner')`,
    );
    await queryRunner.query(
      `CREATE TABLE "property_inquiry_event" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" "public"."property_inquiry_event_type_enum" NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "propertyInquiryId" uuid NOT NULL, CONSTRAINT "PK_f05e63f2ae5b52fa852bff7f2b0" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_stage_enum" AS ENUM('Initial Contact', 'Showing Requested', 'Showing Declined', 'Showing Reschedule Requested by Renter', 'Showing Reschedule Requested by Owner', 'Showing Confirmed', 'Showing Completed', 'Showing Cancelled by Renter', 'Showing Cancelled by Owner', 'Showing Request Ignored by Owner', 'Stopped Property Rented Out')`,
    );
    await queryRunner.query(
      `CREATE TABLE "property_inquiry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "stage" "public"."property_inquiry_stage_enum" DEFAULT 'Initial Contact', "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "renterId" uuid, "propertyId" uuid, "conversationId" uuid NOT NULL, "showingRequestId" uuid, CONSTRAINT "REL_274d052c5617ea51db0baa0e18" UNIQUE ("conversationId"), CONSTRAINT "REL_36fa9847f6fb7af29ae80088bd" UNIQUE ("showingRequestId"), CONSTRAINT "PK_f9fd136a86b838bc9a447acdfce" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "property" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "displayName" character varying(201) NOT NULL, "coverImage" character varying(4097), "isAvailable" boolean DEFAULT true, "allowsSmoking" boolean, "maximumOccupancy" integer, "description" character varying(4097), "highlights" text, "status" character varying(32) NOT NULL DEFAULT 'Draft', "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "ownerId" uuid NOT NULL, "companyId" uuid NOT NULL, "petPolicyId" uuid, "includedUtilitiesId" uuid, "accessibilityId" uuid, "parkingId" uuid, "amenitiesId" uuid, "renterRequirementsId" uuid, "leaseConditionsId" uuid, "specificationsId" uuid, "locationId" uuid, CONSTRAINT "REL_3e08ed31c3c993fef668d221f7" UNIQUE ("petPolicyId"), CONSTRAINT "REL_023f5f645c11fb073582ba6e7b" UNIQUE ("includedUtilitiesId"), CONSTRAINT "REL_e5401ff0ab50cac7ad925fb13a" UNIQUE ("accessibilityId"), CONSTRAINT "REL_3bf509a59fc274a003c6966e38" UNIQUE ("parkingId"), CONSTRAINT "REL_ba97cbbd10394e8bd4932e84ba" UNIQUE ("amenitiesId"), CONSTRAINT "REL_148df9e8949413f451cb99558c" UNIQUE ("renterRequirementsId"), CONSTRAINT "REL_647daf4b5ca4af68a334e5a59b" UNIQUE ("leaseConditionsId"), CONSTRAINT "REL_0469b951a7a25dbbe5d814ab0e" UNIQUE ("specificationsId"), CONSTRAINT "REL_d285b373822984e1951c21a3c1" UNIQUE ("locationId"), CONSTRAINT "PK_d80743e6191258a5003d5843b4f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."showing_status_enum" AS ENUM('Pending', 'Confirmed', 'Completed', 'Canceled', 'Declined', 'Rescheduled', 'Expired')`,
    );
    await queryRunner.query(
      `CREATE TABLE "showing" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."showing_status_enum" NOT NULL DEFAULT 'Pending', "startTime" TIMESTAMP NOT NULL, "endTime" TIMESTAMP NOT NULL, "beforeShowingNotificationSent" boolean NOT NULL DEFAULT false, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "propertyId" uuid, CONSTRAINT "PK_a748a042fe27ce80ae73340d095" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."reschedule_request_status_enum" AS ENUM('Pending', 'Confirmed', 'Declined', 'Canceled', 'Rescheduled')`,
    );
    await queryRunner.query(
      `CREATE TABLE "reschedule_request" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."reschedule_request_status_enum" NOT NULL DEFAULT 'Pending', "comment" text, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "renterId" uuid NOT NULL, "showingId" uuid NOT NULL, CONSTRAINT "PK_b78b430b2c16b3a4d328fdfd6d6" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."renter_employmentstatus_enum" AS ENUM('Employed', 'Self-employed', 'Student', 'Unemployed')`,
    );
    await queryRunner.query(
      `CREATE TABLE "renter" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "employmentStatus" "public"."renter_employmentstatus_enum", "hasPets" boolean, "petType" character varying, "petBreed" character varying, "petSize" character varying, "petWeightPounds" integer, "petDescription" text, "isSmoker" boolean, "occupation" character varying(120), "monthlyIncome" integer, "creditScore" character varying(120), "hasChildren" boolean, "numberOfChildren" integer, "desiredLeaseTerm" character varying, "desiredMoveInDate" date, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid, CONSTRAINT "REL_c54921124f3f91aad2b0bb95d4" UNIQUE ("userId"), CONSTRAINT "PK_7d1963dd773c2a2a44fc93a956f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."rent_blocker_blockertype_enum" AS ENUM('Low Income', 'Low Credit', 'Misaligned Move-In Date', 'Misaligned Lease Term', 'Pets')`,
    );
    await queryRunner.query(
      `CREATE TABLE "rent_blocker" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "blockerType" "public"."rent_blocker_blockertype_enum" NOT NULL, "blockerTypeWeight" integer NOT NULL, "isResolved" boolean NOT NULL DEFAULT false, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "renterId" uuid, "propertyId" uuid NOT NULL, CONSTRAINT "PK_fd697b1cd9c85181a24e87e86d0" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."availability_slot_weekday_enum" AS ENUM('Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday')`,
    );
    await queryRunner.query(
      `CREATE TABLE "availability_slot" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "weekday" "public"."availability_slot_weekday_enum" NOT NULL, "startTime" TIME WITH TIME ZONE NOT NULL, "endTime" TIME WITH TIME ZONE NOT NULL, "investorAvailabilityId" uuid NOT NULL, CONSTRAINT "PK_62a782c29fd83da5ba7c4ea55f7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "investor_availability" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "showingDurationInMinutes" integer NOT NULL DEFAULT '15', "investorId" uuid NOT NULL, CONSTRAINT "REL_37d6f1cc2287bc297114bfeeb8" UNIQUE ("investorId"), CONSTRAINT "PK_fa84eb0728b1d87a27adc7c7298" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "data_feed" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" character varying(12) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "version" integer NOT NULL, "propertyId" uuid, CONSTRAINT "REL_6ed6bbef66ca2f0e47af373c31" UNIQUE ("propertyId"), CONSTRAINT "PK_44e7912fb14dedf213bc9e9473e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_verification_type_enum" AS ENUM('Password reset token', 'Email confirmation code', 'One time sign in code')`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_verification" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "isUsed" boolean NOT NULL DEFAULT false, "type" "public"."user_verification_type_enum" NOT NULL, "code" character varying(120) NOT NULL, "expiresAt" TIMESTAMP NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid, CONSTRAINT "PK_679edeb6fcfcbc4c094573e27e7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "company_persona" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "perceivedProductValuePropostion" text, "numberOfUnitsOverseeing" integer, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "companyId" uuid NOT NULL, CONSTRAINT "REL_7294829cdeded1d8d87ca353bd" UNIQUE ("companyId"), CONSTRAINT "PK_dadae9978da3400251d17427002" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."follow_up_status_enum" AS ENUM('Pending', 'Sending', 'Sent')`);
    await queryRunner.query(
      `CREATE TYPE "public"."follow_up_type_enum" AS ENUM('Renter Stopped Responding', 'Investor Showing Request Follow Up')`,
    );
    await queryRunner.query(
      `CREATE TABLE "follow_up" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "scheduledTime" TIMESTAMP NOT NULL, "status" "public"."follow_up_status_enum" NOT NULL DEFAULT 'Pending', "type" "public"."follow_up_type_enum" NOT NULL DEFAULT 'Renter Stopped Responding', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "conversationId" uuid, "recipientId" uuid, "propertyId" uuid, CONSTRAINT "PK_5c0a5f5b32937e47e0ab09d596c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "inbox_history_version" ("id" SERIAL NOT NULL, "historyId" character varying NOT NULL, "email" character varying NOT NULL, "version" integer NOT NULL, CONSTRAINT "PK_871d197d80eb852db967c8cb0a4" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "conversation_users_user" ("conversationId" uuid NOT NULL, "userId" uuid NOT NULL, CONSTRAINT "PK_39cd0ac92f269976929656be1d7" PRIMARY KEY ("conversationId", "userId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7835ccf192c47ae47cd5c250d5" ON "conversation_users_user" ("conversationId") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b4d7dfd81d3b743bcfd1682abe" ON "conversation_users_user" ("userId") `);
    await queryRunner.query(
      `CREATE TABLE "property_question_renters_renter" ("propertyQuestionId" uuid NOT NULL, "renterId" uuid NOT NULL, CONSTRAINT "PK_1b96f24bfadde61d6642d8adfc1" PRIMARY KEY ("propertyQuestionId", "renterId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2067e4837fcb6600f9d9a5dd7b" ON "property_question_renters_renter" ("propertyQuestionId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f5fb464c8ad37a8b06e8f3caf2" ON "property_question_renters_renter" ("renterId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "message" ADD CONSTRAINT "FK_7cf4a4df1f2627f72bf6231635f" FOREIGN KEY ("conversationId") REFERENCES "conversation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "message" ADD CONSTRAINT "FK_446251f8ceb2132af01b68eb593" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation" ADD CONSTRAINT "FK_a728fe4833611ac6ab4b933ab6a" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation" ADD CONSTRAINT "FK_d0273c0e93140ea5879ce47dc12" FOREIGN KEY ("emailMetadataId") REFERENCES "email_metadata"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "investor" ADD CONSTRAINT "FK_4b29eb750221af2115c4639916e" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "investor" ADD CONSTRAINT "FK_e476aa1a0b5f7d280542f99f640" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "image" ADD CONSTRAINT "FK_e87199b269a72d071b3f4ff3b02" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_question" ADD CONSTRAINT "FK_03007b8433cdbe665ac61cb6e90" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "showing_request" ADD CONSTRAINT "FK_e950c61e0a9f406fb90ae978d46" FOREIGN KEY ("renterId") REFERENCES "renter"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "showing_request" ADD CONSTRAINT "FK_f83ee9ad30934d39fc4938892aa" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "showing_request" ADD CONSTRAINT "FK_e3673026a41ff4b01fec2c5b1e2" FOREIGN KEY ("showingId") REFERENCES "showing"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_location_nearby_place" ADD CONSTRAINT "FK_3d52222dd79cfb2e5280f9e35ff" FOREIGN KEY ("propertyLocationId") REFERENCES "property_location"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_inquiry_event" ADD CONSTRAINT "FK_b32924129d9920d1dbcf8f86c6a" FOREIGN KEY ("propertyInquiryId") REFERENCES "property_inquiry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ADD CONSTRAINT "FK_085a1d7157d6e0adf3e44afa999" FOREIGN KEY ("renterId") REFERENCES "renter"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ADD CONSTRAINT "FK_f0f4caf7705c509b64063fc16b1" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ADD CONSTRAINT "FK_274d052c5617ea51db0baa0e181" FOREIGN KEY ("conversationId") REFERENCES "conversation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ADD CONSTRAINT "FK_36fa9847f6fb7af29ae80088bd4" FOREIGN KEY ("showingRequestId") REFERENCES "showing_request"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_917755242ab5b0a0b08a63016d9" FOREIGN KEY ("ownerId") REFERENCES "investor"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_8d01ee8f3f75c10e18e6b4ef6d0" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_3e08ed31c3c993fef668d221f78" FOREIGN KEY ("petPolicyId") REFERENCES "pet_policy"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_023f5f645c11fb073582ba6e7b9" FOREIGN KEY ("includedUtilitiesId") REFERENCES "included_utilities"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_e5401ff0ab50cac7ad925fb13a0" FOREIGN KEY ("accessibilityId") REFERENCES "accessibility"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_3bf509a59fc274a003c6966e388" FOREIGN KEY ("parkingId") REFERENCES "parking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_ba97cbbd10394e8bd4932e84ba9" FOREIGN KEY ("amenitiesId") REFERENCES "amenities"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_148df9e8949413f451cb99558cf" FOREIGN KEY ("renterRequirementsId") REFERENCES "renter_requirements"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_647daf4b5ca4af68a334e5a59ba" FOREIGN KEY ("leaseConditionsId") REFERENCES "lease_conditions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_0469b951a7a25dbbe5d814ab0e7" FOREIGN KEY ("specificationsId") REFERENCES "property_specifications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_d285b373822984e1951c21a3c18" FOREIGN KEY ("locationId") REFERENCES "property_location"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "showing" ADD CONSTRAINT "FK_73f0d92d688769507819a7b7e0f" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reschedule_request" ADD CONSTRAINT "FK_e408b63ca311b9a4ae665f4fab7" FOREIGN KEY ("renterId") REFERENCES "renter"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reschedule_request" ADD CONSTRAINT "FK_347be753b635b9f9c1c230566f3" FOREIGN KEY ("showingId") REFERENCES "showing"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "renter" ADD CONSTRAINT "FK_c54921124f3f91aad2b0bb95d46" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "rent_blocker" ADD CONSTRAINT "FK_a35d649c01261a256eaca51691f" FOREIGN KEY ("renterId") REFERENCES "renter"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "rent_blocker" ADD CONSTRAINT "FK_c53b9d9b1510e50567dd2775b72" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "availability_slot" ADD CONSTRAINT "FK_4e275502c22d0c7793b1c4b0565" FOREIGN KEY ("investorAvailabilityId") REFERENCES "investor_availability"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "investor_availability" ADD CONSTRAINT "FK_37d6f1cc2287bc297114bfeeb8a" FOREIGN KEY ("investorId") REFERENCES "investor"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_feed" ADD CONSTRAINT "FK_6ed6bbef66ca2f0e47af373c31f" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_verification" ADD CONSTRAINT "FK_b98835ab0c83f27ff7c4c7de3db" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "company_persona" ADD CONSTRAINT "FK_7294829cdeded1d8d87ca353bd4" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "follow_up" ADD CONSTRAINT "FK_68d9322960408f36b72064fb929" FOREIGN KEY ("conversationId") REFERENCES "conversation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "follow_up" ADD CONSTRAINT "FK_54ca4fd782e6d085b820bdb9119" FOREIGN KEY ("recipientId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "follow_up" ADD CONSTRAINT "FK_f084fdcec47998cfd8110d66e62" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation_users_user" ADD CONSTRAINT "FK_7835ccf192c47ae47cd5c250d5a" FOREIGN KEY ("conversationId") REFERENCES "conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation_users_user" ADD CONSTRAINT "FK_b4d7dfd81d3b743bcfd1682abeb" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_question_renters_renter" ADD CONSTRAINT "FK_2067e4837fcb6600f9d9a5dd7b2" FOREIGN KEY ("propertyQuestionId") REFERENCES "property_question"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_question_renters_renter" ADD CONSTRAINT "FK_f5fb464c8ad37a8b06e8f3caf2a" FOREIGN KEY ("renterId") REFERENCES "renter"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log(`You don't want to do it bro`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeleteImage1731585858028 implements MigrationInterface {
  name = 'DeleteImage1731585858028';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE "file"
      SET "deletedAt" = "image"."updatedAt"
      FROM "image"
      WHERE "file"."url" = "image"."url" AND "image"."isDeleted" = true
    `);

    await queryRunner.query(`DROP TABLE "application_additional_documents_image"`);
    await queryRunner.query(`DROP TABLE "application_photo_ids_image"`);
    await queryRunner.query(`DROP TABLE "application_proofs_of_income_image"`);
    await queryRunner.query(`DROP TABLE "application_renter_details_pet_photos_image"`);
    await queryRunner.query(`DROP TABLE "image"`);

    await queryRunner.query(
      `CREATE TABLE "application_renter_details_pet_photos_file" ("applicationRenterDetailsId" uuid NOT NULL, "fileId" uuid NOT NULL, CONSTRAINT "PK_18183f1150da9153461adcadce5" PRIMARY KEY ("applicationRenterDetailsId", "fileId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a2c1e461c857d50fe683da172d" ON "application_renter_details_pet_photos_file" ("applicationRenterDetailsId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_62799200722270f66ca941d5f5" ON "application_renter_details_pet_photos_file" ("fileId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_details_pet_photos_file" ADD CONSTRAINT "FK_a2c1e461c857d50fe683da172d7" FOREIGN KEY ("applicationRenterDetailsId") REFERENCES "application_renter_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_details_pet_photos_file" ADD CONSTRAINT "FK_62799200722270f66ca941d5f55" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_renter_details_pet_photos_file" DROP CONSTRAINT "FK_62799200722270f66ca941d5f55"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_details_pet_photos_file" DROP CONSTRAINT "FK_a2c1e461c857d50fe683da172d7"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_62799200722270f66ca941d5f5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a2c1e461c857d50fe683da172d"`);
    await queryRunner.query(`DROP TABLE "application_renter_details_pet_photos_file"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIndexToCalendarProvider1746626557476 implements MigrationInterface {
  name = 'AddIndexToCalendarProvider1746626557476';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE INDEX "IDX_2d03e0f1221c349aaff64b0c15" ON "calendar_integration" ("provider") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_2d03e0f1221c349aaff64b0c15"`);
  }
}

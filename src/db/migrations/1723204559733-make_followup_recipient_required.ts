/* eslint-disable max-len */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class MakeFollowupRecipientRequired1723204559733 implements MigrationInterface {
  name = 'MakeFollowupRecipientRequired1723204559733';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "follow_up" DROP CONSTRAINT "FK_54ca4fd782e6d085b820bdb9119"`);
    await queryRunner.query(`ALTER TABLE "follow_up" ALTER COLUMN "recipientId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "follow_up" ADD CONSTRAINT "FK_54ca4fd782e6d085b820bdb9119" FOREIGN KEY ("recipientId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "follow_up" DROP CONSTRAINT "FK_54ca4fd782e6d085b820bdb9119"`);
    await queryRunner.query(`ALTER TABLE "follow_up" ALTER COLUMN "recipientId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "follow_up" ADD CONSTRAINT "FK_54ca4fd782e6d085b820bdb9119" FOREIGN KEY ("recipientId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}

/* eslint-disable max-len */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemovePropertyFromFollowup1724409649405 implements MigrationInterface {
  name = 'RemovePropertyFromFollowup1724409649405';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "follow_up" DROP CONSTRAINT "FK_f084fdcec47998cfd8110d66e62"`);
    await queryRunner.query(`ALTER TABLE "follow_up" DROP COLUMN "propertyId"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "follow_up" ADD "propertyId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "follow_up" ADD CONSTRAINT "FK_f084fdcec47998cfd8110d66e62" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}

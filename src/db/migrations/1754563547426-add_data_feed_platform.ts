import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDataFeedPlatform1754563547426 implements MigrationInterface {
  name = 'AddDataFeedPlatform1754563547426';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."data_feed_platform_enum" AS ENUM('zillow')`);
    await queryRunner.query(
      `ALTER TABLE "data_feed" ADD "platform" "public"."data_feed_platform_enum" NOT NULL DEFAULT 'zillow'`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_33d6fccda8f28855d225928ab5" ON "data_feed" ("platform") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_25958a41d57d73d97d81431a4f" ON "data_feed" ("propertyId", "platform") `,
    );

    // set zillow as provider for existing data
    await queryRunner.query(`UPDATE "data_feed" SET "platform" = 'zillow'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_25958a41d57d73d97d81431a4f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_33d6fccda8f28855d225928ab5"`);
    await queryRunner.query(`ALTER TABLE "data_feed" DROP COLUMN "platform"`);
    await queryRunner.query(`DROP TYPE "public"."data_feed_platform_enum"`);
  }
}

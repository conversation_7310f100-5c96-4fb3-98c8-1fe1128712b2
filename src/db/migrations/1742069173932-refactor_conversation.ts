import { MigrationInterface, QueryRunner } from 'typeorm';

export class RefactorConversation1742069173932 implements MigrationInterface {
  name = 'RefactorConversation1742069173932';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "conversation" DROP COLUMN "initiatedBy"`);
    await queryRunner.query(`DROP TYPE "public"."conversation_initiatedby_enum"`);
    await queryRunner.query(`ALTER TABLE "conversation" DROP COLUMN "topic"`);
    await queryRunner.query(`DROP TYPE "public"."conversation_topic_enum"`);
    await queryRunner.query(`ALTER TABLE "conversation" DROP COLUMN "isCompleted"`);
    await queryRunner.query(`ALTER TABLE "conversation" DROP COLUMN "isDeleted"`);
    await queryRunner.query(`ALTER TABLE "conversation" DROP COLUMN "conversationSummary"`);
    await queryRunner.query(`ALTER TABLE "conversation" RENAME COLUMN "isStoppedByInvestor" TO "isEmergencyStopped"`);
    await queryRunner.query(`ALTER TABLE "conversation" ADD "deletedAt" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "conversation" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "conversation" ADD "conversationSummary" character varying`);
    await queryRunner.query(`ALTER TABLE "conversation" ADD "isDeleted" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "conversation" ADD "isCompleted" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "conversation" RENAME COLUMN "isEmergencyStopped" TO "isStoppedByInvestor"`);
    await queryRunner.query(
      `CREATE TYPE "public"."conversation_topic_enum" AS ENUM('New Property Renter Lead', 'Other')`,
    );
    await queryRunner.query(`ALTER TABLE "conversation" ADD "topic" "public"."conversation_topic_enum" NOT NULL`);
    await queryRunner.query(
      `CREATE TYPE "public"."conversation_initiatedby_enum" AS ENUM('Admin', 'Investor', 'Renter', 'System')`,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation" ADD "initiatedBy" "public"."conversation_initiatedby_enum" NOT NULL`,
    );
  }
}

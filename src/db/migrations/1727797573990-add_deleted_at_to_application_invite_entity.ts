import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDeletedAtToApplicationInviteEntity1727797573990 implements MigrationInterface {
  name = 'AddDeletedAtToApplicationInviteEntity1727797573990';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_invite" ADD "deletedAt" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_invite" DROP COLUMN "deletedAt"`);
  }
}

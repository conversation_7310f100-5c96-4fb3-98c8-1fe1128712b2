import { MigrationInterface, QueryRunner } from 'typeorm';

export class SetShowingPropertyIdNotNullable1745687308205 implements MigrationInterface {
  name = 'SetShowingPropertyIdNotNullable1745687308205';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "showing" DROP CONSTRAINT "FK_73f0d92d688769507819a7b7e0f"`);
    await queryRunner.query(`ALTER TABLE "showing" ALTER COLUMN "propertyId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "showing" ADD CONSTRAINT "FK_73f0d92d688769507819a7b7e0f" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "showing" DROP CONSTRAINT "FK_73f0d92d688769507819a7b7e0f"`);
    await queryRunner.query(`ALTER TABLE "showing" ALTER COLUMN "propertyId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "showing" ADD CONSTRAINT "FK_73f0d92d688769507819a7b7e0f" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPropertyInquiryApplicationStages1734714999079 implements MigrationInterface {
  name = 'AddPropertyInquiryApplicationStages1734714999079';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_event_type_enum" RENAME TO "property_inquiry_event_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_event_type_enum" AS ENUM('Was asked if renter has requirements questions', 'Was shared requirements', 'Requested showing', 'Showing declined', 'Showing confirmed', 'Renter Agreed to Reschedule', 'Renter Agreed to Showing Time Different From Reschedule Request', 'Showing reminder sent', 'Showing completed', 'Showing reschedule request by Owner', 'Showing reschedule request by <PERSON><PERSON>', 'Showing cancelled by Owner', 'Showing canceled by <PERSON><PERSON>', 'Property rented out by Owner', 'Showing Request Ignored by Owner', 'Application Invite Sent', 'Application Started', 'Application Completed', 'Application Accepted', 'Application Rejected', 'Application Submitted')`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_inquiry_event" ALTER COLUMN "type" TYPE "public"."property_inquiry_event_type_enum" USING "type"::"text"::"public"."property_inquiry_event_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_event_type_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_stage_enum" RENAME TO "property_inquiry_stage_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_stage_enum" AS ENUM('Initial Contact', 'Showing Requested', 'Showing Declined', 'Showing Reschedule Requested by Renter', 'Showing Reschedule Requested by Owner', 'Showing Confirmed', 'Showing Completed', 'Showing Cancelled by Renter', 'Showing Cancelled by Owner', 'Showing Request Ignored by Owner', 'Stopped Property Rented Out', 'Application Invite Sent', 'Application In Progress', 'Application Completed', 'Application Accepted', 'Application Rejected', 'No Longer Interested')`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ALTER COLUMN "stage" TYPE "public"."property_inquiry_stage_enum" USING "stage"::"text"::"public"."property_inquiry_stage_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" SET DEFAULT 'Initial Contact'`);
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_stage_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_stage_enum_old" AS ENUM('Application Invite Sent', 'Initial Contact', 'No Longer Interested', 'Showing Cancelled by Owner', 'Showing Cancelled by Renter', 'Showing Completed', 'Showing Confirmed', 'Showing Declined', 'Showing Request Ignored by Owner', 'Showing Requested', 'Showing Reschedule Requested by Owner', 'Showing Reschedule Requested by Renter', 'Stopped Property Rented Out')`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ALTER COLUMN "stage" TYPE "public"."property_inquiry_stage_enum_old" USING "stage"::"text"::"public"."property_inquiry_stage_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" SET DEFAULT 'Initial Contact'`);
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_stage_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_stage_enum_old" RENAME TO "property_inquiry_stage_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_event_type_enum_old" AS ENUM('Property rented out by Owner', 'Renter Agreed to Reschedule', 'Renter Agreed to Showing Time Different From Reschedule Request', 'Requested showing', 'Showing Request Ignored by Owner', 'Showing canceled by Renter', 'Showing cancelled by Owner', 'Showing completed', 'Showing confirmed', 'Showing declined', 'Showing reminder sent', 'Showing reschedule request by Owner', 'Showing reschedule request by Renter', 'Was asked if renter has requirements questions', 'Was shared requirements')`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_inquiry_event" ALTER COLUMN "type" TYPE "public"."property_inquiry_event_type_enum_old" USING "type"::"text"::"public"."property_inquiry_event_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_event_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_event_type_enum_old" RENAME TO "property_inquiry_event_type_enum"`,
    );
  }
}

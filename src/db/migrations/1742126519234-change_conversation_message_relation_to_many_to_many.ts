import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeConversationMessageRelationToManyToMany1742126519234 implements MigrationInterface {
  name = 'ChangeConversationMessageRelationToManyToMany1742126519234';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the join table with TypeORM's expected naming convention
    await queryRunner.query(`
      CREATE TABLE "conversation_messages_message" (
        "conversationId" uuid NOT NULL,
        "messageId" integer NOT NULL,
        CONSTRAINT "PK_conversation_messages_message" PRIMARY KEY ("conversationId", "messageId")
      )
    `);

    // Add foreign key constraints to the join table
    await queryRunner.query(`
      ALTER TABLE "conversation_messages_message" 
      ADD CONSTRAINT "FK_conversation_messages_conversation" 
      FOREIGN KEY ("conversationId") REFERENCES "conversation"("id") 
      ON DELETE CASCADE ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "conversation_messages_message" 
      ADD CONSTRAINT "FK_conversation_messages_message" 
      FOREIGN KEY ("messageId") REFERENCES "message"("id") 
      ON DELETE CASCADE ON UPDATE CASCADE
    `);

    // Migrate existing data from the old relationship to the new join table
    await queryRunner.query(`
      INSERT INTO "conversation_messages_message" ("conversationId", "messageId")
      SELECT "conversationId", "id" FROM "message" WHERE "conversationId" IS NOT NULL
    `);

    // Drop the foreign key constraint on the message table
    await queryRunner.query(`
      ALTER TABLE "message" DROP CONSTRAINT IF EXISTS "FK_message_conversation"
    `);

    // Remove the old foreign key column
    await queryRunner.query(`ALTER TABLE "message" DROP COLUMN "conversationId"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back the original column
    await queryRunner.query(`ALTER TABLE "message" ADD "conversationId" uuid`);

    // Add back the foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "message"
      ADD CONSTRAINT "FK_message_conversation"
      FOREIGN KEY ("conversationId") REFERENCES "conversation"("id")
    `);

    // Migrate data back (note: this will only keep the first conversation for each message)
    await queryRunner.query(`
      UPDATE "message" m
      SET "conversationId" = cm."conversationId"
      FROM (
        SELECT DISTINCT ON ("messageId") "conversationId", "messageId"
        FROM "conversation_messages_message"
        ORDER BY "messageId", "conversationId"
      ) cm
      WHERE m.id = cm."messageId"
    `);

    // Drop the foreign keys from the join table
    await queryRunner.query(`
      ALTER TABLE "conversation_messages_message" 
      DROP CONSTRAINT "FK_conversation_messages_message"
    `);

    await queryRunner.query(`
      ALTER TABLE "conversation_messages_message" 
      DROP CONSTRAINT "FK_conversation_messages_conversation"
    `);

    // Drop the join table
    await queryRunner.query(`DROP TABLE "conversation_messages_message"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMessageDeletedAt1743094511457 implements MigrationInterface {
  name = 'AddMessageDeletedAt1743094511457';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "message" RENAME COLUMN "isDeleted" TO "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "message" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "message" ADD "deletedAt" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "message" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "message" ADD "deletedAt" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "message" RENAME COLUMN "deletedAt" TO "isDeleted"`);
  }
}

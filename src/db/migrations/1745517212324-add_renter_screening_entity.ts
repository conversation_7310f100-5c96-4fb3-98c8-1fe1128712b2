import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRenterScreeningEntity1745517212324 implements MigrationInterface {
  name = 'AddRenterScreeningEntity1745517212324';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "renter_screening" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "creditScore" character varying(120), "monthlyIncome" character varying(120), "desiredMoveInDate" character varying(120), "convoHighlights" text array NOT NULL DEFAULT '{}', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_04594c406766175afd27acc725a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "renter" ADD "renterScreeningId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "renter" ADD CONSTRAINT "UQ_82d0d9e8c1f1cbbea2d985bfacc" UNIQUE ("renterScreeningId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "renter" ADD CONSTRAINT "FK_82d0d9e8c1f1cbbea2d985bfacc" FOREIGN KEY ("renterScreeningId") REFERENCES "renter_screening"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "renter" DROP CONSTRAINT "FK_82d0d9e8c1f1cbbea2d985bfacc"`);
    await queryRunner.query(`ALTER TABLE "renter" DROP CONSTRAINT "UQ_82d0d9e8c1f1cbbea2d985bfacc"`);
    await queryRunner.query(`ALTER TABLE "renter" DROP COLUMN "renterScreeningId"`);
    await queryRunner.query(`DROP TABLE "renter_screening"`);
  }
}

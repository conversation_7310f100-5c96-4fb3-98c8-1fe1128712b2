import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSyndicateAsAgent1756391012333 implements MigrationInterface {
  name = 'AddSyndicateAsAgent1756391012333';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "company_settings" ADD "syndicateAsAgent" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "data_feed" ADD "syndicateAsAgent" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "companyType" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "data_feed" DROP COLUMN "syndicateAsAgent"`);
  }
}

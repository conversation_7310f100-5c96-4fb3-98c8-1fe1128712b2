import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddShowingReminderSentEvent1733646909409 implements MigrationInterface {
  name = 'AddShowingReminderSentEvent1733646909409';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_event_type_enum" RENAME TO "property_inquiry_event_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_event_type_enum" AS ENUM('Was asked if renter has requirements questions', 'Was shared requirements', 'Requested showing', 'Showing declined', 'Showing confirmed', 'Renter Agreed to Reschedule', 'Renter Agreed to Showing Time Different From Reschedule Request', 'Showing reminder sent', 'Showing completed', 'Showing reschedule request by Owner', 'Showing reschedule request by <PERSON><PERSON>', 'Showing cancelled by Owner', 'Showing canceled by Ren<PERSON>', 'Property rented out by Owner', 'Showing Request Ignored by Owner')`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_inquiry_event" ALTER COLUMN "type" TYPE "public"."property_inquiry_event_type_enum" USING "type"::"text"::"public"."property_inquiry_event_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_event_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_event_type_enum_old" AS ENUM('Was asked if renter has requirements questions', 'Was shared requirements', 'Requested showing', 'Showing declined', 'Showing confirmed', 'Renter Agreed to Reschedule', 'Renter Agreed to Showing Time Different From Reschedule Request', 'Showing completed', 'Showing reschedule request by Owner', 'Showing reschedule request by Renter', 'Showing cancelled by Owner', 'Showing canceled by Renter', 'Property rented out by Owner', 'Showing Request Ignored by Owner')`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_inquiry_event" ALTER COLUMN "type" TYPE "public"."property_inquiry_event_type_enum_old" USING "type"::"text"::"public"."property_inquiry_event_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_event_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_event_type_enum_old" RENAME TO "property_inquiry_event_type_enum"`,
    );
  }
}

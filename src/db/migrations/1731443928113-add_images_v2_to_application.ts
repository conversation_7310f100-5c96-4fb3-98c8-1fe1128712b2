import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddImagesV2ToApplication1731443928113 implements MigrationInterface {
  name = 'AddImagesV2ToApplication1731443928113';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "property_images_v2_file" ("propertyId" uuid NOT NULL, "fileId" uuid NOT NULL, CONSTRAINT "PK_cde6b8dc2f5c533ceb3293ecfb7" PRIMARY KEY ("propertyId", "fileId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c436b65c6d9521f0a659ad328a" ON "property_images_v2_file" ("propertyId") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_52db3af5ddaeb930c47c538adf" ON "property_images_v2_file" ("fileId") `);
    await queryRunner.query(
      `ALTER TABLE "property_images_v2_file" ADD CONSTRAINT "FK_c436b65c6d9521f0a659ad328a3" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_images_v2_file" ADD CONSTRAINT "FK_52db3af5ddaeb930c47c538adfe" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property_images_v2_file" DROP CONSTRAINT "FK_52db3af5ddaeb930c47c538adfe"`);
    await queryRunner.query(`ALTER TABLE "property_images_v2_file" DROP CONSTRAINT "FK_c436b65c6d9521f0a659ad328a3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_52db3af5ddaeb930c47c538adf"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c436b65c6d9521f0a659ad328a"`);
    await queryRunner.query(`DROP TABLE "property_images_v2_file"`);
  }
}

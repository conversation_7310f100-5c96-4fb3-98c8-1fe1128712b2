import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDeletedAtToApplicationApplicationBundleEntities1727820426389 implements MigrationInterface {
  name = 'AddDeletedAtToApplicationApplicationBundleEntities1727820426389';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_bundle" RENAME COLUMN "isDeleted" TO "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "application" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "application_bundle" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "application_bundle" ADD "deletedAt" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_bundle" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "application_bundle" ADD "deletedAt" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "application" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "application_bundle" RENAME COLUMN "deletedAt" TO "isDeleted"`);
  }
}

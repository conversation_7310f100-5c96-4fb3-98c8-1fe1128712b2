import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMessageSentBy1735916466709 implements MigrationInterface {
  name = 'AddMessageSentBy1735916466709';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."message_sentby_enum" AS ENUM('Email', 'SMS', 'Phone')`);
    await queryRunner.query(`ALTER TABLE "message" ADD "sentBy" "public"."message_sentby_enum"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "message" DROP COLUMN "sentBy"`);
    await queryRunner.query(`DROP TYPE "public"."message_sentby_enum"`);
  }
}

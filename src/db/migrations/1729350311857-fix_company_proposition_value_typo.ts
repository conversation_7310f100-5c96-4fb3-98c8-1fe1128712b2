import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixCompanyPropositionValueTypo1729350311857 implements MigrationInterface {
  name = 'FixCompanyPropositionValueTypo1729350311857';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company_persona" RENAME COLUMN "perceivedProductValuePropostion" TO "perceivedProductValueProposition"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company_persona" RENAME COLUMN "perceivedProductValueProposition" TO "perceivedProductValuePropostion"`,
    );
  }
}

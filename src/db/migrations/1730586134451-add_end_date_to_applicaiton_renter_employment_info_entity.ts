import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEndDateToApplicaitonRenterEmploymentInfoEntity1730586134451 implements MigrationInterface {
  name = 'AddEndDateToApplicaitonRenterEmploymentInfoEntity1730586134451';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_renter_employment_info" ADD "endDate" date`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_renter_employment_info" DROP COLUMN "endDate"`);
  }
}

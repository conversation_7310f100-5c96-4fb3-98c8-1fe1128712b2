import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHasCosignerFieldToRenter1750666226149 implements MigrationInterface {
  name = 'AddHasCosignerFieldToRenter1750666226149';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "renter" ADD "hasCosigner" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "renter" DROP COLUMN "hasCosigner"`);
  }
}

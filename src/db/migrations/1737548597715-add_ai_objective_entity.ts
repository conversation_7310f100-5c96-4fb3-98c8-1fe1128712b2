import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAiObjectiveEntity1737548597715 implements MigrationInterface {
  name = 'AddAiObjectiveEntity1737548597715';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."ai_objective_type_enum" AS ENUM('Evaluate credit score')`);
    await queryRunner.query(
      `CREATE TABLE "ai_objective" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" "public"."ai_objective_type_enum" NOT NULL, "completed" boolean NOT NULL DEFAULT false, "value" character varying(120), "requirementCompensatingFactors" text array NOT NULL DEFAULT '{}', "meetsRequirement" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "propertyInquiryId" uuid NOT NULL, CONSTRAINT "PK_3056bda0166b2e9753f121073ae" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b874d4921a976388eb1529688f" ON "investor" ("address") `);
    await queryRunner.query(
      `ALTER TABLE "ai_objective" ADD CONSTRAINT "FK_9134bf2416c74c6cfde7430fab9" FOREIGN KEY ("propertyInquiryId") REFERENCES "property_inquiry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "ai_objective" DROP CONSTRAINT "FK_9134bf2416c74c6cfde7430fab9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b874d4921a976388eb1529688f"`);
    await queryRunner.query(`DROP TABLE "ai_objective"`);
    await queryRunner.query(`DROP TYPE "public"."ai_objective_type_enum"`);
  }
}

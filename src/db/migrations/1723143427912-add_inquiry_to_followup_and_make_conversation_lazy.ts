/* eslint-disable max-len */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddInquiryToFollowupAndMakeConversationLazy1723143427912 implements MigrationInterface {
  name = 'AddInquiryToFollowupAndMakeConversationLazy1723143427912';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "follow_up" ADD "propertyInquiryId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "follow_up" ADD CONSTRAINT "FK_7ea6cc976b705c6bce0ed00773b" FOREIGN KEY ("propertyInquiryId") REFERENCES "property_inquiry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "follow_up" DROP CONSTRAINT "FK_7ea6cc976b705c6bce0ed00773b"`);
    await queryRunner.query(`ALTER TABLE "follow_up" DROP COLUMN "propertyInquiryId"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameMessageSentByToCommsChannel1736257602253 implements MigrationInterface {
  name = 'RenameMessageSentByToCommsChannel1736257602253';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "message" RENAME COLUMN "sentBy" TO "communicationChannel"`);
    await queryRunner.query(`ALTER TYPE "public"."message_sentby_enum" RENAME TO "message_communicationchannel_enum"`);
    await queryRunner.query(`ALTER TABLE "message" ALTER COLUMN "isAnswered" SET DEFAULT false`);

    // Set default values
    await queryRunner.query(`
            UPDATE "property" 
            SET "listedAt" = "updatedAt" 
            WHERE "listedAt" IS NULL
        `);

    await queryRunner.query(`
            UPDATE "message" 
            SET "communicationChannel" = 'Email' 
            WHERE "communicationChannel" IS NULL
        `);

    await queryRunner.query(`ALTER TABLE "message" ALTER COLUMN "communicationChannel" SET NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "message" ALTER COLUMN "communicationChannel" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "message" ALTER COLUMN "isAnswered" SET DEFAULT true`);
    await queryRunner.query(`ALTER TYPE "public"."message_communicationchannel_enum" RENAME TO "message_sentby_enum"`);
    await queryRunner.query(`ALTER TABLE "message" RENAME COLUMN "communicationChannel" TO "sentBy"`);
  }
}

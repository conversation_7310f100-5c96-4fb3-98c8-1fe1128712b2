import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateFilesFromImages1731444006490 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Enable uuid-ossp extension if not already enabled
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);

    // Copy data from 'image' to 'file', generating new UUIDs and providing default values
    await queryRunner.query(`
            INSERT INTO "file" ("id", "url", "thumbnailUrl", "mimeType", "fileSize", "fileName", "order", "isDeleted", "createdAt", "updatedAt")
            SELECT
                uuid_generate_v4(),                        -- Generate new UUID for 'id'
                "url",                                     -- Copy 'url' from 'image'
                "thumbnailUrl",                            -- Copy 'thumbnailUrl' from 'image'
                'application/octet-stream',                -- Default 'mimeType' (will update later)
                0,                                         -- Default 'fileSize' to 0
                regexp_replace("url", '^.+/([^/\\?]+).*$', '\\1'), -- Extract 'fileName' from 'url'
                "order",                                   -- Copy 'order' from 'image'
                false,                                     -- Set 'isDeleted' to false
                "createdAt",                               -- Copy 'createdAt' from 'image'
                "updatedAt"                                -- Copy 'updatedAt' from 'image'
            FROM "image"
        `);

    // Create a temporary table to store the mapping
    await queryRunner.query(`
            CREATE TEMP TABLE "image_file_mapping" ("imageId" integer, "fileId" uuid)
        `);

    // Insert the mappings into the temporary table
    await queryRunner.query(`
            INSERT INTO "image_file_mapping" ("imageId", "fileId")
            SELECT "image"."id", "file"."id"
            FROM "image"
            JOIN "file" ON "image"."url" = "file"."url"
        `);

    // Insert into 'property_images_v2_file' using the mapping
    await queryRunner.query(`
            INSERT INTO "property_images_v2_file" ("propertyId", "fileId")
            SELECT DISTINCT "image"."propertyId", "mapping"."fileId"
            FROM "image"
            JOIN "image_file_mapping" AS "mapping" ON "image"."id" = "mapping"."imageId"
            WHERE "image"."propertyId" IS NOT NULL
        `);

    // Update the 'mimeType' field in the 'file' table
    await queryRunner.query(`
            UPDATE "file"
            SET "mimeType" = CASE
                WHEN extension = 'webp' THEN 'image/webp'
                WHEN extension = 'png' THEN 'image/png'
                WHEN extension IN ('jpg', 'jpeg') THEN 'image/jpeg'
                WHEN extension = 'gif' THEN 'image/gif'
                WHEN extension = 'svg' THEN 'image/svg+xml'
                WHEN extension = 'mp4' THEN 'video/mp4'
                -- Add more mappings as needed
                ELSE 'application/octet-stream'
            END
            FROM (
                SELECT "id", LOWER(REGEXP_REPLACE("url", '^.*\\.([^.\\?]+)(\\?.*)?$', '\\1')) AS extension
                FROM "file"
            ) AS exts
            WHERE "file"."id" = exts."id"
        `);

    // Drop the temporary table
    await queryRunner.query(`
            DROP TABLE "image_file_mapping"
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Delete associations from 'property_images_v2_file'
    await queryRunner.query(`
            DELETE FROM "property_images_v2_file"
            WHERE "fileId" IN (SELECT "id" FROM "file" WHERE "url" IN (SELECT "url" FROM "image"))
        `);

    // Delete inserted records from 'file' table
    await queryRunner.query(`
            DELETE FROM "file"
            WHERE "url" IN (SELECT "url" FROM "image")
        `);

    // Ensure the temporary table is dropped
    await queryRunner.query(`
            DROP TABLE IF EXISTS "image_file_mapping"
        `);
  }
}

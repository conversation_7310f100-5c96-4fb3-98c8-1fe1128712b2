import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveApplicationBundleApplicationRelation1726949590878 implements MigrationInterface {
  name = 'RemoveApplicationBundleApplicationRelation1726949590878';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."application_type_enum" RENAME TO "application_type_enum_old"`);
    await queryRunner.query(
      `CREATE TYPE "public"."application_type_enum" AS ENUM('Applicant', 'Co-Applicant', 'Co-Signer')`,
    );
    await queryRunner.query(
      `ALTER TABLE "application" ALTER COLUMN "type" TYPE "public"."application_type_enum" USING "type"::"text"::"public"."application_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."application_type_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."application_invite_type_enum" RENAME TO "application_invite_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."application_invite_type_enum" AS ENUM('Applicant', 'Co-Applicant', 'Co-Signer')`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_invite" ALTER COLUMN "type" TYPE "public"."application_invite_type_enum" USING "type"::"text"::"public"."application_invite_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."application_invite_type_enum_old"`);
    await queryRunner.query('DROP TABLE "application_bundle_applications_application"');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."application_invite_type_enum_old" AS ENUM('Applicant', 'Co-Signer')`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_invite" ALTER COLUMN "type" TYPE "public"."application_invite_type_enum_old" USING "type"::"text"::"public"."application_invite_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."application_invite_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."application_invite_type_enum_old" RENAME TO "application_invite_type_enum"`,
    );
    await queryRunner.query(`CREATE TYPE "public"."application_type_enum_old" AS ENUM('Applicant', 'Co-Signer')`);
    await queryRunner.query(
      `ALTER TABLE "application" ALTER COLUMN "type" TYPE "public"."application_type_enum_old" USING "type"::"text"::"public"."application_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."application_type_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."application_type_enum_old" RENAME TO "application_type_enum"`);
  }
}

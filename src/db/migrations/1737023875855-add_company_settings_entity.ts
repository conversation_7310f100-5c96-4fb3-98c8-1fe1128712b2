import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCompanySettingsEntity1737023875855 implements MigrationInterface {
  name = 'AddCompanySettingsEntity1737023875855';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. Create company_settings table
    await queryRunner.query(
      `CREATE TABLE "company_settings" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "advancedRenterScreening" boolean NOT NULL DEFAULT true, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_036b4634217db79c17305442dbe" PRIMARY KEY ("id"))`,
    );

    // 2. Create settings for ALL companies
    await queryRunner.query(`
      INSERT INTO company_settings (id, "advancedRenterScreening", "createdAt", "updatedAt")
      SELECT
        uuid_generate_v4(),
        true,
        NOW(),
        NOW()
      FROM company
    `);

    // 3. Add nullable settingsId initially
    await queryRunner.query(`ALTER TABLE "company" ADD "settingsId" uuid`);

    // 4. Link ALL companies to their settings
    await queryRunner.query(`
      WITH CompanySettings AS (
        SELECT id, ROW_NUMBER() OVER () as rn
        FROM company_settings
      ),
      Companies AS (
        SELECT id, ROW_NUMBER() OVER () as rn
        FROM company
      )
      UPDATE company c
      SET "settingsId" = cs.id
      FROM CompanySettings cs
      JOIN Companies comp ON cs.rn = comp.rn
      WHERE c.id = comp.id
    `);

    // 5. Make settingsId NOT NULL
    await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "settingsId" SET NOT NULL`);

    // 6. Add constraints
    await queryRunner.query(
      `ALTER TABLE "company" ADD CONSTRAINT "UQ_0683ba18fd95ae7547e95ea67e1" UNIQUE ("settingsId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ADD CONSTRAINT "FK_0683ba18fd95ae7547e95ea67e1" FOREIGN KEY ("settingsId") REFERENCES "company_settings"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "company" DROP CONSTRAINT "FK_0683ba18fd95ae7547e95ea67e1"`);
    await queryRunner.query(`ALTER TABLE "company" DROP CONSTRAINT "UQ_0683ba18fd95ae7547e95ea67e1"`);
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "settingsId"`);
    await queryRunner.query(`DROP TABLE "company_settings"`);
  }
}

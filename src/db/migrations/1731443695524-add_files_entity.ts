import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFilesEntity1731443695524 implements MigrationInterface {
  name = 'AddFilesEntity1731443695524';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "file" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "url" character varying(512) NOT NULL, "thumbnailUrl" character varying(512), "mimeType" character varying(512) NOT NULL, "fileSize" integer NOT NULL, "fileName" character varying(512) NOT NULL, "deletedAt" TIMESTAMP, "order" integer NOT NULL DEFAULT '0', "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_36b46d232307066b3a2c9ea3a1d" PRIMARY KEY ("id"))`,
    );

    await queryRunner.query(
      `CREATE TABLE "application_photo_ids_v2_file" ("applicationId" uuid NOT NULL, "fileId" uuid NOT NULL, CONSTRAINT "PK_9c68388e517d0e12552c526fec3" PRIMARY KEY ("applicationId", "fileId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0fb11739ab343ba847c4c8ec92" ON "application_photo_ids_v2_file" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c531a314d08ca7db36d1b6c29c" ON "application_photo_ids_v2_file" ("fileId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "application_proofs_of_income_v2_file" ("applicationId" uuid NOT NULL, "fileId" uuid NOT NULL, CONSTRAINT "PK_87b199341e4b2f3a6e849b8bcd4" PRIMARY KEY ("applicationId", "fileId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a638f83e3a10b841aca63f4179" ON "application_proofs_of_income_v2_file" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5316862855ea11d8306e55ab5e" ON "application_proofs_of_income_v2_file" ("fileId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "application_additional_documents_v2_file" ("applicationId" uuid NOT NULL, "fileId" uuid NOT NULL, CONSTRAINT "PK_317cf5f6d18d50845344021a748" PRIMARY KEY ("applicationId", "fileId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_552a1a665a1840770e96477662" ON "application_additional_documents_v2_file" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_33d8efaabfaa4bfc2e970b2f85" ON "application_additional_documents_v2_file" ("fileId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_v2_file" ADD CONSTRAINT "FK_0fb11739ab343ba847c4c8ec92b" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_v2_file" ADD CONSTRAINT "FK_c531a314d08ca7db36d1b6c29c3" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_v2_file" ADD CONSTRAINT "FK_a638f83e3a10b841aca63f41796" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_v2_file" ADD CONSTRAINT "FK_5316862855ea11d8306e55ab5ea" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_v2_file" ADD CONSTRAINT "FK_552a1a665a1840770e964776620" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_v2_file" ADD CONSTRAINT "FK_33d8efaabfaa4bfc2e970b2f857" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_v2_file" DROP CONSTRAINT "FK_33d8efaabfaa4bfc2e970b2f857"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_v2_file" DROP CONSTRAINT "FK_552a1a665a1840770e964776620"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_v2_file" DROP CONSTRAINT "FK_5316862855ea11d8306e55ab5ea"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_v2_file" DROP CONSTRAINT "FK_a638f83e3a10b841aca63f41796"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_v2_file" DROP CONSTRAINT "FK_c531a314d08ca7db36d1b6c29c3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_v2_file" DROP CONSTRAINT "FK_0fb11739ab343ba847c4c8ec92b"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_33d8efaabfaa4bfc2e970b2f85"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_552a1a665a1840770e96477662"`);
    await queryRunner.query(`DROP TABLE "application_additional_documents_v2_file"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5316862855ea11d8306e55ab5e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a638f83e3a10b841aca63f4179"`);
    await queryRunner.query(`DROP TABLE "application_proofs_of_income_v2_file"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c531a314d08ca7db36d1b6c29c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0fb11739ab343ba847c4c8ec92"`);
    await queryRunner.query(`DROP TABLE "application_photo_ids_v2_file"`);
    await queryRunner.query(`DROP TABLE "file"`);
  }
}

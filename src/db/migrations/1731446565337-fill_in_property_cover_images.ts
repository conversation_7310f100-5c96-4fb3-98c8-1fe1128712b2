import { MigrationInterface, QueryRunner } from 'typeorm';

export class FillInPropertyCoverImages1731446565337 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update properties that don't have a coverImage set
    await queryRunner.query(`
      WITH first_image_per_property AS (
        SELECT DISTINCT ON (p.id)
          p.id AS "propertyId",
          COALESCE(f."thumbnailUrl", f."url") AS "imageUrl"
        FROM
          "property" p
          JOIN "property_images_v2_file" pif ON p.id = pif."propertyId"
          JOIN "file" f ON pif."fileId" = f.id
        WHERE
          f."isDeleted" = false AND (p."coverImage" IS NULL OR p."coverImage" = '')
        ORDER BY
          p.id,
          f."order" ASC
      )
      UPDATE "property" p
      SET "coverImage" = fipp."imageUrl"
      FROM first_image_per_property fipp
      WHERE p.id = fipp."propertyId";
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Optionally reset the coverImage field to NULL
    await queryRunner.query(`
      UPDATE "property"
      SET "coverImage" = NULL
      WHERE "coverImage" IS NOT NULL;
    `);
  }
}

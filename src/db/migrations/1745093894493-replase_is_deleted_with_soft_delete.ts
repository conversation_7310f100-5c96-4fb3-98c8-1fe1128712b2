import { MigrationInterface, QueryRunner } from 'typeorm';

export class ReplaseIsDeletedWithSoftDelete1745093894493 implements MigrationInterface {
  name = 'ReplaseIsDeletedWithSoftDelete1745093894493';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, add the new deletedAt column to each table
    await queryRunner.query(`ALTER TABLE "user" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "company" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "showing_request" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "amenities" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "property_inquiry" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "property" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "showing" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "renter" ADD "deletedAt" TIMESTAMP`);

    // Update all records where isDeleted was true to have a timestamp in deletedAt
    await queryRunner.query(`UPDATE "user" SET "deletedAt" = NOW() WHERE "isDeleted" = true`);
    await queryRunner.query(`UPDATE "company" SET "deletedAt" = NOW() WHERE "isDeleted" = true`);
    await queryRunner.query(`UPDATE "showing_request" SET "deletedAt" = NOW() WHERE "isDeleted" = true`);
    await queryRunner.query(`UPDATE "amenities" SET "deletedAt" = NOW() WHERE "isDeleted" = true`);
    await queryRunner.query(`UPDATE "property_inquiry" SET "deletedAt" = NOW() WHERE "isDeleted" = true`);
    await queryRunner.query(`UPDATE "property" SET "deletedAt" = NOW() WHERE "isDeleted" = true`);
    await queryRunner.query(`UPDATE "showing" SET "deletedAt" = NOW() WHERE "isDeleted" = true`);
    await queryRunner.query(`UPDATE "renter" SET "deletedAt" = NOW() WHERE "isDeleted" = true`);

    // Drop the isDeleted column from each table
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "isDeleted"`);
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "isDeleted"`);
    await queryRunner.query(`ALTER TABLE "showing_request" DROP COLUMN "isDeleted"`);
    await queryRunner.query(`ALTER TABLE "amenities" DROP COLUMN "isDeleted"`);
    await queryRunner.query(`ALTER TABLE "property_inquiry" DROP COLUMN "isDeleted"`);
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "isDeleted"`);
    await queryRunner.query(`ALTER TABLE "showing" DROP COLUMN "isDeleted"`);
    await queryRunner.query(`ALTER TABLE "renter" DROP COLUMN "isDeleted"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back the isDeleted column to each table
    await queryRunner.query(`ALTER TABLE "user" ADD "isDeleted" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "company" ADD "isDeleted" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "showing_request" ADD "isDeleted" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "amenities" ADD "isDeleted" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "property_inquiry" ADD "isDeleted" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "property" ADD "isDeleted" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "showing" ADD "isDeleted" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "renter" ADD "isDeleted" boolean NOT NULL DEFAULT false`);

    // Update isDeleted to true for records that have a deletedAt timestamp
    await queryRunner.query(`UPDATE "user" SET "isDeleted" = true WHERE "deletedAt" IS NOT NULL`);
    await queryRunner.query(`UPDATE "company" SET "isDeleted" = true WHERE "deletedAt" IS NOT NULL`);
    await queryRunner.query(`UPDATE "showing_request" SET "isDeleted" = true WHERE "deletedAt" IS NOT NULL`);
    await queryRunner.query(`UPDATE "amenities" SET "isDeleted" = true WHERE "deletedAt" IS NOT NULL`);
    await queryRunner.query(`UPDATE "property_inquiry" SET "isDeleted" = true WHERE "deletedAt" IS NOT NULL`);
    await queryRunner.query(`UPDATE "property" SET "isDeleted" = true WHERE "deletedAt" IS NOT NULL`);
    await queryRunner.query(`UPDATE "showing" SET "isDeleted" = true WHERE "deletedAt" IS NOT NULL`);
    await queryRunner.query(`UPDATE "renter" SET "isDeleted" = true WHERE "deletedAt" IS NOT NULL`);

    // Drop the deletedAt column from each table
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "showing_request" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "amenities" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "property_inquiry" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "showing" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "renter" DROP COLUMN "deletedAt"`);
  }
}

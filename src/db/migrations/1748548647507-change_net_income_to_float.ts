import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeNetIncomeToFloat1748548647507 implements MigrationInterface {
  name = 'ChangeNetIncomeToFloat1748548647507';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_renter_employment_info" ALTER COLUMN "netIncome" TYPE double precision`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_renter_employment_info" ALTER COLUMN "netIncome" TYPE integer`);
  }
}

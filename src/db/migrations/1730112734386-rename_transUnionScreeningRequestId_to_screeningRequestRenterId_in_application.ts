import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddScreeningRequestRenterIdToApplication1730112734386 implements MigrationInterface {
  name = 'AddScreeningRequestRenterIdToApplication1730112734386';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application" RENAME COLUMN "transUnionScreeningRequestId" TO "screeningRequestRenterId"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application" RENAME COLUMN "screeningRequestRenterId" TO "transUnionScreeningRequestId"`,
    );
  }
}

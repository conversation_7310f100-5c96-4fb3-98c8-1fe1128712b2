import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPropertyImageAnalysisData1750000000001 implements MigrationInterface {
  name = 'AddPropertyImageAnalysisData1750000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add image tags
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "tags" text array`);

    // Image quality metrics
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "qualityLighting" integer`);
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "qualityComposition" integer`);
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "qualityClarity" integer`);
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "qualityOverall" integer`);

    // Staging metrics
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "stagingCleanliness" integer`);
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "stagingFurnished" boolean`);
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "stagingDecluttered" integer`);

    // Appeal metrics
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "uniqueness" integer`);
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "marketAppeal" integer`);

    // Analysis metadata
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "analysisConfidence" integer`);
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "analysisVersion" character varying(10)`);

    // Add timestamps
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "property_images_file" ADD "updatedAt" TIMESTAMP NOT NULL DEFAULT now()`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "updatedAt"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "createdAt"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "analysisVersion"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "analysisConfidence"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "marketAppeal"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "uniqueness"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "stagingDecluttered"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "stagingFurnished"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "stagingCleanliness"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "qualityOverall"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "qualityClarity"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "qualityComposition"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "qualityLighting"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP COLUMN "tags"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddScreeningSensetivityToPropertyReqs1751623299986 implements MigrationInterface {
  name = 'AddScreeningSensetivityToPropertyReqs1751623299986';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP CONSTRAINT "FK_1c888219fe580e97ce3f56c7c03"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP CONSTRAINT "FK_0a3c023e7cda311a5922bd8cd26"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0a3c023e7cda311a5922bd8cd2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1c888219fe580e97ce3f56c7c0"`);
    await queryRunner.query(
      `CREATE TYPE "public"."renter_requirements_screeningsensitivity_enum" AS ENUM('Strict', 'Moderate', 'Owner Review')`,
    );
    await queryRunner.query(
      `ALTER TABLE "renter_requirements" ADD "screeningSensitivity" "public"."renter_requirements_screeningsensitivity_enum" NOT NULL DEFAULT 'Moderate'`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_images_file" ADD CONSTRAINT "FK_1c888219fe580e97ce3f56c7c03" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_images_file" ADD CONSTRAINT "FK_0a3c023e7cda311a5922bd8cd26" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP CONSTRAINT "FK_0a3c023e7cda311a5922bd8cd26"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP CONSTRAINT "FK_1c888219fe580e97ce3f56c7c03"`);
    await queryRunner.query(`ALTER TABLE "renter_requirements" DROP COLUMN "screeningSensitivity"`);
    await queryRunner.query(`DROP TYPE "public"."renter_requirements_screeningsensitivity_enum"`);
    await queryRunner.query(`CREATE INDEX "IDX_1c888219fe580e97ce3f56c7c0" ON "property_images_file" ("propertyId") `);
    await queryRunner.query(`CREATE INDEX "IDX_0a3c023e7cda311a5922bd8cd2" ON "property_images_file" ("fileId") `);
    await queryRunner.query(
      `ALTER TABLE "property_images_file" ADD CONSTRAINT "FK_0a3c023e7cda311a5922bd8cd26" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_images_file" ADD CONSTRAINT "FK_1c888219fe580e97ce3f56c7c03" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPropertyDateListed1735912437499 implements MigrationInterface {
  name = 'AddPropertyDateListed1735912437499';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" ADD "listedAt" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "listedAt"`);
  }
}

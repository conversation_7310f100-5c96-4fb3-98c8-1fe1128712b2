import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApplicationWaitingForReportStatus1734298006912 implements MigrationInterface {
  name = 'AddApplicationWaitingForReportStatus1734298006912';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application" DROP CONSTRAINT "FK_17c3290105fc33d26a5daed009f"`);
    await queryRunner.query(`ALTER TYPE "public"."application_status_enum" RENAME TO "application_status_enum_old"`);
    await queryRunner.query(
      `CREATE TYPE "public"."application_status_enum" AS ENUM('In Progress', 'Waiting for Report', 'Completed')`,
    );
    await queryRunner.query(`ALTER TABLE "application" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "application" ALTER COLUMN "status" TYPE "public"."application_status_enum" USING "status"::"text"::"public"."application_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "application" ALTER COLUMN "status" SET DEFAULT 'In Progress'`);
    await queryRunner.query(`DROP TYPE "public"."application_status_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "application" ADD CONSTRAINT "FK_17c3290105fc33d26a5daed009f" FOREIGN KEY ("renterDetailsId") REFERENCES "application_renter_details"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application" DROP CONSTRAINT "FK_17c3290105fc33d26a5daed009f"`);
    await queryRunner.query(`CREATE TYPE "public"."application_status_enum_old" AS ENUM('In Progress', 'Completed')`);
    await queryRunner.query(`ALTER TABLE "application" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "application" ALTER COLUMN "status" TYPE "public"."application_status_enum_old" USING "status"::"text"::"public"."application_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "application" ALTER COLUMN "status" SET DEFAULT 'In Progress'`);
    await queryRunner.query(`DROP TYPE "public"."application_status_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."application_status_enum_old" RENAME TO "application_status_enum"`);
    await queryRunner.query(
      `ALTER TABLE "application" ADD CONSTRAINT "FK_17c3290105fc33d26a5daed009f" FOREIGN KEY ("renterDetailsId") REFERENCES "application_renter_details"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}

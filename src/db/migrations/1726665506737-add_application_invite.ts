import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApplicationInvite1726665506737 implements MigrationInterface {
  name = 'AddApplicationInvite1726665506737';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application" DROP CONSTRAINT "FK_f9106467f7204b2028a8f498aec"`);
    await queryRunner.query(
      `CREATE TYPE "public"."application_invite_status_enum" AS ENUM('Pending', 'Accepted', 'Rejected', 'Expired', 'Cancelled', 'Revoked')`,
    );
    await queryRunner.query(`CREATE TYPE "public"."application_invite_type_enum" AS ENUM('Applicant', 'Co-Signer')`);
    await queryRunner.query(
      `CREATE TABLE "application_invite" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."application_invite_status_enum" NOT NULL DEFAULT 'Pending', "type" "public"."application_invite_type_enum" NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "applicationBundleId" uuid NOT NULL, "renterId" uuid NOT NULL, CONSTRAINT "PK_3f638247b22d270b54356f1b337" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "application_bundle_applications_application" ("applicationBundleId" uuid NOT NULL, "applicationId" uuid NOT NULL, CONSTRAINT "PK_894e5a32481b5b8800156175672" PRIMARY KEY ("applicationBundleId", "applicationId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_48c7c7ca996771112efcc6e820" ON "application_bundle_applications_application" ("applicationBundleId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ee068bbb6d38f049c7d4e3c015" ON "application_bundle_applications_application" ("applicationId") `,
    );
    await queryRunner.query(`ALTER TABLE "application" DROP COLUMN "applicationBundleId"`);
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_stage_enum" RENAME TO "property_inquiry_stage_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_stage_enum" AS ENUM('Initial Contact', 'Showing Requested', 'Showing Declined', 'Showing Reschedule Requested by Renter', 'Showing Reschedule Requested by Owner', 'Showing Confirmed', 'Showing Completed', 'Showing Cancelled by Renter', 'Showing Cancelled by Owner', 'Showing Request Ignored by Owner', 'Stopped Property Rented Out', 'Application Invite Sent')`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ALTER COLUMN "stage" TYPE "public"."property_inquiry_stage_enum" USING "stage"::"text"::"public"."property_inquiry_stage_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" SET DEFAULT 'Initial Contact'`);
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_stage_enum_old"`);
    await queryRunner.query(`ALTER TYPE "public"."application_status_enum" RENAME TO "application_status_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."application_status_enum" AS ENUM('In Progress', 'Completed')`);
    await queryRunner.query(`ALTER TABLE "application" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "application" ALTER COLUMN "status" TYPE "public"."application_status_enum" USING "status"::"text"::"public"."application_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "application" ALTER COLUMN "status" SET DEFAULT 'In Progress'`);
    await queryRunner.query(`DROP TYPE "public"."application_status_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "application_invite" ADD CONSTRAINT "FK_e7ed1bc65725a23f2afc8d662cb" FOREIGN KEY ("applicationBundleId") REFERENCES "application_bundle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_invite" ADD CONSTRAINT "FK_9f84e319e1dfaf4bfc42fb4fde3" FOREIGN KEY ("renterId") REFERENCES "renter"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_bundle_applications_application" ADD CONSTRAINT "FK_48c7c7ca996771112efcc6e8205" FOREIGN KEY ("applicationBundleId") REFERENCES "application_bundle"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_bundle_applications_application" ADD CONSTRAINT "FK_ee068bbb6d38f049c7d4e3c0151" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_bundle_applications_application" DROP CONSTRAINT "FK_ee068bbb6d38f049c7d4e3c0151"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_bundle_applications_application" DROP CONSTRAINT "FK_48c7c7ca996771112efcc6e8205"`,
    );
    await queryRunner.query(`ALTER TABLE "application_invite" DROP CONSTRAINT "FK_9f84e319e1dfaf4bfc42fb4fde3"`);
    await queryRunner.query(`ALTER TABLE "application_invite" DROP CONSTRAINT "FK_e7ed1bc65725a23f2afc8d662cb"`);
    await queryRunner.query(
      `CREATE TYPE "public"."application_status_enum_old" AS ENUM('Completed', 'Pending', 'Started')`,
    );
    await queryRunner.query(`ALTER TABLE "application" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "application" ALTER COLUMN "status" TYPE "public"."application_status_enum_old" USING "status"::"text"::"public"."application_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "application" ALTER COLUMN "status" SET DEFAULT 'Pending'`);
    await queryRunner.query(`DROP TYPE "public"."application_status_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."application_status_enum_old" RENAME TO "application_status_enum"`);
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_stage_enum_old" AS ENUM('Application Sent', 'Initial Contact', 'Showing Cancelled by Owner', 'Showing Cancelled by Renter', 'Showing Completed', 'Showing Confirmed', 'Showing Declined', 'Showing Request Ignored by Owner', 'Showing Requested', 'Showing Reschedule Requested by Owner', 'Showing Reschedule Requested by Renter', 'Stopped Property Rented Out')`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ALTER COLUMN "stage" TYPE "public"."property_inquiry_stage_enum_old" USING "stage"::"text"::"public"."property_inquiry_stage_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" SET DEFAULT 'Initial Contact'`);
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_stage_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_stage_enum_old" RENAME TO "property_inquiry_stage_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "application" ADD "applicationBundleId" uuid NOT NULL`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ee068bbb6d38f049c7d4e3c015"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_48c7c7ca996771112efcc6e820"`);
    await queryRunner.query(`DROP TABLE "application_bundle_applications_application"`);
    await queryRunner.query(`DROP TABLE "application_invite"`);
    await queryRunner.query(`DROP TYPE "public"."application_invite_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."application_invite_status_enum"`);
    await queryRunner.query(
      `ALTER TABLE "application" ADD CONSTRAINT "FK_f9106467f7204b2028a8f498aec" FOREIGN KEY ("applicationBundleId") REFERENCES "application_bundle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}

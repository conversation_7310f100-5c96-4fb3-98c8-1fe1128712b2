import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApplication1726431464355 implements MigrationInterface {
  name = 'AddApplication1726431464355';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."application_bundle_status_enum" AS ENUM('Sent', 'In Progress', 'Submitted', 'Approved', 'Rejected')`,
    );
    await queryRunner.query(
      `CREATE TABLE "application_bundle" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "numberOfOccupantsUnderEighteen" integer, "status" "public"."application_bundle_status_enum" NOT NULL DEFAULT 'Sent', "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "propertyId" uuid NOT NULL, "primaryApplicantId" uuid NOT NULL, "companyId" uuid NOT NULL, CONSTRAINT "PK_f29ee3ddc86fdbca9bda3d8b6d5" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a9afea72a7ef45c7d2ecd6327c" ON "application_bundle" ("primaryApplicantId") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_70d31a650a80f53b004a44aa2b" ON "application_bundle" ("companyId") `);
    await queryRunner.query(
      `CREATE TYPE "public"."application_backgroundcheckstatus_enum" AS ENUM('Not started', 'Started', 'Finished', 'Error')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."application_status_enum" AS ENUM('Pending', 'Started', 'Completed')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."application_type_enum" AS ENUM('Primary Applicant', 'Co-Applicant', 'Co-Signer')`,
    );
    await queryRunner.query(
      `CREATE TABLE "application" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "transUnionScreeningRequestId" character varying(512), "backgroundCheckStatus" "public"."application_backgroundcheckstatus_enum" NOT NULL DEFAULT 'Not started', "backgroundCheckUrl" character varying(512), "status" "public"."application_status_enum" NOT NULL DEFAULT 'Pending', "type" "public"."application_type_enum" NOT NULL, "isPaid" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "applicationBundleId" uuid NOT NULL, "renterId" uuid NOT NULL, CONSTRAINT "PK_569e0c3e863ebdf5f2408ee1670" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_stage_enum" RENAME TO "property_inquiry_stage_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_stage_enum" AS ENUM('Initial Contact', 'Showing Requested', 'Showing Declined', 'Showing Reschedule Requested by Renter', 'Showing Reschedule Requested by Owner', 'Showing Confirmed', 'Showing Completed', 'Showing Cancelled by Renter', 'Showing Cancelled by Owner', 'Showing Request Ignored by Owner', 'Stopped Property Rented Out', 'Application Sent')`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ALTER COLUMN "stage" TYPE "public"."property_inquiry_stage_enum" USING "stage"::"text"::"public"."property_inquiry_stage_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" SET DEFAULT 'Initial Contact'`);
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_stage_enum_old"`);
    await queryRunner.query(`CREATE INDEX "IDX_085a1d7157d6e0adf3e44afa99" ON "property_inquiry" ("renterId") `);
    await queryRunner.query(`CREATE INDEX "IDX_f0f4caf7705c509b64063fc16b" ON "property_inquiry" ("propertyId") `);
    await queryRunner.query(
      `ALTER TABLE "application_bundle" ADD CONSTRAINT "FK_5bd9209abcd2b97fc1cd9e226dd" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_bundle" ADD CONSTRAINT "FK_a9afea72a7ef45c7d2ecd6327cc" FOREIGN KEY ("primaryApplicantId") REFERENCES "renter"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_bundle" ADD CONSTRAINT "FK_70d31a650a80f53b004a44aa2b0" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "application" ADD CONSTRAINT "FK_f9106467f7204b2028a8f498aec" FOREIGN KEY ("applicationBundleId") REFERENCES "application_bundle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "application" ADD CONSTRAINT "FK_5e8ded363d1bb32c996baea92bc" FOREIGN KEY ("renterId") REFERENCES "renter"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application" DROP CONSTRAINT "FK_5e8ded363d1bb32c996baea92bc"`);
    await queryRunner.query(`ALTER TABLE "application" DROP CONSTRAINT "FK_f9106467f7204b2028a8f498aec"`);
    await queryRunner.query(`ALTER TABLE "application_bundle" DROP CONSTRAINT "FK_70d31a650a80f53b004a44aa2b0"`);
    await queryRunner.query(`ALTER TABLE "application_bundle" DROP CONSTRAINT "FK_a9afea72a7ef45c7d2ecd6327cc"`);
    await queryRunner.query(`ALTER TABLE "application_bundle" DROP CONSTRAINT "FK_5bd9209abcd2b97fc1cd9e226dd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f0f4caf7705c509b64063fc16b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_085a1d7157d6e0adf3e44afa99"`);
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_stage_enum_old" AS ENUM('Initial Contact', 'Showing Requested', 'Showing Declined', 'Showing Reschedule Requested by Renter', 'Showing Reschedule Requested by Owner', 'Showing Confirmed', 'Showing Completed', 'Showing Cancelled by Renter', 'Showing Cancelled by Owner', 'Showing Request Ignored by Owner', 'Stopped Property Rented Out')`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ALTER COLUMN "stage" TYPE "public"."property_inquiry_stage_enum_old" USING "stage"::"text"::"public"."property_inquiry_stage_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" SET DEFAULT 'Initial Contact'`);
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_stage_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_stage_enum_old" RENAME TO "property_inquiry_stage_enum"`,
    );
    await queryRunner.query(`DROP TABLE "application"`);
    await queryRunner.query(`DROP TYPE "public"."application_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."application_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."application_backgroundcheckstatus_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_70d31a650a80f53b004a44aa2b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a9afea72a7ef45c7d2ecd6327c"`);
    await queryRunner.query(`DROP TABLE "application_bundle"`);
    await queryRunner.query(`DROP TYPE "public"."application_bundle_status_enum"`);
  }
}

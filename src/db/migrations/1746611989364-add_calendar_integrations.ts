import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCalendarIntegrations1746611989364 implements MigrationInterface {
  name = 'AddCalendarIntegrations1746611989364';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."calendar_integration_provider_enum" AS ENUM('google')`);
    await queryRunner.query(
      `CREATE TABLE "calendar_integration" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "userId" uuid NOT NULL, "provider" "public"."calendar_integration_provider_enum" NOT NULL, "accessToken" text NOT NULL, "refreshToken" text NOT NULL, "tokenExpiry" TIMESTAMP, "isActive" boolean NOT NULL DEFAULT true, "isPrimary" boolean NOT NULL DEFAULT true, "deletedAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_2c53f913a5cc0499e72e9bf9257" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "calendar_integration" ADD CONSTRAINT "FK_4bdc2b6ee23a128b5c869ea9438" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "calendar_integration" DROP CONSTRAINT "FK_4bdc2b6ee23a128b5c869ea9438"`);
    await queryRunner.query(`DROP TABLE "calendar_integration"`);
    await queryRunner.query(`DROP TYPE "public"."calendar_integration_provider_enum"`);
  }
}

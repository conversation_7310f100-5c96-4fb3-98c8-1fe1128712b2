import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixMessageForeignKeys1742535982882 implements MigrationInterface {
  name = 'FixMessageForeignKeys1742535982882';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "conversation_messages_message" DROP CONSTRAINT "FK_conversation_messages_message"`,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation_messages_message" DROP CONSTRAINT "FK_conversation_messages_conversation"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d2649e7c02da7935806fa1ffb1" ON "conversation_messages_message" ("conversationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b0ce0656ce6ce80b54ce3174b9" ON "conversation_messages_message" ("messageId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation_messages_message" ADD CONSTRAINT "FK_d2649e7c02da7935806fa1ffb1e" FOREIGN KEY ("conversationId") REFERENCES "conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation_messages_message" ADD CONSTRAINT "FK_b0ce0656ce6ce80b54ce3174b92" FOREIGN KEY ("messageId") REFERENCES "message"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "conversation_messages_message" DROP CONSTRAINT "FK_b0ce0656ce6ce80b54ce3174b92"`,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation_messages_message" DROP CONSTRAINT "FK_d2649e7c02da7935806fa1ffb1e"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_b0ce0656ce6ce80b54ce3174b9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d2649e7c02da7935806fa1ffb1"`);
    await queryRunner.query(
      `ALTER TABLE "conversation_messages_message" ADD CONSTRAINT "FK_conversation_messages_conversation" FOREIGN KEY ("conversationId") REFERENCES "conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation_messages_message" ADD CONSTRAINT "FK_conversation_messages_message" FOREIGN KEY ("messageId") REFERENCES "message"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRenterConfirmationFlagToShowing1745309176217 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "showing" ADD "renterConfirmedAttendance" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "showing" DROP COLUMN "renterConfirmedAttendance"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddManagerNameToApplicationRenterEmploymentInfoEntity1730746824063 implements MigrationInterface {
  name = 'AddManagerNameToApplicationRenterEmploymentInfoEntity1730746824063';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_renter_employment_info" ADD "managerName" character varying(128)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_renter_employment_info" DROP COLUMN "managerName"`);
  }
}

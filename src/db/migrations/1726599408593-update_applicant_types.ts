import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateApplicantTypes1726599408593 implements MigrationInterface {
  name = 'UpdateApplicantTypes1726599408593';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."application_type_enum" RENAME TO "application_type_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."application_type_enum" AS ENUM('Applicant', 'Co-Signer')`);
    await queryRunner.query(
      `ALTER TABLE "application" ALTER COLUMN "type" TYPE "public"."application_type_enum" USING "type"::"text"::"public"."application_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."application_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."application_type_enum_old" AS ENUM('Primary Applicant', 'Co-Applicant', 'Co-Signer')`,
    );
    await queryRunner.query(
      `ALTER TABLE "application" ALTER COLUMN "type" TYPE "public"."application_type_enum_old" USING "type"::"text"::"public"."application_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."application_type_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."application_type_enum_old" RENAME TO "application_type_enum"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddShowingAgent1745601433038 implements MigrationInterface {
  name = 'AddShowingAgent1745601433038';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "showing_agent" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "firstName" character varying(100) NOT NULL, "lastName" character varying(100) NOT NULL, "phone" character varying(20) NOT NULL, "companyId" uuid NOT NULL, "isActive" boolean NOT NULL DEFAULT true, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_b69e4757f5d970dd2aae2a13f53" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "showing" ADD "showingAgentId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "showing_agent" ADD CONSTRAINT "FK_f29b9a48b26912c8c98440ee63b" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "showing" ADD CONSTRAINT "FK_a6df60ad3b2b0b338cddbbcd52c" FOREIGN KEY ("showingAgentId") REFERENCES "showing_agent"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "showing" DROP CONSTRAINT "FK_a6df60ad3b2b0b338cddbbcd52c"`);
    await queryRunner.query(`ALTER TABLE "showing_agent" DROP CONSTRAINT "FK_f29b9a48b26912c8c98440ee63b"`);
    await queryRunner.query(`ALTER TABLE "showing" DROP COLUMN "showingAgentId"`);
    await queryRunner.query(`DROP TABLE "showing_agent"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserVerificationTypeForRenterInvite1725786142441 implements MigrationInterface {
  name = 'AddUserVerificationTypeForRenterInvite1725786142441';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."user_verification_type_enum" RENAME TO "user_verification_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_verification_type_enum" AS ENUM('Password reset token', 'Email confirmation code', 'One time sign in code', 'Renter activation token')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_verification" ALTER COLUMN "type" TYPE "public"."user_verification_type_enum" USING "type"::"text"::"public"."user_verification_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."user_verification_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."user_verification_type_enum_old" AS ENUM('Password reset token', 'Email confirmation code', 'One time sign in code')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_verification" ALTER COLUMN "type" TYPE "public"."user_verification_type_enum_old" USING "type"::"text"::"public"."user_verification_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."user_verification_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."user_verification_type_enum_old" RENAME TO "user_verification_type_enum"`,
    );
  }
}

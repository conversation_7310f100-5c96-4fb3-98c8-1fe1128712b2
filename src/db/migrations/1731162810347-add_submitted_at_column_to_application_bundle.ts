import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSubmittedAtColumnToApplicationBundle1731162810347 implements MigrationInterface {
  name = 'AddSubmittedAtColumnToApplicationBundle1731162810347';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_bundle" ADD "submittedAt" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_bundle" DROP COLUMN "submittedAt"`);
  }
}

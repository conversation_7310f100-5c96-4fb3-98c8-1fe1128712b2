import { MigrationInterface, QueryRunner } from 'typeorm';

export class MovePetPhotosToApplication1734450030391 implements MigrationInterface {
  name = 'MovePetPhotosToApplication1734450030391';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "application_pet_photos_file" ("applicationId" uuid NOT NULL, "fileId" uuid NOT NULL, CONSTRAINT "PK_71c8a36e2c68d438fa6a018b68e" PRIMARY KEY ("applicationId", "fileId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a7d28f001e1d2e3d879a3c8eef" ON "application_pet_photos_file" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f9caa081919ee0e319541fc1af" ON "application_pet_photos_file" ("fileId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "application_pet_photos_file" ADD CONSTRAINT "FK_a7d28f001e1d2e3d879a3c8eefd" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_pet_photos_file" ADD CONSTRAINT "FK_f9caa081919ee0e319541fc1afa" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );

    await queryRunner.query('DROP TABLE "application_renter_details_pet_photos_file"');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_pet_photos_file" DROP CONSTRAINT "FK_f9caa081919ee0e319541fc1afa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_pet_photos_file" DROP CONSTRAINT "FK_a7d28f001e1d2e3d879a3c8eefd"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_f9caa081919ee0e319541fc1af"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a7d28f001e1d2e3d879a3c8eef"`);
    await queryRunner.query(`DROP TABLE "application_pet_photos_file"`);
  }
}

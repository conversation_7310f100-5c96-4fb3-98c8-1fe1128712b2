import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPaymentHistory1730321810066 implements MigrationInterface {
  name = 'AddPaymentHistory1730321810066';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."payment_history_status_enum" AS ENUM('Failed', 'Succeeded')`);
    await queryRunner.query(
      `CREATE TYPE "public"."payment_history_product_enum" AS ENUM('Application Base Package', 'Application Full Package')`,
    );
    await queryRunner.query(`CREATE TYPE "public"."payment_history_provider_enum" AS ENUM('Stripe')`);
    await queryRunner.query(
      `CREATE TABLE "payment_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."payment_history_status_enum" NOT NULL, "product" "public"."payment_history_product_enum" NOT NULL, "provider" "public"."payment_history_provider_enum" NOT NULL DEFAULT 'Stripe', "deletedAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid NOT NULL, CONSTRAINT "PK_5fcec51a769b65c0c3c0987f11c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment_history" ADD CONSTRAINT "FK_34d643de1a588d2350297da5c24" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "payment_history" DROP CONSTRAINT "FK_34d643de1a588d2350297da5c24"`);
    await queryRunner.query(`DROP TABLE "payment_history"`);
    await queryRunner.query(`DROP TYPE "public"."payment_history_provider_enum"`);
    await queryRunner.query(`DROP TYPE "public"."payment_history_product_enum"`);
    await queryRunner.query(`DROP TYPE "public"."payment_history_status_enum"`);
  }
}

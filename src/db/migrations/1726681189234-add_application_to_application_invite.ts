import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApplicationToApplicationInvite1726681189234 implements MigrationInterface {
  name = 'AddApplicationToApplicationInvite1726681189234';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_invite" ADD "applicationId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "application_invite" ADD CONSTRAINT "FK_49f8d517c8c2615e59e352c4669" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_invite" DROP CONSTRAINT "FK_49f8d517c8c2615e59e352c4669"`);
    await queryRunner.query(`ALTER TABLE "application_invite" DROP COLUMN "applicationId"`);
  }
}

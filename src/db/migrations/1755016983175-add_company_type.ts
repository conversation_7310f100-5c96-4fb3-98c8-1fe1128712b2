import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCompanyType1755016983175 implements MigrationInterface {
  name = 'AddCompanyType1755016983175';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."investor_preferedscreeningsensitivity_enum" AS ENUM('Strict', 'Moderate', 'Owner Review')`,
    );
    await queryRunner.query(
      `ALTER TABLE "investor" ADD "preferredScreeningSensitivity" "public"."investor_preferedscreeningsensitivity_enum" NOT NULL DEFAULT 'Moderate'`,
    );
    await queryRunner.query(`CREATE TYPE "public"."company_companytype_enum" AS ENUM('Individual', 'Company')`);
    await queryRunner.query(`ALTER TABLE "company" ADD "companyType" "public"."company_companytype_enum"`);
    await queryRunner.query(`UPDATE "company" SET "companyType" = 'Company' WHERE "companyType" IS NULL`);
    await queryRunner.query(
      `UPDATE "investor" SET "preferredScreeningSensitivity" = 'Moderate' WHERE "preferredScreeningSensitivity" IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "companyType"`);
    await queryRunner.query(`DROP TYPE "public"."company_companytype_enum"`);
    await queryRunner.query(`ALTER TABLE "investor" DROP COLUMN "preferedScreeningSensitivity"`);
    await queryRunner.query(`DROP TYPE "public"."investor_preferedscreeningsensitivity_enum"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddVirtualTours1752680699912 implements MigrationInterface {
  name = 'AddVirtualTours1752680699912';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" ADD "allowsInPersonTours" boolean NOT NULL DEFAULT true`);
    await queryRunner.query(`ALTER TABLE "property" ADD "allowsVirtualTours" boolean`);
    await queryRunner.query(`CREATE TYPE "public"."showing_tourtype_enum" AS ENUM('In-person', 'Virtual')`);
    await queryRunner.query(
      `ALTER TABLE "showing" ADD "tourType" "public"."showing_tourtype_enum" NOT NULL DEFAULT 'In-person'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "showing" DROP COLUMN "tourType"`);
    await queryRunner.query(`DROP TYPE "public"."showing_tourtype_enum"`);
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "allowsVirtualTours"`);
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "allowsInPersonTours"`);
  }
}

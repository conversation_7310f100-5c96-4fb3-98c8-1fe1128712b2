import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameImageV2ToImage1731586412011 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_v2_file" RENAME TO "application_additional_documents_file"`,
    );
    await queryRunner.query(`ALTER TABLE "application_photo_ids_v2_file" RENAME TO "application_photo_ids_file"`);
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_v2_file" RENAME TO "application_proofs_of_income_file"`,
    );
    await queryRunner.query(`ALTER TABLE "property_images_v2_file" RENAME TO "property_images_file"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_file" RENAME TO "application_additional_documents_v2_file"`,
    );
    await queryRunner.query(`ALTER TABLE "application_photo_ids_file" RENAME TO "application_photo_ids_v2_file"`);
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_file" RENAME TO "application_proofs_of_income_v2_file"`,
    );
    await queryRunner.query(`ALTER TABLE "property_images_file" RENAME TO "property_images_v2_file"`);
  }
}

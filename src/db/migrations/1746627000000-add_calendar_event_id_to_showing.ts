import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCalendarEventIdToShowing1746627000000 implements MigrationInterface {
  name = 'AddCalendarEventIdToShowing1746627000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "showing" ADD "calendarEventId" text`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "showing" DROP COLUMN "calendarEventId"`);
  }
}

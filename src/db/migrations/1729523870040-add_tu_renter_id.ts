import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTuRenterId1729523870040 implements MigrationInterface {
  name = 'AddTuRenterId1729523870040';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "renter" ADD "transUnionRenterId" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "renter" DROP COLUMN "transUnionRenterId"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHousingVoucher1733929339981 implements MigrationInterface {
  name = 'AddHousingVoucher1733929339981';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "renter" ADD "hasHousingVoucher" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "renter" DROP COLUMN "hasHousingVoucher"`);
  }
}

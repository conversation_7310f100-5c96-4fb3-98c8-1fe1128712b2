import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropSlotInvestorNotNull1752420076858 implements MigrationInterface {
  name = 'DropSlotInvestorNotNull1752420076858';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "investor_availability" DROP CONSTRAINT "FK_37d6f1cc2287bc297114bfeeb8a"`);
    await queryRunner.query(`ALTER TABLE "investor_availability" ALTER COLUMN "investorId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "availability_slot" DROP CONSTRAINT "FK_4e275502c22d0c7793b1c4b0565"`);
    await queryRunner.query(`ALTER TABLE "availability_slot" ALTER COLUMN "investorAvailabilityId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "investor_availability" ADD CONSTRAINT "FK_37d6f1cc2287bc297114bfeeb8a" FOREIGN KEY ("investorId") REFERENCES "investor"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "availability_slot" ADD CONSTRAINT "FK_4e275502c22d0c7793b1c4b0565" FOREIGN KEY ("investorAvailabilityId") REFERENCES "investor_availability"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "availability_slot" DROP CONSTRAINT "FK_4e275502c22d0c7793b1c4b0565"`);
    await queryRunner.query(`ALTER TABLE "investor_availability" DROP CONSTRAINT "FK_37d6f1cc2287bc297114bfeeb8a"`);
    await queryRunner.query(`ALTER TABLE "availability_slot" ALTER COLUMN "investorAvailabilityId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "availability_slot" ADD CONSTRAINT "FK_4e275502c22d0c7793b1c4b0565" FOREIGN KEY ("investorAvailabilityId") REFERENCES "investor_availability"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "investor_availability" ALTER COLUMN "investorId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "investor_availability" ADD CONSTRAINT "FK_37d6f1cc2287bc297114bfeeb8a" FOREIGN KEY ("investorId") REFERENCES "investor"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}

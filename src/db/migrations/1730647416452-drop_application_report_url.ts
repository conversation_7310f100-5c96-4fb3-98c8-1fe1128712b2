import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropApplicationReportUrl1730647416452 implements MigrationInterface {
  name = 'DropApplicationReportUrl1730647416452';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application" DROP COLUMN "backgroundCheckUrl"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application" ADD "backgroundCheckUrl" character varying(512)`);
  }
}

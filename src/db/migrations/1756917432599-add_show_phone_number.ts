import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddShowPhoneNumber1756917432599 implements MigrationInterface {
  name = 'AddShowPhoneNumber1756917432599';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "company_settings" ADD "syndicateAgentNumber" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "data_feed" ADD "syndicateAgentNumber" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "data_feed" DROP COLUMN "syndicateAgentNumber"`);
    await queryRunner.query(`ALTER TABLE "company_settings" DROP COLUMN "syndicateAgentNumber"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddInvestorAddress1737229316204 implements MigrationInterface {
  name = 'AddInvestorAddress1737229316204';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "investor" ADD "state" character varying(20)`);
    await queryRunner.query(`ALTER TABLE "investor" ADD "city" character varying(120)`);
    await queryRunner.query(`ALTER TABLE "investor" ADD "address" character varying(100)`);
    await queryRunner.query(`ALTER TABLE "investor" ADD "zip" integer`);
    await queryRunner.query(`ALTER TABLE "investor" ADD "latitude" double precision`);
    await queryRunner.query(`ALTER TABLE "investor" ADD "longitude" double precision`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "investor" DROP COLUMN "longitude"`);
    await queryRunner.query(`ALTER TABLE "investor" DROP COLUMN "latitude"`);
    await queryRunner.query(`ALTER TABLE "investor" DROP COLUMN "zip"`);
    await queryRunner.query(`ALTER TABLE "investor" DROP COLUMN "address"`);
    await queryRunner.query(`ALTER TABLE "investor" DROP COLUMN "city"`);
    await queryRunner.query(`ALTER TABLE "investor" DROP COLUMN "state"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameApplicationBundleStatuses1732184965998 implements MigrationInterface {
  name = 'RenameApplicationBundleStatuses1732184965998';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. Create temp enum with all values
    await queryRunner.query(
      `ALTER TYPE "public"."application_bundle_status_enum" RENAME TO "application_bundle_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."application_bundle_status_enum" AS ENUM('Sent', 'In Progress', 'Submitted', 'Accepted', 'Declined', 'Approved', 'Rejected')`,
    );

    // 2. Remove default constraint
    await queryRunner.query(`ALTER TABLE "application_bundle" ALTER COLUMN "status" DROP DEFAULT`);

    // 3. Update column to use new enum
    await queryRunner.query(
      `ALTER TABLE "application_bundle" ALTER COLUMN "status" TYPE "public"."application_bundle_status_enum" USING "status"::"text"::"public"."application_bundle_status_enum"`,
    );

    // 4. Update existing values
    await queryRunner.query(`UPDATE "application_bundle" SET "status" = 'Accepted' WHERE "status" = 'Approved'`);
    await queryRunner.query(`UPDATE "application_bundle" SET "status" = 'Declined' WHERE "status" = 'Rejected'`);

    // 5. Create final enum with only new values
    await queryRunner.query(
      `ALTER TYPE "public"."application_bundle_status_enum" RENAME TO "application_bundle_status_enum_temp"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."application_bundle_status_enum" AS ENUM('Sent', 'In Progress', 'Submitted', 'Accepted', 'Declined')`,
    );

    // 6. Update column to use final enum
    await queryRunner.query(
      `ALTER TABLE "application_bundle" ALTER COLUMN "status" TYPE "public"."application_bundle_status_enum" USING "status"::"text"::"public"."application_bundle_status_enum"`,
    );

    // 7. Set default value and cleanup
    await queryRunner.query(`ALTER TABLE "application_bundle" ALTER COLUMN "status" SET DEFAULT 'Sent'`);
    await queryRunner.query(`DROP TYPE "public"."application_bundle_status_enum_old"`);
    await queryRunner.query(`DROP TYPE "public"."application_bundle_status_enum_temp"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."application_bundle_status_enum_old" AS ENUM('Approved', 'In Progress', 'Rejected', 'Sent', 'Submitted')`,
    );
    await queryRunner.query(`ALTER TABLE "application_bundle" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "application_bundle" ALTER COLUMN "status" TYPE "public"."application_bundle_status_enum_old" USING CASE
        WHEN "status" = 'Accepted' THEN 'Approved'::"public"."application_bundle_status_enum_old"
        WHEN "status" = 'Declined' THEN 'Rejected'::"public"."application_bundle_status_enum_old"
        ELSE "status"::"text"::"public"."application_bundle_status_enum_old"
      END`,
    );
    await queryRunner.query(`ALTER TABLE "application_bundle" ALTER COLUMN "status" SET DEFAULT 'Sent'`);
    await queryRunner.query(`DROP TYPE "public"."application_bundle_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."application_bundle_status_enum_old" RENAME TO "application_bundle_status_enum"`,
    );
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class ExistingInvestorsOnboarding1755691935213 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Insert onboarding rows for all investors that do not yet have one, set all flags to true
    await queryRunner.query(`
      INSERT INTO "investor_onboarding" (
        "id",
        "investorId",
        "initialOnboardingCompleted",
        "propertyOnboardingCompleted",
        "dashboardOnboardingCompleted",
        "renterProfileOnboardingCompleted",
        "createdAt",
        "updatedAt"
      )
      SELECT
        uuid_generate_v4(),
        i."id",
        TRUE,
        TRUE,
        TRUE,
        TRUE,
        NOW(),
        NOW()
      FROM "investor" i
      LEFT JOIN "investor_onboarding" io ON io."investorId" = i."id"
      WHERE io."id" IS NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rollback: set all-true flags back to false
    await queryRunner.query(`
      UPDATE "investor_onboarding"
      SET
        "initialOnboardingCompleted" = FALSE,
        "propertyOnboardingCompleted" = FALSE,
        "dashboardOnboardingCompleted" = FALSE,
        "renterProfileOnboardingCompleted" = FALSE,
        "updatedAt" = NOW()
      WHERE
        "initialOnboardingCompleted" = TRUE AND
        "propertyOnboardingCompleted" = TRUE AND
        "dashboardOnboardingCompleted" = TRUE AND
        "renterProfileOnboardingCompleted" = TRUE;
    `);
  }
}

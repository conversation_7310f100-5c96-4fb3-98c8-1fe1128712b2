import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAptlySupport1752421453164 implements MigrationInterface {
  name = 'AddAptlySupport1752421453164';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "company_settings" ADD "aptlyFeedUrl" character varying(512)`);
    await queryRunner.query(`ALTER TABLE "property" ADD "aptlyId" character varying(128)`);
    await queryRunner.query(`CREATE INDEX "IDX_4f11e5063d8daa7ca1d3923e3e" ON "property" ("aptlyId") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_4f11e5063d8daa7ca1d3923e3e"`);
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "aptlyId"`);
    await queryRunner.query(`ALTER TABLE "company_settings" DROP COLUMN "aptlyFeedUrl"`);
  }
}

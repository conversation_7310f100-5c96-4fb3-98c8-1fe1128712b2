import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddShowingReminderEnumTypes1745309176216 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."follow_up_type_enum" RENAME TO "follow_up_type_enum_old"`);
    await queryRunner.query(
      `CREATE TYPE "public"."follow_up_type_enum" AS ENUM('Renter Stopped Responding', 'Investor Showing Request Follow Up', 'Post Showing Follow Up', 'Post Showing Investor Follow Up', 'Showing Reminder Follow Up', 'Showing Cancellation Warning Follow Up', 'Investor Showing Reminder Follow Up')`,
    );
    await queryRunner.query(`ALTER TABLE "follow_up" ALTER COLUMN "type" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "follow_up" ALTER COLUMN "type" TYPE "public"."follow_up_type_enum" USING "type"::"text"::"public"."follow_up_type_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "follow_up" ALTER COLUMN "type" SET DEFAULT 'Renter Stopped Responding'`);
    await queryRunner.query(`DROP TYPE "public"."follow_up_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."follow_up_type_enum_old" AS ENUM('Renter Stopped Responding', 'Investor Showing Request Follow Up', 'Post Showing Follow Up', 'Post Showing Investor Follow Up', 'Showing Reminder Follow Up', 'Showing Cancellation Warning Follow Up')`,
    );
    await queryRunner.query(`ALTER TABLE "follow_up" ALTER COLUMN "type" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "follow_up" ALTER COLUMN "type" TYPE "public"."follow_up_type_enum_old" USING "type"::"text"::"public"."follow_up_type_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "follow_up" ALTER COLUMN "type" SET DEFAULT 'Renter Stopped Responding'`);
    await queryRunner.query(`DROP TYPE "public"."follow_up_type_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."follow_up_type_enum_old" RENAME TO "follow_up_type_enum"`);
  }
}

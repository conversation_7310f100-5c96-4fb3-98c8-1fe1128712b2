import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPropertyManagementFieldsToProperty1757251576080 implements MigrationInterface {
  name = 'AddPropertyManagementFieldsToProperty1757251576080';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."property_propertymanagementtype_enum" AS ENUM('Homeowner', 'Property management company')`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD "propertyManagementType" "public"."property_propertymanagementtype_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "property" ADD "propertyManagementCompanyName" character varying(255)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "propertyManagementCompanyName"`);
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "propertyManagementType"`);
    await queryRunner.query(`DROP TYPE "public"."property_propertymanagementtype_enum"`);
  }
}

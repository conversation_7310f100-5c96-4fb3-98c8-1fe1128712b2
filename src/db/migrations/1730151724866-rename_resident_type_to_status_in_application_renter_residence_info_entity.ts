import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameResidentTypeToStatusInApplicationRenterResidenceInfoEntity1730151724866
  implements MigrationInterface
{
  name = 'RenameResidentTypeToStatusInApplicationRenterResidenceInfoEntity1730151724866';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_renter_residence_info" RENAME COLUMN "residenceType" TO "status"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."application_renter_residence_info_residencetype_enum" RENAME TO "application_renter_residence_info_status_enum"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."application_renter_residence_info_status_enum" RENAME TO "application_renter_residence_info_residencetype_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_residence_info" RENAME COLUMN "status" TO "residenceType"`,
    );
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApplicationExpirationDate1726847339514 implements MigrationInterface {
  name = 'AddApplicationExpirationDate1726847339514';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application" ADD "expiresAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "application_bundle" ADD "expiresAt" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_bundle" DROP COLUMN "expiresAt"`);
    await queryRunner.query(`ALTER TABLE "application" DROP COLUMN "expiresAt"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRentcastFields1748014218529 implements MigrationInterface {
  name = 'AddRentcastFields1748014218529';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add lotSize field to property_specifications table
    await queryRunner.query(`ALTER TABLE "property_specifications" ADD "lotSize" integer`);

    // Add hoaFee field to property_specifications table
    await queryRunner.query(`ALTER TABLE "property_specifications" ADD "hoaFee" numeric(10,2)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the added fields
    await queryRunner.query(`ALTER TABLE "property_specifications" DROP COLUMN "hoaFee"`);
    await queryRunner.query(`ALTER TABLE "property_specifications" DROP COLUMN "lotSize"`);
  }
}

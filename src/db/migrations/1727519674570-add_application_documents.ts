import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApplicationDocuments1727519674570 implements MigrationInterface {
  name = 'AddApplicationDocuments1727519674570';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_renter_reference" DROP CONSTRAINT "FK_39ab2af07d9e5f0785dd2c3d395"`,
    );
    await queryRunner.query(
      `CREATE TABLE "application_renter_details_pet_photos_image" ("applicationRenterDetailsId" uuid NOT NULL, "imageId" integer NOT NULL, CONSTRAINT "PK_496dd21b541449b448018c0f0ba" PRIMARY KEY ("applicationRenterDetailsId", "imageId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c8ad9256a0f99f76ce7bb6f9d0" ON "application_renter_details_pet_photos_image" ("applicationRenterDetailsId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_85b77022420efce71b179bbb16" ON "application_renter_details_pet_photos_image" ("imageId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "application_photo_ids_image" ("applicationId" uuid NOT NULL, "imageId" integer NOT NULL, CONSTRAINT "PK_9d1d163482995ea40fb6f4ed5d8" PRIMARY KEY ("applicationId", "imageId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b1521da7465904e0610bd1fa26" ON "application_photo_ids_image" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d4b7a23f4894830d20ae21ea61" ON "application_photo_ids_image" ("imageId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "application_proofs_of_income_image" ("applicationId" uuid NOT NULL, "imageId" integer NOT NULL, CONSTRAINT "PK_e35c532df762273e8078a39561c" PRIMARY KEY ("applicationId", "imageId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_57f67e420d18d810f0eb7fe33d" ON "application_proofs_of_income_image" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_60e866321a9f5c01d311916974" ON "application_proofs_of_income_image" ("imageId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "application_additional_documents_image" ("applicationId" uuid NOT NULL, "imageId" integer NOT NULL, CONSTRAINT "PK_f47ce5294a502ed81f401ce15be" PRIMARY KEY ("applicationId", "imageId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8504c918ccf05cb77fb77bef41" ON "application_additional_documents_image" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_48e8c81311b87f4a1ad3321b81" ON "application_additional_documents_image" ("imageId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_reference" ADD CONSTRAINT "FK_3b71138cdb4db2c22497e56e02e" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_details_pet_photos_image" ADD CONSTRAINT "FK_c8ad9256a0f99f76ce7bb6f9d07" FOREIGN KEY ("applicationRenterDetailsId") REFERENCES "application_renter_details"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_details_pet_photos_image" ADD CONSTRAINT "FK_85b77022420efce71b179bbb167" FOREIGN KEY ("imageId") REFERENCES "image"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_image" ADD CONSTRAINT "FK_b1521da7465904e0610bd1fa260" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_image" ADD CONSTRAINT "FK_d4b7a23f4894830d20ae21ea61a" FOREIGN KEY ("imageId") REFERENCES "image"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_image" ADD CONSTRAINT "FK_57f67e420d18d810f0eb7fe33da" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_image" ADD CONSTRAINT "FK_60e866321a9f5c01d3119169749" FOREIGN KEY ("imageId") REFERENCES "image"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_image" ADD CONSTRAINT "FK_8504c918ccf05cb77fb77bef410" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_image" ADD CONSTRAINT "FK_48e8c81311b87f4a1ad3321b816" FOREIGN KEY ("imageId") REFERENCES "image"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_image" DROP CONSTRAINT "FK_48e8c81311b87f4a1ad3321b816"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_image" DROP CONSTRAINT "FK_8504c918ccf05cb77fb77bef410"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_image" DROP CONSTRAINT "FK_60e866321a9f5c01d3119169749"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_image" DROP CONSTRAINT "FK_57f67e420d18d810f0eb7fe33da"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_image" DROP CONSTRAINT "FK_d4b7a23f4894830d20ae21ea61a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_image" DROP CONSTRAINT "FK_b1521da7465904e0610bd1fa260"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_details_pet_photos_image" DROP CONSTRAINT "FK_85b77022420efce71b179bbb167"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_details_pet_photos_image" DROP CONSTRAINT "FK_c8ad9256a0f99f76ce7bb6f9d07"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_reference" DROP CONSTRAINT "FK_3b71138cdb4db2c22497e56e02e"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_48e8c81311b87f4a1ad3321b81"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8504c918ccf05cb77fb77bef41"`);
    await queryRunner.query(`DROP TABLE "application_additional_documents_image"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_60e866321a9f5c01d311916974"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_57f67e420d18d810f0eb7fe33d"`);
    await queryRunner.query(`DROP TABLE "application_proofs_of_income_image"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d4b7a23f4894830d20ae21ea61"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b1521da7465904e0610bd1fa26"`);
    await queryRunner.query(`DROP TABLE "application_photo_ids_image"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_85b77022420efce71b179bbb16"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c8ad9256a0f99f76ce7bb6f9d0"`);
    await queryRunner.query(`DROP TABLE "application_renter_details_pet_photos_image"`);
    await queryRunner.query(
      `ALTER TABLE "application_renter_reference" ADD CONSTRAINT "FK_39ab2af07d9e5f0785dd2c3d395" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}

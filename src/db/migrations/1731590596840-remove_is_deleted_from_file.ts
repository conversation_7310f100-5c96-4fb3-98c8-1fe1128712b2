import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveIsDeletedFromFile1731590596840 implements MigrationInterface {
  name = 'RemoveIsDeletedFromFile1731590596840';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP CONSTRAINT "FK_c436b65c6d9521f0a659ad328a3"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP CONSTRAINT "FK_52db3af5ddaeb930c47c538adfe"`);
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_file" DROP CONSTRAINT "FK_0fb11739ab343ba847c4c8ec92b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_file" DROP CONSTRAINT "FK_c531a314d08ca7db36d1b6c29c3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_file" DROP CONSTRAINT "FK_a638f83e3a10b841aca63f41796"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_file" DROP CONSTRAINT "FK_5316862855ea11d8306e55ab5ea"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_file" DROP CONSTRAINT "FK_552a1a665a1840770e964776620"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_file" DROP CONSTRAINT "FK_33d8efaabfaa4bfc2e970b2f857"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_c436b65c6d9521f0a659ad328a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_52db3af5ddaeb930c47c538adf"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0fb11739ab343ba847c4c8ec92"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c531a314d08ca7db36d1b6c29c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a638f83e3a10b841aca63f4179"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5316862855ea11d8306e55ab5e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_552a1a665a1840770e96477662"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_33d8efaabfaa4bfc2e970b2f85"`);
    await queryRunner.query(`ALTER TABLE "file" DROP COLUMN "isDeleted"`);
    await queryRunner.query(`CREATE INDEX "IDX_1c888219fe580e97ce3f56c7c0" ON "property_images_file" ("propertyId") `);
    await queryRunner.query(`CREATE INDEX "IDX_0a3c023e7cda311a5922bd8cd2" ON "property_images_file" ("fileId") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_8b700fad54fe58e004fc44e2ed" ON "application_photo_ids_file" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_edd8f8fb3e14e8b5f099e15561" ON "application_photo_ids_file" ("fileId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6fcda08bd937c40fbd15b98134" ON "application_proofs_of_income_file" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cba54eba3dca0807ccab6119b5" ON "application_proofs_of_income_file" ("fileId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_71f21c4f30cc77b0227f59182c" ON "application_additional_documents_file" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_516da542e161245c29afe8acd3" ON "application_additional_documents_file" ("fileId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "property_images_file" ADD CONSTRAINT "FK_1c888219fe580e97ce3f56c7c03" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_images_file" ADD CONSTRAINT "FK_0a3c023e7cda311a5922bd8cd26" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_file" ADD CONSTRAINT "FK_8b700fad54fe58e004fc44e2ed7" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_file" ADD CONSTRAINT "FK_edd8f8fb3e14e8b5f099e155612" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_file" ADD CONSTRAINT "FK_6fcda08bd937c40fbd15b98134c" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_file" ADD CONSTRAINT "FK_cba54eba3dca0807ccab6119b5d" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_file" ADD CONSTRAINT "FK_71f21c4f30cc77b0227f59182c4" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_file" ADD CONSTRAINT "FK_516da542e161245c29afe8acd32" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_file" DROP CONSTRAINT "FK_516da542e161245c29afe8acd32"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_file" DROP CONSTRAINT "FK_71f21c4f30cc77b0227f59182c4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_file" DROP CONSTRAINT "FK_cba54eba3dca0807ccab6119b5d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_file" DROP CONSTRAINT "FK_6fcda08bd937c40fbd15b98134c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_file" DROP CONSTRAINT "FK_edd8f8fb3e14e8b5f099e155612"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_file" DROP CONSTRAINT "FK_8b700fad54fe58e004fc44e2ed7"`,
    );
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP CONSTRAINT "FK_0a3c023e7cda311a5922bd8cd26"`);
    await queryRunner.query(`ALTER TABLE "property_images_file" DROP CONSTRAINT "FK_1c888219fe580e97ce3f56c7c03"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_516da542e161245c29afe8acd3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_71f21c4f30cc77b0227f59182c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cba54eba3dca0807ccab6119b5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6fcda08bd937c40fbd15b98134"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_edd8f8fb3e14e8b5f099e15561"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8b700fad54fe58e004fc44e2ed"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0a3c023e7cda311a5922bd8cd2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1c888219fe580e97ce3f56c7c0"`);
    await queryRunner.query(`ALTER TABLE "file" ADD "isDeleted" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(
      `CREATE INDEX "IDX_33d8efaabfaa4bfc2e970b2f85" ON "application_additional_documents_file" ("fileId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_552a1a665a1840770e96477662" ON "application_additional_documents_file" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5316862855ea11d8306e55ab5e" ON "application_proofs_of_income_file" ("fileId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a638f83e3a10b841aca63f4179" ON "application_proofs_of_income_file" ("applicationId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c531a314d08ca7db36d1b6c29c" ON "application_photo_ids_file" ("fileId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0fb11739ab343ba847c4c8ec92" ON "application_photo_ids_file" ("applicationId") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_52db3af5ddaeb930c47c538adf" ON "property_images_file" ("fileId") `);
    await queryRunner.query(`CREATE INDEX "IDX_c436b65c6d9521f0a659ad328a" ON "property_images_file" ("propertyId") `);
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_file" ADD CONSTRAINT "FK_33d8efaabfaa4bfc2e970b2f857" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_additional_documents_file" ADD CONSTRAINT "FK_552a1a665a1840770e964776620" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_file" ADD CONSTRAINT "FK_5316862855ea11d8306e55ab5ea" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_proofs_of_income_file" ADD CONSTRAINT "FK_a638f83e3a10b841aca63f41796" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_file" ADD CONSTRAINT "FK_c531a314d08ca7db36d1b6c29c3" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_photo_ids_file" ADD CONSTRAINT "FK_0fb11739ab343ba847c4c8ec92b" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_images_file" ADD CONSTRAINT "FK_52db3af5ddaeb930c47c538adfe" FOREIGN KEY ("fileId") REFERENCES "file"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_images_file" ADD CONSTRAINT "FK_c436b65c6d9521f0a659ad328a3" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }
}

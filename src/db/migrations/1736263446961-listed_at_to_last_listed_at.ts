import { MigrationInterface, QueryRunner } from 'typeorm';

export class ListedAtToLastListedAt1736263446961 implements MigrationInterface {
  name = 'ListedAtToLastListedAt1736263446961';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" RENAME COLUMN "listedAt" TO "lastListedAt"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" RENAME COLUMN "lastListedAt" TO "listedAt"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLastShowingAgentToProperty1748198871860 implements MigrationInterface {
  name = 'AddLastShowingAgentToProperty1748198871860';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" ADD "lastShowingAgentId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_fff85b24b5159ce95ae60ec12ad" FOREIGN KEY ("lastShowingAgentId") REFERENCES "showing_agent"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_fff85b24b5159ce95ae60ec12ad"`);
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "lastShowingAgentId"`);
  }
}

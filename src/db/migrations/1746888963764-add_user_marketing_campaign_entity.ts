import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserMarketingCampaignEntity1746888963764 implements MigrationInterface {
  name = 'AddUserMarketingCampaignEntity1746888963764';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user_marketing_campaign" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "utm_source" character varying(120) NOT NULL DEFAULT 'unknown', "utm_medium" character varying(120) NOT NULL DEFAULT 'unknown', "utm_campaign" character varying(120) NOT NULL DEFAULT 'unknown', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid NOT NULL, CONSTRAINT "REL_2f6f7b796dcf1fd7ac5c625421" UNIQUE ("userId"), CONSTRAINT "PK_7d531c100bf43b11bc5459f65f8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_marketing_campaign" ADD CONSTRAINT "FK_2f6f7b796dcf1fd7ac5c625421f" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_marketing_campaign" DROP CONSTRAINT "FK_2f6f7b796dcf1fd7ac5c625421f"`);
    await queryRunner.query(`DROP TABLE "user_marketing_campaign"`);
  }
}

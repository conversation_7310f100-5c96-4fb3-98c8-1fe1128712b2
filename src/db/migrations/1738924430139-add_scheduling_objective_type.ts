import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSchedulingObjectiveType1738924430139 implements MigrationInterface {
  name = 'AddSchedulingObjectiveType1738924430139';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."ai_objective_type_enum" RENAME TO "ai_objective_type_enum_old"`);
    await queryRunner.query(
      `CREATE TYPE "public"."ai_objective_type_enum" AS ENUM('Ask if renter has requirements question', 'Evaluate credit score', 'Schedule showing')`,
    );
    await queryRunner.query(
      `ALTER TABLE "ai_objective" ALTER COLUMN "type" TYPE "public"."ai_objective_type_enum" USING "type"::"text"::"public"."ai_objective_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."ai_objective_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."ai_objective_type_enum_old" AS ENUM('Ask if renter has requirements question', 'Evaluate credit score')`,
    );
    await queryRunner.query(
      `ALTER TABLE "ai_objective" ALTER COLUMN "type" TYPE "public"."ai_objective_type_enum_old" USING "type"::"text"::"public"."ai_objective_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."ai_objective_type_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."ai_objective_type_enum_old" RENAME TO "ai_objective_type_enum"`);
  }
}

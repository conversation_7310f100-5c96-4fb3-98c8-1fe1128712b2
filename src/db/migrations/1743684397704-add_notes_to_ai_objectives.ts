import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNotesToAiObjectives1743684397704 implements MigrationInterface {
  name = 'AddNotesToAiObjectives1743684397704';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "ai_objective" ADD "notes" text array NOT NULL DEFAULT '{}'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "ai_objective" DROP COLUMN "notes"`);
  }
}

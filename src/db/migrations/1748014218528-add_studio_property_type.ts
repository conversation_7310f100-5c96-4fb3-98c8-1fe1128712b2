import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStudioPropertyType1748014218528 implements MigrationInterface {
  name = 'AddStudioPropertyType1748014218528';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."property_specifications_propertytype_enum" RENAME TO "property_specifications_propertytype_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_specifications_propertytype_enum" AS ENUM('Room', 'Studio', 'Apartment', 'House', 'Condo', 'Townhouse', 'Duplex')`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_specifications" ALTER COLUMN "propertyType" TYPE "public"."property_specifications_propertytype_enum" USING "propertyType"::"text"::"public"."property_specifications_propertytype_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."property_specifications_propertytype_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."property_specifications_propertytype_enum_old" AS ENUM('Room', 'Apartment', 'House', 'Condo', 'Townhouse', 'Duplex')`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_specifications" ALTER COLUMN "propertyType" TYPE "public"."property_specifications_propertytype_enum_old" USING "propertyType"::"text"::"public"."property_specifications_propertytype_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."property_specifications_propertytype_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."property_specifications_propertytype_enum_old" RENAME TO "property_specifications_propertytype_enum"`,
    );
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMissingIndexes1736623302264 implements MigrationInterface {
  name = 'AddMissingIndexes1736623302264';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE INDEX "IDX_e12875dfb3b1d92d7d7c5377e2" ON "user" ("email") `);
    await queryRunner.query(`CREATE INDEX "IDX_f2578043e491921209f5dadd08" ON "user" ("phoneNumber") `);
    await queryRunner.query(`CREATE INDEX "IDX_be7d6d2bd691b7969bf5a5809a" ON "conversation" ("isAnswered") `);
    await queryRunner.query(`CREATE INDEX "IDX_66a03c6018e0d86da3a6ffba98" ON "property" ("status") `);
    await queryRunner.query(`CREATE INDEX "IDX_30d31fc67f50dc8ba0d8ed56d4" ON "inbox_history_version" ("email") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_30d31fc67f50dc8ba0d8ed56d4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_66a03c6018e0d86da3a6ffba98"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_be7d6d2bd691b7969bf5a5809a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f2578043e491921209f5dadd08"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e12875dfb3b1d92d7d7c5377e2"`);
  }
}

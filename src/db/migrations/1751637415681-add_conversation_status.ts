import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddConversationStatus1751637415681 implements MigrationInterface {
  name = 'AddConversationStatus1751637415681';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."conversation_status_enum" AS ENUM('Pending', 'In Progress', 'Answered', 'Error')`,
    );
    await queryRunner.query(
      `ALTER TABLE "conversation" ADD "status" "public"."conversation_status_enum" NOT NULL DEFAULT 'Pending'`,
    );

    await queryRunner.query(`UPDATE "conversation" SET "status" = 'Answered' WHERE "isAnswered" = true`);
    await queryRunner.query(`UPDATE "conversation" SET "status" = 'Pending' WHERE "isAnswered" = false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "conversation" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."conversation_status_enum"`);
  }
}

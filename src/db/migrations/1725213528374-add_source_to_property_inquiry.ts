import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSourceToPropertyInquiry1725213528374 implements MigrationInterface {
  name = 'AddSourceToPropertyInquiry1725213528374';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."property_inquiry_source_enum" AS ENUM('Zillow', 'Manually Added')`);
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ADD "source" "public"."property_inquiry_source_enum" NOT NULL DEFAULT 'Zillow'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property_inquiry" DROP COLUMN "source"`);
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_source_enum"`);
  }
}

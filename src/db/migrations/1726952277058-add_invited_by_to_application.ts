import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddInvitedByToApplication1726952277058 implements MigrationInterface {
  name = 'AddInvitedByToApplication1726952277058';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_invite" ADD "invitedById" uuid`);
    await queryRunner.query(
      `ALTER TABLE "application_invite" ADD CONSTRAINT "FK_096ecec1b666b9fc754a6f288ee" FOREIGN KEY ("invitedById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_invite" DROP CONSTRAINT "FK_096ecec1b666b9fc754a6f288ee"`);
    await queryRunner.query(`ALTER TABLE "application_invite" DROP COLUMN "invitedById"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNoLongerInterestedRentStage1728297434006 implements MigrationInterface {
  name = 'AddNoLongerInterestedRentStage1728297434006';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_stage_enum" RENAME TO "property_inquiry_stage_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_stage_enum" AS ENUM('Initial Contact', 'Showing Requested', 'Showing Declined', 'Showing Reschedule Requested by Ren<PERSON>', 'Showing Reschedule Requested by Owner', 'Showing Confirmed', 'Showing Completed', 'Showing Cancelled by Ren<PERSON>', 'Showing Cancelled by Owner', 'Showing Request Ignored by Owner', 'Stopped Property Rented Out', 'Application Invite Sent', 'No Longer Interested')`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ALTER COLUMN "stage" TYPE "public"."property_inquiry_stage_enum" USING "stage"::"text"::"public"."property_inquiry_stage_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" SET DEFAULT 'Initial Contact'`);
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_stage_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."property_inquiry_stage_enum_old" AS ENUM('Application Invite Sent', 'Initial Contact', 'Showing Cancelled by Owner', 'Showing Cancelled by Renter', 'Showing Completed', 'Showing Confirmed', 'Showing Declined', 'Showing Request Ignored by Owner', 'Showing Requested', 'Showing Reschedule Requested by Owner', 'Showing Reschedule Requested by Renter', 'Stopped Property Rented Out')`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "property_inquiry" ALTER COLUMN "stage" TYPE "public"."property_inquiry_stage_enum_old" USING "stage"::"text"::"public"."property_inquiry_stage_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "property_inquiry" ALTER COLUMN "stage" SET DEFAULT 'Initial Contact'`);
    await queryRunner.query(`DROP TYPE "public"."property_inquiry_stage_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."property_inquiry_stage_enum_old" RENAME TO "property_inquiry_stage_enum"`,
    );
  }
}

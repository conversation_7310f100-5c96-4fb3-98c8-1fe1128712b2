import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPropertyQuestionType1735831304409 implements MigrationInterface {
    name = 'AddPropertyQuestionType1735831304409'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."property_question_type_enum" AS ENUM('Property Question')`);
        await queryRunner.query(`ALTER TABLE "property_question" ADD "type" "public"."property_question_type_enum" NOT NULL DEFAULT 'Property Question'`);
        await queryRunner.query(`ALTER TYPE "public"."property_question_status_enum" RENAME TO "property_question_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."property_question_status_enum" AS ENUM('Pending', 'Answered', 'Ignored')`);
        await queryRunner.query(`ALTER TABLE "property_question" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "property_question" ALTER COLUMN "status" TYPE "public"."property_question_status_enum" USING "status"::"text"::"public"."property_question_status_enum"`);
        await queryRunner.query(`ALTER TABLE "property_question" ALTER COLUMN "status" SET DEFAULT 'Pending'`);
        await queryRunner.query(`DROP TYPE "public"."property_question_status_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."property_question_status_enum_old" AS ENUM('Answered', 'Pending')`);
        await queryRunner.query(`ALTER TABLE "property_question" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "property_question" ALTER COLUMN "status" TYPE "public"."property_question_status_enum_old" USING "status"::"text"::"public"."property_question_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "property_question" ALTER COLUMN "status" SET DEFAULT 'Pending'`);
        await queryRunner.query(`DROP TYPE "public"."property_question_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."property_question_status_enum_old" RENAME TO "property_question_status_enum"`);
        await queryRunner.query(`ALTER TABLE "property_question" DROP COLUMN "type"`);
        await queryRunner.query(`DROP TYPE "public"."property_question_type_enum"`);
    }

}

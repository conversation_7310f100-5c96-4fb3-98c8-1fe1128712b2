import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAskAboutMoveInDateObjective1744383293922 implements MigrationInterface {
  name = 'AddAskAboutMoveInDateObjective1744383293922';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."ai_objective_type_enum" RENAME TO "ai_objective_type_enum_old"`);
    await queryRunner.query(
      `CREATE TYPE "public"."ai_objective_type_enum" AS ENUM('Ask if renter has requirements question', 'Ask about move in date', 'Evaluate credit score', 'Evaluate income', 'Schedule showing')`,
    );
    await queryRunner.query(
      `ALTER TABLE "ai_objective" ALTER COLUMN "type" TYPE "public"."ai_objective_type_enum" USING "type"::"text"::"public"."ai_objective_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."ai_objective_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."ai_objective_type_enum_old" AS ENUM('Ask if renter has requirements question', 'Evaluate credit score', 'Evaluate income', 'Schedule showing')`,
    );
    await queryRunner.query(
      `ALTER TABLE "ai_objective" ALTER COLUMN "type" TYPE "public"."ai_objective_type_enum_old" USING "type"::"text"::"public"."ai_objective_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."ai_objective_type_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."ai_objective_type_enum_old" RENAME TO "ai_objective_type_enum"`);
  }
}

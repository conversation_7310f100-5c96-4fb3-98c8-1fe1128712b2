import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameLandLordToLandlordInApplicationRenterResidenceEntity1729455018605 implements MigrationInterface {
  name = 'RenameLandLordToLandlordInApplicationRenterResidenceEntity1729455018605';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" DROP COLUMN "landLordName"`);
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" DROP COLUMN "landLordPhone"`);
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" DROP COLUMN "landLordEmail"`);
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" ADD "landlordName" character varying`);
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" ADD "landlordPhone" character varying`);
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" ADD "landlordEmail" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" DROP COLUMN "landlordEmail"`);
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" DROP COLUMN "landlordPhone"`);
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" DROP COLUMN "landlordName"`);
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" ADD "landLordEmail" character varying`);
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" ADD "landLordPhone" character varying`);
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" ADD "landLordName" character varying`);
  }
}

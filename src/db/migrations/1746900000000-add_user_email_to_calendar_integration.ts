import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserEmailToCalendarIntegration1746900000000 implements MigrationInterface {
  name = 'AddUserEmailToCalendarIntegration1746900000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "calendar_integration" ADD "userEmail" character varying(255)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "calendar_integration" DROP COLUMN "userEmail"`);
  }
}

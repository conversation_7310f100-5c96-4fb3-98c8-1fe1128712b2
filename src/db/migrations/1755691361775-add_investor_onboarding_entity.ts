import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddInvestorOnboardingEntity1755691361775 implements MigrationInterface {
  name = 'AddInvestorOnboardingEntity1755691361775';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "investor_onboarding" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "initialOnboardingCompleted" boolean NOT NULL DEFAULT false, "propertyOnboardingCompleted" boolean NOT NULL DEFAULT false, "dashboardOnboardingCompleted" boolean NOT NULL DEFAULT false, "renterProfileOnboardingCompleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "investorId" uuid, CONSTRAINT "REL_e48683fb217710c9845fddbe95" UNIQUE ("investorId"), CONSTRAINT "PK_06750365eadcd189f48fb299f51" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."investor_preferedscreeningsensitivity_enum" RENAME TO "investor_preferedscreeningsensitivity_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."investor_preferredscreeningsensitivity_enum" AS ENUM('Strict', 'Moderate', 'Owner Review')`,
    );
    await queryRunner.query(`ALTER TABLE "investor" ALTER COLUMN "preferredScreeningSensitivity" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "investor" ALTER COLUMN "preferredScreeningSensitivity" TYPE "public"."investor_preferredscreeningsensitivity_enum" USING "preferredScreeningSensitivity"::"text"::"public"."investor_preferredscreeningsensitivity_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "investor" ALTER COLUMN "preferredScreeningSensitivity" SET DEFAULT 'Moderate'`,
    );
    await queryRunner.query(`DROP TYPE "public"."investor_preferedscreeningsensitivity_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "investor_onboarding" ADD CONSTRAINT "FK_e48683fb217710c9845fddbe957" FOREIGN KEY ("investorId") REFERENCES "investor"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "investor_onboarding" DROP CONSTRAINT "FK_e48683fb217710c9845fddbe957"`);
    await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "companyType" DROP NOT NULL`);
    await queryRunner.query(
      `CREATE TYPE "public"."investor_preferedscreeningsensitivity_enum_old" AS ENUM('Strict', 'Moderate', 'Owner Review')`,
    );
    await queryRunner.query(`ALTER TABLE "investor" ALTER COLUMN "preferredScreeningSensitivity" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "investor" ALTER COLUMN "preferredScreeningSensitivity" TYPE "public"."investor_preferedscreeningsensitivity_enum_old" USING "preferredScreeningSensitivity"::"text"::"public"."investor_preferedscreeningsensitivity_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "investor" ALTER COLUMN "preferredScreeningSensitivity" SET DEFAULT 'Moderate'`,
    );
    await queryRunner.query(`DROP TYPE "public"."investor_preferredscreeningsensitivity_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."investor_preferedscreeningsensitivity_enum_old" RENAME TO "investor_preferedscreeningsensitivity_enum"`,
    );
    await queryRunner.query(`DROP TABLE "investor_onboarding"`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLandlordIdToInvestor1727887614183 implements MigrationInterface {
  name = 'AddLandlordIdToInvestor1727887614183';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "investor" ADD "transUnionLandlordId" character varying(120)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "investor" DROP COLUMN "transUnionLandlordId"`);
  }
}

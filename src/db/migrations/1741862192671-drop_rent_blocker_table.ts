import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropRentBlockerTable1741862192671 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop any foreign key constraints first
    await queryRunner.query(`ALTER TABLE "rent_blocker" DROP CONSTRAINT IF EXISTS "FK_rent_blocker_renter_renter_id"`);
    await queryRunner.query(
      `ALTER TABLE "rent_blocker" DROP CONSTRAINT IF EXISTS "FK_rent_blocker_property_property_id"`,
    );

    // Drop the table
    await queryRunner.query(`DROP TABLE "rent_blocker"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Recreate the table if needed to rollback
    await queryRunner.query(
      `CREATE TABLE "rent_blocker" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "blockerType" "blocker_type_enum" NOT NULL, "blockerTypeWeight" integer NOT NULL, "isResolved" boolean NOT NULL DEFAULT false, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "renterId" uuid, "propertyId" uuid NOT NULL, CONSTRAINT "PK_rent_blocker" PRIMARY KEY ("id"))`,
    );

    // Recreate foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "rent_blocker" ADD CONSTRAINT "FK_rent_blocker_renter_renter_id" FOREIGN KEY ("renterId") REFERENCES "renter"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "rent_blocker" ADD CONSTRAINT "FK_rent_blocker_property_property_id" FOREIGN KEY ("propertyId") REFERENCES "property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}

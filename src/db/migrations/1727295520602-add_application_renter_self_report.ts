import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApplicationRenterSelfReport1727295520602 implements MigrationInterface {
  name = 'AddApplicationRenterSelfReport1727295520602';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "application_renter_details" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "aboutYou" text, "isSmoker" boolean, "desiredMoveInDate" date, "isMoveInDateFlexible" boolean, "hasPets" boolean, "numberOfSmallDogs" integer, "numberOfMediumDogs" integer, "numberOfLargeDogs" integer, "numberOfCats" integer, "numberOfOtherPets" integer, "petDetails" text, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_02b0943a780142c48c98bbff3ba" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "application_renter_reference" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "fullName" character varying(512), "phoneNumber" character varying(120), "email" character varying(120), "relationship" text, "agreedToDisclosures" boolean, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "applicationId" uuid NOT NULL, CONSTRAINT "UQ_21013d2f90e93e02eae15b0b97d" UNIQUE ("email"), CONSTRAINT "PK_0092fa077ff3bc4f221b00f88db" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "application_renter_employment_info" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "jobTitle" character varying(512), "companyName" character varying(512), "netIncome" integer, "startDate" date, "isCurrent" boolean, "managerPhone" character varying(128), "managerEmail" character varying(128), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "applicationId" uuid NOT NULL, CONSTRAINT "PK_5af3e13b3a651d2a57453f063f9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."application_renter_residence_info_residencetype_enum" AS ENUM('Rental', 'Own')`,
    );
    await queryRunner.query(
      `CREATE TABLE "application_renter_residence_info" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "residenceType" "public"."application_renter_residence_info_residencetype_enum", "approvesToContactLandlord" boolean, "propertyAddress" character varying, "landLordName" character varying, "landLordPhone" character varying, "landLordEmail" character varying, "monthlyRent" integer, "moveInDate" date, "moveOutDate" date, "moveOutReason" text, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "applicationId" uuid NOT NULL, CONSTRAINT "PK_ec1ddeadbff17f8732234bbd53a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "application" ADD "renterDetailsId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "application" ADD CONSTRAINT "UQ_17c3290105fc33d26a5daed009f" UNIQUE ("renterDetailsId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_reference" ADD CONSTRAINT "FK_39ab2af07d9e5f0785dd2c3d395" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_employment_info" ADD CONSTRAINT "FK_13af1a015b2e2d5e80e0b4b471f" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_residence_info" ADD CONSTRAINT "FK_e45da5ed0561e23df58101ddcfa" FOREIGN KEY ("applicationId") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "application" ADD CONSTRAINT "FK_17c3290105fc33d26a5daed009f" FOREIGN KEY ("renterDetailsId") REFERENCES "application_renter_details"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application" DROP CONSTRAINT "FK_17c3290105fc33d26a5daed009f"`);
    await queryRunner.query(
      `ALTER TABLE "application_renter_residence_info" DROP CONSTRAINT "FK_e45da5ed0561e23df58101ddcfa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_employment_info" DROP CONSTRAINT "FK_13af1a015b2e2d5e80e0b4b471f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_reference" DROP CONSTRAINT "FK_39ab2af07d9e5f0785dd2c3d395"`,
    );
    await queryRunner.query(`ALTER TABLE "application" DROP CONSTRAINT "UQ_17c3290105fc33d26a5daed009f"`);
    await queryRunner.query(`ALTER TABLE "application" DROP COLUMN "renterDetailsId"`);
    await queryRunner.query(`DROP TABLE "application_renter_residence_info"`);
    await queryRunner.query(`DROP TYPE "public"."application_renter_residence_info_residencetype_enum"`);
    await queryRunner.query(`DROP TABLE "application_renter_employment_info"`);
    await queryRunner.query(`DROP TABLE "application_renter_reference"`);
    await queryRunner.query(`DROP TABLE "application_renter_details"`);
  }
}

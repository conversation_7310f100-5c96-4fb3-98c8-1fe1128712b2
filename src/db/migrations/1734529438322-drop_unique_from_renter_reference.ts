import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropUniqueFromRenterReference1734529438322 implements MigrationInterface {
  name = 'DropUniqueFromRenterReference1734529438322';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_renter_reference" DROP CONSTRAINT "UQ_21013d2f90e93e02eae15b0b97d"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "application_renter_reference" ADD CONSTRAINT "UQ_21013d2f90e93e02eae15b0b97d" UNIQUE ("email")`,
    );
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class MakeMeetsRequirementFieldNullableAndRemoveDefaultValue1738667729635 implements MigrationInterface {
  name = 'MakeMeetsRequirementFieldNullableAndRemoveDefaultValue1738667729635';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "ai_objective" ALTER COLUMN "meetsRequirement" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "ai_objective" ALTER COLUMN "meetsRequirement" DROP DEFAULT`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "ai_objective" ALTER COLUMN "meetsRequirement" SET DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "ai_objective" ALTER COLUMN "meetsRequirement" SET NOT NULL`);
  }
}

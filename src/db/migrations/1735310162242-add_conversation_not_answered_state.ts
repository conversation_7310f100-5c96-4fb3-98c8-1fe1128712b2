import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddConversationNotAnsweredState1735310162242 implements MigrationInterface {
  name = 'AddConversationNotAnsweredState1735310162242';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "message" RENAME COLUMN "isDistributed" TO "isAnswered"`);
    await queryRunner.query(`ALTER TABLE "conversation" ADD "isAnswered" boolean NOT NULL DEFAULT true`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "conversation" DROP COLUMN "isAnswered"`);
    await queryRunner.query(`ALTER TABLE "message" RENAME COLUMN "isAnswered" TO "isDistributed"`);
  }
}

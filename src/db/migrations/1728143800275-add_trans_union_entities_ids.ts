import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTransUnionEntitiesIds1728143800275 implements MigrationInterface {
  name = 'AddTransUnionEntitiesIds1728143800275';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" ADD "transUnionPropertyId" character varying(128)`);
    await queryRunner.query(`ALTER TABLE "application_invite" ADD "transUnionScreeningRequestId" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_invite" DROP COLUMN "transUnionScreeningRequestId"`);
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "transUnionPropertyId"`);
  }
}

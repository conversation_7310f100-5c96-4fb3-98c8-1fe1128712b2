import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTypeToApplicationRenterResidenceInfoEntity1729719193517 implements MigrationInterface {
  name = 'AddTypeToApplicationRenterResidenceInfoEntity1729719193517';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."application_renter_residence_info_type_enum" AS ENUM('Current', 'Previous')`,
    );
    await queryRunner.query(
      `ALTER TABLE "application_renter_residence_info" ADD "type" "public"."application_renter_residence_info_type_enum" NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "application_renter_residence_info" DROP COLUMN "type"`);
    await queryRunner.query(`DROP TYPE "public"."application_renter_residence_info_type_enum"`);
  }
}

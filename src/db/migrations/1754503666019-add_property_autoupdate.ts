import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPropertyAutoupdate1754503666019 implements MigrationInterface {
  name = 'AddPropertyAutoupdate1754503666019';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" ADD "autoRefresh" boolean DEFAULT true`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "autoRefresh"`);
  }
}

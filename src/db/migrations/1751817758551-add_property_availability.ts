import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPropertyAvailability1751817758551 implements MigrationInterface {
  name = 'AddPropertyAvailability1751817758551';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "property_availability" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "showingDurationInMinutes" integer NOT NULL DEFAULT '15', CONSTRAINT "PK_cb34e181b77cc6add128c0b40c1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "availability_slot" ADD "propertyAvailabilityId" uuid`);
    await queryRunner.query(`ALTER TABLE "investor_availability" ALTER COLUMN "investorId" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "property" ADD "availabilityId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "UQ_19f49d91f3bf8399d188aac85c6" UNIQUE ("availabilityId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "availability_slot" ADD CONSTRAINT "FK_826c69ae00ca3316754bd3d68a1" FOREIGN KEY ("propertyAvailabilityId") REFERENCES "property_availability"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD CONSTRAINT "FK_19f49d91f3bf8399d188aac85c6" FOREIGN KEY ("availabilityId") REFERENCES "property_availability"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_19f49d91f3bf8399d188aac85c6"`);
    await queryRunner.query(`ALTER TABLE "availability_slot" DROP CONSTRAINT "FK_826c69ae00ca3316754bd3d68a1"`);
    await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "UQ_19f49d91f3bf8399d188aac85c6"`);
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "availabilityId"`);
    await queryRunner.query(`ALTER TABLE "availability_slot" DROP COLUMN "propertyAvailabilityId"`);
    await queryRunner.query(`DROP TABLE "property_availability"`);
  }
}

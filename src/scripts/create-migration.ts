import { exec } from 'child_process';

/**
 * Creates an empty migration file with provided name
 */
async function createAnEmptyMigrationFile() {
  const { input } = await import('@inquirer/prompts');

  input({
    message: 'Please enter the migration name, for example "add_email_to_user_entity":',
    validate: (value) => (value ? true : 'Please enter the migration name'),
  }).then((answer) => {
    const command = `npm run typeorm -- migration:create ./src/db/migrations/${answer}`;
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.log(`Error: ${error.message}`);
        return;
      }
      if (stderr) {
        console.log(`stderr: ${stderr}`);
        return;
      }
      console.log(`stdout: ${stdout}`);
    });
  });
}

createAnEmptyMigrationFile();

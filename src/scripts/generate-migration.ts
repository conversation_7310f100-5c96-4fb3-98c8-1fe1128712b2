import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function generateMigration() {
  const { input } = await import('@inquirer/prompts');

  try {
    const [, answer] = await Promise.all([
      (async () => {
        console.log('1️⃣ Building project...');
        return execAsync('npm run build');
      })(),
      input({
        message: 'Please enter the migration name, for example "add_email_to_user_entity":',
        validate: (value) => (value ? true : 'Please enter the migration name'),
      }).then((answer) => {
        console.log('2️⃣ Project build in progress...');

        return answer;
      }),
    ]);

    console.log('3️⃣ Project built successfully!');
    console.log('4️⃣ Generating migration...');
    const command = `npm run typeorm -- migration:generate ./src/db/migrations/${answer} -d ./src/config/orm/typeorm.ts`;
    const { stdout, stderr } = await execAsync(command);

    if (stderr && !stderr.includes('Debugger attached')) {
      console.error(`❌ Error: ${stderr}`);
      return;
    }

    console.log('✅ Migration generated successfully!', stdout);
    console.log('TypeOrm output:', stdout);
  } catch (error) {
    console.error('❌ Failed to generate migration:', error);
    process.exit(1);
  }
}

generateMigration();

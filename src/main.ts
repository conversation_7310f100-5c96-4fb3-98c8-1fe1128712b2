import tracer from 'dd-trace';

import { Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

import { AppModule } from './app.module';
import { EntityNotFoundExceptionFilter } from './middlaware/exception-filters/entity-not-found-exception-filter';
import { GlobalExceptionFilter } from './middlaware/exception-filters/global-exception-filter';
import { QueryFailedErrorExceptionFilter } from './middlaware/exception-filters/query-failed-error-exception-filter';
import { WinstonLoggerService } from './middlaware/logging/winston/winston-logger.service';
import {
  validationExceptionFactory,
  ValidationExceptionFilter,
} from './middlaware/exception-filters/validation-exception-filter';

declare const module: any;

tracer.init({
  service: 'Tallo Backend',
});

async function bootstrap() {
  const app: NestExpressApplication = await NestFactory.create(AppModule, { rawBody: true, bodyParser: true });
  const config: ConfigService = app.get(ConfigService);
  const port = config.get('PORT');

  app.enableCors();
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidUnknownValues: true,
      exceptionFactory: validationExceptionFactory,
    }),
  );

  const winstonLogger = app.get(WinstonLoggerService);
  reassignLogger(winstonLogger);

  const filters = [
    new EntityNotFoundExceptionFilter(),
    new QueryFailedErrorExceptionFilter(),
    new GlobalExceptionFilter(),
    new ValidationExceptionFilter(),
  ];

  app.useGlobalFilters(...filters);

  process.on('unhandledRejection', (reason: any) => {
    const errorLog = {
      message: 'Unhandled Rejection',
      timestamp: new Date().toISOString(),
      reason: reason.message || reason,
    };

    console.error(errorLog);
  });

  // swagger setup
  const swaggerConfig = new DocumentBuilder()
    .setTitle('TALLO app')
    .setDescription('TALLO app API description')
    .setVersion('1.0')
    .addBearerAuth()
    .addBasicAuth(
      {
        type: 'http',
        scheme: 'basic',
        name: 'basic',
        description: 'Basic Authentication for external endpoints',
      },
      'basic',
    )
    .build();
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  const swaggerPath = '';
  SwaggerModule.setup(swaggerPath, app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  await app.listen(port);

  Logger.log(`🚀 Application is running on: http://localhost:${port}`);
  Logger.log(`🚀 Swagger: http://localhost:${port}/${swaggerPath}`);

  if (module.hot) {
    module.hot.accept();
    module.hot.dispose(() => app.close());
  }
}
bootstrap().then();

function reassignLogger(winstonLogger: WinstonLoggerService) {
  console.log = (...args: any) => winstonLogger.log(args);
  console.error = (...args: any) => winstonLogger.error(args, null);
  console.warn = (...args: any) => winstonLogger.warn(args);
  console.debug = (...args: any) => winstonLogger.debug(args);
  console.info = (...args: any) => winstonLogger.verbose(args);
}

import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtAuthGuard } from './modules/shared/auth/guards/jwt-auth.guard';
import { APP_GUARD } from '@nestjs/core';
import { LoggingMiddleware } from './middlaware/logging/logging.middleware';
import { WebsocketService } from './websocket.service';
import { ScheduleModule } from '@nestjs/schedule';
import { InvestorModule } from './modules/investor/investor.module';
import { SharedModule } from './modules/shared/shared.module';
import { DataGenModule } from './modules/testing/data-gen.module';
import { DbTransactionModule } from './config/orm/transactions/db-transaction.module';
import { BasicAuthGuard } from './modules/shared/auth/guards/basic-auth.guard';
import { RenterCoreModule } from './modules/renter/renter-core.module';
import { LoggerModule } from './middlaware/logging/winston/winston-logger.module';
import { SlackCommunicationModule } from './modules/shared/communication/outbound-communication/slack/slack-communication.module';
import { LeadsModule } from './modules/investor/leads/leads.module';
import typeorm from './config/orm/typeorm';
import { plainToClass } from 'class-transformer';
import { DotEnvConfig } from './dot-env.config';
import { validateSync } from 'class-validator';
import { AnalyticsModule } from './modules/analytics/analytics.module';
import { StatsdInterceptor } from './middlaware/monitoring/statsd.interceptor';
import { PublicModule } from './modules/public/public.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';

@Module({
  imports: [
    LeadsModule,
    InvestorModule,
    PublicModule,
    RenterCoreModule,
    SharedModule,
    AnalyticsModule,
    DataGenModule,
    DbTransactionModule,
    LoggerModule,
    SlackCommunicationModule,
    ScheduleModule.forRoot(),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
      serveRoot: '/',
    }),
    ConfigModule.forRoot({
      envFilePath: './src/config/envs/.env',
      isGlobal: true,
      load: [typeorm],
      validate: (config: Record<string, unknown>) => {
        const validatedConfig = plainToClass(DotEnvConfig, config);
        const errors = validateSync(validatedConfig);

        if (errors.length > 0) {
          throw new Error(errors.toString());
        }

        return validatedConfig;
      },
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => configService.get('typeorm' as any),
    }),
  ],
  providers: [
    AppService,
    WebsocketService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: BasicAuthGuard,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggingMiddleware, StatsdInterceptor).forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}

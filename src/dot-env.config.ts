import { Transform } from 'class-transformer';
import { IsArray, IsBoolean, IsEmail, IsEnum, IsNumber, IsString, IsUrl } from 'class-validator';

export enum Environment {
  LOCALHOST = 'localhost',
  STAGING = 'staging',
  PRODUCTION = 'production',
}

export class DotEnvConfig {
  // Environment
  @IsEnum(Environment)
  @Transform(({ value }) => value.toLowerCase())
  ENV: Environment;

  @Transform(({ obj }) => obj.ENV === Environment.LOCALHOST)
  get IS_LOCALHOST(): boolean {
    return this.ENV === Environment.LOCALHOST;
  }

  @Transform(({ obj }) => obj.ENV === Environment.STAGING)
  get IS_STAGING(): boolean {
    return this.ENV === Environment.STAGING;
  }

  @Transform(({ obj }) => obj.ENV === Environment.PRODUCTION)
  get IS_PRODUCTION(): boolean {
    return this.ENV === Environment.PRODUCTION;
  }

  // Server
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  PORT: number;

  // Database
  @IsString()
  DATABASE_HOST: string;

  @IsString()
  DATABASE_NAME: string;

  @IsString()
  DATABASE_USER: string;

  @IsString()
  DATABASE_PASSWORD: string;

  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  DATABASE_PORT: number;

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  DATABASE_SSL: boolean;

  // URLs
  @IsUrl({
    protocols: ['http', 'https'],
    host_whitelist: ['localhost', 'api-staging.tallo.ai', 'api.tallo.ai'],
  })
  BACK_END_URL: string;

  @IsUrl({
    protocols: ['http', 'https'],
    host_whitelist: ['localhost', 'app-staging.tallo.ai', 'app.tallo.ai'],
  })
  INVESTOR_APP_URL: string;

  @IsUrl({
    protocols: ['http', 'https'],
    host_whitelist: ['localhost', 'homebase-staging.tallo.ai', 'homebase.tallo.ai'],
  })
  RENTER_APP_URL: string;

  // AI Services
  @IsString()
  OPEN_AI_ORGANIZATION_ID: string;

  @IsString()
  OPEN_AI_API_KEY: string;

  @IsString()
  ANTHROPIC_AI_API_KEY: string;

  @IsString()
  DEEP_SEEK_API_KEY: string;

  // AWS
  @IsString()
  S3_ACCESS_KEY_ID: string;

  @IsString()
  S3_SECRET_ACCESS_KEY: string;

  @IsString()
  S3_TARGET_BUCKET: string;

  // External APIs
  @IsString()
  SERPAPI_API_KEY: string;

  @IsString()
  GOOGLE_MAPS_KEY: string;

  @IsString()
  BREVO_API_KEY: string;

  @IsString()
  SENDGRID_API_KEY: string;

  @IsString()
  SMARTY_API_AUTH_ID: string;

  @IsString()
  SMARTY_API_AUTH_TOKEN: string;

  @IsString()
  RENTCAST_API_KEY: string;

  // Authentication
  @IsString()
  AUTH_GOOGLE_CLIENT_ID: string;

  @IsString()
  AUTH_GOOGLE_CLIENT_SECRET: string;

  @IsString()
  AUTH_GOOGLE_REDIRECT_URI: string;

  @IsString()
  BASE_AUTH_USER: string;

  @IsString()
  BASE_AUTH_PASSWORD: string;

  // Email Configuration
  @IsString()
  TALLO_SENDER_NAME: string;

  @IsEmail()
  TALLO_SENDER_EMAIL: string;

  @IsString()
  TALLO_SENDER_NUMBER: string;

  @IsEmail()
  TALLO_TRACKED_EMAIL: string;

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  EMAIL_NOTIFICATIONS_ACTIVE: boolean;

  @IsArray()
  @Transform(({ value }) => value.split(','))
  EMAIL_WHITELIST_DOMAINS: string[];

  @IsArray()
  @Transform(({ value }) => value.split(','))
  EMAIL_WHITELIST_ADDRESSES: string[];

  // Twilio
  @IsString()
  TWILIO_ACCOUNT_SID: string;

  @IsString()
  TWILIO_AUTH_TOKEN: string;

  @IsString()
  TWILIO_MESSAGING_SERVICE_SID: string;

  // SMS & Slack
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  SMS_NOTIFICATIONS_ACTIVE: boolean;

  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  SLACK_NOTIFICATIONS_ACTIVE: boolean;

  @IsString()
  SLACK_CONVO_LOGGER_BOT_TOKEN: string;

  // Stripe
  @IsString()
  STRIPE_SECRET_KEY: string;

  @IsString()
  STRIPE_WEBHOOK_SECRET: string;

  @IsString()
  STRIPE_APPLICATION_BASE_PACKAGE_ID: string;

  @IsString()
  STRIPE_APPLICATION_FULL_PACKAGE_ID: string;

  // TransUnion
  @IsString()
  TRANS_UNION_BASE_URL: string;

  @IsString()
  TRANS_UNION_CLIENT_ID: string;

  @IsString()
  TRANS_UNION_API_KEY: string;

  @IsString()
  TRANS_UNION_API_SECRET: string;

  // Gmail Service Account
  @IsEmail()
  GMAIL_SERVICE_ACCOUNT_EMAIL: string;

  @IsString()
  GMAIL_SERVICE_ACCOUNT_PRIVATE_KEY: string;
}

/**
 * Global Jest setup file for memory leak prevention and cleanup
 */

// Global timeout for all tests
jest.setTimeout(30000);

// Memory monitoring
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

// Suppress excessive logging during tests to prevent memory accumulation
console.log = (...args: any[]) => {
  // Only log critical test information
  if (args[0] && typeof args[0] === 'string' && args[0].includes('[TEST]')) {
    originalConsoleLog(...args);
  }
};

console.warn = (...args: any[]) => {
  // Only log warnings that don't contain calendar integration messages
  if (args[0] && typeof args[0] === 'string' && !args[0].includes('[Calendar Integration]')) {
    originalConsoleWarn(...args);
  }
};

console.error = (...args: any[]) => {
  // Always log errors
  originalConsoleError(...args);
};

// Global cleanup after each test
afterEach(async () => {
  // Clear all timers
  jest.clearAllTimers();

  // Clear all mocks
  jest.clearAllMocks();

  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }

  // Clear any remaining promises
  await new Promise((resolve) => setImmediate(resolve));
});

// Global cleanup after all tests
afterAll(async () => {
  // Restore original console methods
  console.log = originalConsoleLog;
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;

  // Final garbage collection
  if (global.gc) {
    global.gc();
  }
});

// Memory usage monitoring
beforeAll(() => {
  const memUsage = process.memoryUsage();
  console.log(`[TEST] Initial memory usage: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
});

afterAll(() => {
  const memUsage = process.memoryUsage();
  console.log(`[TEST] Final memory usage: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
});

// Prevent memory leaks from unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('[TEST] Unhandled Rejection at:', promise, 'reason:', reason);
});

// Prevent memory leaks from uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('[TEST] Uncaught Exception:', error);
});

import { LoggerService } from '@nestjs/common';
import * as winston from 'winston';

export class WinstonLoggerService implements LoggerService {
  private readonly logger: winston.Logger;

  constructor(options?: { simpleLogging: boolean }) {
    this.logger = winston.createLogger({
      transports: [
        new winston.transports.Console({
          format: options?.simpleLogging
            ? winston.format.prettyPrint({
                colorize: true,
                depth: 3,
              })
            : winston.format.combine(winston.format.json()),
        }),
      ],
    });
  }

  log(message: string | object) {
    this.logger.info(message);
  }

  error(message: string, trace?: string) {
    this.logger.error(trace ? `${message} - ${trace}` : message);
  }

  warn(message: string | object) {
    this.logger.warn(message);
  }

  debug(message: string | object) {
    this.logger.debug(message);
  }

  verbose(message: string | object) {
    this.logger.verbose(message);
  }
}

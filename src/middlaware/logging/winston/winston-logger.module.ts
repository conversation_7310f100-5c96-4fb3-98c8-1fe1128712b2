import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { WinstonLoggerService } from './winston-logger.service';

@Global()
@Module({
  providers: [
    {
      provide: WinstonLoggerService,
      useFactory: (configService: ConfigService) => {
        return new WinstonLoggerService({ simpleLogging: configService.get('IS_LOCALHOST') });
      },
      inject: [ConfigService],
    },
  ],
  exports: [WinstonLoggerService],
})
export class LoggerModule {}

import { NextFunction, Request, Response } from 'express';

import { Injectable, NestMiddleware } from '@nestjs/common';

import { DateUtils } from '../../utils/date.utils';
import { WinstonLoggerService } from './winston/winston-logger.service';

@Injectable()
export class LoggingMiddleware implements NestMiddleware {
  constructor(private readonly logger: WinstonLoggerService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();

    res.on('finish', () => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Create the current date and format it.
      const currentDate = new Date();
      const formattedDate = `${getDay(currentDate.getDate())}/${getMonthName(
        currentDate.getMonth(),
        // eslint-disable-next-line max-len
      )}/${currentDate.getFullYear()}:${currentDate.getHours()}:${currentDate.getMinutes()}:${currentDate.getSeconds()} +0000`;

      this.logger.log({
        level: 'info',
        message: `${req.ip} - - [${formattedDate}] "${req.method} ${req.originalUrl} HTTP/${
          req.httpVersion
        }" ${res.statusCode} ${res.get('Content-Length') || '-'} "-" "${
          req.headers['user-agent'] || '-'
        }" ${responseTime}ms`,
      });
    });

    next();
  }
}

// Helper function to convert a month number to a three-letter abbreviation.
function getMonthName(monthNumber: number): string {
  return DateUtils.monthsListShort[monthNumber];
}

// Helper function to pad single digit days with a leading zero.
function getDay(dayNumber: number): string {
  return dayNumber < 10 ? `0${dayNumber}` : `${dayNumber}`;
}

import { ExceptionFilter, Catch, ArgumentsHost } from '@nestjs/common';
import { EntityNotFoundError } from 'typeorm/error/EntityNotFoundError';

@Catch(EntityNotFoundError)
export class EntityNotFoundExceptionFilter implements ExceptionFilter {
  catch(exception: EntityNotFoundError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();

    // You can further refine the error message if needed
    const message = `Entity not found: ${exception.message}`;

    console.error(message, exception);

    response.status(404).json({
      statusCode: 404,
      message: message,
    });
  }
}

import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AnalyticsService } from '../../modules/analytics/analytics.service';

@Injectable()
export class StatsdInterceptor implements NestMiddleware {
  constructor(private analyticsService: AnalyticsService) {}

  use(req: Request, res: Response, next: NextFunction) {
    // Start a timer
    const start = process.hrtime();

    res.on('finish', () => {
      const duration = process.hrtime(start);
      const durationMs = duration[0] * 1000 + duration[1] / 1e6;
      this.analyticsService.histogram('http.response.time', durationMs);
    });

    next();
  }
}

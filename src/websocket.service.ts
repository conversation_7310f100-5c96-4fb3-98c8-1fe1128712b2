import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { Server } from 'socket.io';
import { Injectable } from '@nestjs/common';
import { MessageDto } from './modules/shared/communication/conversation/message/message.dto';

@Injectable()
@WebSocketGateway({ cors: true })
export class WebsocketService {
  @WebSocketServer()
  server: Server;

  sendMessage(conversationId: string, message: MessageDto): void {
    const eventName = `chat:${conversationId}`;
    this.server.emit(eventName, message);
  }
}

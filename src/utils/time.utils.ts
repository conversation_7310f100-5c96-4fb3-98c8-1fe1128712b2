import { parseISO } from 'date-fns';
import { format, toZonedTime } from 'date-fns-tz';

/**
 * converts DB column with type "timetz" to user's local time
 * @example timetzToLocalTime('15:45:30+04:00', 'Europe/Kiev') // Output in Kiev(+2): '13:45'
 */
export function timetzToLocalTime(timetz: string, userTimeZone: string): string {
  const [, time, timeZoneOffset] = timetz.match(/(\d{2}:\d{2}:\d{2})([+\-]\d{2})/) || [];

  if (!time || !timeZoneOffset) {
    return 'Invalid time format';
  }

  const dateString = getCurrentDateString();

  // Parse the PostgreSQL timetz with the original time zone offset
  const inputTime = parseISO(`${dateString}T${time}${timeZoneOffset}`);
  const localTime = toZonedTime(inputTime, userTimeZone);
  const formattedLocalTime = format(localTime, 'HH:mm', {
    timeZone: userTimeZone,
  });

  return formattedLocalTime;
}

/**
 * Converts user's local time to DB column with type "timetz"
 * @example localTimeToTimetz('09:45', 'Europe/Kiev') // Output: '09:45:00+02:00'
 */
export function localTimeToTimetz(localTime: string, userTimeZone: string): string {
  const dateString = getCurrentDateString();
  const inputTime = new Date(`${dateString}T${localTime}:00`);
  // Determine the time zone offset from UTC
  const timeZoneOffset = format(inputTime, 'XXX', { timeZone: userTimeZone });

  // Construct the "timetz" string required by postgres DB time column
  return `${localTime}:00${timeZoneOffset}`;
}

export function parseTimetz(timeString: string, referenceDate: Date): Date {
  const [time] = timeString.split(/[+-]/);
  const [hours, minutes, seconds] = time.split(':').map(Number);

  const date = new Date(referenceDate);
  date.setUTCHours(hours, minutes, seconds, 0);

  return date;
}

function getCurrentDateString(): string {
  const currentDate = new Date();
  const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
  const date = currentDate.getDate().toString().padStart(2, '0');

  return `${currentDate.getFullYear()}-${month}-${date}`;
}

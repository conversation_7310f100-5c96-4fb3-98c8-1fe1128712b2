import { isPhoneNumber } from 'class-validator';

class ParsingUtils {
  parseStringToNumber(value: string): number {
    if (!value) {
      return null;
    }
    return parseInt(value, 10);
  }

  parseStringToBoolean(value: string): boolean {
    value = value?.toLowerCase();
    if (value === 'true') {
      return true;
    } else if (value === 'false') {
      return false;
    } else {
      return undefined;
    }
  }

  parseAddress(subject: string): { street: string; apartmentNumber: string } {
    const regexPattern = /(Your inquiry for )/;

    // take everything after 'Lead for ' in the subject
    const address = subject.split(regexPattern).pop();

    const regex = /^(Re: \s*)*(\[.*?\]\s*)*/i;

    // remove unexpected characters if any
    const cleanAddress = address.replace(regex, '').trim();

    // get the street address and apartment number if any
    const street = cleanAddress.split('#')[0];
    const apartment = cleanAddress.split('#')[1];

    // trim the address and return
    return {
      street: street.trim(),
      apartmentNumber: apartment ? apartment.trim() : null,
    };
  }

  formatPhoneNumber(phoneNumber: unknown): string {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      console.error('Invalid Phone number');
      return null;
    }

    // Remove any non-digit characters (exclude +)
    let formattedValue = phoneNumber.replace(/[^+\d]/g, '');

    switch (true) {
      case /^\d{10}$/.test(formattedValue):
        formattedValue = `+1${formattedValue}`;
        break;

      case /^1\d{10}$/.test(formattedValue):
        formattedValue = `+${formattedValue}`;
        break;
    }

    return isPhoneNumber(formattedValue, 'US') ? formattedValue : null;
  }
}

export default new ParsingUtils();

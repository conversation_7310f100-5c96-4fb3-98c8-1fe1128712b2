import { TimeFilter } from '../modules/investor/property/statistics/time-filter.enum';

export class FilterUtils {
  static getStartDateByFilter(filter?: TimeFilter): Date | undefined {
    // default to all time
    if (!filter) {
      return new Date(0);
    }

    const now = new Date();

    switch (filter) {
      case TimeFilter.LAST_24_HOURS:
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case TimeFilter.LAST_7_DAYS:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case TimeFilter.LAST_30_DAYS:
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case TimeFilter.LAST_12_MONTHS:
        return new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      case TimeFilter.ALL_TIME:
      default:
        return new Date(0);
    }
  }
}

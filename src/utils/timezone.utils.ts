import * as cityTimezones from 'city-timezones';
import { addMinutes } from 'date-fns';
import { format } from 'date-fns-tz';
import * as moment from 'moment-timezone';

import { DateUtils } from './date.utils';

export class TimezoneUtils {
  static lookupTimezone(cityName: string): string {
    const cityLookup = cityTimezones.lookupViaCity(cityName);
    if (cityLookup && cityLookup.length > 0) {
      return cityLookup.map((cityInfo) => cityInfo.timezone)[0];
    }

    throw new Error('Timezone not found');
  }

  /**
   * Converts current server date to the city's date
   *
   * @example for system time '2024-01-25T17:00:00.000Z' will return '2024-01-25T11:00:00.000Z' for Chicago
   */
  static getCurrentCityDate(cityName: string, timezone: string): Date {
    let cityTimezone: string;

    if (!timezone) {
      cityTimezone = this.lookupTimezone(cityName);
    } else {
      cityTimezone = timezone;
    }

    // Convert the server date to the city's timezone
    const userTime = moment(new Date()).tz(cityTimezone);

    const utcEquivalent = moment.utc([
      userTime.year(),
      userTime.month(),
      userTime.date(),
      userTime.hour(),
      userTime.minute(),
      userTime.second(),
    ]);

    // Create a JavaScript Date object from the UTC equivalent
    return new Date(utcEquivalent.format());
  }

  static getCurrentCityDateInAiReadableFormat(cityName: string, timezone: string): string {
    return DateUtils.toAiReadableFormat(this.getCurrentCityDate(cityName, timezone));
  }

  /**
   * @example TimezoneUtils.convertDateToStringInCityTz(new Date('2024-01-25T17:00:00.000Z'), 'Chicago')
   * // Output: 'Fri, January 25, 2024, 11:00 AM'
   */
  static convertDateToStringInCityTz(date: Date, cityName: string, timezone: string): string {
    if (!timezone) {
      timezone = this.lookupTimezone(cityName);
    }

    // Convert the date to the provided timezone
    const dateInTimezone = moment(date).tz(timezone);

    return dateInTimezone.format('ddd, MMMM D, YYYY, h:mm A');
  }

  /**
   * Returns the timezone offset for a specific city.
   * @example TimezoneUtils.getTimezoneOffsetByCity('Chicago') // Output: '-06:00'
   */
  static getTimezoneOffsetByCity(cityName: string, timezone: string) {
    const cityTimezone = timezone || this.lookupTimezone(cityName);

    return format(new Date(), 'XXX', { timeZone: cityTimezone });
  }

  /**
   * Takes a valid UTC date and converts it to the city's timezone, so time is correct in the city
   * but the timezone is missing, so returned date is not valid UTC time.
   * Only use to provide the correct time in the city for AI prompts.
   *
   * @example TimezoneUtils.convertUtcDateToCityTimeWithMissingTz(new Date('2024-01-25T17:00:00.000Z'), 'Chicago')
   * // Output: '2024-01-25T11:00:00.000Z'
   */
  static convertUtcDateToCityTimeWithMissingTz(date: Date, cityName: string, timezone: string): Date {
    const offset = this.getTimezoneOffsetByCity(cityName, timezone);

    const offsetMinutes = parseInt(offset.slice(1, 3)) * 60 + parseInt(offset.slice(4, 6));
    const offsetSign = offset[0] === '+' ? 1 : -1;

    const adjustedDate = addMinutes(date, offsetSign * offsetMinutes);

    return adjustedDate;
  }

  /**
   * Accepts the js Date with correct time (e.g. Chicago time), but with missing timezone,
   * for example 5pm Chicago time, but it is not adjusted to the timezone
   * `new Date('2024-01-25T17:00:00.000Z')`
   *
   * Returns the same date & time adjusted to UTC time
   * @example convertDateWithMissingTzToUtcDate(new Date('2024-01-25T17:00:00.000Z'), 'Chicago')
   * // Output: '2024-01-25T23:00:00.000Z'
   */
  static convertDateWithMissingTzToUtcDate(dateWithoutTimezone: Date, cityName: string, timezone: string): Date {
    if (!timezone) {
      timezone = this.lookupTimezone(cityName);
    }

    if (!DateUtils.isValid(dateWithoutTimezone)) {
      throw new Error('"dateWithoutTimezone" is not a valid JS Date instance.');
    }

    const year = dateWithoutTimezone.getUTCFullYear();
    const month = (dateWithoutTimezone.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = dateWithoutTimezone.getUTCDate().toString().padStart(2, '0');
    const hours = dateWithoutTimezone.getUTCHours().toString().padStart(2, '0');
    const minutes = dateWithoutTimezone.getUTCMinutes().toString().padStart(2, '0');
    const seconds = dateWithoutTimezone.getUTCSeconds().toString().padStart(2, '0');

    const timezoneOffset = format(dateWithoutTimezone, 'XXX', {
      timeZone: timezone,
    });

    // Create a date string with the timezone offset
    const dateString = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${timezoneOffset}`;

    return new Date(dateString);
  }

  /**
   * Calculates the next business hour based on the provided date, days to add, and timezone.
   * Business hours are considered to be between 10 AM and 8 PM in the specified timezone.
   *
   * @param currentTime The base date to calculate from
   * @param daysToAdd Number of days to add to the current date
   * @param timeZone The timezone to use for calculations
   * @returns A Date object representing the next business hour in UTC
   *
   * @example TimezoneUtils.calculateNextBusinessHour(new Date(), 1, 'America/Chicago')
   * // If current time is 9 AM, returns tomorrow at 10 AM
   * // If current time is 9 PM, returns tomorrow at 10 AM
   * // If current time is 2 PM, returns tomorrow at 2 PM
   */
  static calculateNextBusinessHour(currentTime: Date, daysToAdd: number, timeZone: string): Date {
    let zonedDate = moment(currentTime).tz(timeZone);

    zonedDate = zonedDate.add(daysToAdd, 'days');

    const businessStartHour = 10;
    const businessEndHour = 20;

    const hour = zonedDate.hour();

    if (hour < businessStartHour) {
      zonedDate = zonedDate.set({
        hour: businessStartHour,
        minute: 0,
        second: 0,
        millisecond: 0,
      });
    } else if (hour >= businessEndHour) {
      zonedDate = zonedDate.add(1, 'day').set({
        hour: businessStartHour,
        minute: 0,
        second: 0,
        millisecond: 0,
      });
    } else {
      zonedDate = zonedDate.set({
        second: 0,
        millisecond: 0,
      });
    }

    return zonedDate.utc().toDate();
  }
}

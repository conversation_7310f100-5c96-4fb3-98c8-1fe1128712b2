export class DateUtils {
  static daysListShort = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  static daysListFull = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  static monthsListShort = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  static formatDateToGmailFormat(date: Date): string {
    const day = this.daysListShort[date.getDay()];
    const month = this.monthsListShort[date.getMonth()];
    const dateNumber = date.getDate();
    const year = date.getFullYear();

    const hours = date.getHours();
    const isAM = hours < 12;
    const formattedHour = hours % 12 || 12;

    const minutes = date.getMinutes();
    const formattedMinutes = String(minutes).padStart(2, '0');

    const ampm = isAM ? 'AM' : 'PM';

    return `${day}, ${month} ${dateNumber}, ${year} at ${formattedHour}:${formattedMinutes}${ampm}`;
  }

  static toAiReadableFormat(date: Date, params: { skipTime?: boolean } = {}): string {
    if (!date) {
      return 'N/A';
    }

    const day = this.daysListShort[date.getUTCDay()];
    const month = this.monthsListShort[date.getUTCMonth()];
    const dateNumber = date.getUTCDate();
    const year = date.getUTCFullYear();

    const hours = date.getUTCHours();
    const isAM = hours < 12;
    const formattedHour = hours % 12 || 12;

    const minutes = date.getUTCMinutes();
    const formattedMinutes = String(minutes).padStart(2, '0');

    const ampm = isAM ? 'AM' : 'PM';

    if (params.skipTime) {
      return `${day}, ${month} ${dateNumber}, ${year}`;
    }

    return `${day}, ${month} ${dateNumber}, ${year}, ${formattedHour}:${formattedMinutes}${ampm}`;
  }

  static roundUpToNearestHalfHour(date: Date): Date {
    const minutes = date.getMinutes();
    const roundedMinutes = minutes < 15 ? 0 : minutes < 45 ? 30 : 0;
    const roundedDate = new Date(date);
    roundedDate.setMinutes(roundedMinutes);
    if (minutes >= 45) {
      roundedDate.setHours(roundedDate.getHours() + 1);
    }
    roundedDate.setSeconds(0);
    roundedDate.setMilliseconds(0);
    return roundedDate;
  }

  static roundDownToNearestHalfHour(date: Date): Date {
    const minutes = date.getMinutes();
    const roundedMinutes = minutes < 30 ? 0 : 30;
    const roundedDate = new Date(date);
    roundedDate.setMinutes(roundedMinutes);
    roundedDate.setSeconds(0);
    roundedDate.setMilliseconds(0);
    return roundedDate;
  }

  static isValid(date: Date): boolean {
    return date instanceof Date && !isNaN(date.getTime());
  }

  static getNextDayFormatted(date: Date): string {
    const nextDay = new Date(date);
    nextDay.setDate(date.getDate() + 1);

    const day = this.daysListShort[nextDay.getDay()];
    const month = this.monthsListShort[nextDay.getMonth()];
    const dateNumber = nextDay.getDate();
    const year = nextDay.getFullYear();

    return `${day}, ${month} ${dateNumber}, ${year}`;
  }
}

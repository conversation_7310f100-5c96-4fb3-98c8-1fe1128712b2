import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import parsingUtils from '../parsing.utils';

@ValidatorConstraint({ name: 'USPhoneNumber' })
export class USPhoneNumberValidator implements ValidatorConstraintInterface {
  validate(value: unknown, validationArguments: ValidationArguments) {
    const formattedValue = parsingUtils.formatPhoneNumber(value);

    if (formattedValue) {
      validationArguments.object[validationArguments.property] = formattedValue;

      return true;
    }

    return false;
  }

  defaultMessage() {
    return 'Phone number must be a valid US phone number';
  }
}

export function IsUSPhoneNumber(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: USPhoneNumberValidator,
    });
  };
}

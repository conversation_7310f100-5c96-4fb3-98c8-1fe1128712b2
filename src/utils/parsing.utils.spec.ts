import ParsingUtils from './parsing.utils';
import { InboundEmailsSubjects } from '../modules/shared/communication/inbound-communication/enums/inbound-emails-subjects';

describe('ParsingUtils', () => {
  const street = '123 Example St';
  describe('parseStringToNumber', () => {
    it('should parse a valid string to a number', () => {
      expect(ParsingUtils.parseStringToNumber('123')).toBe(123);
    });

    it('should return null for an empty string', () => {
      expect(ParsingUtils.parseStringToNumber('')).toBeNull();
    });

    it('should return NaN for a non-numeric string', () => {
      expect(ParsingUtils.parseStringToNumber('abc')).toBeNaN();
    });
  });

  describe('parseStringToBoolean', () => {
    it('should return true for "true"', () => {
      expect(ParsingUtils.parseStringToBoolean('true')).toBe(true);
    });

    it('should return false for "false"', () => {
      expect(ParsingUtils.parseStringToBoolean('false')).toBe(false);
    });

    it('should return undefined for any other string', () => {
      expect(ParsingUtils.parseStringToBoolean('maybe')).toBeUndefined();
    });

    it('should be case insensitive', () => {
      expect(ParsingUtils.parseStringToBoolean('TrUe')).toBe(true);
      expect(ParsingUtils.parseStringToBoolean('FaLsE')).toBe(false);
    });
  });

  describe('parseAddress', () => {
    it('should extract the address from a subject line', () => {
      const subject = `${InboundEmailsSubjects.NEW_PROPERTY_MESSAGE} ${street}`;
      expect(ParsingUtils.parseAddress(subject)).toStrictEqual({
        street: street,
        apartmentNumber: null,
      });
    });

    it('should extract the address and apartment from the subject with # in it', () => {
      const subject = `${InboundEmailsSubjects.NEW_PROPERTY_MESSAGE} ${street} #1234`;
      expect(ParsingUtils.parseAddress(subject)).toStrictEqual({
        street: street,
        apartmentNumber: '1234',
      });
    });

    it('should handle subjects with "Re: " prefix', () => {
      const subject = `Re: ${InboundEmailsSubjects.NEW_PROPERTY_MESSAGE} ${street}`;
      expect(ParsingUtils.parseAddress(subject)).toStrictEqual({
        street: street,
        apartmentNumber: null,
      });
    });

    it('should handle subjects with tags and "Re: " prefix', () => {
      const subject = `Re: [Tag] ${InboundEmailsSubjects.NEW_PROPERTY_MESSAGE} ${street}`;
      expect(ParsingUtils.parseAddress(subject)).toStrictEqual({
        street: street,
        apartmentNumber: null,
      });
    });

    it('should handle subjects with multiple "Re: " prefix', () => {
      const subject = `Re: Re: Re: [Tag] ${InboundEmailsSubjects.NEW_PROPERTY_MESSAGE} ${street}`;
      expect(ParsingUtils.parseAddress(subject)).toStrictEqual({
        street: street,
        apartmentNumber: null,
      });
    });

    it('should handle subjects with multiple "Re: " prefix and apartment number', () => {
      const subject = `Re: Re: Re: [Tag] ${InboundEmailsSubjects.NEW_PROPERTY_MESSAGE} ${street}#1234`;
      expect(ParsingUtils.parseAddress(subject)).toStrictEqual({
        street: street,
        apartmentNumber: '1234',
      });
    });
  });

  describe('formatPhoneNumber', () => {
    it('should format a 10-digit US number with country code', () => {
      const number = '8145713172';
      const formattedNumber = ParsingUtils.formatPhoneNumber(number);

      expect(formattedNumber).toBe('+18145713172');
    });

    it('should accept an already formatted number', () => {
      const number = '+18145713172';
      const formattedNumber = ParsingUtils.formatPhoneNumber(number);

      expect(formattedNumber).toBe('+18145713172');
    });

    it('should throw an error for an invalid number', () => {
      const number = '12345';
      const formattedNumber = ParsingUtils.formatPhoneNumber(number);

      expect(formattedNumber).toBe(null);
    });
  });
});

import { TimezoneUtils } from './timezone.utils';

describe('timezone.utils', () => {
  it('should extract timezone by city name', () => {
    expect(TimezoneUtils.lookupTimezone('Chicago')).toBe('America/Chicago');
    expect(TimezoneUtils.lookupTimezone('Pittsburgh')).toBe('America/New_York');
  });

  it('convert server date to city date', () => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-01-25T17:00:00.000Z'));

    expect(TimezoneUtils.getCurrentCityDate('Chicago', null)).toStrictEqual(new Date('2024-01-25T11:00:00.000Z'));

    jest.useRealTimers();
  });

  it('convert js Date to string adjusted to city timezone', () => {
    const resultDateString = TimezoneUtils.convertDateToStringInCityTz(
      new Date('2024-01-25T17:00:00.000Z'),
      'Chicago',
      null,
    );

    expect(resultDateString).toStrictEqual('Thu, January 25, 2024, 11:00 AM');
  });

  describe('should convert js Date with correct time, but not adjusted to TZ to city UTC time', () => {
    it('"2024-01-25T17:00:00.000Z" to "2024-01-25T22:00:00.000Z"', () => {
      const dateWithZuluTz = new Date('2024-01-25T17:00:00.000Z');
      const dateInUtc = TimezoneUtils.convertDateWithMissingTzToUtcDate(dateWithZuluTz, 'Pittsburgh', null);

      expect(dateInUtc.toISOString()).toBe('2024-01-25T22:00:00.000Z');
    });

    it('"2024-01-25T22:00:00.000Z" to "2024-01-26T03:00:00.000Z"', () => {
      const dateWithZuluTz = new Date('2024-01-25T22:00:00.000Z');
      const dateInUtc = TimezoneUtils.convertDateWithMissingTzToUtcDate(dateWithZuluTz, 'Pittsburgh', null);

      expect(dateInUtc.toISOString()).toBe('2024-01-26T03:00:00.000Z');
    });

    it('"2024-12-31T23:00:00.000Z" to "2025-01-01T04:00:00.000Z"', () => {
      const dateWithZuluTz = new Date('2024-12-31T23:00:00.000Z');
      const dateInUtc = TimezoneUtils.convertDateWithMissingTzToUtcDate(dateWithZuluTz, 'Pittsburgh', null);

      expect(dateInUtc.toISOString()).toBe('2025-01-01T04:00:00.000Z');
    });

    it('"2024-01-17T00:00:00.000Z" to "2024-01-17T05:00:00.000Z"', () => {
      const dateWithZuluTz = new Date('2024-01-17T00:00:00.000Z');
      const dateInUtc = TimezoneUtils.convertDateWithMissingTzToUtcDate(dateWithZuluTz, 'Pittsburgh', null);

      expect(dateInUtc.toISOString()).toBe('2024-01-17T05:00:00.000Z');
    });
  });

  describe('calculateNextBusinessHour', () => {
    it('should set time to 10 AM when current hour is before business hours', () => {
      const date = new Date('2024-01-25T08:30:00.000Z'); // 8:30 AM UTC
      const result = TimezoneUtils.calculateNextBusinessHour(date, 1, 'America/Chicago');

      // Should be next day at 10 AM Chicago time (16:00 UTC)
      expect(result.getUTCHours()).toBe(16);
      expect(result.getUTCMinutes()).toBe(0);
      expect(result.getUTCDate()).toBe(26); // Next day
    });

    it('should keep the same hour when current hour is within business hours', () => {
      const date = new Date('2024-01-25T18:45:00.000Z'); // 18:45 UTC (12:45 PM Chicago)
      const result = TimezoneUtils.calculateNextBusinessHour(date, 1, 'America/Chicago');

      // Should be next day at same hour (12:45 PM Chicago time, 18:45 UTC)
      expect(result.getUTCHours()).toBe(18);
      expect(result.getUTCMinutes()).toBe(45);
      expect(result.getUTCDate()).toBe(26); // Next day
    });

    it('should set time to 10 AM next day when current hour is after business hours', () => {
      const date = new Date('2024-01-25T03:30:00.000Z'); // 3:30 AM UTC (9:30 PM Chicago previous day)
      const result = TimezoneUtils.calculateNextBusinessHour(date, 1, 'America/Chicago');

      // Should be next day at 10 AM Chicago time (16:00 UTC)
      expect(result.getUTCHours()).toBe(16);
      expect(result.getUTCMinutes()).toBe(0);
      expect(result.getUTCDate()).toBe(26); // Next day
    });

    it('should add the specified number of days', () => {
      const date = new Date('2024-01-25T18:30:00.000Z'); // 18:30 UTC (12:30 PM Chicago)
      const result = TimezoneUtils.calculateNextBusinessHour(date, 3, 'America/Chicago');

      // Should be 3 days later at same hour
      expect(result.getUTCHours()).toBe(18);
      expect(result.getUTCMinutes()).toBe(30);
      expect(result.getUTCDate()).toBe(28); // 3 days later
    });
  });
});

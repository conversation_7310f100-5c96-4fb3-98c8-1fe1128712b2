import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Property } from '../../modules/investor/property/property/entities/property.entity';
import { Repository } from 'typeorm';

@Injectable()
export class IsPropertyOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const propertyId = request.params.propertyId || request.body.propertyId;

    if (!propertyId) {
      throw new NotFoundException('Property not found');
    }

    const property = await this.propertyRepository.findOneOrFail({
      where: { id: propertyId },
      relations: ['owner'],
    });

    const investor = await property.owner;

    if ((await investor.user).id !== request.user.id) {
      throw new HttpException('You are not the owner of this property', HttpStatus.FORBIDDEN);
    }

    // Add the fetched property to the request object for later use
    request.property = property;

    return true;
  }
}

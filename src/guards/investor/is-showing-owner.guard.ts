import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Showing } from '../../modules/investor/showing/showing.entity';

@Injectable()
export class IsShowingOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(Showing)
    private readonly showingRepository: Repository<Showing>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const showingId = request.params.showingId || request.body.showingId;

    if (!showingId) {
      throw new NotFoundException('Showing not found');
    }

    const showing = await this.showingRepository.findOne({
      where: { id: showingId },
      relations: ['property', 'property.owner', 'property.owner.user'],
    });

    if (!showing) {
      throw new NotFoundException('Showing not found');
    }

    const property = await showing.property;
    const owner = await property.owner;
    const user = await owner.user;

    if (user.id !== request.user.id) {
      throw new ForbiddenException('You are not the owner of this showing');
    }

    request.showing = showing;

    return true;
  }
}

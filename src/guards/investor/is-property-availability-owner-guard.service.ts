import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PropertyAvailability } from '../../modules/investor/property/availability/property-availability.entity';

@Injectable()
export class IsPropertyAvailabilityOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(PropertyAvailability)
    private readonly propertyAvailabilityRepository: Repository<PropertyAvailability>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const propertyAvailabilityId = request.params.propertyAvailabilityId || request.body.propertyAvailabilityId;

    if (!propertyAvailabilityId) {
      throw new NotFoundException('Property availability not found');
    }

    const propertyAvailability = await this.propertyAvailabilityRepository.findOne({
      where: { id: propertyAvailabilityId },
      relations: ['property', 'property.owner'],
    });

    if (!propertyAvailability) {
      throw new NotFoundException('Property availability not found');
    }

    const property = await propertyAvailability.property;
    const investor = await property.owner;
    const user = await investor.user;

    if (user.id !== request.user.id) {
      throw new HttpException('You are not the owner of this property', HttpStatus.FORBIDDEN);
    }

    return true;
  }
}

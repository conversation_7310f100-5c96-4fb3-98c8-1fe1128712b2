import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AvailabilitySlot } from '../../modules/investor/availability/availability-slot.entity';

@Injectable()
export class IsAvailabilitySlotOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(AvailabilitySlot)
    private readonly availabilitySlotRepository: Repository<AvailabilitySlot>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    let slotId;

    // Handle different possible formats of the request body
    if (request.body.id) {
      // If body has an id property
      slotId = request.body.id;
    } else if (request.body.ids && request.body.ids.length > 0) {
      // If body has an ids array property
      slotId = request.body.ids[0];
    } else if (Array.isArray(request.body) && request.body.length > 0) {
      // If body is an array directly
      slotId = request.body[0];
    } else {
      slotId = null;
    }

    if (!slotId) {
      throw new NotFoundException('Availability slot not found');
    }

    const availabilitySlot = await this.availabilitySlotRepository.findOne({
      where: { id: slotId },
      relations: ['propertyAvailability', 'propertyAvailability.property', 'propertyAvailability.property.owner'],
    });

    if (!availabilitySlot) {
      throw new NotFoundException('Availability slot not found');
    }

    const propertyAvailability = await availabilitySlot.propertyAvailability;
    const property = await propertyAvailability.property;
    const investor = await property.owner;
    const user = await investor.user;

    if (user.id !== request.user.id) {
      throw new HttpException('You are not the owner of this availability slot', HttpStatus.FORBIDDEN);
    }

    return true;
  }
}

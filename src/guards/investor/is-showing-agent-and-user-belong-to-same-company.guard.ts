import { CanActivate, ExecutionContext, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ShowingAgent } from '../../modules/investor/showing-agent/entities/showing-agent.entity';
import { CompanyService } from '../../modules/shared/company/company.service';

@Injectable()
export class IsShowingAgentAndUserBelongToSameCompanyGuard implements CanActivate {
  constructor(
    @InjectRepository(ShowingAgent)
    private readonly showingAgentRepository: Repository<ShowingAgent>,
    private readonly companyService: CompanyService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const showingAgentId = request.params.showingAgentId || request.body.showingAgentId;
    const userId = request.user.id;

    if (!showingAgentId) {
      throw new NotFoundException('Showing agent not found');
    }

    const company = await this.companyService.getCompanyByUser(userId);
    const agent = await this.showingAgentRepository.findOneBy({ id: showingAgentId });

    if (!agent || !company || company.id !== agent.companyId) {
      throw new ForbiddenException('Agent does not belong to your company');
    }

    return true;
  }
}

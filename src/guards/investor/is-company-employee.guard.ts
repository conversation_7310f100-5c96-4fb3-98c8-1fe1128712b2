import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Company } from '../../modules/shared/company/entities/company.entity';

@Injectable()
export class IsCompanyEmployeeGuard implements CanActivate {
  constructor(
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const companyId = request.params.companyId || request.body.companyId || request.user.companyId;

    if (!companyId) {
      throw new NotFoundException('Company not found');
    }

    const company = await this.companyRepository.findOneOrFail({
      where: { id: companyId },
      relations: ['investors'],
    });

    const currentUserId = request.user.id;

    const isCompanyEmployee = (await company.investors).some(
      async (investor) => (await investor.user).id === currentUserId,
    );

    if (!isCompanyEmployee) {
      throw new HttpException('You are not the employee of this company', HttpStatus.FORBIDDEN);
    }

    return true;
  }
}

import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationInvite } from '../../modules/shared/application/application-invite/application-invite.entity';

@Injectable()
export class IsApplicationInviteInvitedByGuard implements CanActivate {
  constructor(
    @InjectRepository(ApplicationInvite)
    private readonly applicationInviteRepository: Repository<ApplicationInvite>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const applicationInviteId = request.params.applicationInviteId || request.body.applicationInviteId;

    if (!applicationInviteId) {
      throw new NotFoundException('Application invite not found');
    }

    const applicationInvite = await this.applicationInviteRepository.findOneOrFail({
      where: { id: applicationInviteId },
      relations: ['renter'],
    });

    const applicationBundle = await applicationInvite.applicationBundle;

    const primaryApplicant = await applicationBundle.primaryApplicant;
    const invitedBy = await applicationInvite.invitedBy;

    if (primaryApplicant.user.id !== request.user.id && invitedBy.id !== request.user.id) {
      throw new HttpException(
        'This application invite was not sent by you or you are not the owner of this application',
        HttpStatus.FORBIDDEN,
      );
    }

    return true;
  }
}

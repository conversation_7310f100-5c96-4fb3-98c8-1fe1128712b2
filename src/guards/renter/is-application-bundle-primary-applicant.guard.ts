import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationBundle } from '../../modules/shared/application/application-bundle/application-bundle.entity';

@Injectable()
export class IsApplicationBundlePrimaryApplicantGuard implements CanActivate {
  constructor(
    @InjectRepository(ApplicationBundle)
    private readonly applicationBundleRepository: Repository<ApplicationBundle>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const applicationBundleId = request.params.applicationBundleId || request.body.applicationBundleId;

    if (!applicationBundleId) {
      throw new NotFoundException('Application bundle not found');
    }

    const applicationBundle = await this.applicationBundleRepository.findOneOrFail({
      where: { id: applicationBundleId },
      relations: ['primaryApplicant'],
    });

    const primaryApplicant = await applicationBundle.primaryApplicant;

    if (primaryApplicant.user.id !== request.user.id) {
      throw new HttpException('You are not the primary applicant of this application bundle', HttpStatus.FORBIDDEN);
    }

    return true;
  }
}

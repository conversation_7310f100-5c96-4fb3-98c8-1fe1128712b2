import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationBundle } from '../../modules/shared/application/application-bundle/application-bundle.entity';

@Injectable()
export class IsApplicationBundleParticipantGuard implements CanActivate {
  constructor(
    @InjectRepository(ApplicationBundle)
    private readonly applicationBundleRepository: Repository<ApplicationBundle>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const applicationBundleId = request.params.applicationBundleId || request.body.applicationBundleId;

    if (!applicationBundleId) {
      throw new NotFoundException('Application bundle not found');
    }

    const applicationBundle = await this.applicationBundleRepository.findOneOrFail({
      where: { id: applicationBundleId },
      relations: ['applicationInvites', 'applicationInvites.renter'],
    });

    const invites = await applicationBundle.applicationInvites;

    const renters = invites.map((invite) => invite.renter);

    await Promise.all(renters).then((renters) => {
      const isParticipant = renters.some((renter) => renter.user.id === request.user.id);

      if (!isParticipant) {
        throw new HttpException('You are not the participant of this application bundle', HttpStatus.FORBIDDEN);
      }
    });

    return true;
  }
}

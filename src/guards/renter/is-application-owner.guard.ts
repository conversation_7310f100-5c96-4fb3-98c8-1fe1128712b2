import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Application } from '../../modules/shared/application/application/application.entity';

@Injectable()
export class IsApplicationOwnerGuard implements CanActivate {
  constructor(
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const applicationId = request.params.applicationId || request.body.applicationId;

    if (!applicationId) {
      throw new NotFoundException('Application not found');
    }

    const application = await this.applicationRepository.findOneOrFail({
      where: { id: applicationId },
      relations: ['renter'],
    });

    const renter = await application.renter;

    if (renter.user.id !== request.user.id) {
      throw new HttpException('You are not the owner of this application', HttpStatus.FORBIDDEN);
    }

    return true;
  }
}

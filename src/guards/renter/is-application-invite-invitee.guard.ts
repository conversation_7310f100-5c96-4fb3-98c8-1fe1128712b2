import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationInvite } from '../../modules/shared/application/application-invite/application-invite.entity';

@Injectable()
export class IsApplicationInviteInviteeGuard implements CanActivate {
  constructor(
    @InjectRepository(ApplicationInvite)
    private readonly applicationInviteRepository: Repository<ApplicationInvite>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const applicationInviteId = request.params.applicationInviteId || request.body.applicationInviteId;

    if (!applicationInviteId) {
      throw new NotFoundException('Application invite not found');
    }

    const applicationInvite = await this.applicationInviteRepository.findOneOrFail({
      where: { id: applicationInviteId },
      relations: ['renter'],
    });

    const renter = await applicationInvite.renter;

    if (renter.user.id !== request.user.id) {
      throw new HttpException('You are not the owner of this application invite', HttpStatus.FORBIDDEN);
    }

    return true;
  }
}

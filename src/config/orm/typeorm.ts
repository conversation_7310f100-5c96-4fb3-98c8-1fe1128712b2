import { registerAs } from '@nestjs/config';
import { config as dotenvConfig } from 'dotenv';
import { DataSource, DataSourceOptions } from 'typeorm';

import { TypeormLogger } from './typeorm-logger';

dotenvConfig({ path: './src/config/envs/.env' });

const config = {
  type: 'postgres',
  host: process.env.DATABASE_HOST,
  port: process.env.DATABASE_PORT,
  username: process.env.DATABASE_USER,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  entities: ['dist/**/*.entity{.ts,.js}'],
  migrations: ['dist/db/migrations/*{.ts,.js}'],
  migrationsRun: process.env.DATABASE_HOST !== 'localhost',
  migrationsTableName: 'migration',
  autoLoadEntities: true,
  synchronize: false,
  logger: new TypeormLogger(false),
  ssl: process.env.DATABASE_SSL === 'true',
};

export default registerAs('typeorm', () => config);
export const connectionSource = new DataSource(config as DataSourceOptions);

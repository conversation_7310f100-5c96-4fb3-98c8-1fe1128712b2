import { AbstractLogger, LogLevel, LogMessage, LoggerOptions } from 'typeorm';

export class TypeormLogger extends AbstractLogger {
  constructor(options?: LoggerOptions) {
    super(options);
  }

  protected writeLog(level: LogLevel, logMessage: LogMessage | LogMessage[]) {
    const messages = this.prepareLogMessages(logMessage, {
      highlightSql: false,
    });

    for (const message of messages) {
      switch (message.type ?? level) {
        case 'log':
        case 'schema-build':
        case 'migration':
          console.log(message.message);
          break;

        case 'info':
        case 'query':
          if (message.prefix) {
            console.log(message.prefix, message.message);
          } else {
            console.log(message.message);
          }
          break;

        case 'warn':
        case 'query-slow':
          if (message.prefix) {
            console.log(message.prefix, message.message);
          } else {
            console.log(message.message);
          }
          break;

        case 'error':
        case 'query-error':
          if (message.prefix) {
            console.log(message.prefix, message.message);
          } else {
            console.log(message.message);
          }
          break;
      }
    }
  }
}

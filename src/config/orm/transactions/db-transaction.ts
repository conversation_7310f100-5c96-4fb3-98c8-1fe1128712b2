import { Injectable, Scope } from '@nestjs/common';
import { Connection, QueryRunner } from 'typeorm';

@Injectable({ scope: Scope.REQUEST })
export class DbTransaction {
  private queryRunner: QueryRunner;

  constructor(private readonly connection: Connection) {
    this.queryRunner = this.connection.createQueryRunner();
  }

  async start(): Promise<void> {
    if (!this.queryRunner.isReleased) {
      await this.queryRunner.connect();
      await this.queryRunner.startTransaction();
    }
  }

  async commitTransaction(): Promise<void> {
    if (this.queryRunner.isTransactionActive) {
      await this.queryRunner.commitTransaction();
    }
  }

  async rollbackTransaction(): Promise<void> {
    if (this.queryRunner.isTransactionActive) {
      await this.queryRunner.rollbackTransaction();
    }
  }

  async release(): Promise<void> {
    if (!this.queryRunner.isReleased) {
      await this.queryRunner.release();
    }
  }
}
